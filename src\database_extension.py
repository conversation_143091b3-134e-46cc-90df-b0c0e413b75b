"""
Extension to Database class with improved terminal loading
"""

def get_terminal_as_object(self, terminal_id=None):
    """Get complete terminal data as a Terminal object"""
    if terminal_id is None:
        terminal_id = self.get_active_terminal_id()
        
    # Get basic terminal info
    terminal_data = self.get_terminal(terminal_id)
    if not terminal_data:
        return None
        
    # Get jetties with full data
    jetties_data = self.get_jetties(terminal_id)
    
    # Import Terminal classes
    from .models.terminal import Terminal, Jetty, JettyType, LoadingArm, Tank, Pump, Surveyor
    
    # Convert jetties to Jetty objects
    jetties = []
    for jetty_data in jetties_data:
        # Map database jetty type to JettyType enum
        jetty_type = JettyType.VESSEL_BERTH if jetty_data['type'] == 'VESSEL' else JettyType.BARGE_BERTH
        
        # Create a simple loading arm for the flow rate from database
        loading_arms = [
            LoadingArm(
                id=f"{jetty_data['id']}_LA1",
                name=f"{jetty_data['name']} Loading Arm",
                flow_rate=jetty_data.get('max_flow_rate', 1000.0),
                compatible_products=["hydrocarbons", "naphtha", "benzene", "minerals", "butane", "acrylonitrile", "propylene_oxide"],
                is_operational=bool(jetty_data.get('is_operational', True)),
                has_vapor_return=True
            )
        ]
        
        jetty = Jetty(
            id=str(jetty_data['id']),
            name=jetty_data['name'],
            jetty_type=jetty_type,
            max_length=jetty_data.get('max_loa', 200.0),
            max_draft=jetty_data.get('max_draft', 12.0),
            max_deadweight=jetty_data.get('max_dwt', 50000.0),
            loading_arms=loading_arms,
            connected_tanks=[],
            connected_pumps=[],
            is_operational=bool(jetty_data.get('is_operational', True))
        )
        jetties.append(jetty)
    
    # Create Terminal object
    terminal = Terminal(
        name=terminal_data['name'],
        jetties=jetties,
        tanks=[],  # Add tanks conversion if needed
        pumps=[],  # Add pumps conversion if needed
        surveyors=[],  # Add surveyors conversion if needed
        location=(terminal_data.get('location_lat'), terminal_data.get('location_lon'))
    )
    
    return terminal

# Monkey patch the Database class
from .database import Database
Database.get_terminal_as_object = get_terminal_as_object
