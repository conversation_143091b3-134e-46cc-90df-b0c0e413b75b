"""
Product Model

This module defines the Product class for different petrochemical products handled by the terminal.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Set
from enum import Enum


class ProductCategory(Enum):
    """Category of petrochemical product"""
    GASOLINE = "gasoline"
    GASOIL = "gasoil"
    JETFUEL = "jetfuel"
    BIOFUEL = "biofuel"


class ProductProperty(Enum):
    """Physical or chemical property of a product"""
    DENSITY = "density"  # kg/m³
    VISCOSITY = "viscosity"  # centistokes
    FLASH_POINT = "flash_point"  # °C
    POUR_POINT = "pour_point"  # °C
    SULFUR_CONTENT = "sulfur_content"  # % by weight


@dataclass
class Product:
    """Petrochemical product handled by the terminal"""
    id: str
    name: str
    category: ProductCategory
    properties: Dict[ProductProperty, float] = field(default_factory=dict)
    requires_floating_roof: bool = False
    requires_dedicated_tank: bool = False
    requires_specialized_surveyor: bool = False
    requires_heated_storage: bool = False
    min_throughput_rate: float = 0.0  # Minimum flow rate in m³/h
    compatible_products: Set[str] = field(default_factory=set)  # Products that can be mixed
    
    def __post_init__(self):
        # Set default properties based on category if not provided
        if not self.properties:
            if self.category == ProductCategory.GASOLINE:
                self.properties = {
                    ProductProperty.DENSITY: 750.0,
                    ProductProperty.VISCOSITY: 0.5,
                    ProductProperty.FLASH_POINT: -40.0,
                    ProductProperty.POUR_POINT: -40.0,
                    ProductProperty.SULFUR_CONTENT: 0.001
                }
                self.requires_floating_roof = True
            
            elif self.category == ProductCategory.GASOIL:
                self.properties = {
                    ProductProperty.DENSITY: 850.0,
                    ProductProperty.VISCOSITY: 5.0,
                    ProductProperty.FLASH_POINT: 55.0,
                    ProductProperty.POUR_POINT: -10.0,
                    ProductProperty.SULFUR_CONTENT: 0.05
                }
            
            elif self.category == ProductCategory.JETFUEL:
                self.properties = {
                    ProductProperty.DENSITY: 800.0,
                    ProductProperty.VISCOSITY: 4.0,
                    ProductProperty.FLASH_POINT: 38.0,
                    ProductProperty.POUR_POINT: -47.0,
                    ProductProperty.SULFUR_CONTENT: 0.003
                }
                self.requires_specialized_surveyor = True
            
            elif self.category == ProductCategory.BIOFUEL:
                self.properties = {
                    ProductProperty.DENSITY: 880.0,
                    ProductProperty.VISCOSITY: 4.5,
                    ProductProperty.FLASH_POINT: 130.0,
                    ProductProperty.POUR_POINT: -5.0,
                    ProductProperty.SULFUR_CONTENT: 0.0001
                }
                self.requires_dedicated_tank = True
        
        # Set compatible products based on category if not provided
        if not self.compatible_products:
            if self.category == ProductCategory.GASOLINE:
                self.compatible_products = {
                    "regular_gasoline", "premium_gasoline", "super_gasoline"
                }
            elif self.category == ProductCategory.GASOIL:
                self.compatible_products = {
                    "diesel", "heating_oil", "marine_gasoil"
                }
            elif self.category == ProductCategory.JETFUEL:
                self.compatible_products = {
                    "jet_a", "jet_a1", "jp8"
                }
            elif self.category == ProductCategory.BIOFUEL:
                self.compatible_products = {
                    "biodiesel", "ethanol", "renewable_diesel"
                }
    
    @property
    def is_gasoline(self) -> bool:
        """Check if product is gasoline"""
        return self.category == ProductCategory.GASOLINE
    
    @property
    def is_gasoil(self) -> bool:
        """Check if product is gasoil"""
        return self.category == ProductCategory.GASOIL
    
    @property
    def is_jetfuel(self) -> bool:
        """Check if product is jetfuel"""
        return self.category == ProductCategory.JETFUEL
    
    @property
    def is_biofuel(self) -> bool:
        """Check if product is biofuel"""
        return self.category == ProductCategory.BIOFUEL
    
    def get_density(self) -> float:
        """Get product density"""
        return self.properties.get(ProductProperty.DENSITY, 0.0)
    
    def get_viscosity(self) -> float:
        """Get product viscosity"""
        return self.properties.get(ProductProperty.VISCOSITY, 0.0)
    
    def get_flash_point(self) -> float:
        """Get product flash point"""
        return self.properties.get(ProductProperty.FLASH_POINT, 0.0)
    
    def get_pour_point(self) -> float:
        """Get product pour point"""
        return self.properties.get(ProductProperty.POUR_POINT, 0.0)
    
    def get_sulfur_content(self) -> float:
        """Get product sulfur content"""
        return self.properties.get(ProductProperty.SULFUR_CONTENT, 0.0)
    
    def can_mix_with(self, other_product: 'Product') -> bool:
        """Check if this product can be mixed with another product"""
        # Can always mix with itself
        if self.id == other_product.id:
            return True
        
        # Check if other product id is in compatible products
        return (other_product.id in self.compatible_products or 
                any(p_id in self.compatible_products for p_id in other_product.compatible_products))
