{% extends "base.html" %}

{% block title %}Terminals - Terneuzen Terminal Jetty Planning{% endblock %}

{% block header %}Terminal Comparison{% endblock %}

{% block user_actions %}
    <button id="refresh-comparison-btn" class="btn btn-secondary">
        <i class="fas fa-sync-alt"></i>
        Refresh
    </button>
    <button id="compare-selected-btn" class="btn btn-primary" disabled>
        <i class="fas fa-balance-scale"></i>
        Compare Selected
    </button>
{% endblock %}

{% block content %}
    <!-- Terminal Selection -->
    <div class="card">
        <div class="card-header">
            <h3>Select Terminals to Compare</h3>
            <p class="card-subtitle">Choose terminals to compare their capabilities, capacity, and operational metrics</p>
        </div>
        <div class="card-body">
            <div id="terminal-selection-grid" class="terminal-grid">
                <!-- Terminal cards will be loaded here -->
                <div class="loading-text">Loading terminals...</div>
            </div>
        </div>
    </div>

    <!-- Comparison Results -->
    <div id="comparison-results" class="card" style="display: none;">
        <div class="card-header">
            <h3>Terminal Comparison Results</h3>
            <div class="comparison-actions">
                <button id="export-comparison-btn" class="btn btn-sm btn-secondary">
                    <i class="fas fa-download"></i>
                    Export
                </button>
                <button id="clear-selection-btn" class="btn btn-sm btn-outline">
                    <i class="fas fa-times"></i>
                    Clear Selection
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- Summary Metrics -->
            <div id="comparison-summary" class="comparison-section">
                <h4>Summary Metrics</h4>
                <div id="summary-grid" class="metrics-grid">
                    <!-- Summary comparison will be loaded here -->
                </div>
            </div>

            <!-- Detailed Comparison Table -->
            <div id="detailed-comparison" class="comparison-section">
                <h4>Detailed Comparison</h4>
                <div class="table-container">
                    <table id="comparison-table">
                        <thead>
                            <tr>
                                <th>Metric</th>
                                <!-- Terminal columns will be added dynamically -->
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Comparison rows will be added here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Operational Charts -->
            <div id="comparison-charts" class="comparison-section">
                <h4>Operational Overview</h4>
                <div class="charts-grid">
                    <div class="chart-card">
                        <h5>Capacity Comparison</h5>
                        <div id="capacity-chart" class="chart-placeholder">
                            <div class="chart-bars" id="capacity-bars">
                                <!-- Capacity bars will be generated here -->
                            </div>
                        </div>
                    </div>
                    <div class="chart-card">
                        <h5>Infrastructure Overview</h5>
                        <div id="infrastructure-chart" class="chart-placeholder">
                            <div class="chart-bars" id="infrastructure-bars">
                                <!-- Infrastructure bars will be generated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script nonce="{{ nonce }}">
        let selectedTerminals = [];
        let allTerminals = [];

        // Load all terminals on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadTerminals();
        });

        // Function to load all terminals
        async function loadTerminals() {
            try {
                console.log('Loading terminals for comparison...');
                const response = await fetch('/api/terminals/');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const terminals = await response.json();
                allTerminals = terminals;
                
                displayTerminalSelection(terminals);
                
            } catch (error) {
                console.error('Error loading terminals:', error);
                const grid = document.getElementById('terminal-selection-grid');
                grid.innerHTML = '<div class="error-text">Error loading terminals. Please try again.</div>';
            }
        }

        // Function to display terminal selection cards
        function displayTerminalSelection(terminals) {
            const grid = document.getElementById('terminal-selection-grid');
            
            if (!terminals || terminals.length === 0) {
                grid.innerHTML = '<div class="no-data">No terminals available for comparison</div>';
                return;
            }

            grid.innerHTML = terminals.map(terminal => `
                <div class="terminal-card" data-terminal-id="${terminal.id}">
                    <div class="terminal-card-header">
                        <div class="selection-checkbox">
                            <input type="checkbox" id="terminal-${terminal.id}" value="${terminal.id}">
                            <label for="terminal-${terminal.id}"></label>
                        </div>
                        <h4 class="terminal-name">${terminal.name}</h4>
                        <span class="terminal-code">${terminal.code}</span>
                    </div>
                    <div class="terminal-card-body">
                        <div class="terminal-metrics">
                            <div class="metric">
                                <span class="metric-label">Total Capacity</span>
                                <span class="metric-value">${terminal.total_capacity.toLocaleString()} m³</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Max Draft</span>
                                <span class="metric-value">${terminal.max_draft} m</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Vessel Berths</span>
                                <span class="metric-value">${terminal.vessel_berths}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">Barge Berths</span>
                                <span class="metric-value">${terminal.barge_berths}</span>
                            </div>
                        </div>
                        <div class="terminal-products">
                            <h5>Products</h5>
                            <div class="product-tags">
                                ${terminal.products.slice(0, 3).map(product => 
                                    `<span class="product-tag">${product}</span>`
                                ).join('')}
                                ${terminal.products.length > 3 ? 
                                    `<span class="product-tag more">+${terminal.products.length - 3} more</span>` : 
                                    ''
                                }
                            </div>
                        </div>
                        <div class="terminal-certifications">
                            <h5>Certifications</h5>
                            <div class="cert-count">${terminal.certifications.length} certifications</div>
                        </div>
                    </div>
                </div>
            `).join('');

            // Add event listeners for checkboxes
            const checkboxes = grid.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectedTerminals();
                });
            });
        }

        // Function to update selected terminals
        function updateSelectedTerminals() {
            const checkboxes = document.querySelectorAll('#terminal-selection-grid input[type="checkbox"]:checked');
            selectedTerminals = Array.from(checkboxes).map(cb => cb.value);
            
            // Update compare button state
            const compareBtn = document.getElementById('compare-selected-btn');
            compareBtn.disabled = selectedTerminals.length < 2;
            
            if (selectedTerminals.length >= 2) {
                compareBtn.textContent = `Compare ${selectedTerminals.length} Terminals`;
            } else {
                compareBtn.innerHTML = '<i class="fas fa-balance-scale"></i> Compare Selected';
            }
        }

        // Function to perform comparison
        async function performComparison() {
            if (selectedTerminals.length < 2) {
                showWarningToast('Please select at least 2 terminals to compare');
                return;
            }

            try {
                console.log('Comparing terminals:', selectedTerminals);

                const response = await fetch('/api/terminals/compare', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        terminal_ids: selectedTerminals
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const comparisonData = await response.json();
                displayComparisonResults(comparisonData);
                showSuccessToast('Terminal comparison completed successfully');

            } catch (error) {
                console.error('Error performing comparison:', error);
                showErrorToast('Error performing comparison. Please try again.');
            }
        }

        // Function to display comparison results
        function displayComparisonResults(data) {
            const resultsCard = document.getElementById('comparison-results');
            
            // Show results card
            resultsCard.style.display = 'block';
            resultsCard.scrollIntoView({ behavior: 'smooth' });

            // Display summary metrics
            displaySummaryMetrics(data.summary);

            // Display detailed comparison table
            displayDetailedComparison(data.terminals);

            // Display charts
            displayComparisonCharts(data.terminals);
        }

        // Function to display summary metrics
        function displaySummaryMetrics(summary) {
            const summaryGrid = document.getElementById('summary-grid');
            
            summaryGrid.innerHTML = `
                <div class="summary-metric">
                    <h5>Total Capacity</h5>
                    <div class="metric-value-large">${summary.total_capacity.toLocaleString()} m³</div>
                    <div class="metric-detail">Across ${summary.terminal_count} terminals</div>
                </div>
                <div class="summary-metric">
                    <h5>Average Capacity</h5>
                    <div class="metric-value-large">${summary.avg_capacity.toLocaleString()} m³</div>
                    <div class="metric-detail">Per terminal</div>
                </div>
                <div class="summary-metric">
                    <h5>Total Berths</h5>
                    <div class="metric-value-large">${summary.total_berths}</div>
                    <div class="metric-detail">${summary.total_vessel_berths} vessel + ${summary.total_barge_berths} barge</div>
                </div>
                <div class="summary-metric">
                    <h5>Max Draft</h5>
                    <div class="metric-value-large">${summary.max_draft} m</div>
                    <div class="metric-detail">Deepest terminal capability</div>
                </div>
                <div class="summary-metric">
                    <h5>Total Products</h5>
                    <div class="metric-value-large">${summary.unique_products}</div>
                    <div class="metric-detail">Unique product types</div>
                </div>
                <div class="summary-metric">
                    <h5>Certifications</h5>
                    <div class="metric-value-large">${summary.total_certifications}</div>
                    <div class="metric-detail">Combined certifications</div>
                </div>
            `;
        }

        // Function to display detailed comparison table
        function displayDetailedComparison(terminals) {
            const table = document.getElementById('comparison-table');
            const thead = table.querySelector('thead tr');
            const tbody = table.querySelector('tbody');

            // Clear existing content
            thead.innerHTML = '<th>Metric</th>';
            tbody.innerHTML = '';

            // Add terminal columns to header
            terminals.forEach(terminal => {
                const th = document.createElement('th');
                th.innerHTML = `${terminal.name}<br><small>${terminal.code}</small>`;
                thead.appendChild(th);
            });

            // Define comparison metrics
            const metrics = [
                { label: 'Total Capacity (m³)', getValue: t => t.total_capacity.toLocaleString() },
                { label: 'Max Draft (m)', getValue: t => t.max_draft },
                { label: 'Vessel Berths', getValue: t => t.vessel_berths },
                { label: 'Barge Berths', getValue: t => t.barge_berths },
                { label: 'Total Berths', getValue: t => t.vessel_berths + t.barge_berths },
                { label: 'Products', getValue: t => t.products.length },
                { label: 'Certifications', getValue: t => t.certifications.length },
                { label: 'Current Vessels', getValue: t => t.statistics?.current_vessels || 0 },
                { label: 'Active Assignments', getValue: t => t.statistics?.active_assignments || 0 },
                { label: 'Jetties', getValue: t => t.statistics?.jetties || 0 },
                { label: 'Tanks', getValue: t => t.statistics?.tanks || 0 },
                { label: 'Pumps', getValue: t => t.statistics?.pumps || 0 }
            ];

            // Add rows for each metric
            metrics.forEach(metric => {
                const row = document.createElement('tr');
                
                // Metric label
                const labelCell = document.createElement('td');
                labelCell.className = 'metric-label-cell';
                labelCell.textContent = metric.label;
                row.appendChild(labelCell);

                // Values for each terminal
                terminals.forEach(terminal => {
                    const valueCell = document.createElement('td');
                    valueCell.textContent = metric.getValue(terminal);
                    row.appendChild(valueCell);
                });

                tbody.appendChild(row);
            });
        }

        // Function to display comparison charts
        function displayComparisonCharts(terminals) {
            // Capacity chart
            const capacityBars = document.getElementById('capacity-bars');
            const maxCapacity = Math.max(...terminals.map(t => t.total_capacity));
            
            capacityBars.innerHTML = terminals.map(terminal => {
                const percentage = (terminal.total_capacity / maxCapacity) * 100;
                return `
                    <div class="bar-item">
                        <div class="bar-label">${terminal.name}</div>
                        <div class="bar-container">
                            <div class="bar" style="width: ${percentage}%"></div>
                        </div>
                        <div class="bar-value">${terminal.total_capacity.toLocaleString()} m³</div>
                    </div>
                `;
            }).join('');

            // Infrastructure chart
            const infrastructureBars = document.getElementById('infrastructure-bars');
            const maxBerths = Math.max(...terminals.map(t => t.vessel_berths + t.barge_berths));
            
            infrastructureBars.innerHTML = terminals.map(terminal => {
                const totalBerths = terminal.vessel_berths + terminal.barge_berths;
                const percentage = (totalBerths / maxBerths) * 100;
                return `
                    <div class="bar-item">
                        <div class="bar-label">${terminal.name}</div>
                        <div class="bar-container">
                            <div class="bar" style="width: ${percentage}%"></div>
                        </div>
                        <div class="bar-value">${totalBerths} berths</div>
                    </div>
                `;
            }).join('');
        }

        // Event listeners
        document.getElementById('compare-selected-btn').addEventListener('click', performComparison);
        
        document.getElementById('refresh-comparison-btn').addEventListener('click', function() {
            loadTerminals();
            document.getElementById('comparison-results').style.display = 'none';
            selectedTerminals = [];
            updateSelectedTerminals();
        });

        document.getElementById('clear-selection-btn').addEventListener('click', function() {
            // Clear all checkboxes
            const checkboxes = document.querySelectorAll('#terminal-selection-grid input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            
            // Update state
            selectedTerminals = [];
            updateSelectedTerminals();
            
            // Hide results
            document.getElementById('comparison-results').style.display = 'none';
        });

        document.getElementById('export-comparison-btn').addEventListener('click', function() {
            // Simple export functionality
            if (selectedTerminals.length < 2) {
                showWarningToast('No comparison data to export');
                return;
            }

            showInfoToast('Export functionality would be implemented here. This could generate PDF, Excel, or CSV exports of the comparison data.', { timeout: 6000 });
        });

        // Listen for terminal changes
        document.addEventListener('terminalChanged', function(event) {
            console.log('Terminal changed on comparison page:', event.detail);
            // Reload terminals to reflect any changes
            loadTerminals();
        });

        // Listen for page refresh requests  
        document.addEventListener('refreshPageData', function(event) {
            console.log('Refreshing comparison page data:', event.detail);
            loadTerminals();
        });
    </script>
{% endblock %} 