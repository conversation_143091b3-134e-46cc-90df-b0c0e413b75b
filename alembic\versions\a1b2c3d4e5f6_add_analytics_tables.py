"""add analytics tables

Revision ID: a1b2c3d4e5f6
Revises: f1c2d3e4abcd
Create Date: 2025-01-27 12:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a1b2c3d4e5f6'
down_revision = 'f1c2d3e4abcd'
branch_labels = None
depends_on = None


def upgrade():
    """Create analytics tables for PostgreSQL"""
    
    # Create ml_predictions_log table
    try:
        op.create_table('ml_predictions_log',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('assignment_id', sa.Integer(), nullable=True),
            sa.Column('vessel_id', sa.String(length=255), nullable=True),
            sa.Column('vessel_name', sa.String(length=255), nullable=True),
            sa.Column('prediction_type', sa.String(length=50), nullable=False),
            sa.Column('predicted_minutes', sa.Integer(), nullable=True),
            sa.Column('actual_minutes', sa.Integer(), nullable=True),
            sa.Column('confidence_score', sa.Float(), nullable=True),
            sa.Column('prediction_timestamp', sa.DateTime(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
            sa.Column('actual_timestamp', sa.DateTime(), nullable=True),
            sa.Column('accuracy_percentage', sa.Float(), nullable=True),
            sa.Column('absolute_error_minutes', sa.Integer(), nullable=True),
            sa.Column('terminal_id', sa.String(length=50), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
            sa.ForeignKeyConstraint(['terminal_id'], ['terminals.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
    except Exception as e:
        print(f"Error creating ml_predictions_log table: {e}")
        pass

    # Create planning_metrics table
    try:
        op.create_table('planning_metrics',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('date', sa.Date(), nullable=False),
            sa.Column('terminal_id', sa.String(length=50), nullable=False),
            sa.Column('total_assignments', sa.Integer(), nullable=True, server_default='0'),
            sa.Column('optimized_assignments', sa.Integer(), nullable=True, server_default='0'),
            sa.Column('manual_changes', sa.Integer(), nullable=True, server_default='0'),
            sa.Column('schedule_utilization_percent', sa.Float(), nullable=True),
            sa.Column('average_turnaround_hours', sa.Float(), nullable=True),
            sa.Column('throughput_efficiency', sa.Float(), nullable=True),
            sa.Column('total_vessels_processed', sa.Integer(), nullable=True),
            sa.Column('idle_time_hours', sa.Float(), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
            sa.ForeignKeyConstraint(['terminal_id'], ['terminals.id'], ),
            sa.PrimaryKeyConstraint('id'),
            sa.UniqueConstraint('date', 'terminal_id', name='uq_planning_metrics_date_terminal')
        )
    except Exception as e:
        print(f"Error creating planning_metrics table: {e}")
        pass

    # Create change_analysis table
    try:
        op.create_table('change_analysis',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('assignment_id', sa.Integer(), nullable=False),
            sa.Column('change_type', sa.String(length=50), nullable=False),
            sa.Column('change_category', sa.String(length=50), nullable=True),
            sa.Column('reason_category', sa.String(length=50), nullable=True),
            sa.Column('reason_text', sa.Text(), nullable=True),
            sa.Column('original_value', sa.String(length=255), nullable=True),
            sa.Column('new_value', sa.String(length=255), nullable=True),
            sa.Column('change_impact_minutes', sa.Integer(), nullable=True),
            sa.Column('change_frequency_score', sa.Float(), nullable=True),
            sa.Column('vessel_id', sa.String(length=255), nullable=True),
            sa.Column('vessel_name', sa.String(length=255), nullable=True),
            sa.Column('terminal_id', sa.String(length=50), nullable=False),
            sa.Column('changed_by', sa.String(length=100), nullable=True),
            sa.Column('changed_at', sa.DateTime(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
            sa.ForeignKeyConstraint(['terminal_id'], ['terminals.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
    except Exception as e:
        print(f"Error creating change_analysis table: {e}")
        pass

    # Create performance_alerts table
    try:
        op.create_table('performance_alerts',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('alert_type', sa.String(length=100), nullable=False),
            sa.Column('metric_name', sa.String(length=100), nullable=True),
            sa.Column('threshold_value', sa.Float(), nullable=True),
            sa.Column('current_value', sa.Float(), nullable=True),
            sa.Column('severity', sa.String(length=20), nullable=True, server_default='info'),
            sa.Column('description', sa.Text(), nullable=True),
            sa.Column('is_resolved', sa.Boolean(), nullable=True, server_default='false'),
            sa.Column('terminal_id', sa.String(length=50), nullable=True),
            sa.Column('created_at', sa.DateTime(), nullable=True, server_default=sa.text('CURRENT_TIMESTAMP')),
            sa.Column('resolved_at', sa.DateTime(), nullable=True),
            sa.Column('resolved_by', sa.String(length=100), nullable=True),
            sa.ForeignKeyConstraint(['terminal_id'], ['terminals.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
    except Exception as e:
        print(f"Error creating performance_alerts table: {e}")
        pass


def downgrade():
    """Drop analytics tables"""
    try:
        op.drop_table('performance_alerts')
    except Exception:
        pass
    try:
        op.drop_table('change_analysis')
    except Exception:
        pass
    try:
        op.drop_table('planning_metrics')
    except Exception:
        pass
    try:
        op.drop_table('ml_predictions_log')
    except Exception:
        pass
