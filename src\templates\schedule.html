{% extends "base.html" %}
{% block title %}Schedule - Terneuzen Terminal Jetty Planning{% endblock %}
{% block head %}
    <style>
        .gantt-container {
            min-height: 400px;
            overflow-x: auto;
            position: relative;
            padding-top: 10px; /* Prevent top text clipping */
            padding-bottom: 5px; /* Prevent bottom clipping */
        }

        .time-axis text {
            font-size: clamp(9px, 1.2vw, 14px);
            fill: #495057;
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .time-axis-major text {
            font-size: clamp(10px, 1.4vw, 16px);
            font-weight: 600;
            fill: #212529;
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        /* Responsive time axis scaling */
        @media (max-width: 768px) {
            .time-axis text {
                font-size: clamp(8px, 2.5vw, 12px);
            }
            .time-axis-major text {
                font-size: clamp(9px, 2.8vw, 14px);
            }
        }

        @media (min-width: 1200px) {
            .time-axis text {
                font-size: clamp(11px, 1.0vw, 15px);
            }
            .time-axis-major text {
                font-size: clamp(12px, 1.2vw, 17px);
            }
        }

        @media (min-width: 1920px) {
            .time-axis text {
                font-size: clamp(12px, 0.8vw, 16px);
            }
            .time-axis-major text {
                font-size: clamp(14px, 1.0vw, 18px);
            }
        }

        .jetty-label {
            fill: #003b6f;
            font-weight: bold;
        }

        .no-data {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 400px;
            color: #6c757d;
            font-style: italic;
        }

        .schedule-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .schedule-controls span {
            font-weight: 500;
        }
        
        /* Schedule Legend Styles */
        .schedule-legend {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 10px;
            padding: 8px 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.85rem;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 3px;
        }

        .form-select {
            padding: 0.375rem 1.75rem 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 0.25rem;
            border: 1px solid #ced4da;
            background-color: white;
        }

        .text-center {
            text-align: center;
        }

        .prepump-segment {
            opacity: 0.8;
        }

        .operation-segment {
            opacity: 1.0;
        }

        .postpump-segment {
            opacity: 0.8;
        }

        .jetty-background {
            fill: #f8f9fa;
            stroke: #dee2e6;
            stroke-width: 1;
        }

        .label-divider {
            stroke: #adb5bd;
            stroke-width: 1;
        }

        .header-divider {
            stroke: #6c757d;
            stroke-width: 2;
            shape-rendering: crispEdges;
        }

        .time-grid line {
            stroke: #e9ecef;
            stroke-opacity: 0.8;
            shape-rendering: crispEdges;
        }
        .time-grid path.domain { display: none; }

        /* Enhanced time axis styling */
        .time-axis path.domain {
            stroke: #495057;
            stroke-width: 1;
        }

        .time-axis line {
            stroke: #495057;
            stroke-width: 1;
        }

        .time-axis-major path.domain {
            stroke: #212529;
            stroke-width: 2;
        }

        .time-axis-major line {
            stroke: #212529;
            stroke-width: 1.5;
        }

        /* Improve readability on high-DPI displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .time-axis text, .time-axis-major text {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
        }

        /* Smooth viewport transitions for enhanced UX */
        .gantt-container {
            scroll-behavior: smooth;
        }

        .gantt-transition {
            transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
        }

        .snap-animation {
            transition: transform 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* Enhanced NOW button styling */
        #now-button {
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        #now-button:hover:not(.btn-loading) {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 59, 111, 0.3);
        }

        #now-button:active:not(.btn-loading) {
            transform: translateY(0);
        }

        #now-button.btn-loading {
            pointer-events: none;
            opacity: 0.8;
        }

        #now-button.btn-loading .fa-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* View mode dropdown enhancements */
        #view-mode {
            transition: all 0.2s ease;
        }

        #view-mode:focus {
            box-shadow: 0 0 0 0.2rem rgba(0, 59, 111, 0.25);
            border-color: #003b6f;
        }

        .segment-label {
            font-size: 10px;
            fill: #333;
            text-anchor: middle;
            dominant-baseline: middle;
        }

        .handle {
            fill: #444;
            fill-opacity: 0.3;
            cursor: ew-resize;
        }

        .handle:hover {
            fill-opacity: 0.5;
        }

        /* Drag tooltip */
        .drag-tooltip {
            position: fixed;
            z-index: 10001;
            pointer-events: none;
            background: #003b6f;
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            white-space: nowrap;
            display: none;
        }

        /* Optimization Presets Styles */
        .optimization-presets {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .preset-selector {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .preset-selector label {
            font-weight: 600;
            color: #003b6f;
            font-size: 1.1rem;
        }

        .preset-selector .form-select {
            padding: 0.75rem 1rem;
            font-size: 1rem;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            background-color: white;
            transition: all 0.3s ease;
        }

        .preset-selector .form-select:focus {
            border-color: #003b6f;
            box-shadow: 0 0 0 0.2rem rgba(0, 59, 111, 0.25);
        }

        .preset-description {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 12px;
            padding: 1.5rem;
            border-left: 4px solid #003b6f;
        }

        .description-content h4 {
            color: #003b6f;
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .description-content p {
            color: #495057;
            margin: 0 0 1rem 0;
            line-height: 1.5;
        }

        .preset-details {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .detail-item {
            background: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            color: #495057;
            border: 1px solid #dee2e6;
        }

        .detail-item strong {
            color: #003b6f;
        }

        .preset-parameters {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .parameters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .parameter-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 1rem;
            background: white;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .parameter-display label {
            font-weight: 500;
            color: #495057;
            margin: 0;
        }

        .parameter-display span {
            font-weight: 600;
            color: #003b6f;
            font-size: 1.1rem;
        }

        .preset-options {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .option-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .option-label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            font-weight: 500;
            color: #495057;
            margin: 0;
        }

        .option-label input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #003b6f;
        }

        .option-description {
            font-size: 0.85rem;
            color: #6c757d;
            margin-left: 1.75rem;
            line-height: 1.4;
        }

        /* Reason Selection Modal Styles */
        .reason-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(3px);
            animation: fadeIn 0.2s ease-out;
        }

        .reason-modal {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            max-width: 600px;
            width: 90%;
            max-height: 90vh;
            display: flex;
            flex-direction: column;
            animation: slideUp 0.3s ease-out;
            overflow: hidden;
        }

        .reason-modal-header {
            display: flex;
            align-items: center;
            justify-content: between;
            padding: 20px 24px;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .reason-modal-header h3 {
            margin: 0;
            color: #003b6f;
            font-size: 1.25rem;
            flex: 1;
        }

        .reason-modal-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #6c757d;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .reason-modal-close:hover {
            background: #e9ecef;
            color: #495057;
        }

        .reason-modal-body {
            padding: 24px;
            overflow-y: auto;
            flex: 1;
        }

        .reason-search-container {
            position: relative;
            margin-bottom: 20px;
        }

        .reason-search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            font-size: 14px;
        }

        .reason-search {
            width: 100%;
            padding: 10px 12px 10px 36px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .reason-search:focus {
            outline: none;
            border-color: #003b6f;
            box-shadow: 0 0 0 3px rgba(0, 59, 111, 0.1);
        }

        .reason-categories {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .reason-category {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 12px;
            overflow: hidden;
            transition: box-shadow 0.2s;
        }

        .reason-category:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .reason-category-header {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .reason-category-header:hover {
            background: #e9ecef;
        }

        .reason-category-icon {
            color: #003b6f;
            margin-right: 10px;
            width: 16px;
        }

        .reason-category-header span {
            flex: 1;
            font-weight: 600;
            color: #495057;
        }

        .reason-category-toggle {
            color: #6c757d;
            font-size: 12px;
            transition: transform 0.2s;
        }

        .reason-category.collapsed .reason-category-toggle {
            transform: rotate(-90deg);
        }

        .reason-category-content {
            max-height: 300px;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }

        .reason-category.collapsed .reason-category-content {
            max-height: 0;
        }

        .reason-option {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s;
            border-bottom: 1px solid #f1f3f4;
        }

        .reason-option:last-child {
            border-bottom: none;
        }

        .reason-option:hover {
            background: #f8f9fa;
            padding-left: 20px;
        }

        .reason-option.selected {
            background: #e3f2fd;
            border-left: 4px solid #003b6f;
        }

        .reason-option i {
            color: #6c757d;
            margin-right: 10px;
            width: 16px;
            font-size: 14px;
        }

        .reason-option.selected i {
            color: #003b6f;
        }

        .reason-option-other {
            font-weight: 500;
            color: #003b6f;
        }

        .custom-reason-container {
            margin-top: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px dashed #dee2e6;
        }

        .custom-reason-container label {
            display: block;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }

        .custom-reason-input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            font-family: inherit;
            resize: vertical;
            min-height: 60px;
            transition: border-color 0.2s;
        }

        .custom-reason-input:focus {
            outline: none;
            border-color: #003b6f;
            box-shadow: 0 0 0 3px rgba(0, 59, 111, 0.1);
        }

        .recent-reasons-container {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .recent-reasons-container h4 {
            color: #495057;
            font-size: 1rem;
            margin: 0 0 12px 0;
            display: flex;
            align-items: center;
        }

        .recent-reasons-container h4 i {
            margin-right: 8px;
            color: #6c757d;
        }

        .recent-reasons-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .recent-reason-item {
            background: #e9ecef;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            color: #495057;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid transparent;
        }

        .recent-reason-item:hover {
            background: #003b6f;
            color: white;
        }

        .reason-modal-footer {
            padding: 20px 24px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .reason-modal-footer .btn {
            padding: 8px 20px;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            border: none;
            cursor: pointer;
            transition: all 0.2s;
        }

        .reason-modal-footer .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .reason-modal-footer .btn-secondary:hover {
            background: #5a6268;
        }

        .reason-modal-footer .btn-primary {
            background: #003b6f;
            color: white;
        }

        .reason-modal-footer .btn-primary:hover:not(:disabled) {
            background: #002a52;
        }

        .reason-modal-footer .btn-primary:disabled {
            background: #adb5bd;
            cursor: not-allowed;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .reason-modal {
                width: 95%;
                margin: 20px;
                max-height: calc(100vh - 40px);
            }
            
            .reason-modal-header {
                padding: 16px 20px;
            }
            
            .reason-modal-body {
                padding: 20px;
            }
            
            .reason-modal-footer {
                padding: 16px 20px;
                flex-direction: column;
            }
            
            .reason-modal-footer .btn {
                width: 100%;
            }
        }
    </style>
{% endblock %}
{% block header %}Terneuzen Terminal Schedule{% endblock %}
{% block user_actions %}
    <a href="/logs" class="btn btn-info">
        <i class="fas fa-history"></i> View Logs
    </a>
    <a href="/schedule/add" class="btn btn-secondary">
        <i class="fas fa-plus"></i> Add Assignment
    </a>
    <button class="btn btn-warning" id="reset-data-btn">
        <i class="fas fa-undo"></i> Reset All Data
    </button>
    <button class="btn btn-secondary" id="view-toggle">
        <i class="fas fa-th-list"></i> Toggle View
    </button>
    <button class="btn btn-secondary" id="refresh-btn">
        <i class="fas fa-sync-alt"></i> Refresh
    </button>
    <button class="btn btn-secondary" id="mock-toggle-btn">
        <i class="fas fa-vial"></i> Use Mock Data
    </button>
    <button class="btn btn-primary" id="optimize-button">
        <i class="fas fa-magic"></i> Optimize
    </button>
{% endblock %}
{% block content %}
            <div class="card">
                <div class="card-header">
                    <h3>Schedule Overview</h3>
                    <div class="schedule-controls">
                        <button class="btn btn-sm btn-secondary" id="prev-week">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span id="date-range">Loading...</span>
                        <button class="btn btn-sm btn-secondary" id="next-week">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="btn btn-sm btn-secondary" id="now-button" title="Jump to now">
                            <i class="fas fa-clock"></i> Now
                        </button>
                        <button class="btn btn-sm btn-info" id="debug-snap-button" title="Debug: Test snap function" style="margin-left: 5px;">
                            <i class="fas fa-bug"></i> Test
                        </button>
                        <select id="view-mode" class="form-select">
                            <option value="day">Day View</option>
                            <option value="week" selected>Week View</option>
                            <option value="month">Month View</option>
                            <option value="72h">72 Hours</option>
                        </select>
                        <select id="snap-interval" class="form-select" title="Snap interval">
                            <option value="0" selected>No snap</option>
                            <option value="5">Snap 5 min</option>
                            <option value="15">Snap 15 min</option>
                            <option value="30">Snap 30 min</option>
                            <option value="60">Snap 60 min</option>
                        </select>
                        <div class="optimization-mode-selector" style="margin-bottom:12px;">
                            <label for="optimization-mode"><strong>Optimization Mode</strong></label>
                            <select id="optimization-mode">
                                <option value="FRESH">Fresh Schedule</option>
                                <option value="INCREMENTAL">Add New Vessels</option>
                                <option value="SELECTIVE">Selective Re-optimization</option>
                                <option value="PRESET_CHANGE">Apply New Weights</option>
                            </select>
                            <button id="btn-start-optimization" class="btn btn-primary" style="margin-left:8px;">Start</button>
                            <span id="mode-explanation" class="mode-help-text" style="margin-left:8px; font-size: 0.9em; color:#666;"></span>
                        </div>
                        <div class="optimization-status-panel" style="display:block; margin-bottom:8px;">
                            <i class="fas fa-circle" id="optimization-status-icon"></i>
                            <span id="optimization-status-text">Ready for optimization</span>
                            <div class="progress-section" id="optimization-progress" style="display:none; margin-top:6px;">
                                <div class="progress-bar" style="background:#eee; height:8px; width:240px; display:inline-block; vertical-align:middle;">
                                    <div class="progress-fill" id="optimization-progress-fill" style="background:#4caf50; width:0%; height:8px;"></div>
                                </div>
                                <span class="progress-text" id="optimization-progress-text" style="margin-left:6px;">0%</span>
                            </div>
                            <div class="current-activity" style="font-size:0.9em; color:#666; margin-top:4px;">
                                <span id="current-activity-text">Idle</span>
                            </div>
                        </div>
                        <a href="/logs" class="btn btn-info btn-sm" title="View Schedule Change Logs">
                            <i class="fas fa-history"></i> Logs
                        </a>
                        <!-- Impact Preview Modal -->
                        <div id="impactPreviewModal" style="display:none; position:fixed; top:0; left:0; right:0; bottom:0; background:rgba(0,0,0,0.4); align-items:center; justify-content:center; z-index:1000;">
                          <div style="background:#fff; padding:16px; border-radius:6px; width:420px; box-shadow:0 2px 8px rgba(0,0,0,0.2);">
                            <h4 style="margin-top:0;">Optimization Impact</h4>
                            <div id="impactPreviewContent">
                              <p>Loading preview...</p>
                            </div>
                            <div style="text-align:right; margin-top:12px;">
                              <button id="btn-preview-cancel" class="btn btn-secondary">Cancel</button>
                              <button id="btn-preview-continue" class="btn btn-primary">Continue</button>
                            </div>
                          </div>
                        </div>
                        <script nonce="{{ nonce }}">
                        (function(){
                            const helpByMode = {
                                FRESH: 'Clear and re-optimize all (unlocked) assignments.',
                                INCREMENTAL: 'Add new vessels while keeping existing schedule.',
                                SELECTIVE: 'Re-optimize specific vessels or window.',
                                PRESET_CHANGE: 'Re-run with new weights; preserve locks.'
                            };
                            const modeSel = document.getElementById('optimization-mode');
                            const modeExp = document.getElementById('mode-explanation');
                            const startBtn = document.getElementById('btn-start-optimization');
                            const statusText = document.getElementById('optimization-status-text');
                            const progress = document.getElementById('optimization-progress');
                            const fill = document.getElementById('optimization-progress-fill');
                            const pct = document.getElementById('optimization-progress-text');
                            function setModeExp(){ modeExp.textContent = helpByMode[modeSel.value] || ''; }
                            modeSel.addEventListener('change', setModeExp); setModeExp();
                            const previewModal = document.getElementById('impactPreviewModal');
                            const previewContent = document.getElementById('impactPreviewContent');
                            const btnCancel = document.getElementById('btn-preview-cancel');
                            const btnContinue = document.getElementById('btn-preview-continue');

                            function openPreview() { previewModal.style.display = 'flex'; }
                            function closePreview() { previewModal.style.display = 'none'; }
                            btnCancel.addEventListener('click', closePreview);

                            async function runOptimizationAfterPreview() {
                                try {
                                    statusText.textContent = 'Starting optimization...';
                                    progress.style.display = 'inline-block';
                                    fill.style.width = '10%'; pct.textContent = '10%';
                                    const mode = modeSel.value;
                                    if (mode === 'SELECTIVE') {
                                        await window.optimizationClient.startSelectiveOptimization([], { preserve_locked: true });
                                    } else {
                                        await window.optimizationClient.startOptimization({ preserve_locked: true });
                                    }
                                    closePreview();
                                    setTimeout(()=>{ fill.style.width='60%'; pct.textContent='60%'; statusText.textContent='Running...'; }, 1500);
                                    setTimeout(()=>{ fill.style.width='100%'; pct.textContent='100%'; statusText.textContent='Completed'; }, 3000);
                                } catch(e){
                                    statusText.textContent = 'Failed to start optimization';
                                }
                            }
                            btnContinue.addEventListener('click', runOptimizationAfterPreview);

                            startBtn.addEventListener('click', async () => {
                                try {
                                    previewContent.innerHTML = '<p>Loading preview...</p>';
                                    openPreview();
                                    const resp = await window.optimizationClient.previewOptimization({ preserve_locked: true });
                                    if (!resp || !resp.success) {
                                        previewContent.innerHTML = '<p>Preview unavailable.</p>';
                                        return;
                                    }
                                    previewContent.innerHTML = `
                                      <ul style="margin:0 0 8px 16px;">
                                        <li><strong>${resp.locked_preserved}</strong> locked assignments preserved</li>
                                        <li><strong>${resp.unlocked_candidates}</strong> unlocked assignments considered</li>
                                        <li><strong>${resp.vessels_considered}</strong> vessels in scope</li>
                                      </ul>
                                      <div>
                                        <strong>Estimated Improvement:</strong>
                                        <span>+${(resp.estimated_improvement && resp.estimated_improvement.throughput_percent) || 0}% throughput,
                                        ${(resp.estimated_improvement && resp.estimated_improvement.avg_wait_hours_delta) || 0}h avg wait</span>
                                      </div>`;
                                } catch(e) {
                                    previewContent.innerHTML = '<p>Failed to load preview.</p>';
                                }
                            });
                        })();
                        </script>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="gantt-chart" class="gantt-container"></div>
                    <div class="schedule-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #4caf50;"></div>
                            <span>Completed</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #2196f3;"></div>
                            <span>In Progress</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #ff9800;"></div>
                            <span>Scheduled</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: #9e9e9e;"></div>
                            <span>Other</span>
                        </div>
                    </div>
                    <!-- Overview Table (shown when toggled to table view) -->
                    <div id="overview-table-container" style="display:none; padding: 1rem;">
                        <div class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Jetty</th>
                                        <th>Vessel</th>
                                        <th>Product</th>
                                        <th>Volume</th>
                                        <th>Start</th>
                                        <th>End</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="overview-table-body">
                                    <tr><td colspan="8" class="text-center">No data</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3>Optimization Parameters</h3>
                    <button class="btn btn-sm btn-primary" id="apply-parameters">
                        <i class="fas fa-check"></i> Apply Parameters
                    </button>
                </div>
                <div class="card-body">
                    <div class="optimization-presets">
                        <div class="preset-selector">
                            <label for="optimization-preset">Optimization Strategy</label>
                            <select id="optimization-preset" class="form-select">
                                <option value="throughput" selected>🚀 Maximum Throughput</option>
                                <option value="cost">💰 Cost Efficiency</option>
                                <option value="infrastructure">🏗️ Infrastructure Efficiency</option>
                                <option value="balanced">⚖️ Balanced</option>
                            </select>
                        </div>

                        <div class="preset-description" id="preset-description">
                            <div class="description-content">
                                <h4>🚀 Maximum Throughput</h4>
                                <p>Prioritizes maximizing the number of vessels processed within the planning horizon. Ideal for high-demand periods when throughput is critical.</p>
                                <div class="preset-details">
                                    <span class="detail-item">Throughput: <strong>High</strong></span>
                                    <span class="detail-item">Cost Control: <strong>Medium</strong></span>
                                    <span class="detail-item">Safety: <strong>Medium</strong></span>
                                </div>
                            </div>
                        </div>

                        <div class="preset-parameters">
                            <div class="parameters-grid">
                                <div class="parameter-display">
                                    <label>Throughput Weight:</label>
                                    <span id="display-throughput">15.0</span>
                                </div>
                                <div class="parameter-display">
                                    <label>Demurrage Cost Weight:</label>
                                    <span id="display-demurrage">3.0</span>
                                </div>
                                <div class="parameter-display">
                                    <label>Customer Priority Weight:</label>
                                    <span id="display-priority">5.0</span>
                                </div>
                                <div class="parameter-display">
                                    <label>Weather Safety Weight:</label>
                                    <span id="display-weather">5.0</span>
                                </div>
                                <div class="parameter-display">
                                    <label>Planning Horizon:</label>
                                    <span id="display-horizon">7 Days</span>
                                </div>
                                <div class="parameter-display">
                                    <label>Time Granularity:</label>
                                    <span id="display-granularity">1 Hour</span>
                                </div>
                            </div>
                        </div>

                        <div class="preset-options">
                            <div class="option-item">
                                <label for="force-assign-all" class="option-label">
                                    <input type="checkbox" id="force-assign-all">
                                    <span class="checkmark"></span>
                                    Force Assign All Vessels
                                </label>
                                <div class="option-description">
                                    When enabled, the optimizer will ensure every vessel gets assigned to a jetty.
                                    This may result in a less optimal schedule but ensures no vessels are left out.
                                    <br><strong>When disabled:</strong> The optimizer may leave vessels unassigned if it's not cost-effective to assign them (e.g., high demurrage costs, time conflicts). Check the "Reason Not Assigned" column in the Unscheduled Vessels table for details.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="optimization-log" class="optimization-log">
                        Adjust parameters and click "Optimize" to run scheduling optimization.
                    </div>
                </div>
            </div>

            <div class="card" id="optimizer-scores-card" style="display:none;">
                <div class="card-header">
                    <h3>Optimizer Scores</h3>
                    <div class="schedule-controls">
                        <span id="score-updated-at"></span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="parameters-grid">
                        <div class="parameter-display"><label>Total Objective</label><span id="score-total">-</span></div>
                        <div class="parameter-display"><label>Throughput</label><span id="score-throughput">-</span></div>
                        <div class="parameter-display"><label>Demurrage (penalty)</label><span id="score-demurrage">-</span></div>
                        <div class="parameter-display"><label>Priority</label><span id="score-priority">-</span></div>
                        <div class="parameter-display" id="score-force-wrap" style="display:none;"><label>Force Assign</label><span id="score-force">-</span></div>
                        <div class="parameter-display" id="score-balance-wrap" style="display:none;"><label>Jetty Balancing</label><span id="score-balance">-</span></div>
                    </div>

                    <div class="preset-description" style="margin-top: 1rem;">
                        <div class="description-content">
                            <h4>How to read these scores</h4>
                            <p>
                                The optimizer maximizes the total objective = Throughput + Priority − Demurrage (plus optional terms).
                                Scores are weighted by the selected strategy. Higher is better.
                            </p>
                            <div class="preset-details" id="weights-inline">
                                <span class="detail-item">Throughput weight: <strong id="w-throughput">-</strong></span>
                                <span class="detail-item">Demurrage weight: <strong id="w-demurrage">-</strong></span>
                                <span class="detail-item">Priority weight: <strong id="w-priority">-</strong></span>
                                <span class="detail-item">Free-wait buffer: <strong id="w-buffer">-</strong></span>
                                <span class="detail-item">Approach time: <strong id="w-approach">-</strong></span>
                            </div>
                            <div class="option-description" style="margin-top: 0.5rem;">
                                Throughput rewards moving more cargo. Demurrage penalizes waiting beyond the free buffer (after ETA + approach).
                                Priority rewards higher customer priority vessels. Optional Force Assign strongly encourages assigning every vessel.
                            </div>
                        </div>
                    </div>

                    <div class="table-container" style="margin-top:1rem;">
                        <table>
                            <thead>
                                <tr>
                                    <th>Assignment</th>
                                    <th>Jetty</th>
                                    <th>Throughput</th>
                                    <th>Demurrage</th>
                                    <th>Priority</th>
                                    <th>Explain</th>
                                </tr>
                            </thead>
                            <tbody id="scores-assignment-body">
                                <tr><td colspan="6" class="text-center">No data</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3>Assignment Details</h3>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Jetty</th>
                                    <th>Vessel</th>
                                    <th>Product</th>
                                    <th>Volume</th>
                                    <th>Operation</th>
                                    <th>Start Time</th>
                                    <th>End Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="assignments-table-body">
                                <!-- Assignments will be loaded here -->
                                <tr>
                                    <td colspan="10" class="text-center">Loading assignments...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3>Unscheduled Vessels</h3>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Vessel Name</th>
                                    <th>Type</th>
                                    <th>ETA</th>
                                    <th>Length (m)</th>
                                    <th>Draft (m)</th>
                                    <th>Cargo (m³)</th>
                                    <th>Priority</th>
                                    <th>Reason Not Assigned</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="unscheduled-vessels-table">
                                <!-- Unscheduled vessels will be loaded here -->
                                <tr>
                                    <td colspan="10" class="text-center">No unscheduled vessels</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3>Schedule Change Log</h3>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Assignment</th>
                                    <th>Vessel</th>
                                    <th>Jetty</th>
                                    <th>Old</th>
                                    <th>New</th>
                                    <th>Reason</th>
                                </tr>
                            </thead>
                            <tbody id="change-log-table">
                                <tr>
                                    <td colspan="7" class="text-center">No changes yet</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
{% endblock %}
{% block scripts %}
    <script src="/static/vendor/js/d3.v7.min.js"></script>
    <script src="/static/js/optimization.js"></script>
    <script nonce="{{ nonce }}">
    // Reason Modal Management Variables
    let currentReasonCallback = null;
    let currentChangeType = '';
    let selectedReason = '';
    // Drag assist state
    let dragTooltipEl = null;
    let currentSnapMinutes = 0; // 0 = no snapping
    // Reason reuse state (per assignment)
    const REASON_REUSE_WINDOW_MS = 90 * 1000; // 90s window to reuse last reason
    const recentAssignmentReasons = new Map(); // assignmentId -> { reason, sessionId, time }

    function generateSessionId() {
        try {
            // Prefer crypto for better uniqueness
            if (window.crypto && window.crypto.randomUUID) return window.crypto.randomUUID();
        } catch (_) {}
        return 'sess_' + Math.random().toString(36).slice(2) + Date.now();
    }

    function setRecentAssignmentReason(assignmentId, reason, sessionId) {
        if (!assignmentId || !reason) return;
        recentAssignmentReasons.set(String(assignmentId), {
            reason: String(reason),
            sessionId: sessionId || generateSessionId(),
            time: Date.now()
        });
    }

    function getRecentAssignmentReason(assignmentId) {
        const entry = recentAssignmentReasons.get(String(assignmentId));
        if (!entry) return null;
        if (Date.now() - (entry.time || 0) > REASON_REUSE_WINDOW_MS) return null;
        return entry;
    }

    // Reason Modal Functions
    function showReasonModal(changeType, callback) {
        currentReasonCallback = callback;
        currentChangeType = changeType;
        selectedReason = '';
        
        // Update modal title based on change type
        const titles = {
            'start_time': 'Reason for Start Time Change',
            'end_time': 'Reason for End Time Change', 
            'jetty': 'Reason for Jetty Change'
        };
        document.getElementById('reasonModalTitle').textContent = titles[changeType] || 'Select Change Reason';
        
        // Reset modal state
        resetModalState();
        
        // Load recent reasons
        loadRecentReasons();
        
        // Show modal
        document.getElementById('reasonModal').style.display = 'flex';
        
        // Focus search input
        setTimeout(() => {
            document.getElementById('reasonSearch').focus();
        }, 100);
    }

    function closeReasonModal() {
        document.getElementById('reasonModal').style.display = 'none';
        currentReasonCallback = null;
        currentChangeType = '';
        selectedReason = '';
        resetModalState();
    }

    function resetModalState() {
        // Clear search
        document.getElementById('reasonSearch').value = '';
        
        // Clear custom reason
        document.getElementById('customReasonInput').value = '';
        document.getElementById('customReasonContainer').style.display = 'none';
        
        // Clear all selections
        document.querySelectorAll('.reason-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        // Disable confirm button
        document.getElementById('confirmReasonBtn').disabled = true;
        
        // Expand operational category by default, collapse others
        document.querySelectorAll('.reason-category').forEach(category => {
            if (category.querySelector('#operational-content')) {
                category.classList.remove('collapsed');
            } else {
                category.classList.add('collapsed');
            }
        });
        
        // Show all reason options
        document.querySelectorAll('.reason-option').forEach(option => {
            option.style.display = 'flex';
        });
        document.querySelectorAll('.reason-category').forEach(category => {
            category.style.display = 'block';
        });
    }

    function toggleCategory(categoryId) {
        const category = document.querySelector(`#${categoryId}-content`).closest('.reason-category');
        category.classList.toggle('collapsed');
    }

    function selectReason(reasonText) {
        // Clear previous selections
        document.querySelectorAll('.reason-option').forEach(option => {
            option.classList.remove('selected');
        });
        
        selectedReason = reasonText;
        
        if (reasonText === 'other') {
            // Show custom input
            document.getElementById('customReasonContainer').style.display = 'block';
            document.querySelector('.reason-option-other').classList.add('selected');
            document.getElementById('customReasonInput').focus();
            // Disable confirm until custom reason is entered
            document.getElementById('confirmReasonBtn').disabled = true;
        } else {
            // Hide custom input
            document.getElementById('customReasonContainer').style.display = 'none';
            // Find and select the clicked option
            document.querySelectorAll('.reason-option').forEach(option => {
                if (option.dataset.reason === reasonText) {
                    option.classList.add('selected');
                }
            });
            // Enable confirm button
            document.getElementById('confirmReasonBtn').disabled = false;
        }
    }

    function confirmReason() {
        console.log('confirmReason() called, selectedReason:', selectedReason);
        let finalReason = selectedReason;
        
        if (selectedReason === 'other') {
            const customReason = document.getElementById('customReasonInput').value.trim();
            if (!customReason) {
                showErrorToast('Please enter a custom reason.');
                return;
            }
            finalReason = customReason;
        }
        
        if (!finalReason) {
            showErrorToast('Please select a reason.');
            return;
        }
        
        console.log('Final reason:', finalReason);
        console.log('Current callback exists:', !!currentReasonCallback);
        
        // Save to recent reasons
        saveToRecentReasons(finalReason);

        // Capture callback reference before closing (closing clears it)
        const cb = currentReasonCallback;
        
        // Close modal (resets state and clears currentReasonCallback)
        closeReasonModal();
        
        // Execute callback with the reason after closing
        if (typeof cb === 'function') {
            console.log('Executing callback with reason:', finalReason);
            cb(finalReason);
        } else {
            console.error('No callback function available!');
        }
    }

    function saveToRecentReasons(reason) {
        try {
            let recentReasons = JSON.parse(localStorage.getItem('scheduleChangeReasons') || '[]');
            
            // Remove if already exists
            recentReasons = recentReasons.filter(r => r !== reason);
            
            // Add to beginning
            recentReasons.unshift(reason);
            
            // Keep only last 8 reasons
            recentReasons = recentReasons.slice(0, 8);
            
            localStorage.setItem('scheduleChangeReasons', JSON.stringify(recentReasons));
        } catch (e) {
            console.warn('Could not save recent reasons:', e);
        }
    }

    function loadRecentReasons() {
        try {
            const recentReasons = JSON.parse(localStorage.getItem('scheduleChangeReasons') || '[]');
            const container = document.getElementById('recentReasonsContainer');
            const list = document.getElementById('recentReasonsList');
            
            if (recentReasons.length === 0) {
                container.style.display = 'none';
                return;
            }
            
            // Render without inline handlers (CSP-safe)
            list.innerHTML = recentReasons.map(reason => {
                const safe = String(reason)
                  .replace(/&/g, '&amp;')
                  .replace(/</g, '&lt;')
                  .replace(/>/g, '&gt;')
                  .replace(/"/g, '&quot;')
                  .replace(/'/g, '&#39;');
                return `<div class="recent-reason-item" data-reason="${safe}">${safe}</div>`;
            }).join('');

            // Attach click listeners for recent reasons
            list.querySelectorAll('.recent-reason-item').forEach(el => {
                el.addEventListener('click', () => {
                    const r = el.getAttribute('data-reason') || '';
                    selectReason(r);
                });
            });
            
            container.style.display = 'block';
        } catch (e) {
            console.warn('Could not load recent reasons:', e);
        }
    }

    // Search functionality
    function setupReasonSearch() {
        const searchInput = document.getElementById('reasonSearch');
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase().trim();
            
            if (!searchTerm) {
                // Show all options and categories
                document.querySelectorAll('.reason-option').forEach(option => {
                    option.style.display = 'flex';
                });
                document.querySelectorAll('.reason-category').forEach(category => {
                    category.style.display = 'block';
                });
                return;
            }
            
            // Filter options
            document.querySelectorAll('.reason-category').forEach(category => {
                let hasVisibleOptions = false;
                
                category.querySelectorAll('.reason-option').forEach(option => {
                    const reason = option.dataset.reason?.toLowerCase() || option.textContent.toLowerCase();
                    if (reason.includes(searchTerm)) {
                        option.style.display = 'flex';
                        hasVisibleOptions = true;
                    } else {
                        option.style.display = 'none';
                    }
                });
                
                // Show/hide category based on whether it has visible options
                category.style.display = hasVisibleOptions ? 'block' : 'none';
                
                // Expand categories with matches
                if (hasVisibleOptions) {
                    category.classList.remove('collapsed');
                }
            });
        });
    }

    // Attach CSP-safe event listeners after DOM ready
    document.addEventListener('DOMContentLoaded', () => {
        // Category toggles
        document.querySelectorAll('.reason-category-header[data-category]').forEach(header => {
            header.addEventListener('click', () => {
                const id = header.getAttribute('data-category') || '';
                if (id) toggleCategory(id);
            });
        });

        // Modal footer buttons
        const cancelBtn = document.getElementById('cancelReasonBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => closeReasonModal());
        }

        const confirmBtn = document.getElementById('confirmReasonBtn');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => confirmReason());
        }

        // Modal header close button
        const closeBtn = document.querySelector('.reason-modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => closeReasonModal());
        }
    });

    // Custom reason input handling
    function setupCustomReasonInput() {
        const customInput = document.getElementById('customReasonInput');
        customInput.addEventListener('input', function(e) {
            const hasText = e.target.value.trim().length > 0;
            document.getElementById('confirmReasonBtn').disabled = !hasText;
        });
        
        // Allow Enter to confirm if custom reason is filled
        customInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.target.value.trim() && !document.getElementById('confirmReasonBtn').disabled) {
                confirmReason();
            }
        });
    }

    // Setup reason option click handlers
    function setupReasonOptionHandlers() {
        document.querySelectorAll('.reason-option').forEach(option => {
            option.addEventListener('click', function() {
                const reason = this.dataset.reason;
                selectReason(reason);
            });
        });
    }

    // Escape key to close modal
    document.addEventListener('keydown', function(e) {
        const modalEl = document.getElementById('reasonModal');
        if (e.key === 'Escape' && modalEl && modalEl.style.display === 'flex') {
            closeReasonModal();
        }
    });

    // Click outside modal to close
    const reasonModalEl = document.getElementById('reasonModal');
    if (reasonModalEl) {
        reasonModalEl.addEventListener('click', function(e) {
            if (e.target === this) {
                closeReasonModal();
            }
        });
    } else {
        // If the modal isn't yet in the DOM (script executes before modal markup), attach after DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            const modalEl = document.getElementById('reasonModal');
            if (!modalEl) return;
            modalEl.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeReasonModal();
                }
            });
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, initializing schedule...');
        
        // Initialize
        setupEventListeners();
        loadRealData();
        
        // Initialize reason modal functionality
        setupReasonSearch();
        setupCustomReasonInput();
        setupReasonOptionHandlers();
        
        // Make schedule state available
        window.testData = testData;
        
        // Render initial views
        renderGanttChart();
        renderAssignmentTable();
        renderOverviewTable();
        renderUnscheduledVesselsTable();
        renderScoresPanel();

        // Add responsive resize handler
        let resizeTimeout;
        window.addEventListener('resize', function() {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(function() {
                if (isGanttView && viewportManager) {
                    // Re-initialize viewport manager after resize
                    const container = document.getElementById('gantt-chart');
                    if (container) {
                        viewportManager.container = container;
                    }
                    renderGanttChart();
                }
            }, 250); // Debounce resize events
        });

        // Debug: Log initialization status
        setTimeout(() => {
            console.log('Initialization check:', {
                viewportManager: !!viewportManager,
                ganttContainer: !!document.getElementById('gantt-chart'),
                nowButton: !!document.getElementById('now-button')
            });
        }, 1000);
    });

    // ========== VIEWPORT MANAGER CLASS ==========
    class ViewportManager {
        constructor(container) {
            this.container = container;
            this.nowPosition = 0.2; // 20% from left
            this.currentViewMode = 'week';
            this.isAnimating = false;
            
            // Configuration for different view modes
            this.viewModeConfig = {
                'day': {
                    pixelsPerHour: 60,
                    majorTickHours: 6,
                    minorTickHours: 1,
                    visibleHours: 24,
                    snapMinutes: 15,
                    loadBufferHours: 48
                },
                'week': {
                    pixelsPerHour: 20,
                    majorTickHours: 24,
                    minorTickHours: 6,
                    visibleHours: 168, // 7 days
                    snapMinutes: 60,
                    loadBufferHours: 336 // 2 weeks
                },
                '72h': {
                    pixelsPerHour: 30,
                    majorTickHours: 12,
                    minorTickHours: 3,
                    visibleHours: 72,
                    snapMinutes: 30,
                    loadBufferHours: 168 // 1 week
                },
                'month': {
                    pixelsPerHour: 8,
                    majorTickHours: 168, // 7 days
                    minorTickHours: 24,
                    visibleHours: 720, // 30 days
                    snapMinutes: 240, // 4 hours
                    loadBufferHours: 1008 // 6 weeks
                }
            };
        }

        // Get current view configuration
        getCurrentConfig() {
            return this.viewModeConfig[this.currentViewMode];
        }

        // Calculate viewport bounds based on NOW position
        getViewportBounds() {
            const now = new Date();
            const config = this.getCurrentConfig();
            const containerWidth = this.container.clientWidth;
            const labelWidth = 120;
            const availableWidth = containerWidth - labelWidth;
            
            // Calculate how many hours are visible in the viewport
            const visibleHours = availableWidth / config.pixelsPerHour;
            
            // Position NOW at 20% from left of the VISIBLE viewport
            const hoursBeforeNow = visibleHours * this.nowPosition;
            const hoursAfterNow = visibleHours * (1 - this.nowPosition);
            
            const startTime = new Date(now.getTime() - (hoursBeforeNow * 60 * 60 * 1000));
            const endTime = new Date(now.getTime() + (hoursAfterNow * 60 * 60 * 1000));
            
            console.log('ViewportBounds calculation:', {
                now: now.toISOString(),
                visibleHours,
                hoursBeforeNow,
                hoursAfterNow,
                startTime: startTime.toISOString(),
                endTime: endTime.toISOString(),
                pixelsPerHour: config.pixelsPerHour,
                availableWidth
            });
            
            return { startTime, endTime, visibleHours };
        }

        // Get extended bounds for data loading (with buffer)
        getLoadBounds() {
            const now = new Date();
            const config = this.getCurrentConfig();
            const bufferMs = config.loadBufferHours * 60 * 60 * 1000;
            
            return {
                startTime: new Date(now.getTime() - bufferMs),
                endTime: new Date(now.getTime() + bufferMs)
            };
        }

        // Snap to NOW position with animation
        snapToNow(animated = true) {
            if (this.isAnimating) return;
            
            console.log('ViewportManager: snapToNow called');
            
            // Get the bounds for NOW at 20% position (this is what user should see)
            const viewportBounds = this.getViewportBounds();
            
            // Update global date range to what user expects to see
            currentDateRange.startDate = viewportBounds.startTime;
            currentDateRange.endDate = viewportBounds.endTime;
            
            // Update display
            updateDateRangeDisplay();
            
            // Re-render chart - this will use extended bounds for data loading
            // but the viewport will be positioned correctly
            renderGanttChart();
            
            // The chart should now render with NOW at the correct position
            // No additional scrolling needed since we set the date range correctly
            
            // Show feedback
            if (typeof showInfoToast === 'function') {
                showInfoToast('Snapped to current time');
            }
        }

        // Scroll container to position NOW at 20% from left
        scrollToNowPosition(animated = true) {
            try {
                const svg = this.container.querySelector('svg');
                if (!svg) return;
                
                const labelWidth = 120;
                const width = parseFloat(svg.getAttribute('width')) || svg.viewBox.baseVal.width;
                const containerWidth = this.container.clientWidth;
                
                // Create time scale based on current date range
                const timeScale = d3.scaleTime()
                    .domain([currentDateRange.startDate, currentDateRange.endDate])
                    .range([labelWidth, width]);
                
                const now = new Date();
                const nowX = timeScale(now);
                const desiredX = labelWidth + (containerWidth - labelWidth) * this.nowPosition;
                const delta = nowX - desiredX;
                
                console.log('Scroll calculation:', {
                    nowX,
                    desiredX,
                    delta,
                    currentScrollLeft: this.container.scrollLeft,
                    targetScrollLeft: Math.max(0, this.container.scrollLeft + delta)
                });
                
                if (Math.abs(delta) < 10) {
                    console.log('Already close to target position');
                    return; // Already close enough
                }
                
                const targetScrollLeft = Math.max(0, this.container.scrollLeft + delta);
                
                if (animated) {
                    this.container.scrollTo({ 
                        left: targetScrollLeft, 
                        behavior: 'smooth' 
                    });
                } else {
                    this.container.scrollLeft = targetScrollLeft;
                }
                
            } catch (error) {
                console.error('Error in scrollToNowPosition:', error);
            }
        }

        // Set view mode and auto-snap to NOW
        setViewMode(mode, animated = true) {
            if (this.currentViewMode === mode) return;
            
            this.currentViewMode = mode;
            this.snapToNow(animated);
            
            // Update dropdown if it wasn't the trigger
            const dropdown = document.getElementById('view-mode');
            if (dropdown && dropdown.value !== mode) {
                dropdown.value = mode;
            }
            
            showSuccessToast(`Switched to ${mode} view`);
        }

        // Set date range with optional animation
        setDateRange(startDate, endDate, animated = true) {
            if (animated && !this.isAnimating) {
                this.isAnimating = true;
                
                // Use CSS transition for smooth animation
                this.container.style.transition = 'transform 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                
                setTimeout(() => {
                    this.isAnimating = false;
                    this.container.style.transition = '';
                }, 500);
            }
            
            // Update global state
            currentDateRange.startDate = new Date(startDate);
            currentDateRange.endDate = new Date(endDate);
            
            // Update display
            updateDateRangeDisplay();
            
            // Re-render chart
            renderGanttChart();
        }

        // Calculate scroll position for infinite scrolling
        getScrollInfo() {
            const scrollLeft = this.container.scrollLeft;
            const scrollWidth = this.container.scrollWidth;
            const clientWidth = this.container.clientWidth;
            
            return {
                scrollLeft,
                scrollWidth,
                clientWidth,
                scrollPercentage: scrollLeft / (scrollWidth - clientWidth)
            };
        }

        // Handle infinite scroll (expand data range as needed)
        handleScroll() {
            const scrollInfo = this.getScrollInfo();
            const config = this.getCurrentConfig();
            const threshold = 0.1; // Load more data when within 10% of edges
            
            if (scrollInfo.scrollPercentage < threshold) {
                // Near left edge - extend backwards
                this.extendDataRange('backward');
            } else if (scrollInfo.scrollPercentage > (1 - threshold)) {
                // Near right edge - extend forwards  
                this.extendDataRange('forward');
            }
        }

        // Extend data range for infinite scrolling
        extendDataRange(direction) {
            const config = this.getCurrentConfig();
            const extensionHours = config.visibleHours; // Extend by one viewport width
            const extensionMs = extensionHours * 60 * 60 * 1000;
            
            if (direction === 'backward') {
                const newStartDate = new Date(currentDateRange.startDate.getTime() - extensionMs);
                currentDateRange.startDate = newStartDate;
            } else {
                const newEndDate = new Date(currentDateRange.endDate.getTime() + extensionMs);
                currentDateRange.endDate = newEndDate;
            }
            
            // Re-render with extended range
            renderGanttChart();
            
            // Maintain scroll position after re-render
            setTimeout(() => {
                if (direction === 'backward') {
                    // Adjust scroll to maintain visual position
                    const addedWidth = extensionHours * config.pixelsPerHour;
                    this.container.scrollLeft += addedWidth;
                }
            }, 50);
        }
    }

    // Global viewport manager instance
    let viewportManager = null;

    // Global date range state (maintained for compatibility)
    let currentDateRange = {
        startDate: new Date(),
        endDate: new Date()
    };
    
    console.log('Initial currentDateRange set to:', {
        start: currentDateRange.startDate.toISOString(),
        end: currentDateRange.endDate.toISOString()
    });

    // View state
    let isGanttView = true;

    // Initialize viewport manager and date range
    function initializeViewport() {
        console.log('initializeViewport called, currentDateRange before init:', {
            start: currentDateRange.startDate.toISOString(),
            end: currentDateRange.endDate.toISOString()
        });
        
        // FORCE reset currentDateRange to current time to override any stale data
        const now = new Date();
        currentDateRange.startDate = new Date(now);
        currentDateRange.endDate = new Date(now);
        console.log('FORCED reset currentDateRange to NOW:', {
            start: currentDateRange.startDate.toISOString(),
            end: currentDateRange.endDate.toISOString()
        });
        
        const container = document.getElementById('gantt-chart');
        if (!container) {
            console.error('Gantt chart container not found!');
            return;
        }
        
        // Create viewport manager
        viewportManager = new ViewportManager(container);
        console.log('ViewportManager created');
        
        // Set initial view mode from dropdown
        const viewModeDropdown = document.getElementById('view-mode');
        if (viewModeDropdown) {
            viewportManager.currentViewMode = viewModeDropdown.value || 'week';
            console.log('ViewportManager view mode set to:', viewportManager.currentViewMode);
        }
        
        // Initialize with snap to now
        console.log('About to call snapToNow for initialization...');
        viewportManager.snapToNow(false); // No animation on first load
        
        console.log('ViewportManager initialization complete, currentDateRange after init:', {
            start: currentDateRange.startDate.toISOString(),
            end: currentDateRange.endDate.toISOString()
        });
        
        // Add scroll event listener for infinite scrolling
        let scrollTimeout;
        container.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                if (viewportManager) {
                    viewportManager.handleScroll();
                }
            }, 150); // Debounce scroll events
        });
    }

    // Legacy snap function - kept for fallback compatibility
    function snapNowIntoView(fraction = 0.2) {
        try {
            const container = document.getElementById('gantt-chart');
            if (!container) return;
            const svg = container.querySelector('svg');
            if (!svg) return;
            const labelWidth = 120;
            const width = svg.viewBox && svg.viewBox.baseVal && svg.viewBox.baseVal.width ? svg.viewBox.baseVal.width : svg.getAttribute('width');

            // Reconstruct scales similar to renderGanttChart
            const viewModeSel = document.getElementById('view-mode');
            const viewMode = viewModeSel ? viewModeSel.value : 'week';
            let startDate = new Date(currentDateRange.startDate);
            let endDate = new Date(currentDateRange.endDate);

            const timeScale = d3.scaleTime()
                .domain([startDate, endDate])
                .range([labelWidth, Number(width)]);

            const now = new Date();
            if (now < startDate || now > endDate) {
                console.log('NOW is outside visible range, re-rendering chart');
                // If NOW is outside range, use ViewportManager if available
                if (viewportManager) {
                    viewportManager.snapToNow(true);
                } else {
                    // Fallback: center around now
                    const hoursVisible = 168; // Default week view
                    const hoursBeforeNow = hoursVisible * fraction;
                    const newStartDate = new Date(now.getTime() - (hoursBeforeNow * 60 * 60 * 1000));
                    const newEndDate = new Date(now.getTime() + ((hoursVisible - hoursBeforeNow) * 60 * 60 * 1000));
                    currentDateRange.startDate = newStartDate;
                    currentDateRange.endDate = newEndDate;
                    updateDateRangeDisplay();
                    renderGanttChart();
                }
                return;
            }
            
            const nowX = timeScale(now);
            const desiredX = labelWidth + (container.clientWidth - labelWidth) * fraction;
            const delta = nowX - desiredX;
            const threshold = 60; // 60px threshold
            if (Math.abs(delta) < threshold) return; // close enough
            const targetScrollLeft = Math.max(0, container.scrollLeft + delta);
            container.scrollTo({ left: targetScrollLeft, behavior: 'smooth' });
        } catch (error) {
            console.error('Error in snapNowIntoView:', error);
        }
    }

    // Update the date range text display
    function updateDateRangeDisplay() {
        const formatDate = (date) => {
            // Format as DD/MM/YYYY
            const day = date.getDate().toString().padStart(2, '0');
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        };
        
        const dateSpan = document.getElementById('date-range');
        dateSpan.textContent = `${formatDate(currentDateRange.startDate)} - ${formatDate(currentDateRange.endDate)}`;
    }

    const testData = {
        jetties: [],
        assignments: [],
        unscheduled: [],
        defaultPrePump: { hours: 1, minutes: 0 },
        defaultPostPump: { hours: 1, minutes: 0 }
    };

    // Normalize assignment statuses to a consistent set used by the UI
    function getNormalizedAssignmentStatus(status) {
        const s = (status || '').toString().trim().toUpperCase();
        if (s === 'ACTIVE') return 'IN_PROGRESS';
        if (s === 'APPROVED' || s === 'PENDING_APPROVAL') return 'SCHEDULED';
        return s;
    }

    // Optimization preset configurations
    const optimizationPresets = {
        throughput: {
            name: "🚀 Maximum Throughput",
            description: "Prioritizes maximizing the number of vessels processed within the planning horizon. Ideal for high-demand periods when throughput is critical.",
            details: { throughput: "High", cost: "Medium", safety: "Medium" },
            weights: {
                throughput: 15.0,
                demurrage: 3.0,
                priority: 5.0,
                weather: 5.0,
                horizon: 7,
                granularity: 1
            }
        },
        cost: {
            name: "💰 Cost Optimized",
            description: "Focuses on minimizing demurrage costs and operational expenses. Best for periods when cost control is the primary concern.",
            details: { throughput: "Medium", cost: "High", safety: "Low" },
            weights: {
                throughput: 8.0,
                demurrage: 18.0,
                priority: 2.0,
                weather: 1.0,
                horizon: 14,
                granularity: 2
            }
        },
        safety: {
            name: "🛡️ Safety First",
            description: "Prioritizes weather safety and customer priority requirements. Recommended during adverse weather conditions or for high-value customers.",
            details: { throughput: "Low", cost: "Medium", safety: "High" },
            weights: {
                throughput: 5.0,
                demurrage: 8.0,
                priority: 15.0,
                weather: 12.0,
                horizon: 10,
                granularity: 1
            }
        }
    };

    // Current optimization parameters (will be updated by preset selection)
    let currentOptimizationParams = { ...optimizationPresets.throughput.weights };

    // Simple client-side optimization log utilities
    let optimizationPollCount = 0;
    function clearOptimizationLog() {
        try {
            const log = document.getElementById('optimization-log');
            if (log) { log.innerHTML = ''; }
        } catch (_) {}
    }
    function addOptimizationLog(message) {
        try {
            const log = document.getElementById('optimization-log');
            if (!log) return;
            // If placeholder text exists, clear it first
            if (log.textContent && log.textContent.includes('Adjust parameters')) {
                log.innerHTML = '';
            }
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            const timeEl = document.createElement('span');
            timeEl.className = 'log-time';
            timeEl.textContent = new Date().toLocaleString();
            const msgEl = document.createElement('span');
            msgEl.className = 'log-message';
            msgEl.textContent = String(message || '');
            entry.appendChild(timeEl);
            entry.appendChild(msgEl);
            log.appendChild(entry);
            // Auto-scroll
            log.scrollTop = log.scrollHeight;
        } catch (_) {}
    }

    // Update preset description and parameter displays
    function updatePresetDisplay(presetKey) {
        const preset = optimizationPresets[presetKey];
        const descriptionDiv = document.getElementById('preset-description');

        descriptionDiv.innerHTML = `
            <div class="description-content">
                <h4>${preset.name}</h4>
                <p>${preset.description}</p>
                <div class="preset-details">
                    <span class="detail-item">Throughput: <strong>${preset.details.throughput}</strong></span>
                    <span class="detail-item">Cost Control: <strong>${preset.details.cost}</strong></span>
                    <span class="detail-item">Safety: <strong>${preset.details.safety}</strong></span>
                </div>
            </div>
        `;

        // Update parameter displays
        document.getElementById('display-throughput').textContent = preset.weights.throughput.toFixed(1);
        document.getElementById('display-demurrage').textContent = preset.weights.demurrage.toFixed(1);
        document.getElementById('display-priority').textContent = preset.weights.priority.toFixed(1);
        document.getElementById('display-weather').textContent = preset.weights.weather.toFixed(1);
        document.getElementById('display-horizon').textContent = preset.weights.horizon + ' Days';
        document.getElementById('display-granularity').textContent = preset.weights.granularity + ' Hour' + (preset.weights.granularity > 1 ? 's' : '');

        // Update current parameters
        currentOptimizationParams = { ...preset.weights };
    }

    // Setup event listeners for UI interaction
    function setupEventListeners() {
        // Initialize viewport manager
        initializeViewport();

        // Optimization preset change handler
        document.getElementById('optimization-preset').addEventListener('change', function() {
            const selectedPreset = this.value;
            updatePresetDisplay(selectedPreset);
        });

        // Snap interval change
        const snapSel = document.getElementById('snap-interval');
        if (snapSel) {
            currentSnapMinutes = parseInt(snapSel.value, 10) || 0;
            snapSel.addEventListener('change', function() {
                currentSnapMinutes = parseInt(this.value, 10) || 0;
            });
        }

        // Initialize with default preset
        updatePresetDisplay('throughput');
        
        // View mode change with auto-snap to NOW
        document.getElementById('view-mode').addEventListener('change', function() {
            const viewMode = this.value;
            
            if (viewportManager) {
                // Use ViewportManager for smooth transition and auto-snap
                viewportManager.setViewMode(viewMode, true);
            } else {
                // Fallback to original behavior
                currentDateRange.endDate = new Date();
            updateDateRangeDisplay();
            renderGanttChart();
            }
        });

        // Date navigation
        document.getElementById('prev-week').addEventListener('click', function() {
            const viewMode = document.getElementById('view-mode').value;
            let daysToShift;
            
            // Determine how many days to shift based on view mode
            if (viewMode === 'day') {
                daysToShift = 1;
            } else if (viewMode === 'week') {
                daysToShift = 7;
            } else if (viewMode === '72h') {
                daysToShift = 3;
            } else { // month
                daysToShift = 30;
            }
            
            // Shift dates backward
            const newStartDate = new Date(currentDateRange.startDate);
            newStartDate.setDate(newStartDate.getDate() - daysToShift);
            
            const newEndDate = new Date(currentDateRange.endDate);
            newEndDate.setDate(newEndDate.getDate() - daysToShift);
            
            currentDateRange.startDate = newStartDate;
            currentDateRange.endDate = newEndDate;
            
            updateDateRangeDisplay();
            renderGanttChart();
            setTimeout(() => snapNowIntoView(), 0);
        });

        document.getElementById('next-week').addEventListener('click', function() {
            const viewMode = document.getElementById('view-mode').value;
            let daysToShift;
            
            // Determine how many days to shift based on view mode
            if (viewMode === 'day') {
                daysToShift = 1;
            } else if (viewMode === 'week') {
                daysToShift = 7;
            } else if (viewMode === '72h') {
                daysToShift = 3;
            } else { // month
                daysToShift = 30;
            }
            
            // Shift dates forward
            const newStartDate = new Date(currentDateRange.startDate);
            newStartDate.setDate(newStartDate.getDate() + daysToShift);
            
            const newEndDate = new Date(currentDateRange.endDate);
            newEndDate.setDate(newEndDate.getDate() + daysToShift);
            
            currentDateRange.startDate = newStartDate;
            currentDateRange.endDate = newEndDate;
            
            updateDateRangeDisplay();
            renderGanttChart();
            setTimeout(() => snapNowIntoView(), 0);
        });

        // Dummy handlers for other controls
        document.getElementById('optimize-button').addEventListener('click', async function() {
            try {
                // Disable button and show loading state
                const button = this;
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Optimizing...';

                // Reset and write initial log entries
                optimizationPollCount = 0;
                clearOptimizationLog();
                addOptimizationLog('Starting optimization...');
                addOptimizationLog('Parameters: ' + JSON.stringify({
                    horizon_days: currentOptimizationParams.horizon,
                    time_granularity_hours: currentOptimizationParams.granularity,
                    weight_throughput: currentOptimizationParams.throughput,
                    weight_demurrage: currentOptimizationParams.demurrage,
                    weight_priority: currentOptimizationParams.priority,
                    weight_weather: currentOptimizationParams.weather,
                    force_assign_all: document.getElementById('force-assign-all').checked,
                    include_mock_assignments: !!window.isUsingMockAssignments
                }));

                // Snapshot current assignments for diffing after optimization
                try {
                    window._assignmentsBeforeOptimize = (testData.assignments || []).map(a => ({
                        id: a.id,
                        vesselId: String(a.vesselId || ''),
                        vesselName: a.vesselName,
                        jettyName: a.jettyName,
                        startTime: a.startTime ? new Date(a.startTime) : null,
                        endTime: a.endTime ? new Date(a.endTime) : null
                    }));
                } catch (_) { window._assignmentsBeforeOptimize = []; }

                // Call optimize API using current preset parameters
                const response = await fetch('/api/optimize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        horizon_days: currentOptimizationParams.horizon,
                        time_granularity_hours: currentOptimizationParams.granularity,
                        weight_throughput: currentOptimizationParams.throughput,
                        weight_demurrage: currentOptimizationParams.demurrage,
                        weight_priority: currentOptimizationParams.priority,
                        weight_weather: currentOptimizationParams.weather,
                        force_assign_all: document.getElementById('force-assign-all').checked,
                        approach_time_hours: currentOptimizationParams.approach || 2,
                        free_wait_buffer_hours: currentOptimizationParams.free_wait || 1,
                        include_mock_assignments: !!window.isUsingMockAssignments
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to start optimization');
                }

                const result = await response.json();

                addOptimizationLog(result.message || 'Optimization started');

                // Start polling for status
                pollOptimizationStatus();

            } catch (error) {
                console.error('Error starting optimization:', error);
                showErrorToast('Failed to start optimization: ' + error.message);
                addOptimizationLog('Error: ' + (error.message || error));

                // Re-enable button
                const button = this;
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-cogs"></i> Optimize';
            }
        });

        document.getElementById('apply-parameters').addEventListener('click', function() {
            showInfoToast('Apply parameters functionality will be implemented soon');
        });

        document.getElementById('view-toggle').addEventListener('click', function() {
            // Toggle between Gantt view and table-only view
            isGanttView = !isGanttView;
            
            // Update button icon based on current view
            if (isGanttView) {
                this.innerHTML = '<i class="fas fa-th-list"></i> Toggle View';
            } else {
                this.innerHTML = '<i class="fas fa-chart-bar"></i> Toggle View';
            }
            
            // Show/hide Gantt chart and legend based on toggle state
            const ganttContainer = document.getElementById('gantt-chart');
            const legendContainer = document.querySelector('.schedule-legend');
            const overviewTable = document.getElementById('overview-table-container');
            
            if (isGanttView) {
                ganttContainer.style.display = 'block';
                if (legendContainer) legendContainer.style.display = 'flex';
                if (overviewTable) overviewTable.style.display = 'none';
                renderGanttChart(); // Re-render chart when showing
            } else {
                ganttContainer.style.display = 'none';
                if (legendContainer) legendContainer.style.display = 'none';
                if (overviewTable) {
                    overviewTable.style.display = 'block';
                    renderOverviewTable();
                }
            }
            
            // Get the card that contains the Gantt chart
            const ganttCard = ganttContainer.closest('.card');
            
            // Update card header based on view
            const cardHeader = ganttCard.querySelector('.card-header h3');
            if (cardHeader) {
                cardHeader.textContent = isGanttView ? 'Schedule Overview' : 'Schedule Overview (Table View)';
            }
            
            // Always make sure the assignments table is updated
            renderAssignmentTable();
        });

        // Reset data functionality
        document.getElementById('reset-data-btn').addEventListener('click', async function() {
            if (!confirm('Are you sure you want to reset ALL data? This will:\n\n• Delete all assignments\n• Delete all vessels\n• Reset all settings to defaults\n\nThis action cannot be undone.')) {
                return;
            }

            const btn = this;
            const originalHtml = btn.innerHTML;
            try {
                btn.disabled = true;
                btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Resetting...';

                // Call reset API endpoint
                const response = await fetch('/api/reset-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    const error = await response.text();
                    throw new Error(error || 'Failed to reset data');
                }

                const result = await response.json();

                if (result.success) {
                    showSuccessToast('All data has been reset successfully. The page will reload.');
                    window.location.reload();
                } else {
                    showErrorToast('Reset failed: ' + (result.message || 'Unknown error'));
                }

            } catch (error) {
                console.error('Reset failed:', error);
                showErrorToast('Failed to reset data: ' + (error.message || error));
            } finally {
                btn.disabled = false;
                btn.innerHTML = originalHtml;
            }
        });

        // Add more event listeners here
        // Jump to NOW button with enhanced functionality
        const nowBtn = document.getElementById('now-button');
        if (nowBtn) {
            nowBtn.addEventListener('click', function(e) {
                console.log('NOW button clicked');
                e.preventDefault();
                
                // Add visual feedback
                this.classList.add('btn-loading');
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Snapping...';
                
                // Ensure ViewportManager is initialized
                if (!viewportManager) {
                    console.log('ViewportManager not initialized, creating...');
                    initializeViewport();
                }
                
                if (viewportManager) {
                    console.log('Using ViewportManager for snap to NOW');
                    viewportManager.snapToNow(true);
                } else {
                    console.log('Falling back to legacy snap function');
                snapNowIntoView(0.2);
                }
                
                // Reset button after delay
                setTimeout(() => {
                    this.classList.remove('btn-loading');
                    this.innerHTML = '<i class="fas fa-clock"></i> Now';
                }, 1000);
            });
        }

        // Debug snap button for testing
        const debugSnapBtn = document.getElementById('debug-snap-button');
        if (debugSnapBtn) {
            debugSnapBtn.addEventListener('click', function() {
                console.log('=== DEBUG SNAP TEST ===');
                
                const now = new Date();
                const container = document.getElementById('gantt-chart');
                const svg = container ? container.querySelector('svg') : null;
                
                console.log('Current state:', {
                    viewportManager: !!viewportManager,
                    currentDateRange: {
                        start: currentDateRange.startDate.toISOString(),
                        end: currentDateRange.endDate.toISOString(),
                        duration: (currentDateRange.endDate - currentDateRange.startDate) / (1000 * 60 * 60) + ' hours'
                    },
                    now: now.toISOString(),
                    nowInRange: now >= currentDateRange.startDate && now <= currentDateRange.endDate,
                    ganttContainer: !!container,
                    svg: !!svg
                });
                
                if (viewportManager) {
                    const viewportBounds = viewportManager.getViewportBounds();
                    const loadBounds = viewportManager.getLoadBounds();
                    console.log('ViewportManager bounds:', {
                        viewport: {
                            start: viewportBounds.startTime.toISOString(),
                            end: viewportBounds.endTime.toISOString(),
                            hours: viewportBounds.visibleHours
                        },
                        loadBounds: {
                            start: loadBounds.startTime.toISOString(),
                            end: loadBounds.endTime.toISOString()
                        }
                    });
                }
                
                if (container && svg) {
                    const labelWidth = 120;
                    const width = parseFloat(svg.getAttribute('width'));
                    const timeScale = d3.scaleTime()
                        .domain([currentDateRange.startDate, currentDateRange.endDate])
                        .range([labelWidth, width]);
                    
                    const nowX = timeScale(now);
                    const containerWidth = container.clientWidth;
                    const expectedNowX = labelWidth + (containerWidth - labelWidth) * 0.2; // 20%
                    
                    console.log('NOW position analysis:', {
                        nowX: nowX,
                        expectedNowX: expectedNowX,
                        delta: nowX - expectedNowX,
                        nowInCurrentRange: now >= currentDateRange.startDate && now <= currentDateRange.endDate,
                        scrollLeft: container.scrollLeft,
                        scrollWidth: container.scrollWidth,
                        clientWidth: container.clientWidth
                    });
                }
                
                if (typeof showInfoToast === 'function') {
                    showInfoToast('Debug analysis complete - check console');
                } else {
                    alert('Debug analysis complete - check console');
                }
            });
        }

        document.getElementById('refresh-btn').addEventListener('click', function() {
            // Show loading state
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';

            // Reload data from API
            loadRealData().finally(() => {
                // Reset button state
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh';
            });
        });

        // Toggle mock assignments on/off
        document.getElementById('mock-toggle-btn').addEventListener('click', async function() {
            const btn = this;
            try {
                btn.disabled = true;
                if (!window.isUsingMockAssignments) {
                    window.isUsingMockAssignments = true;
                    btn.innerHTML = '<i class="fas fa-undo"></i> Use Real Data';
                    await generateMockAssignments();
                    renderGanttChart();
                    renderAssignmentTable();
                    renderUnscheduledVesselsTable();
                } else {
                    window.isUsingMockAssignments = false;
                    btn.innerHTML = '<i class="fas fa-vial"></i> Use Mock Data';
                    await loadRealData();
                }
            } finally {
                btn.disabled = false;
            }
        });
    }

    // Replace the test data creation with real API data loading
    async function loadRealData(forceAssignmentsFetch = false) {
        try {
            // Load jetties from API
            const jettiesResponse = await fetch('/api/jetties');
            if (!jettiesResponse.ok) {
                throw new Error('Failed to load jetties');
            }
            const jettiesData = await jettiesResponse.json();
            // Keep full jetty objects so we can use their types for compatibility
            testData.jetties = jettiesData.map(j => ({
                id: j.id,
                name: j.name,
                // Normalize possible fields and casing
                type: ((j.jetty_type || j.type || '') + '').toLowerCase()
            }));
            
            // Load assignments from API unless mock mode is enabled
            if (!window.isUsingMockAssignments || forceAssignmentsFetch) {
                const assignmentsResponse = await fetch('/api/schedule/assignments');
                if (!assignmentsResponse.ok) {
                    throw new Error('Failed to load assignments');
                }
                const assignmentsData = await assignmentsResponse.json();
                
                // Transform API assignments to our format with robust jetty name resolution
                const realAssignments = assignmentsData.map(assignment => {
                    const resolveJettyName = () => {
                        const byExactName = jettiesData.find(j => j.name === assignment.jetty_name);
                        if (byExactName) return byExactName.name;
                        const candidateId = assignment.jetty_id || assignment.jetty_name;
                        const byId = jettiesData.find(j => String(j.id) === String(candidateId));
                        if (byId) return byId.name;
                        return assignment.jetty_name || '';
                    };
                    return ({
                    id: assignment.id,
                    jettyName: resolveJettyName(),
                    vesselName: assignment.vessel_name,
                    vesselId: assignment.vessel_id,
                    product: assignment.product || assignment.cargo_product || null,
                    volume: assignment.volume || assignment.cargo_volume || null,
                    startTime: new Date(assignment.start_time),
                    endTime: new Date(assignment.end_time),
                    status: assignment.status,
                    prePumpTime: testData.defaultPrePump,
                    postPumpTime: testData.defaultPostPump
                });
                });

                // In mock mode, keep mock assignments visible and append real ones
                if (window.isUsingMockAssignments) {
                    const mocks = Array.isArray(testData.mockAssignments) ? testData.mockAssignments : [];
                    testData.assignments = [...mocks, ...realAssignments];
                } else {
                    testData.assignments = realAssignments;
                }
            } else {
                console.log('Mock mode enabled: skipping assignments fetch');
            }
            
            // Load unscheduled vessels (approaching or arrived but not assigned)
            const vesselsResponse = await fetch('/api/vessels?status=EN_ROUTE&status=APPROACHING&status=ARRIVED');
            if (!vesselsResponse.ok) {
                throw new Error('Failed to load vessels');
            }
            const vesselsData = await vesselsResponse.json();
            
            // Create comprehensive set of assigned vessel identifiers
            // Only include active assignments (exclude CANCELLED)
            const assignedIdentifiers = new Set();
            for (const assignment of (testData.assignments || [])) {
                const status = getNormalizedAssignmentStatus(assignment.status);
                // Skip cancelled assignments - these vessels should be available for scheduling
                if (status === 'CANCELLED') {
                    continue;
                }
                
                // Add vessel ID
                if (assignment.vesselId) {
                    assignedIdentifiers.add(String(assignment.vesselId));
                }
                // Add vessel name for fallback matching
                if (assignment.vesselName) {
                    assignedIdentifiers.add(String(assignment.vesselName));
                }
            }

            // Get unassignment reasons from last optimization result
            let unassignmentReasons = {};
            try {
                const optimizationStatusResponse = await fetch('/api/optimize/status');
                if (optimizationStatusResponse.ok) {
                    const optimizationStatus = await optimizationStatusResponse.json();
                    if (optimizationStatus.result && optimizationStatus.result.unassigned) {
                        optimizationStatus.result.unassigned.forEach(unassigned => {
                            unassignmentReasons[unassigned.vessel_id] = unassigned.reason;
                        });
                    }
                }
            } catch (error) {
                console.warn('Failed to load optimization status for unassignment reasons:', error);
            }

            // Transform API vessels to unscheduled format, filtering out assigned vessels
            testData.unscheduled = vesselsData
                .filter(vessel => {
                    // Check if vessel is assigned by ID, name, MMSI, or IMO
                    const vesselId = String(vessel.id);
                    const vesselName = String(vessel.name || '');
                    const vesselMmsi = String(vessel.mmsi || '');
                    const vesselImo = String(vessel.imo || '');

                    return !assignedIdentifiers.has(vesselId) &&
                           !assignedIdentifiers.has(vesselName) &&
                           !(vesselMmsi && assignedIdentifiers.has(vesselMmsi)) &&
                           !(vesselImo && assignedIdentifiers.has(vesselImo));
                })
                .map(vessel => ({
                    id: vessel.id,
                    name: vessel.name,
                    type: vessel.type.toUpperCase(), // Ensure uppercase for consistency
                    eta: vessel.eta ? new Date(vessel.eta) : new Date(),
                    length: vessel.length,
                    draft: vessel.draft,
                    cargo: vessel.total_cargo_volume || 0,
                    priority: vessel.priority || 1,
                    cargoType: vessel.cargoes && vessel.cargoes.length > 0
                        ? vessel.cargoes[0].product
                        : "Unknown",
                    isLoading: vessel.cargoes && vessel.cargoes.length > 0
                        ? vessel.cargoes[0].is_loading
                        : false,
                    unassignment_reason: unassignmentReasons[vessel.id] || null
                }));
                
            // Update UI with real data
            renderGanttChart();
            renderAssignmentTable();
            renderOverviewTable();
            renderUnscheduledVesselsTable();
            await renderScoresPanel();
            renderChangeLog();
            
            console.log('Loaded real data:', {
                jetties: testData.jetties.length,
                assignments: testData.assignments.length,
                unscheduled: testData.unscheduled.length
            });
            
        } catch (error) {
            console.error('Error loading real data:', error);
            console.log('No data available');
            renderGanttChart();
            renderAssignmentTable();
            renderUnscheduledVesselsTable();
        }
    }

    async function fetchOptimizationStatus() {
        try {
            const res = await fetch('/api/optimize/status');
            if (!res.ok) return null;
            return await res.json();
        } catch (_) { return null; }
    }

    async function renderScoresPanel() {
        const card = document.getElementById('optimizer-scores-card');
        if (!card) return;
        const status = await fetchOptimizationStatus();
        if (!status || !status.result || !status.result.breakdown) {
            card.style.display = 'none';
            return;
        }
        const breakdown = status.result.breakdown;
        const totals = breakdown.totals || {};
        const totalObjective = typeof breakdown.total_objective === 'number' ? breakdown.total_objective : (status.result.objective_value || 0);
        const fmt = (v) => (typeof v === 'number' ? v.toFixed(1) : '-');

        document.getElementById('score-total').textContent = fmt(totalObjective);
        document.getElementById('score-throughput').textContent = fmt(totals.throughput);
        document.getElementById('score-demurrage').textContent = fmt(totals.demurrage);
        document.getElementById('score-priority').textContent = fmt(totals.priority);

        const forceWrap = document.getElementById('score-force-wrap');
        const balanceWrap = document.getElementById('score-balance-wrap');
        if (forceWrap) {
            const fv = totals.force_assign || 0;
            forceWrap.style.display = fv ? 'flex' : 'none';
            const forceSpan = document.getElementById('score-force');
            if (forceSpan) forceSpan.textContent = fmt(fv);
        }
        if (balanceWrap) {
            const bv = totals.balancing || 0;
            balanceWrap.style.display = bv ? 'flex' : 'none';
            const balSpan = document.getElementById('score-balance');
            if (balSpan) balSpan.textContent = fmt(bv);
        }

        const updatedAt = document.getElementById('score-updated-at');
        if (updatedAt) updatedAt.textContent = status.last_optimization_time ? new Date(status.last_optimization_time).toLocaleString() : '';

        // Show weights
        try {
            const weights = breakdown.weights || {};
            const setText = (id, v) => { const el = document.getElementById(id); if (el) el.textContent = (v ?? '-') + (id === 'w-buffer' || id === 'w-approach' ? ' h' : ''); };
            setText('w-throughput', typeof weights.throughput === 'number' ? weights.throughput.toFixed(1) : '-');
            setText('w-demurrage', typeof weights.demurrage === 'number' ? weights.demurrage.toFixed(1) : '-');
            setText('w-priority', typeof weights.priority === 'number' ? weights.priority.toFixed(1) : '-');
            setText('w-buffer', typeof weights.free_wait_buffer_hours === 'number' ? weights.free_wait_buffer_hours : '-');
            setText('w-approach', typeof weights.approach_time_hours === 'number' ? weights.approach_time_hours : '-');
        } catch (_) {}

        const tbody = document.getElementById('scores-assignment-body');
        if (tbody) {
            const rows = (breakdown.per_assignment || []).map(item => {
                const id = item.assignment_id || '';
                const jetty = item.jetty_name || item.jetty_id || '';
                const vessel = item.vessel_name || item.vessel_id || '';
                const explainBtn = `<button class="btn btn-sm btn-secondary" data-exp="${id}"><i class="fas fa-info-circle"></i></button>`;
                return `<tr>
                    <td>${id} – ${vessel}</td>
                    <td>${jetty}</td>
                    <td>${fmt(item.throughput)}</td>
                    <td>${fmt(item.demurrage)}</td>
                    <td>${fmt(item.priority)}</td>
                    <td>${explainBtn}</td>
                </tr>`;
            });
            tbody.innerHTML = rows.length ? rows.join('') : '<tr><td colspan="6" class="text-center">No data</td></tr>';

            // Wire explain buttons (simple alert for now)
            tbody.querySelectorAll('button[data-exp]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const id = btn.getAttribute('data-exp');
                    const item = (breakdown.per_assignment || []).find(x => String(x.assignment_id) === String(id));
                    if (!item) return;
                    const weights = breakdown.weights || {};
                    const fmtn = (n) => (typeof n === 'number' ? n.toFixed(1) : '-');
                    let volTxt = '';
                    try {
                        if (typeof weights.throughput === 'number' && weights.throughput > 0 && typeof item.throughput === 'number') {
                            const approxVol = item.throughput / weights.throughput;
                            volTxt = ` (≈ ${Math.round(approxVol).toLocaleString()} m³)`;
                        }
                    } catch (_) {}

                    // Try parse wait hours from reasons if present
                    let waitTxt = '';
                    try {
                        const r = (item.reasons || []).find(s => /Wait beyond buffer:\s*/i.test(s));
                        if (r) {
                            const m = r.match(/([\d\.]+)\s*h/);
                            if (m) waitTxt = ` (≈ ${parseFloat(m[1]).toFixed(1)} h over buffer)`;
                        }
                    } catch (_) {}

                    const msg = `Assignment ${id} – ${item.vessel_name || item.vessel_id || ''} at ${item.jetty_name || item.jetty_id || ''}\n\n` +
                        `Throughput: ${fmtn(item.throughput)}${volTxt} = volume × w_throughput\n` +
                        `Demurrage: ${fmtn(item.demurrage)}${waitTxt} = − wait_over_buffer × w_demurrage\n` +
                        `Priority: ${fmtn(item.priority)} = priority × w_priority\n\n` +
                        `Why this assignment helps the plan:\n - ${(item.reasons || []).join('\n - ') || 'No details'}\n\n` +
                        `Weights used: throughput ${fmtn(weights.throughput)}, demurrage ${fmtn(weights.demurrage)}, priority ${fmtn(weights.priority)}; ` +
                        `buffer ${weights.free_wait_buffer_hours ?? '-'} h, approach ${weights.approach_time_hours ?? '-'} h.`;
                    openExplainModal(msg);
                });
            });
        }

        card.style.display = 'block';
    }

    // Simple modal for explanations
    function openExplainModal(text) {
        let modal = document.getElementById('explain-modal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'explain-modal';
            modal.style.position = 'fixed';
            modal.style.left = 0; modal.style.top = 0; modal.style.right = 0; modal.style.bottom = 0;
            modal.style.background = 'rgba(0,0,0,0.4)';
            modal.style.display = 'flex';
            modal.style.alignItems = 'center';
            modal.style.justifyContent = 'center';
            modal.innerHTML = `
                <div id="explain-modal-content" style="background:#fff; max-width: 720px; width: 92%; border-radius: 8px; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                    <div style="display:flex; align-items:center; justify-content:space-between; padding: 0.75rem 1rem; border-bottom:1px solid #dee2e6;">
                        <strong>Assignment Explanation</strong>
                        <button id="explain-close" class="btn btn-sm btn-secondary">Close</button>
                    </div>
                    <div style="padding: 1rem;">
                        <pre id="explain-text" style="white-space: pre-wrap; font-family: var(--font-mono, monospace); font-size: 0.9rem; margin:0;"></pre>
                    </div>
                    <div style="display:flex; justify-content:flex-end; gap:0.5rem; padding: 0.75rem 1rem; border-top:1px solid #dee2e6;">
                        <button id="explain-copy" class="btn btn-sm btn-primary"><i class="fas fa-copy"></i> Copy</button>
                    </div>
                </div>`;
            document.body.appendChild(modal);
            modal.addEventListener('click', (e) => { if (e.target === modal) closeExplainModal(); });
            modal.querySelector('#explain-close').addEventListener('click', closeExplainModal);
            modal.querySelector('#explain-copy').addEventListener('click', async () => {
                try {
                    const content = document.getElementById('explain-text').textContent || '';
                    await navigator.clipboard.writeText(content);
                } catch (_) {}
            });
        }
        const pre = document.getElementById('explain-text');
        if (pre) pre.textContent = String(text || '');
        modal.style.display = 'flex';
    }
    function closeExplainModal() {
        const modal = document.getElementById('explain-modal');
        if (modal) modal.style.display = 'none';
    }

    function formatTimeDuration(durationObj) {
        const hours = durationObj.hours || 0;
        const minutes = durationObj.minutes || 0;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }

    // Format a JS Date to DD/MM/YYYY HH:MM (24h)
    function formatDateTimeDDMMYYYY(date) {
        const d = new Date(date);
        const day = d.getDate().toString().padStart(2, '0');
        const month = (d.getMonth() + 1).toString().padStart(2, '0');
        const year = d.getFullYear();
        const hours = d.getHours().toString().padStart(2, '0');
        const minutes = d.getMinutes().toString().padStart(2, '0');
        return `${day}/${month}/${year} ${hours}:${minutes}`;
    }

    // Create tooltip element on demand
    function ensureDragTooltip() {
        if (!dragTooltipEl) {
            dragTooltipEl = document.createElement('div');
            dragTooltipEl.className = 'drag-tooltip';
            document.body.appendChild(dragTooltipEl);
        }
    }

    function showDragTooltip(text, clientX, clientY) {
        ensureDragTooltip();
        dragTooltipEl.textContent = text;
        dragTooltipEl.style.left = `${clientX + 12}px`;
        dragTooltipEl.style.top = `${clientY - 24}px`;
        dragTooltipEl.style.display = 'block';
    }

    function hideDragTooltip() {
        if (dragTooltipEl) dragTooltipEl.style.display = 'none';
    }

    // Snap a Date to the nearest N minutes (round to nearest)
    function snapDateToMinutes(date, minutes) {
        if (!minutes || minutes <= 0) return date;
        const ms = minutes * 60 * 1000;
        const t = date.getTime();
        const snapped = Math.round(t / ms) * ms;
        return new Date(snapped);
    }

    function renderAssignmentTable() {
        const tableBody = document.getElementById('assignments-table-body');

        // Filter out CANCELLED assignments for cleaner view
        const activeAssignments = testData.assignments.filter(assignment => {
            const status = getNormalizedAssignmentStatus(assignment.status);
            return status !== 'CANCELLED';
        });

        if (activeAssignments.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="10" class="text-center">No active assignments found</td></tr>';
            return;
        }

        tableBody.innerHTML = activeAssignments.map(assignment => {
            // Format date/time for display - DD/MM/YYYY HH:MM format (24h)
            const formatDate = (date) => {
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                
                return `${day}/${month}/${year} ${hours}:${minutes}`;
            };

            // Calculate duration
            const duration = Math.round((assignment.endTime - assignment.startTime) / (1000 * 60 * 60));

            // Status CSS class
            const normalizedStatus = getNormalizedAssignmentStatus(assignment.status);
            let statusClass = 'waiting';
            if (normalizedStatus === 'COMPLETED' || normalizedStatus === 'IN_PROGRESS') {
                statusClass = 'berthed';
            } else if (normalizedStatus === 'SCHEDULED') {
                statusClass = 'scheduled';
            }

            // Set default cargo values if not present
            const product = assignment.product || assignment.cargoType || "Unknown Product";
            const volume = assignment.volume ? `${assignment.volume.toLocaleString()} m³` : 
                           (assignment.cargo ? `${assignment.cargo.toLocaleString()} m³` : "Unknown");
            const operation = assignment.operation || (assignment.isLoading ? "Loading" : "Unloading");

            // Create a vessel link for navigation
            const vesselLink = `<a href="/nominated-vessels?vessel_id=${assignment.vesselId || ''}" class="vessel-link">${assignment.vesselName}</a>`;

            return `
                <tr>
                    <td>${assignment.id}</td>
                    <td>${assignment.jettyName}</td>
                    <td>${vesselLink}</td>
                    <td>${product}</td>
                    <td>${volume}</td>
                    <td>${operation}</td>
                    <td>${formatDate(assignment.startTime)}</td>
                    <td>${formatDate(assignment.endTime)}</td>
                    <td><span class="vessel-status ${statusClass}">${normalizedStatus}</span></td>
                    <td>
                        <button class="btn btn-sm btn-secondary edit-btn" data-id="${assignment.id}">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${normalizedStatus === 'SCHEDULED' ? 
                            `<button class="btn btn-sm btn-warning unschedule-btn" data-id="${assignment.id}" title="Unschedule assignment">
                                <i class="fas fa-undo"></i>
                            </button>` : ''
                        }
                        <button class="btn btn-sm btn-secondary delete-btn" data-id="${assignment.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');

        // Add event listeners for edit, unschedule, and delete buttons
        document.querySelectorAll('.edit-btn').forEach(button => {
            button.addEventListener('click', function() {
                const assignmentId = this.getAttribute('data-id');
                window.location.href = `/schedule/add?id=${assignmentId}`;
            });
        });

        document.querySelectorAll('.unschedule-btn').forEach(button => {
            button.addEventListener('click', async function() {
                const assignmentId = this.getAttribute('data-id');
                const assignment = testData.assignments.find(a => String(a.id) === String(assignmentId));
                const vesselName = assignment ? assignment.vesselName : 'Unknown vessel';
                
                if (!confirm(`Are you sure you want to unschedule ${vesselName}? This will cancel the assignment and make the vessel available for optimization again.`)) {
                    return;
                }
                
                const btn = this;
                const originalHtml = btn.innerHTML;
                try {
                    btn.disabled = true;
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    
                    const res = await fetch(`/api/schedule/assignments/${encodeURIComponent(assignmentId)}/unschedule`, { 
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ reason: 'Unscheduled by user' })
                    });
                    
                    if (!res.ok) {
                        const errorData = await res.json().catch(() => ({}));
                        throw new Error(errorData.detail || 'Failed to unschedule assignment');
                    }
                    
                    const result = await res.json();
                    
                    // Reload from API to ensure consistency
                    await loadRealData();
                    renderGanttChart();
                    renderAssignmentTable();
                    renderUnscheduledVesselsTable();
                    renderChangeLog();
                    
                    showSuccessToast(result.message || 'Assignment unscheduled successfully');
                } catch (e) {
                    console.error('Unschedule failed', e);
                    showErrorToast('Failed to unschedule assignment: ' + (e.message || e));
                } finally {
                    btn.disabled = false;
                    btn.innerHTML = originalHtml;
                }
            });
        });

        document.querySelectorAll('.delete-btn').forEach(button => {
            button.addEventListener('click', async function() {
                const assignmentId = this.getAttribute('data-id');
                if (!confirm('Are you sure you want to delete this assignment?')) return;
                const btn = this;
                const originalHtml = btn.innerHTML;
                try {
                    btn.disabled = true;
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    const res = await fetch(`/api/schedule/assignments/${encodeURIComponent(assignmentId)}`, { method: 'DELETE' });
                    if (!res.ok) {
                        const msg = await res.text();
                        throw new Error(msg || 'Failed to delete assignment');
                    }
                    // Reload from API to ensure consistency
                    await loadRealData();
                } catch (e) {
                    console.error('Delete failed', e);
                    showErrorToast('Failed to delete assignment: ' + (e.message || e));
                } finally {
                    btn.disabled = false;
                    btn.innerHTML = originalHtml;
                }
            });
        });
    }

    // Toast notification functions are now handled by the unified toast system
    // The global functions showToast, showSuccessToast, showErrorToast, showInfoToast, showWarningToast are available

    // Compact table for the Overview card when toggled to table view
    function renderOverviewTable() {
        try {
            const tbody = document.getElementById('overview-table-body');
            const container = document.getElementById('overview-table-container');
            if (!tbody || !container) return;

            // Filter out CANCELLED for overview as well
            const rows = (testData.assignments || []).filter(a => getNormalizedAssignmentStatus(a.status) !== 'CANCELLED');

            if (!rows.length) {
                tbody.innerHTML = '<tr><td colspan="8" class="text-center">No active assignments found</td></tr>';
                return;
            }

            const fmt = (d) => {
                const date = new Date(d);
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');
                return `${day}/${month}/${year} ${hours}:${minutes}`;
            };

            tbody.innerHTML = rows.map(a => {
                const product = a.product || a.cargoType || 'Unknown Product';
                const volume = a.volume ? `${a.volume.toLocaleString()} m³` : (a.cargo ? `${a.cargo.toLocaleString()} m³` : 'Unknown');
                const status = getNormalizedAssignmentStatus(a.status);
                return `
                    <tr>
                        <td>${a.id}</td>
                        <td>${a.jettyName}</td>
                        <td>${a.vesselName}</td>
                        <td>${product}</td>
                        <td>${volume}</td>
                        <td>${fmt(a.startTime)}</td>
                        <td>${fmt(a.endTime)}</td>
                        <td>${status}</td>
                    </tr>
                `;
            }).join('');
        } catch (_) {}
    }

    function renderGanttChart() {
        // Skip rendering if the view is toggled off
        if (!isGanttView) return;
        
        const container = document.getElementById('gantt-chart');
        container.innerHTML = '';

        // Check if jetty data is available
        if (!testData.jetties || testData.jetties.length === 0) {
            container.innerHTML = '<div class="text-center p-4">No jetty data available. Please ensure jetties are loaded.</div>';
            return;
        }
        
        console.log('renderGanttChart called, currentDateRange at start of render:', {
            start: currentDateRange.startDate.toISOString(),
            end: currentDateRange.endDate.toISOString(),
            duration: (currentDateRange.endDate - currentDateRange.startDate) / (1000 * 60 * 60) + ' hours',
            now: new Date().toISOString()
        });

        // Use ViewportManager configuration if available
        const viewMode = document.getElementById('view-mode') ? document.getElementById('view-mode').value : 'week';
        let startDate = new Date(currentDateRange.startDate);
        let endDate = new Date(currentDateRange.endDate);
        let viewConfig = null;
        let loadBounds = null;
        
        if (viewportManager) {
            viewConfig = viewportManager.getCurrentConfig();
            // Get extended range for data loading (which assignments to include)
            loadBounds = viewportManager.getLoadBounds();
            
            // But use currentDateRange for chart rendering (viewport positioning)
            // This ensures NOW appears at the correct 20% position
            console.log('Rendering with ViewportManager:', {
                currentDateRange: {
                    start: currentDateRange.startDate.toISOString(),
                    end: currentDateRange.endDate.toISOString()
                },
                loadBounds: {
                    start: loadBounds.startTime.toISOString(),
                    end: loadBounds.endTime.toISOString()
                }
            });
        } else {
            // Fallback to original logic for backward compatibility
        if (viewMode === 'day') {
                const padDays = 7;
            const sd = new Date(startDate);
            sd.setDate(sd.getDate() - padDays);
            const ed = new Date(startDate);
            ed.setDate(ed.getDate() + padDays + 1);
            ed.setHours(23, 59, 59, 999);
            startDate = sd;
            endDate = ed;
            }
        }

        // Setup dimensions (wider width for day view to enable horizontal scrolling)
        const labelWidth = 120; // Fixed width for jetty labels
        const jettyCount = testData.jetties.length;
        const rowHeight = 50;
        const headerHeight = 80; // Space for both major and minor time labels above chart
        const height = (jettyCount * rowHeight) + headerHeight;
        const totalHours = Math.max(1, Math.round((endDate - startDate) / (1000 * 60 * 60)));
        
        // Use ViewportManager configuration for consistent scaling
        let pixelsPerHour = 20; // Default fallback
        if (viewConfig) {
            pixelsPerHour = viewConfig.pixelsPerHour;
        } else {
            // Fallback scaling for different view modes
            if (viewMode === 'day') pixelsPerHour = 40;
            else if (viewMode === '72h') pixelsPerHour = 24;
            else if (viewMode === 'month') pixelsPerHour = 8;
        }

        const width = Math.max(container.clientWidth, labelWidth + (totalHours * pixelsPerHour));

        // Create SVG with proper overflow handling
        const svg = d3.select(container)
            .append('svg')
            .attr('width', width)
            .attr('height', height)
            .attr('viewBox', `0 0 ${width} ${height}`)
            .style('overflow', 'visible'); // Allow text to extend beyond SVG boundaries

        // Create fixed label column background (important to make labels always visible)
        svg.append('rect')
            .attr('class', 'jetty-background')
            .attr('x', 0)
            .attr('y', headerHeight)
            .attr('width', labelWidth)
            .attr('height', height - headerHeight);

        // Create scales
        const timeScale = d3.scaleTime()
            .domain([startDate, endDate])
            .range([labelWidth, width]);

        const jettyScale = d3.scaleBand()
            .domain(testData.jetties.map(j => j.name))
            .range([headerHeight, height])
            .padding(0.1);

        // Dynamic time axes with ViewportManager configuration
        const viewModeForAxis = viewMode;
        const containerWidth = container.clientWidth;
        const availableWidth = width - labelWidth;
        
        // Use ViewportManager config for optimal tick intervals
        let majorTickInterval, minorTickInterval, majorFormat, minorFormat;
        
        if (viewConfig) {
            // Use ViewportManager configuration
            majorTickInterval = d3.timeHour.every(viewConfig.majorTickHours);
            minorTickInterval = d3.timeHour.every(viewConfig.minorTickHours);
            
            // Smart formatting based on view mode and screen size
            if (viewModeForAxis === 'day') {
                majorFormat = containerWidth > 800 ? d3.timeFormat("%a %d %b %H:00") : d3.timeFormat("%d/%m %H:00");
                minorFormat = d3.timeFormat("%H:00");
            } else if (viewModeForAxis === '72h') {
                majorFormat = containerWidth > 800 ? d3.timeFormat("%a %d %b") : d3.timeFormat("%d/%m");
                minorFormat = d3.timeFormat("%H:00");
            } else if (viewModeForAxis === 'week') {
                majorFormat = containerWidth > 800 ? d3.timeFormat("%a %d %b") : d3.timeFormat("%d/%m");
                minorFormat = containerWidth > 600 ? d3.timeFormat("%H:00") : d3.timeFormat("%H");
            } else { // month
                majorFormat = containerWidth > 800 ? d3.timeFormat("%d %b") : d3.timeFormat("%d/%m");
                minorFormat = d3.timeFormat("%d");
            }
        } else {
            // Fallback to original logic
            if (viewModeForAxis === 'day') {
                majorTickInterval = d3.timeHour.every(6);
                minorTickInterval = d3.timeHour.every(1);
                majorFormat = containerWidth > 800 ? d3.timeFormat("%a %d %b %H:00") : d3.timeFormat("%d/%m %H:00");
                minorFormat = d3.timeFormat("%H:00");
            } else if (viewModeForAxis === '72h') {
                majorTickInterval = d3.timeHour.every(12);
                minorTickInterval = d3.timeHour.every(3);
                majorFormat = containerWidth > 800 ? d3.timeFormat("%a %d %b") : d3.timeFormat("%d/%m");
                minorFormat = d3.timeFormat("%H:00");
            } else if (viewModeForAxis === 'week') {
                majorTickInterval = d3.timeDay.every(1);
                minorTickInterval = d3.timeHour.every(6);
                majorFormat = containerWidth > 800 ? d3.timeFormat("%a %d %b") : d3.timeFormat("%d/%m");
                minorFormat = containerWidth > 600 ? d3.timeFormat("%H:00") : d3.timeFormat("%H");
            } else { // month
                majorTickInterval = d3.timeDay.every(7);
                minorTickInterval = d3.timeDay.every(1);
                majorFormat = containerWidth > 800 ? d3.timeFormat("Week %d %b") : d3.timeFormat("%d %b");
                minorFormat = d3.timeFormat("%d");
            }
        }

        const majorAxis = d3.axisTop(timeScale)
            .ticks(majorTickInterval)
            .tickFormat(majorFormat)
            .tickSize(8);
        
        const majorAxisGroup = svg.append('g')
            .attr('class', 'time-axis time-axis-major')
            .attr('transform', `translate(0, 35)`) // Fixed position above chart content
            .call(majorAxis);

        // Position major labels clearly above the chart
        majorAxisGroup.selectAll('text')
            .style('text-anchor', 'middle')
            .attr('dy', '-0.5em');

        const minorAxis = d3.axisTop(timeScale)
            .ticks(minorTickInterval)
            .tickFormat(minorFormat)
            .tickSize(6);
            
        const minorAxisGroup = svg.append('g')
            .attr('class', 'time-axis')
            .attr('transform', `translate(0, 60)`) // Fixed position for minor axis
            .call(minorAxis);

        // Position minor labels clearly above jetty bars
        minorAxisGroup.selectAll('text')
            .style('text-anchor', 'middle')
            .attr('dy', '-0.3em')
            .each(function(d, i) {
                // Hide every other minor tick label if space is tight
                if (availableWidth < 600 && i % 2 === 1) {
                    d3.select(this).style('display', 'none');
                } else if (availableWidth < 400 && i % 3 !== 0) {
                    d3.select(this).style('display', 'none');
                }
            });

        // Vertical grid lines for minor ticks (responsive density)
        let gridTickInterval;
        if (viewModeForAxis === 'month') {
            gridTickInterval = d3.timeDay.every(1);
        } else if (availableWidth < 400) {
            gridTickInterval = viewModeForAxis === 'day' ? d3.timeHour.every(4) : d3.timeHour.every(12);
        } else if (availableWidth < 600) {
            gridTickInterval = viewModeForAxis === 'day' ? d3.timeHour.every(2) : d3.timeHour.every(6);
        } else {
            gridTickInterval = viewModeForAxis === 'day' ? d3.timeHour.every(1) : d3.timeHour.every(3);
        }
        
        const grid = d3.axisTop(timeScale)
            .ticks(gridTickInterval)
            .tickSize(-(height - headerHeight))
            .tickFormat('');
        svg.append('g')
            .attr('class', 'time-grid')
            .attr('transform', `translate(0, ${headerHeight})`) // Grid starts from jetty area
            .call(grid);

        // Draw divider line between labels and chart
        svg.append('line')
            .attr('class', 'label-divider')
            .attr('x1', labelWidth)
            .attr('y1', 0)
            .attr('x2', labelWidth)
            .attr('y2', height)
            .attr('stroke', '#adb5bd')
            .attr('stroke-width', 1);

        // Draw horizontal divider between time header and jetty content
        svg.append('line')
            .attr('class', 'header-divider')
            .attr('x1', 0)
            .attr('y1', headerHeight)
            .attr('x2', width)
            .attr('y2', headerHeight)
            .attr('stroke', '#adb5bd')
            .attr('stroke-width', 2);
            
        // Add vertical line for current time if it's within the visible range
        const now = new Date();
        if (now >= startDate && now <= endDate) {
            const nowX = timeScale(now);
            
            // Draw the "now" line
            svg.append('line')
                .attr('class', 'now-line')
                .attr('x1', nowX)
                .attr('y1', headerHeight)
                .attr('x2', nowX)
                .attr('y2', height)
                .attr('stroke', '#dc3545') // Bootstrap danger red
                .attr('stroke-width', 2)
                .attr('stroke-dasharray', '5,5'); // Dashed line
                
            // Add "NOW" label with responsive sizing
            const nowLabelSize = containerWidth > 800 ? '12px' : (containerWidth > 600 ? '11px' : '10px');
            svg.append('text')
                .attr('class', 'now-label')
                .attr('x', nowX)
                .attr('y', 15) // Position at the very top of the chart
                .attr('text-anchor', 'middle')
                .attr('fill', '#dc3545')
                .attr('font-size', nowLabelSize)
                .attr('font-weight', 'bold')
                .attr('font-family', 'Roboto, -apple-system, BlinkMacSystemFont, sans-serif')
                .style('text-shadow', '1px 1px 2px rgba(255,255,255,0.8)')
                .text('NOW');
        }

        // Draw grid lines
        testData.jetties.forEach(jetty => {
            svg.append('line')
                .attr('class', 'grid-line')
                .attr('x1', labelWidth)
                .attr('y1', jettyScale(jetty.name) + jettyScale.bandwidth())
                .attr('x2', width)
                .attr('y2', jettyScale(jetty.name) + jettyScale.bandwidth())
                .attr('stroke', '#ddd')
                .attr('stroke-width', 1);
        });

        // In day view, auto-center the selected day within the scrollable container
        if (viewMode === 'day') {
            try {
                const focusDate = new Date(currentDateRange.startDate);
                const focusX = timeScale(focusDate);
                const desiredLeft = Math.max(0, focusX - labelWidth - (container.clientWidth / 2));
                container.scrollLeft = desiredLeft;
            } catch (e) {
                // no-op
            }
        }

        // Draw jetty labels (on top of bars to ensure they are always visible)
        testData.jetties.forEach(jetty => {
            svg.append('text')
                .attr('class', 'jetty-label')
                .attr('x', labelWidth / 2)
                .attr('y', jettyScale(jetty.name) + (jettyScale.bandwidth() / 2))
                .attr('text-anchor', 'middle')
                .attr('dominant-baseline', 'middle')
                .text(jetty.name);
        });

        // Filter assignments based on load bounds (wider range for infinite scroll)
        // but render them on the chart based on the current viewport
        let assignmentFilterStartDate = startDate;
        let assignmentFilterEndDate = endDate;
        
        if (loadBounds) {
            // Use extended bounds for assignment loading
            assignmentFilterStartDate = loadBounds.startTime;
            assignmentFilterEndDate = loadBounds.endTime;
        }
        
        const visibleAssignments = testData.assignments.filter(a => {
            const status = getNormalizedAssignmentStatus(a.status);
            return (a.startTime <= assignmentFilterEndDate && a.endTime >= assignmentFilterStartDate) && status !== 'CANCELLED';
        });
        
        console.log('Assignment filtering:', {
            totalAssignments: testData.assignments.length,
            visibleAssignments: visibleAssignments.length,
            filterRange: {
                start: assignmentFilterStartDate.toISOString(),
                end: assignmentFilterEndDate.toISOString()
            },
            chartRange: {
                start: startDate.toISOString(),
                end: endDate.toISOString()
            }
        });

        // Draw assignments
        visibleAssignments.forEach(assignment => {
            const jettyY = jettyScale(assignment.jettyName);
            
            // Skip if jetty position is invalid (NaN)
            if (isNaN(jettyY)) {
                console.warn(`Invalid jetty position for assignment ${assignment.id} with jetty ${assignment.jettyName}`);
                return;
            }
            
            const startX = Math.max(labelWidth, timeScale(assignment.startTime));
            const endX = Math.min(width, timeScale(assignment.endTime));
            const barWidth = endX - startX;

            if (barWidth <= 0) return; // Skip if not visible

            // Calculate pre/post-pump times in milliseconds
            const prePumpMs = (assignment.prePumpTime.hours * 3600000) + (assignment.prePumpTime.minutes * 60000);
            const postPumpMs = (assignment.postPumpTime.hours * 3600000) + (assignment.postPumpTime.minutes * 60000);

            // Calculate operation time points
            const operationStartTime = new Date(assignment.startTime.getTime() + prePumpMs);
            const operationEndTime = new Date(assignment.endTime.getTime() - postPumpMs);

            // Map to x-coordinates
            const operationStartX = Math.max(labelWidth, timeScale(operationStartTime));
            const operationEndX = Math.min(width, timeScale(operationEndTime));

            // Choose color based on (normalized) status
            const normalizedStatus = getNormalizedAssignmentStatus(assignment.status);
            let baseColor;
            switch(normalizedStatus) {
                case 'COMPLETED': baseColor = '#4caf50'; break; // Green
                case 'IN_PROGRESS': baseColor = '#2196f3'; break; // Blue
                case 'SCHEDULED': baseColor = '#ff9800'; break; // Orange
                default: baseColor = '#9e9e9e'; break; // Gray
            }

            // Create a group for this assignment
            const group = svg.append('g')
                .attr('class', 'assignment-group')
                .attr('data-id', assignment.id);

            // Navigate to edit screen on click (unless a drag occurred)
            group.on('click', function(event) {
                // If a drag is/was in progress, do not treat as a click
                const isDragging = d3.select(this).attr('data-dragging') === '1' || !!this.__dragMoved;
                if (isDragging) {
                    if (event) event.stopPropagation();
                    return;
                }
                const assignmentId = this.getAttribute('data-id');
                if (!assignmentId) return;
                try {
                    window.location.href = `/schedule/add?id=${encodeURIComponent(assignmentId)}`;
                } catch (_) {}
            });

            // 1. Draw pre-pump segment (if visible)
            if (operationStartX > startX) {
                group.append('rect')
                    .attr('class', 'prepump-segment')
                    .attr('x', startX)
                    .attr('y', jettyY)
                    .attr('width', operationStartX - startX)
                    .attr('height', jettyScale.bandwidth())
                    .attr('fill', d3.color(baseColor).brighter(0.8))
                    .attr('stroke', baseColor)
                    .attr('stroke-width', 1)
                    .attr('rx', 3);

                // Add pre-pump label if enough space
                if (operationStartX - startX > 50) {
                    group.append('text')
                        .attr('class', 'segment-label')
                        .attr('x', startX + (operationStartX - startX) / 2)
                        .attr('y', jettyY + jettyScale.bandwidth() / 2)
                        .text(`Pre: ${formatTimeDuration(assignment.prePumpTime)}`);
                }
            }

            // 2. Draw operation segment (if visible)
            if (operationEndX > operationStartX) {
                group.append('rect')
                    .attr('class', 'operation-segment')
                    .attr('x', operationStartX)
                    .attr('y', jettyY)
                    .attr('width', operationEndX - operationStartX)
                    .attr('height', jettyScale.bandwidth())
                    .attr('fill', baseColor)
                    .attr('stroke', d3.color(baseColor).darker(0.3))
                    .attr('stroke-width', 1);

                // Add vessel name if enough space
                if (operationEndX - operationStartX > 80) {
                    group.append('text')
                        .attr('class', 'segment-label')
                        .attr('x', operationStartX + (operationEndX - operationStartX) / 2)
                        .attr('y', jettyY + jettyScale.bandwidth() / 2)
                        .attr('fill', 'white')
                        .text(assignment.vesselName);
                }
            }

            // 3. Draw post-pump segment (if visible)
            if (endX > operationEndX) {
                group.append('rect')
                    .attr('class', 'postpump-segment')
                    .attr('x', operationEndX)
                    .attr('y', jettyY)
                    .attr('width', endX - operationEndX)
                    .attr('height', jettyScale.bandwidth())
                    .attr('fill', d3.color(baseColor).brighter(0.8))
                    .attr('stroke', baseColor)
                    .attr('stroke-width', 1)
                    .attr('rx', 3);

                // Add post-pump label if enough space
                if (endX - operationEndX > 50) {
                    group.append('text')
                        .attr('class', 'segment-label')
                        .attr('x', operationEndX + (endX - operationEndX) / 2)
                        .attr('y', jettyY + jettyScale.bandwidth() / 2)
                        .text(`Post: ${formatTimeDuration(assignment.postPumpTime)}`);
                }
            }

            // Add drag handles for interactivity
            const startHandle = group.append('rect')
                .attr('class', 'handle start-handle')
                .attr('x', startX - 4)
                .attr('y', jettyY)
                .attr('width', 8)
                .attr('height', jettyScale.bandwidth())
                .attr('fill', '#444')
                .attr('fill-opacity', 0.3)
                .attr('cursor', 'ew-resize');

            // Prevent handle clicks from bubbling to the group click (edit)
            startHandle.on('click', function(event) { if (event) event.stopPropagation(); });

            const endHandle = group.append('rect')
                .attr('class', 'handle end-handle')
                .attr('x', endX - 4)
                .attr('y', jettyY)
                .attr('width', 8)
                .attr('height', jettyScale.bandwidth())
                .attr('fill', '#444')
                .attr('fill-opacity', 0.3)
                .attr('cursor', 'ew-resize');

            // Prevent handle clicks from bubbling to the group click (edit)
            endHandle.on('click', function(event) { if (event) event.stopPropagation(); });

            // Hover effect for handles
            [startHandle, endHandle].forEach(handle => {
                handle.on('mouseover', function() {
                    d3.select(this).attr('fill-opacity', 0.6);
                }).on('mouseout', function() {
                    d3.select(this).attr('fill-opacity', 0.3);
                });
            });

            // Dragging behavior
            startHandle.call(d3.drag()
                .on('start', function(event) {
                    // Store initial position
                    d3.select(this).attr('fill-opacity', 0.8);
                    const assignment = testData.assignments.find(a => a.id === parseInt(this.parentNode.getAttribute('data-id')));
                    if (assignment) {
                        assignment.dragStartX = event.x;
                        assignment.dragStartTime = assignment.startTime;
                        // Compute cursor-to-edge offset so the handle sticks to the cursor
                        let localX = event.x;
                        try {
                            const svgEl = this.ownerSVGElement;
                            if (event.sourceEvent && svgEl && svgEl.getScreenCTM) {
                                const pt = svgEl.createSVGPoint();
                                pt.x = event.sourceEvent.clientX; pt.y = event.sourceEvent.clientY;
                                const ctm = svgEl.getScreenCTM().inverse();
                                const local = pt.matrixTransform(ctm);
                                localX = local.x;
                            }
                        } catch (e) {}
                        assignment.__dragOffsetStart = localX - timeScale(assignment.startTime);
                        showDragTooltip(formatDateTimeDDMMYYYY(assignment.startTime), event.sourceEvent?.clientX || 0, event.sourceEvent?.clientY || 0);
                    }
                })
                .on('drag', function(event) {
                    const assignment = testData.assignments.find(a => a.id === parseInt(this.parentNode.getAttribute('data-id')));
                    if (!assignment) return;
                    // Convert pointer to SVG-local X coordinate to avoid offset issues
                    let localX = event.x;
                    try {
                        const svgEl = this.ownerSVGElement;
                        if (event.sourceEvent && svgEl && svgEl.getScreenCTM) {
                            const pt = svgEl.createSVGPoint();
                            pt.x = event.sourceEvent.clientX; pt.y = event.sourceEvent.clientY;
                            const ctm = svgEl.getScreenCTM().inverse();
                            const local = pt.matrixTransform(ctm);
                            localX = local.x;
                        }
                    } catch (e) {}

                    // Use the stored offset to keep the edge under the cursor
                    const offset = typeof assignment.__dragOffsetStart === 'number' ? assignment.__dragOffsetStart : 0;
                    let newStartX = localX - offset;
                    // Clamp within chart and before current end
                    const minX = labelWidth;
                    const maxX = timeScale(assignment.endTime) - 1;
                    newStartX = Math.max(minX, Math.min(maxX, newStartX));
                    let newStartTime = timeScale.invert(newStartX);
                    // Apply snapping if enabled
                    newStartTime = snapDateToMinutes(newStartTime, currentSnapMinutes);

                    // Check for overlaps with other assignments on the same jetty
                    const hasOverlap = testData.assignments.some(a => {
                        if (a.id === assignment.id || a.jettyName !== assignment.jettyName) return false;
                        return (newStartTime < a.endTime && assignment.endTime > a.startTime);
                    });
                    if (hasOverlap) return;

                    // Update assignment time (local only during drag)
                    assignment.startTime = newStartTime;

                    // Update only this group's visuals for smooth UX (avoid full re-render mid-drag)
                    const g = d3.select(this.parentNode);
                    const startX = Math.max(labelWidth, timeScale(assignment.startTime));
                    const endX = Math.min(width, timeScale(assignment.endTime));
                    const prePumpMs = (assignment.prePumpTime.hours * 3600000) + (assignment.prePumpTime.minutes * 60000);
                    const postPumpMs = (assignment.postPumpTime.hours * 3600000) + (assignment.postPumpTime.minutes * 60000);
                    const operationStartX = Math.max(labelWidth, timeScale(new Date(assignment.startTime.getTime() + prePumpMs)));
                    const operationEndX = Math.min(width, timeScale(new Date(assignment.endTime.getTime() - postPumpMs)));

                    g.select('rect.prepump-segment')
                        .attr('x', startX)
                        .attr('width', Math.max(0, operationStartX - startX));
                    g.select('rect.operation-segment')
                        .attr('x', operationStartX)
                        .attr('width', Math.max(0, operationEndX - operationStartX));
                    g.select('rect.postpump-segment')
                        .attr('x', operationEndX)
                        .attr('width', Math.max(0, endX - operationEndX));
                    g.select('rect.start-handle').attr('x', startX - 4);
                    g.select('rect.end-handle').attr('x', endX - 4);

                    // Adjust labels positions/visibility
                    const preWidth = Math.max(0, operationStartX - startX);
                    const opWidth = Math.max(0, operationEndX - operationStartX);
                    const postWidth = Math.max(0, endX - operationEndX);
                    g.selectAll('text.segment-label').each(function() {
                        const el = d3.select(this);
                        const txt = (this.textContent || '');
                        if (txt.startsWith('Pre:')) {
                            el.attr('x', startX + preWidth / 2)
                              .style('display', preWidth > 50 ? null : 'none');
                        } else if (txt.startsWith('Post:')) {
                            el.attr('x', operationEndX + postWidth / 2)
                              .style('display', postWidth > 50 ? null : 'none');
                        } else {
                            // Assume vessel name label
                            el.attr('x', operationStartX + opWidth / 2)
                              .style('display', opWidth > 80 ? null : 'none');
                        }
                    });

                    // Update tooltip
                    showDragTooltip(`Start: ${formatDateTimeDDMMYYYY(assignment.startTime)}`, event.sourceEvent?.clientX || 0, event.sourceEvent?.clientY || 0);
                })
                .on('end', async function(event) {
                    d3.select(this).attr('fill-opacity', 0.3);
                    hideDragTooltip();
                    try {
                        const assignment = testData.assignments.find(a => a.id === parseInt(this.parentNode.getAttribute('data-id')));
                        if (!assignment) return;
                        
                        // Show reason selection modal
                        // Try reason reuse
                        const reuse = getRecentAssignmentReason(assignment.id);
                        const proceedWithUpdate = async (reasonToUse, sessionId) => {
                            try {
                                // Persist to server
                                await fetch(`/api/schedule/assignments/${encodeURIComponent(assignment.id)}`, {
                                    method: 'PATCH',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        start_time: assignment.startTime.toISOString(),
                                        end_time: assignment.endTime.toISOString(),
                                        reason: reasonToUse,
                                        change_session_id: sessionId
                                    })
                                });
                                // Reload to reflect authoritative data and update changelog
                                await loadRealData();
                                renderGanttChart();
                                renderAssignmentTable();
                                renderChangeLog();
                            } catch (error) {
                                console.error('Error updating assignment:', error);
                                // Reload from server to revert local change
                                await loadRealData();
                            }
                        };

                        if (reuse && reuse.reason) {
                            await proceedWithUpdate(reuse.reason, reuse.sessionId);
                        } else {
                            showReasonModal('start_time', async function(reason) {
                                const sessionId = generateSessionId();
                                setRecentAssignmentReason(assignment.id, reason, sessionId);
                                await proceedWithUpdate(reason, sessionId);
                            });
                        }
                        
                        renderChangeLog();
                    } catch (e) {
                        console.error('Failed to persist change', e);
                        showErrorToast('Failed to save change: ' + (e.message || e));
                        await loadRealData();
                    }
                }));

            endHandle.call(d3.drag()
                .on('start', function(event) {
                    // Store initial position
                    d3.select(this).attr('fill-opacity', 0.8);
                    const assignment = testData.assignments.find(a => a.id === parseInt(this.parentNode.getAttribute('data-id')));
                    if (assignment) {
                        assignment.dragStartX = event.x;
                        assignment.dragStartTime = assignment.endTime;
                        // Compute cursor-to-edge offset so the handle sticks to the cursor
                        let localX = event.x;
                        try {
                            const svgEl = this.ownerSVGElement;
                            if (event.sourceEvent && svgEl && svgEl.getScreenCTM) {
                                const pt = svgEl.createSVGPoint();
                                pt.x = event.sourceEvent.clientX; pt.y = event.sourceEvent.clientY;
                                const ctm = svgEl.getScreenCTM().inverse();
                                const local = pt.matrixTransform(ctm);
                                localX = local.x;
                            }
                        } catch (e) {}
                        assignment.__dragOffsetEnd = localX - timeScale(assignment.endTime);
                        showDragTooltip(formatDateTimeDDMMYYYY(assignment.endTime), event.sourceEvent?.clientX || 0, event.sourceEvent?.clientY || 0);
                    }
                })
                .on('drag', function(event) {
                    const assignment = testData.assignments.find(a => a.id === parseInt(this.parentNode.getAttribute('data-id')));
                    if (!assignment) return;
                    // Convert pointer to SVG-local X coordinate to avoid offset issues
                    let localX = event.x;
                    try {
                        const svgEl = this.ownerSVGElement;
                        if (event.sourceEvent && svgEl && svgEl.getScreenCTM) {
                            const pt = svgEl.createSVGPoint();
                            pt.x = event.sourceEvent.clientX; pt.y = event.sourceEvent.clientY;
                            const ctm = svgEl.getScreenCTM().inverse();
                            const local = pt.matrixTransform(ctm);
                            localX = local.x;
                        }
                    } catch (e) {}

                    // Use the stored offset to keep the edge under the cursor
                    const offset = typeof assignment.__dragOffsetEnd === 'number' ? assignment.__dragOffsetEnd : 0;
                    let newEndX = localX - offset;
                    // Clamp within chart and after current start
                    const minX = timeScale(assignment.startTime) + 1;
                    const maxX = width;
                    newEndX = Math.max(minX, Math.min(maxX, newEndX));
                    let newEndTime = timeScale.invert(newEndX);
                    // Apply snapping if enabled
                    newEndTime = snapDateToMinutes(newEndTime, currentSnapMinutes);

                    // Check for overlaps with other assignments on the same jetty
                    const hasOverlap = testData.assignments.some(a => {
                        if (a.id === assignment.id || a.jettyName !== assignment.jettyName) return false;
                        return (assignment.startTime < a.endTime && newEndTime > a.startTime);
                    });
                    if (hasOverlap) return;

                    // Update assignment time (local only during drag)
                    assignment.endTime = newEndTime;

                    // Update only this group's visuals for smooth UX (avoid full re-render mid-drag)
                    const g = d3.select(this.parentNode);
                    const startX = Math.max(labelWidth, timeScale(assignment.startTime));
                    const endX = Math.min(width, timeScale(assignment.endTime));
                    const prePumpMs = (assignment.prePumpTime.hours * 3600000) + (assignment.prePumpTime.minutes * 60000);
                    const postPumpMs = (assignment.postPumpTime.hours * 3600000) + (assignment.postPumpTime.minutes * 60000);
                    const operationStartX = Math.max(labelWidth, timeScale(new Date(assignment.startTime.getTime() + prePumpMs)));
                    const operationEndX = Math.min(width, timeScale(new Date(assignment.endTime.getTime() - postPumpMs)));

                    g.select('rect.prepump-segment')
                        .attr('x', startX)
                        .attr('width', Math.max(0, operationStartX - startX));
                    g.select('rect.operation-segment')
                        .attr('x', operationStartX)
                        .attr('width', Math.max(0, operationEndX - operationStartX));
                    g.select('rect.postpump-segment')
                        .attr('x', operationEndX)
                        .attr('width', Math.max(0, endX - operationEndX));
                    g.select('rect.start-handle').attr('x', startX - 4);
                    g.select('rect.end-handle').attr('x', endX - 4);

                    // Adjust labels positions/visibility
                    const preWidth = Math.max(0, operationStartX - startX);
                    const opWidth = Math.max(0, operationEndX - operationStartX);
                    const postWidth = Math.max(0, endX - operationEndX);
                    g.selectAll('text.segment-label').each(function() {
                        const el = d3.select(this);
                        const txt = (this.textContent || '');
                        if (txt.startsWith('Pre:')) {
                            el.attr('x', startX + preWidth / 2)
                              .style('display', preWidth > 50 ? null : 'none');
                        } else if (txt.startsWith('Post:')) {
                            el.attr('x', operationEndX + postWidth / 2)
                              .style('display', postWidth > 50 ? null : 'none');
                        } else {
                            // Assume vessel name label
                            el.attr('x', operationStartX + opWidth / 2)
                              .style('display', opWidth > 80 ? null : 'none');
                        }
                    });

                    // Update tooltip
                    showDragTooltip(`End: ${formatDateTimeDDMMYYYY(assignment.endTime)}`, event.sourceEvent?.clientX || 0, event.sourceEvent?.clientY || 0);
                })
                .on('end', async function(event) {
                    d3.select(this).attr('fill-opacity', 0.3);
                    hideDragTooltip();
                    try {
                        const assignment = testData.assignments.find(a => a.id === parseInt(this.parentNode.getAttribute('data-id')));
                        if (!assignment) return;
                        
                        // Show reason selection modal
                        // Try reason reuse
                        const reuse = getRecentAssignmentReason(assignment.id);
                        const proceedWithUpdate = async (reasonToUse, sessionId) => {
                            try {
                                await fetch(`/api/schedule/assignments/${encodeURIComponent(assignment.id)}`, {
                                    method: 'PATCH',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        start_time: assignment.startTime.toISOString(),
                                        end_time: assignment.endTime.toISOString(),
                                        reason: reasonToUse,
                                        change_session_id: sessionId
                                    })
                                });
                                await loadRealData();
                                renderGanttChart();
                                renderAssignmentTable();
                                renderChangeLog();
                            } catch (error) {
                                console.error('Error updating assignment:', error);
                                await loadRealData();
                            }
                        };
                        if (reuse && reuse.reason) {
                            await proceedWithUpdate(reuse.reason, reuse.sessionId);
                        } else {
                            showReasonModal('end_time', async function(reason) {
                                const sessionId = generateSessionId();
                                setRecentAssignmentReason(assignment.id, reason, sessionId);
                                await proceedWithUpdate(reason, sessionId);
                            });
                        }
                    } catch (e) {
                        console.error('Failed to persist change', e);
                        showErrorToast('Failed to save change: ' + (e.message || e));
                        await loadRealData();
                    }
                }));

            // Enable dragging the whole bar to another jetty row to move assignment between jetties
            group.call(d3.drag()
                .on('start', function(event) {
                    d3.select(this).attr('opacity', 0.7).attr('data-dragging', '1');
                    this.__dragMoved = false;
                })
                .on('drag', function(event) {
                    if (event && (Math.abs(event.dx || 0) > 0 || Math.abs(event.dy || 0) > 0)) {
                        this.__dragMoved = true;
                    }
                    // Move vertically with cursor for visual feedback
                    const dy = event.dy || 0;
                    const currentTransform = d3.select(this).attr('transform');
                    const currentYOffset = currentTransform && currentTransform.startsWith('translate(')
                        ? parseFloat(currentTransform.replace(/.*,(.*)\)/, '$1')) || 0
                        : 0;
                    d3.select(this).attr('transform', `translate(0, ${currentYOffset + dy})`);
                })
                .on('end', async function(event) {
                    d3.select(this).attr('opacity', 1.0).attr('transform', null).attr('data-dragging', '0');
                    
                    // Only process if a significant drag occurred
                    if (!this.__dragMoved) {
                        return;
                    }
                    
                    try {
                        const assignmentId = this.getAttribute('data-id');
                        if (!assignmentId) {
                            console.error('No assignment ID found');
                            return;
                        }
                        
                        // Get mouse position relative to SVG
                        let y;
                        try {
                            if (!event.sourceEvent) {
                                console.error('No sourceEvent available for coordinate calculation');
                                return;
                            }
                            
                            const pt = this.ownerSVGElement.createSVGPoint();
                            pt.x = event.sourceEvent.clientX; 
                            pt.y = event.sourceEvent.clientY;
                            const ctm = this.ownerSVGElement.getScreenCTM();
                            if (!ctm) {
                                console.error('Cannot get screen CTM');
                                return;
                            }
                            const local = pt.matrixTransform(ctm.inverse());
                            y = local.y;
                        } catch (coordError) {
                            console.error('Error calculating coordinates:', coordError);
                            return;
                        }
                        
                        // Determine target jetty by nearest band center
                        let closestJetty = null;
                        let minDist = Infinity;
                        testData.jetties.forEach(j => {
                            const center = jettyScale(j.name) + jettyScale.bandwidth() / 2;
                            const dist = Math.abs(y - center);
                            if (dist < minDist) { 
                                minDist = dist; 
                                closestJetty = j; 
                            }
                        });
                        
                        if (!closestJetty) {
                            console.error('No closest jetty found');
                            return;
                        }
                        
                        const original = testData.assignments.find(a => String(a.id) === String(assignmentId));
                        if (!original) {
                            console.error('Original assignment not found:', assignmentId);
                            return;
                        }
                        
                        if (closestJetty.name === original.jettyName) {
                            console.log('No jetty change detected');
                            return; // no change
                        }
                        
                        console.log(`Moving assignment ${assignmentId} from ${original.jettyName} to ${closestJetty.name}`);
                        
                        // Update modal title BEFORE showing the modal
                        const modalTitle = document.getElementById('reasonModalTitle');
                        if (modalTitle) {
                            modalTitle.textContent = `Move to ${closestJetty.name} - Select Reason`;
                        }
                        
                        // Jetty change: reason required once, reuse for follow-up edits in short window
                        const proceedWithUpdate = async (reasonToUse, sessionId) => {
                            try {
                                const response = await fetch(`/api/schedule/assignments/${encodeURIComponent(assignmentId)}`, {
                                    method: 'PATCH',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                        start_time: (original.startTime && original.startTime.toISOString()) || null,
                                        end_time: (original.endTime && original.endTime.toISOString()) || null,
                                        jetty_name: closestJetty.name,
                                        reason: reasonToUse,
                                        change_session_id: sessionId
                                    })
                                });
                                if (!response.ok) {
                                    const errorText = await response.text();
                                    throw new Error(`API call failed: ${response.status} ${response.statusText} - ${errorText}`);
                                }
                                await loadRealData();
                                renderGanttChart();
                                renderAssignmentTable();
                                renderChangeLog();
                            } catch (error) {
                                console.error('Error updating assignment:', error);
                                showErrorToast(`Failed to move assignment: ${error.message || error}`);
                                await loadRealData();
                            }
                        };

                        const reuse = getRecentAssignmentReason(assignmentId);
                        if (reuse && reuse.reason) {
                            await proceedWithUpdate(reuse.reason, reuse.sessionId);
                        } else {
                            showReasonModal('jetty', async function(reason) {
                                const sessionId = generateSessionId();
                                setRecentAssignmentReason(assignmentId, reason, sessionId);
                                await proceedWithUpdate(reason, sessionId);
                            });
                        }
                        
                    } catch (e) {
                        console.error('Failed to process assignment move:', e);
                        showErrorToast('Failed to move assignment: ' + (e.message || e));
                        await loadRealData();
                    } finally {
                        // Reset drag state
                        this.__dragMoved = false;
                    }
                })
            );
        });
    }

    // No mock vessel generation in production
    
    // Generate realistic mock assignments across available jetties
    async function generateMockAssignments() {
        try {
            // Ensure jetties are loaded
            if (!testData.jetties || testData.jetties.length === 0) {
                const res = await fetch('/api/jetties');
                const jettiesData = await res.json();
                testData.jetties = jettiesData.map(j => ({ id: j.id, name: j.name, type: ((j.jetty_type || j.type || '') + '').toLowerCase() }));
            }

            const now = new Date();
            let seq = Math.floor(Date.now() / 1000);
            const rnd = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
            const hoursFrom = (base, h) => new Date(base.getTime() + h * 3600000);
            const minutesFrom = (base, m) => new Date(base.getTime() + m * 60000);

            // Realistic vessel names (actual ship naming conventions)
            const vesselNames = [
                'MT Nordic Spirit', 'MV Ocean Trader', 'MT Petro Explorer', 'MV Gulf Navigator',
                'MT Arctic Dawn', 'MV Pacific Voyager', 'MT Chemical Carrier', 'MV Bulk Master',
                'MT Gas Transport', 'MV Liquid Gold', 'MT Fuel Express', 'MV Tank Pioneer',
                'MT Marine Eagle', 'MV Coastal Trader', 'MT Energy Ship', 'MV Port Connector',
                'MT Terminal Link', 'MV Cargo Flow', 'MT Jetty Service', 'MV Harbor Master'
            ];

            // Realistic petroleum products and their typical volumes
            const products = [
                { name: 'Diesel', volumeRange: [15000, 35000], type: 'diesel' },
                { name: 'Gasoline', volumeRange: [12000, 28000], type: 'gasoline' },
                { name: 'Jet Fuel', volumeRange: [10000, 25000], type: 'jetfuel' },
                { name: 'Fuel Oil', volumeRange: [20000, 45000], type: 'fueloil' },
                { name: 'Crude Oil', volumeRange: [30000, 80000], type: 'crude' },
                { name: 'LPG', volumeRange: [5000, 15000], type: 'lpg' },
                { name: 'LNG', volumeRange: [3000, 8000], type: 'lng' },
                { name: 'Chemicals', volumeRange: [8000, 20000], type: 'chemical' }
            ];

            const jetties = testData.jetties.map(j => j.name);
            const mocks = [];

            // Helper function to create realistic assignments
            const createRealisticAssignment = (jettyName, baseTime, isHistorical = false) => {
                const vesselName = vesselNames[rnd(0, vesselNames.length - 1)];
                const product = products[rnd(0, products.length - 1)];
                const volume = rnd(product.volumeRange[0], product.volumeRange[1]);
                const isLoading = Math.random() > 0.6; // 40% loading, 60% discharging

                // Realistic operation duration based on volume and product type
                let baseDuration = Math.ceil(volume / 1000); // Roughly 1000 m³ per hour
                if (product.type === 'chemical') baseDuration *= 1.5; // Chemicals take longer
                if (product.type === 'crude') baseDuration *= 1.2; // Crude takes longer
                if (isLoading) baseDuration *= 0.8; // Loading is typically faster

                // Add some randomness (±20%)
                const durationVariation = baseDuration * 0.2;
                const duration = baseDuration + rnd(-durationVariation, durationVariation);

                // Realistic buffer times
                const prePumpHours = product.type === 'chemical' ? rnd(2, 4) : rnd(1, 2);
                const postPumpHours = product.type === 'chemical' ? rnd(2, 4) : rnd(1, 2);

                // Create assignment with realistic timing
                const startTime = minutesFrom(baseTime, rnd(-30, 30)); // ±30 min variation
                const endTime = hoursFrom(startTime, duration);

                // Only create COMPLETED and IN_PROGRESS assignments
                let status;
                if (isHistorical) {
                    status = 'COMPLETED';
                } else {
                    // For non-historical assignments, determine if they're currently in progress
                    if (startTime <= now && endTime >= now) {
                        status = 'IN_PROGRESS';
                    } else {
                        status = 'COMPLETED'; // Past assignments are completed
                    }
                }

                return {
                    id: ++seq,
                    jettyName,
                    vesselName,
                    vesselId: rnd(10000, 99999), // More realistic vessel IDs
                    product: product.name,
                    volume: volume,
                    startTime,
                    endTime,
                    status,
                    isLoading,
                    prePumpTime: { hours: prePumpHours, minutes: 0 },
                    postPumpTime: { hours: postPumpHours, minutes: 0 }
                };
            };

            // Generate only COMPLETED and IN_PROGRESS assignments for context
            // These serve as historical context and current operations

            // Generate historical assignments (last 3 days, all COMPLETED)
            const historicalStart = hoursFrom(now, -72); // 3 days ago
            for (let i = 0; i < 12; i++) { // 12 historical assignments
                const randomTime = hoursFrom(historicalStart, rnd(0, 72));
                const jettyName = jetties[rnd(0, jetties.length - 1)];
                const assignment = createRealisticAssignment(jettyName, randomTime, true);
                // Force COMPLETED status for historical data
                assignment.status = 'COMPLETED';
                mocks.push(assignment);
            }

            // Generate current IN_PROGRESS assignments (spanning current time)
            for (let i = 0; i < 6; i++) { // 6 current assignments
                // Create assignments that started recently and are still in progress
                const startOffset = rnd(-8, -1); // Started 1-8 hours ago
                const randomTime = hoursFrom(now, startOffset);
                const jettyName = jetties[rnd(0, jetties.length - 1)];
                const assignment = createRealisticAssignment(jettyName, randomTime, false);
                // Force IN_PROGRESS status for current operations
                assignment.status = 'IN_PROGRESS';
                // Adjust end time to be in the future
                assignment.endTime = hoursFrom(assignment.startTime, rnd(4, 12));
                mocks.push(assignment);
            }

            // Sort by start time for realistic ordering
            mocks.sort((a, b) => a.startTime - b.startTime);

            // Remove any assignments that would create unrealistic overlaps on the same jetty
            const validAssignments = [];
            const jettySchedules = {};

            jetties.forEach(jetty => {
                jettySchedules[jetty] = [];
            });

            mocks.forEach(assignment => {
                const jettySchedule = jettySchedules[assignment.jettyName];
                const assignmentStart = assignment.startTime.getTime();
                const assignmentEnd = assignment.endTime.getTime();

                // Check for overlaps with buffer time
                const bufferMs = 2 * 60 * 60 * 1000; // 2 hour buffer
                const hasOverlap = jettySchedule.some(existing => {
                    const existingStart = existing.startTime.getTime();
                    const existingEnd = existing.endTime.getTime();
                    return (assignmentStart < existingEnd + bufferMs && assignmentEnd + bufferMs > existingStart);
                });

                if (!hasOverlap) {
                    jettySchedule.push(assignment);
                    validAssignments.push(assignment);
                }
            });

            // Preserve mock assignments separately so they can remain visible after optimization
            testData.mockAssignments = validAssignments;
            testData.assignments = validAssignments;
            console.log(`Generated ${validAssignments.length} realistic mock assignments`);

            // In demo mode, we no longer auto-create unscheduled vessels.
            // Unscheduled vessels will come from nominations via API.

        } catch (e) {
            console.error('Failed to generate mock assignments', e);
        }
    }

    // Generate unscheduled vessels for optimization testing
    async function generateUnscheduledVessels(existingAssignments) {
        const now = new Date();
        const vesselNames = [
            'MT Nordic Spirit', 'MV Ocean Trader', 'MT Petro Explorer', 'MV Gulf Navigator',
            'MT Arctic Dawn', 'MV Pacific Voyager', 'MT Chemical Carrier', 'MV Bulk Master',
            'MT Gas Transport', 'MV Liquid Gold', 'MT Fuel Express', 'MV Tank Pioneer',
            'MT Marine Eagle', 'MV Coastal Trader', 'MT Energy Ship', 'MV Port Connector'
        ];

        const products = [
            { name: 'Diesel', volumeRange: [15000, 35000] },
            { name: 'Gasoline', volumeRange: [12000, 28000] },
            { name: 'Jet Fuel', volumeRange: [10000, 25000] },
            { name: 'Fuel Oil', volumeRange: [20000, 45000] },
            { name: 'Crude Oil', volumeRange: [30000, 80000] }
        ];

        // No-op: demo will use real nominations as unscheduled vessels
        testData.unscheduled = testData.unscheduled || [];
        console.log('Skipping mock unscheduled vessel generation (demo uses nominations)');
    }

    // Normalize front-end product names to terminal-compatible product keys used by jetties
    function normalizeProductForTerminal(product) {
        const p = (product || '').toString().toLowerCase();
        if (p.includes('benzene')) return 'benzene';
        if (p.includes('acrylonitrile')) return 'acrylonitrile';
        if (p.includes('propylene')) return 'propylene_oxide';
        if (p.includes('butane')) return 'butane';
        // Map common oil products to hydrocarbons for Jetty 1/2 compatibility
        if (p.includes('diesel') || p.includes('gasoline') || p.includes('jet') || p.includes('fuel') || p.includes('crude')) {
            return 'hydrocarbons';
        }
        // Fallback
        return 'minerals';
    }

    // Removed: syncing mock vessels to backend. Demo will use user-created nominations only.

    // Render the unscheduled vessels table
    function renderUnscheduledVesselsTable() {
        const tableBody = document.getElementById('unscheduled-vessels-table');

        if (testData.unscheduled.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="10" class="text-center">No unscheduled vessels</td></tr>';
            return;
        }

        tableBody.innerHTML = testData.unscheduled.map(vessel => {
            // Format date/time for display - DD/MM/YYYY HH:MM format
            const formatDate = (date) => {
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                const hours = date.getHours().toString().padStart(2, '0');
                const minutes = date.getMinutes().toString().padStart(2, '0');

                return `${day}/${month}/${year} ${hours}:${minutes}`;
            };

            // Priority label
            const priorityLabel = vessel.priority === 1 ? 'High' : (vessel.priority === 2 ? 'Medium' : 'Low');

            // Reason for not being assigned (from optimization logs or default)
            const reason = vessel.unassignment_reason || 'Not optimal to assign (enable Force Assign All to override)';

            return `
                <tr data-id="${vessel.id}">
                    <td>${vessel.id}</td>
                    <td>${vessel.name}</td>
                    <td>${vessel.type}</td>
                    <td>${formatDate(vessel.eta)}</td>
                    <td>${vessel.length}</td>
                    <td>${vessel.draft}</td>
                    <td>${vessel.cargo} m³ ${vessel.isLoading ? '(Loading)' : '(Unloading)'}</td>
                    <td>${priorityLabel}</td>
                    <td><span class="text-muted" title="${reason}">${reason}</span></td>
                    <td>
                        <button class="btn btn-sm btn-danger delete-unscheduled-btn" data-id="${vessel.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        }).join('');
        
        // Add event listeners for delete buttons
        document.querySelectorAll('.delete-unscheduled-btn').forEach(button => {
            button.addEventListener('click', async function() {
                const vesselId = this.getAttribute('data-id');
                if (!confirm('Are you sure you want to remove this vessel?')) {
                    return;
                }

                try {
                    const res = await fetch(`/api/vessels/${encodeURIComponent(vesselId)}`, { method: 'DELETE' });
                    if (!res.ok) {
                        const err = await res.json().catch(() => ({}));
                        throw new Error(err.detail || 'Failed to delete vessel');
                    }

                    // Remove locally regardless of type mismatches (number vs string IDs)
                    testData.unscheduled = testData.unscheduled.filter(v => String(v.id) !== String(vesselId));
                    renderUnscheduledVesselsTable();
                } catch (e) {
                    console.error('Delete vessel failed', e);
                    showErrorToast('Error deleting vessel: ' + (e.message || e));
                }
            });
        });
    }
    
    // No mock optimization in production
    
    // Set up the Add Test Vessel button
    // Removed test vessel generator

    async function pollOptimizationStatus() {
        try {
            // Poll the real API status until completion
            const response = await fetch('/api/optimize/status');
            if (!response.ok) {
                throw new Error('Failed to get optimization status');
            }

            const status = await response.json();

            if (status.in_progress) {
                // Throttled progress log
                optimizationPollCount += 1;
                if (optimizationPollCount % 3 === 1) {
                    addOptimizationLog('Optimization in progress...');
                }
                setTimeout(pollOptimizationStatus, 1500);
            } else {
                // Completed: refresh data and UI (force assignments fetch in mock mode)
                await loadRealData(true);
                try {
                    const result = status.result || {};
                    if (result.message) {
                        const assigned = typeof result.assigned_count === 'number' ? result.assigned_count : 0;
                        const baseMsg = `${result.message}${assigned ? ` (Assigned: ${assigned})` : ''}`;
                        if (assigned === 0) {
                            if (Array.isArray(result.unassigned) && result.unassigned.length > 0) {
                                const details = result.unassigned.slice(0, 5).map(u => `- ${u.vessel_name || u.vessel_id}: ${u.reason}`).join('\n');
                                addOptimizationLog(baseMsg);
                                addOptimizationLog('Unassigned vessels:');
                                details.split('\n').forEach(line => addOptimizationLog(line));
                                showWarningToast(baseMsg + ' (See optimization log for unassigned vessel details)', 8000);
                            } else {
                                addOptimizationLog(baseMsg);
                                showInfoToast(baseMsg, 6000);
                            }
                        } else {
                            addOptimizationLog(baseMsg);
                            console.log(baseMsg);
                        }
                    }
                } catch (e) {
                    console.warn('Could not display optimization result details:', e);
                    addOptimizationLog('Completed, but failed to display result details');
                }
                // Summarize assignment changes
                try {
                    const before = Array.isArray(window._assignmentsBeforeOptimize) ? window._assignmentsBeforeOptimize : [];
                    const beforeKey = new Map(before.map(a => [String(a.vesselId), a]));
                    const after = Array.isArray(testData.assignments) ? testData.assignments : [];
                    const events = [];
                    const fmt = (d) => (d ? formatDateTimeDDMMYYYY(d) : '');
                    after.forEach(a => {
                        const key = String(a.vesselId || '');
                        const prev = beforeKey.get(key);
                        if (!prev) {
                            events.push(`Assigned ${a.vesselName || key} to ${a.jettyName} (${fmt(a.startTime)} → ${fmt(a.endTime)})`);
                        } else if (prev.jettyName !== a.jettyName) {
                            events.push(`Moved ${a.vesselName || key} to ${a.jettyName} (${fmt(a.startTime)} → ${fmt(a.endTime)})`);
                        }
                    });
                    if (events.length) {
                        addOptimizationLog('Assignments updated:');
                        events.slice(0, 10).forEach(m => addOptimizationLog(' - ' + m));
                        if (events.length > 10) addOptimizationLog(` - ... and ${events.length - 10} more`);
                    }
                } catch (e) {
                    console.warn('Assignment diff failed', e);
                }

                const button = document.getElementById('optimize-button');
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-cogs"></i> Optimize';
                renderGanttChart();
                renderAssignmentTable();
            }
            
        } catch (error) {
            console.error('Error checking optimization status:', error);
            showErrorToast('Error checking optimization status: ' + error.message);
            addOptimizationLog('Error checking status: ' + (error.message || error));

            // Re-enable button
            const button = document.getElementById('optimize-button');
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-cogs"></i> Optimize';
        }
    }

    async function loadChangeLog() {
        try {
            const res = await fetch('/api/schedule/changes?limit=50');
            if (!res.ok) throw new Error('Failed to load change log');
            return await res.json();
        } catch (e) {
            console.error('loadChangeLog error', e);
            return [];
        }
    }

    async function renderChangeLog() {
        const tbody = document.getElementById('change-log-table');
        if (!tbody) return;
        const changes = await loadChangeLog();
        if (!changes || changes.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center">No changes yet</td></tr>';
            return;
        }
        const formatDT = (s) => {
            try { const d = new Date(s); return d.toLocaleString(); } catch { return s; }
        };
        const fmt = (s, e) => {
            try { return `${new Date(s).toLocaleString()} → ${new Date(e).toLocaleString()}`; } catch { return `${s} → ${e}`; }
        };
        tbody.innerHTML = changes.map(c => `
            <tr>
                <td>${formatDT(c.changed_at)}</td>
                <td>#${c.assignment_id}</td>
                <td>${c.vessel_name || ''}</td>
                <td>${c.jetty_name || ''}</td>
                <td>${fmt(c.old_start_time, c.old_end_time)}</td>
                <td>${fmt(c.new_start_time, c.new_end_time)}</td>
                <td>${(c.reason || '').replace(/</g,'&lt;')}</td>
            </tr>
        `).join('');
    }
    </script>
    <!-- Modern Reason Selection Modal -->
    <div id="reasonModal" class="reason-modal-overlay" style="display: none;">
        <div class="reason-modal">
            <div class="reason-modal-header">
                <h3 id="reasonModalTitle">Select Change Reason</h3>
                <button class="reason-modal-close" type="button" aria-label="Close">&times;</button>
            </div>
            
            <div class="reason-modal-body">
                <div class="reason-search-container">
                    <i class="fas fa-search reason-search-icon"></i>
                    <input type="text" id="reasonSearch" class="reason-search" placeholder="Search reasons..." />
                </div>
                
                <div class="reason-categories">
                    <!-- Operational Reasons -->
                    <div class="reason-category">
                        <div class="reason-category-header" data-category="operational">
                            <i class="fas fa-cog reason-category-icon"></i>
                            <span>Operational</span>
                            <i class="fas fa-chevron-down reason-category-toggle"></i>
                        </div>
                        <div class="reason-category-content" id="operational-content">
                            <div class="reason-option" data-reason="Weather conditions delay" data-category="operational">
                                <i class="fas fa-cloud-rain"></i> Weather conditions delay
                            </div>
                            <div class="reason-option" data-reason="Port congestion" data-category="operational">
                                <i class="fas fa-anchor"></i> Port congestion
                            </div>
                            <div class="reason-option" data-reason="Berthing availability" data-category="operational">
                                <i class="fas fa-ship"></i> Berthing availability
                            </div>
                            <div class="reason-option" data-reason="Tide/draft restrictions" data-category="operational">
                                <i class="fas fa-water"></i> Tide/draft restrictions
                            </div>
                            <div class="reason-option" data-reason="Pilot availability" data-category="operational">
                                <i class="fas fa-user-tie"></i> Pilot availability
                            </div>
                        </div>
                    </div>

                    <!-- Vessel-Related -->
                    <div class="reason-category">
                        <div class="reason-category-header" data-category="vessel">
                            <i class="fas fa-ship reason-category-icon"></i>
                            <span>Vessel-Related</span>
                            <i class="fas fa-chevron-down reason-category-toggle"></i>
                        </div>
                        <div class="reason-category-content" id="vessel-content">
                            <div class="reason-option" data-reason="Vessel breakdown/repair" data-category="vessel">
                                <i class="fas fa-wrench"></i> Vessel breakdown/repair
                            </div>
                            <div class="reason-option" data-reason="Crew change requirement" data-category="vessel">
                                <i class="fas fa-users"></i> Crew change requirement
                            </div>
                            <div class="reason-option" data-reason="Cargo preparation delay" data-category="vessel">
                                <i class="fas fa-boxes"></i> Cargo preparation delay
                            </div>
                            <div class="reason-option" data-reason="Documentation issues" data-category="vessel">
                                <i class="fas fa-file-alt"></i> Documentation issues
                            </div>
                            <div class="reason-option" data-reason="ETA change from vessel" data-category="vessel">
                                <i class="fas fa-clock"></i> ETA change from vessel
                            </div>
                        </div>
                    </div>

                    <!-- Commercial -->
                    <div class="reason-category">
                        <div class="reason-category-header" data-category="commercial">
                            <i class="fas fa-handshake reason-category-icon"></i>
                            <span>Commercial</span>
                            <i class="fas fa-chevron-down reason-category-toggle"></i>
                        </div>
                        <div class="reason-category-content" id="commercial-content">
                            <div class="reason-option" data-reason="Customer request" data-category="commercial">
                                <i class="fas fa-user"></i> Customer request
                            </div>
                            <div class="reason-option" data-reason="Priority change" data-category="commercial">
                                <i class="fas fa-star"></i> Priority change
                            </div>
                            <div class="reason-option" data-reason="Cargo specification change" data-category="commercial">
                                <i class="fas fa-edit"></i> Cargo specification change
                            </div>
                            <div class="reason-option" data-reason="Commercial negotiation" data-category="commercial">
                                <i class="fas fa-comments"></i> Commercial negotiation
                            </div>
                        </div>
                    </div>

                    <!-- Terminal Operations -->
                    <div class="reason-category">
                        <div class="reason-category-header" data-category="terminal">
                            <i class="fas fa-industry reason-category-icon"></i>
                            <span>Terminal Operations</span>
                            <i class="fas fa-chevron-down reason-category-toggle"></i>
                        </div>
                        <div class="reason-category-content" id="terminal-content">
                            <div class="reason-option" data-reason="Equipment maintenance" data-category="terminal">
                                <i class="fas fa-tools"></i> Equipment maintenance
                            </div>
                            <div class="reason-option" data-reason="Tank availability" data-category="terminal">
                                <i class="fas fa-fill-drip"></i> Tank availability
                            </div>
                            <div class="reason-option" data-reason="Pipeline scheduling" data-category="terminal">
                                <i class="fas fa-project-diagram"></i> Pipeline scheduling
                            </div>
                            <div class="reason-option" data-reason="Safety requirements" data-category="terminal">
                                <i class="fas fa-shield-alt"></i> Safety requirements
                            </div>
                            <div class="reason-option" data-reason="Resource optimization" data-category="terminal">
                                <i class="fas fa-chart-line"></i> Resource optimization
                            </div>
                        </div>
                    </div>

                    <!-- Regulatory -->
                    <div class="reason-category">
                        <div class="reason-category-header" data-category="regulatory">
                            <i class="fas fa-gavel reason-category-icon"></i>
                            <span>Regulatory</span>
                            <i class="fas fa-chevron-down reason-category-toggle"></i>
                        </div>
                        <div class="reason-category-content" id="regulatory-content">
                            <div class="reason-option" data-reason="Port authority directive" data-category="regulatory">
                                <i class="fas fa-flag"></i> Port authority directive
                            </div>
                            <div class="reason-option" data-reason="Environmental restrictions" data-category="regulatory">
                                <i class="fas fa-leaf"></i> Environmental restrictions
                            </div>
                            <div class="reason-option" data-reason="Safety inspection" data-category="regulatory">
                                <i class="fas fa-search"></i> Safety inspection
                            </div>
                            <div class="reason-option" data-reason="Customs clearance" data-category="regulatory">
                                <i class="fas fa-stamp"></i> Customs clearance
                            </div>
                        </div>
                    </div>

                    <!-- Other -->
                    <div class="reason-category">
                        <div class="reason-category-header" data-category="other">
                            <i class="fas fa-ellipsis-h reason-category-icon"></i>
                            <span>Other</span>
                            <i class="fas fa-chevron-down reason-category-toggle"></i>
                        </div>
                        <div class="reason-category-content" id="other-content">
                            <div class="reason-option reason-option-other" data-reason="other" data-category="other">
                                <i class="fas fa-edit"></i> Other (specify)
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom reason input (hidden initially) -->
                <div id="customReasonContainer" class="custom-reason-container" style="display: none;">
                    <label for="customReasonInput">Please specify the reason:</label>
                    <textarea id="customReasonInput" class="custom-reason-input" placeholder="Enter custom reason..." rows="3"></textarea>
                </div>

                <!-- Recent reasons (if any) -->
                <div id="recentReasonsContainer" class="recent-reasons-container" style="display: none;">
                    <h4><i class="fas fa-history"></i> Recent Reasons</h4>
                    <div id="recentReasonsList" class="recent-reasons-list"></div>
                </div>
            </div>

            <div class="reason-modal-footer">
                <button class="btn btn-secondary" id="cancelReasonBtn">Cancel</button>
                <button class="btn btn-primary" id="confirmReasonBtn" disabled>Confirm Change</button>
            </div>
        </div>
    </div>
{% endblock %}
