### Nginx config for planner.evosgpt.eu (with CSP nonce + OSM tiles)

Copy/paste into your main nginx.conf (replace the existing content), then run `nginx -t` and `nginx -s reload`.

```nginx
worker_processes  auto;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    # DNS resolver
    resolver 8.8.8.8 8.8.4.4 valid=300s;
    resolver_timeout 5s;

    # Add explicit font MIME mappings (Windows builds can lack these)
    types {
        font/ttf    ttf;
        font/woff   woff;
        font/woff2  woff2;
    }

    # Cache fonts aggressively
    map $sent_http_content_type $font_cache_control {
        default                              "public, max-age=0";
        ~*^font/(woff2|woff|ttf)$            "public, max-age=31536000, immutable";
    }

    # Rate-limit zones
    limit_req_zone $binary_remote_addr zone=api:10m  rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    # Cloudflare Access header → boolean (used in planner block)
    map $http_cf_access_authenticated_user_email $has_user { default 0; ~.+ 1; }

    # CSP nonce per request
    map $request_id $csp_nonce { default $request_id; }

    # ───── upstreams ───────────────────────────────────────────────────────────
    upstream openwebui      { server 127.0.0.1:3000; }
    upstream cost_api       { server 127.0.0.1:4001; }
    upstream cost_frontend  { server 127.0.0.1:4173; }
    upstream n8n            { server 127.0.0.1:5678; }
    upstream training       { server 127.0.0.1:5050; }
    upstream lean           { server 127.0.0.1:3030; }
    upstream planner        { server 127.0.0.1:7000; }

    # ───── evosgpt.eu (Open WebUI) ────────────────────────────────────────────
    server {
        listen 80;
        server_name evosgpt.eu www.evosgpt.eu;

        location /.well-known/acme-challenge/ {
            root C:/Users/<USER>/nginx-1.26.3/html;
        }

        # TEMP: serve WebUI over plain HTTP
        location / {
            proxy_pass http://openwebui;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade           $http_upgrade;
            proxy_set_header Connection        "upgrade";
            proxy_buffering off;
            proxy_connect_timeout 10s;
            proxy_send_timeout    300s;
            proxy_read_timeout    300s;
        }
    }

    # ───── cost.evosgpt.eu (Cost Estimator) ───────────────────────────────────
    server {
        listen 80;
        server_name cost.evosgpt.eu;

        location /.well-known/acme-challenge/ {
            root C:/Users/<USER>/nginx-1.26.3/html;
        }

        # Cloudflare real-IP
        set_real_ip_from ************/20;
        set_real_ip_from ************/22;
        set_real_ip_from ************/22;
        set_real_ip_from **********/22;
        set_real_ip_from ************/18;
        set_real_ip_from *************/18;
        set_real_ip_from ************/20;
        set_real_ip_from ************/20;
        set_real_ip_from *************/22;
        set_real_ip_from ************/17;
        set_real_ip_from ***********/15;
        set_real_ip_from **********/13;
        set_real_ip_from **********/14;
        set_real_ip_from **********/13;
        set_real_ip_from **********/22;
        real_ip_header CF-Connecting-IP;
        real_ip_recursive on;

        # API
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://cost_api/api/;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade           $http_upgrade;
            proxy_set_header Connection        "upgrade";
            proxy_buffering off;
            proxy_read_timeout 300s;
            proxy_connect_timeout 10s;
        }

        # Auth (tighter rate limit)
        location /api/auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://cost_api/api/auth/;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_buffering off;
        }

        # WebSocket
        location /socket.io/ {
            proxy_pass http://cost_api/socket.io/;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade           $http_upgrade;
            proxy_set_header Connection        "upgrade";
            proxy_buffering off;
            proxy_read_timeout 86400;
        }

        # Front-end
        location / {
            proxy_pass http://cost_frontend;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade           $http_upgrade;
            proxy_set_header Connection        "upgrade";
            proxy_buffering off;
        }
    }

    # ───── n8n.evosgpt.eu ─────────────────────────────────────────────────────
    server {
        listen 80;
        server_name n8n.evosgpt.eu;

        location /.well-known/acme-challenge/ {
            root C:/Users/<USER>/nginx-1.26.3/html;
        }

        location / {
            proxy_pass http://n8n;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade           $http_upgrade;
            proxy_set_header Connection        "upgrade";
            proxy_connect_timeout 10s;
            proxy_send_timeout    300s;
            proxy_read_timeout    300s;
        }
    }

    # ───── training.evosgpt.eu ────────────────────────────────────────────────
    server {
        listen 80;
        server_name training.evosgpt.eu;

        location /.well-known/acme-challenge/ {
            root C:/Users/<USER>/nginx-1.26.3/html;
        }

        location / {
            proxy_pass http://training;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade           $http_upgrade;
            proxy_set_header Connection        "upgrade";
            proxy_buffering off;
            proxy_connect_timeout 10s;
            proxy_send_timeout    300s;
            proxy_read_timeout    300s;
        }
    }

    # ───── lean.evosgpt.eu ────────────────────────────────────────────────────
    server {
        listen 80;
        server_name lean.evosgpt.eu;

        location /.well-known/acme-challenge/ {
            root C:/Users/<USER>/nginx-1.26.3/html;
        }

        # Cloudflare real-IP (same as cost.evosgpt.eu)
        set_real_ip_from ************/20;
        set_real_ip_from ************/22;
        set_real_ip_from ************/22;
        set_real_ip_from **********/22;
        set_real_ip_from ************/18;
        set_real_ip_from *************/18;
        set_real_ip_from ************/20;
        set_real_ip_from ************/20;
        set_real_ip_from *************/22;
        set_real_ip_from ************/17;
        set_real_ip_from ***********/15;
        set_real_ip_from **********/13;
        set_real_ip_from **********/14;
        set_real_ip_from **********/13;
        set_real_ip_from **********/22;
        real_ip_header CF-Connecting-IP;
        real_ip_recursive on;

        # API endpoints (general rate limiting)
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://lean/api/;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_connect_timeout 10s;
            proxy_send_timeout    300s;
            proxy_read_timeout    300s;
        }

        # Auth endpoints (stricter rate limiting for password reset security)
        location /api/auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://lean/api/auth/;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_buffering off;
            proxy_connect_timeout 10s;
            proxy_send_timeout    300s;
            proxy_read_timeout    300s;
        }

        # Static files and main app
        location / {
            proxy_pass http://lean;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade           $http_upgrade;
            proxy_set_header Connection        "upgrade";
            proxy_buffering off;
            proxy_connect_timeout 10s;
            proxy_send_timeout    300s;
            proxy_read_timeout    300s;
        }
    }

    # ───── planner.evosgpt.eu (Jetty Planner via Cloudflare Tunnel) ───────────
    server {
        # Local-only: cloudflared connects here
        listen 127.0.0.1:8080;
        server_name planner.evosgpt.eu;

        # Optional ACME path (harmless if unused)
        location /.well-known/acme-challenge/ {
            root C:/Users/<USER>/nginx-1.26.3/html;
        }

        client_max_body_size 100M;

        # ---- API ----
        location /api/ {
            if ($has_user = 0) { return 403; }  # require Access header
            limit_req zone=api burst=20 nodelay;

            proxy_pass http://planner/api/;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-CSP-Nonce       $csp_nonce;

            proxy_http_version 1.1;
            proxy_set_header Upgrade           $http_upgrade;
            proxy_set_header Connection        "upgrade";

            proxy_buffering off;
            proxy_connect_timeout 10s;
            proxy_send_timeout    300s;
            proxy_read_timeout    300s;
        }

        # ---- Backup Files Browser ----
        location /backups/ {
            if ($has_user = 0) { return 403; }  # require Cloudflare Access
            
            alias "C:/Users/<USER>/Jettyplanner/backups/";
            autoindex on;
            autoindex_exact_size off;
            autoindex_localtime on;
            autoindex_format html;
            
            # Security headers
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            
            # Force download for backup files
            location ~* \.(sql|gz|tar|dump)$ {
                if ($has_user = 0) { return 403; }
                add_header Content-Disposition 'attachment';
                add_header Cache-Control "private, no-cache, no-store, must-revalidate";
            }
            
            # Deny access to hidden files
            location ~ /\. {
                deny all;
            }
        }

        # ---- Static assets: enable long caching for fonts ----
        location ~* ^/static/fonts/.*\.(woff2|woff|ttf)$ {
            if ($has_user = 0) { return 403; }
            proxy_pass http://planner;
            proxy_set_header Host $host;
            add_header Cache-Control $font_cache_control always;
        }

        # ---- UI / websockets / everything else ----
        location / {
            if ($has_user = 0) { return 403; }  # require Access header

            proxy_pass http://planner;
            proxy_set_header Host              $host;
            proxy_set_header X-Real-IP         $remote_addr;
            proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-CSP-Nonce       $csp_nonce;

            proxy_http_version 1.1;
            proxy_set_header Upgrade           $http_upgrade;
            proxy_set_header Connection        "upgrade";

            proxy_buffering off;
            proxy_connect_timeout 10s;
            proxy_send_timeout    300s;
            proxy_read_timeout    300s;

            # Security headers
            add_header X-Frame-Options "DENY" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header Referrer-Policy "no-referrer" always;
            add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

            # Content Security Policy
            # - Keep nonce for scripts we generate server-side
            # - Allow unsafe-inline styles only if needed (Bootstrap inline)
            # - Allow Google Fonts for external font loading
            add_header Content-Security-Policy "
              default-src 'self';
              script-src 'self' 'nonce-$csp_nonce';
              style-src  'self' 'unsafe-inline' https://fonts.googleapis.com;
              font-src   'self' data: https://fonts.gstatic.com https://r2cdn.perplexity.ai;
              img-src    'self' data: blob: https://*.tile.openstreetmap.org;
              connect-src 'self' https: wss:;
              frame-ancestors 'none';
              base-uri 'self';
            " always;
        }
    }
}


