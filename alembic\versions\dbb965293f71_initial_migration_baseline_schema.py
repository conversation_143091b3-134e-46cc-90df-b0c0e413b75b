"""Initial migration - baseline schema

Revision ID: dbb965293f71
Revises: 
Create Date: 2025-09-02 09:53:00.907637

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'dbb965293f71'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('jetties_old')
    op.drop_table('assignments_old')
    op.drop_table('settings_old')
    op.drop_table('vessels_old')
    op.alter_column('assignment_changes', 'id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
    op.alter_column('assignment_changes', 'terminal_id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('assignment_changes', 'vessel_name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('assignment_changes', 'jetty_name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    # Allow alphanumeric vessel IDs in assignment change audit trail
    op.alter_column('assignment_changes', 'vessel_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('assignment_changes', 'changed_by',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('assignments', 'id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
    op.alter_column('assignments', 'terminal_id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    # Allow alphanumeric vessel IDs in assignments
    op.alter_column('assignments', 'vessel_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(),
               existing_nullable=False)

    op.alter_column('assignments', 'vessel_name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('assignments', 'vessel_type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('assignments', 'jetty_name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('assignments', 'cargo_product',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('assignments', 'cargo_volume',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('assignments', 'status',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'SCHEDULED'"))
    op.alter_column('cargoes', 'id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
    op.alter_column('cargoes', 'terminal_id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('cargoes', 'product',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('cargoes', 'volume',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('cargoes', 'connection_size',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.drop_constraint(None, 'cargoes', type_='foreignkey')
    op.create_foreign_key(None, 'cargoes', 'vessels', ['vessel_id'], ['id'])
    op.alter_column('jetties', 'id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
    op.alter_column('jetties', 'terminal_id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('jetties', 'name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('jetties', 'type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('jetties', 'vessel_type_restriction',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('jetties', 'min_dwt',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('jetties', 'max_dwt',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('jetties', 'min_loa',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('jetties', 'max_loa',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('jetties', 'max_beam',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('jetties', 'max_draft',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('jetties', 'primary_use',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('jetties', 'max_flow_rate',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('pumps', 'id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
    op.alter_column('pumps', 'terminal_id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('pumps', 'name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('pumps', 'type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('pumps', 'flow_rate',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('pumps', 'status',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'OPERATIONAL'"))
    op.alter_column('settings', 'key',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('settings', 'terminal_id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('settings', 'category',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('surveyors', 'id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
    op.alter_column('surveyors', 'terminal_id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('surveyors', 'name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('surveyors', 'status',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'AVAILABLE'"))
    op.alter_column('tanks', 'id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
    op.alter_column('tanks', 'terminal_id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('tanks', 'name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('tanks', 'type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('tanks', 'capacity',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('tanks', 'current_level',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('tanks', 'product_type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('terminals', 'id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               nullable=False)
    op.alter_column('terminals', 'name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('terminals', 'location_lat',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('terminals', 'location_lon',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('terminals', 'total_capacity_cbm',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('terminals', 'draft_meters',
               existing_type=sa.REAL(),
               type_=sa.Float(),
               existing_nullable=False)
    op.alter_column('terminals', 'operational_since',
               existing_type=sa.DATE(),
               type_=sa.DateTime(),
               existing_nullable=False)
    op.alter_column('terminals', 'timezone',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'Europe/Brussels'"))
    op.alter_column('terminals', 'currency',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'EUR'"))
    op.alter_column('vessels', 'id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
    op.alter_column('vessels', 'terminal_id',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('vessels', 'name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('vessels', 'type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('vessels', 'type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('vessels', 'name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('vessels', 'terminal_id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('vessels', 'id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
    op.alter_column('terminals', 'currency',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'EUR'"))
    op.alter_column('terminals', 'timezone',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'Europe/Brussels'"))
    op.alter_column('terminals', 'operational_since',
               existing_type=sa.DateTime(),
               type_=sa.DATE(),
               existing_nullable=False)
    op.alter_column('terminals', 'draft_meters',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('terminals', 'total_capacity_cbm',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('terminals', 'location_lon',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('terminals', 'location_lat',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('terminals', 'name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('terminals', 'id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               nullable=True)
    op.alter_column('tanks', 'product_type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('tanks', 'current_level',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('tanks', 'capacity',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('tanks', 'type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('tanks', 'name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('tanks', 'terminal_id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('tanks', 'id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
    op.alter_column('surveyors', 'status',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'AVAILABLE'"))
    op.alter_column('surveyors', 'name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('surveyors', 'terminal_id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('surveyors', 'id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
    op.alter_column('settings', 'category',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('settings', 'terminal_id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('settings', 'key',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('pumps', 'status',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'OPERATIONAL'"))
    op.alter_column('pumps', 'flow_rate',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('pumps', 'type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('pumps', 'name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('pumps', 'terminal_id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('pumps', 'id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
    op.alter_column('jetties', 'max_flow_rate',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=True)
    op.alter_column('jetties', 'primary_use',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('jetties', 'max_draft',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('jetties', 'max_beam',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('jetties', 'max_loa',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('jetties', 'min_loa',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=True)
    op.alter_column('jetties', 'max_dwt',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('jetties', 'min_dwt',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False)
    op.alter_column('jetties', 'vessel_type_restriction',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('jetties', 'type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('jetties', 'name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('jetties', 'terminal_id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('jetties', 'id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
    op.drop_constraint(None, 'cargoes', type_='foreignkey')
    op.create_foreign_key(None, 'cargoes', 'vessels_old', ['vessel_id'], ['id'])
    op.alter_column('cargoes', 'connection_size',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('cargoes', 'volume',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('cargoes', 'product',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('cargoes', 'terminal_id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('cargoes', 'id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
    # Revert vessel_id back to INTEGER
    op.alter_column('assignments', 'vessel_id',
               existing_type=sa.String(),
               type_=sa.INTEGER(),
               existing_nullable=False)

    op.alter_column('assignments', 'status',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'SCHEDULED'"))
    op.alter_column('assignments', 'cargo_volume',
               existing_type=sa.Float(),
               type_=sa.REAL(),
               existing_nullable=True)
    op.alter_column('assignments', 'cargo_product',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('assignments', 'jetty_name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('assignments', 'vessel_type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('assignments', 'vessel_name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('assignments', 'terminal_id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'TNZN'"))
    op.alter_column('assignments', 'id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
    op.alter_column('assignment_changes', 'changed_by',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('assignment_changes', 'jetty_name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('assignment_changes', 'vessel_name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('assignment_changes', 'terminal_id',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('assignment_changes', 'id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)
    op.create_table('vessels_old',
    sa.Column('id', sa.INTEGER(), nullable=True),
    sa.Column('terminal_id', sa.TEXT(), server_default=sa.text("'TNZN'"), nullable=False),
    sa.Column('name', sa.TEXT(), nullable=False),
    sa.Column('type', sa.TEXT(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['terminal_id'], ['terminals.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('settings_old',
    sa.Column('key', sa.TEXT(), nullable=False),
    sa.Column('terminal_id', sa.TEXT(), server_default=sa.text("'TNZN'"), nullable=False),
    sa.Column('value', sa.TEXT(), nullable=False),
    sa.Column('category', sa.TEXT(), nullable=False),
    sa.Column('is_sensitive', sa.BOOLEAN(), server_default=sa.text('0'), nullable=True),
    sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['terminal_id'], ['terminals.id'], ),
    sa.PrimaryKeyConstraint('key', 'terminal_id')
    )
    op.create_table('assignments_old',
    sa.Column('id', sa.INTEGER(), nullable=True),
    sa.Column('terminal_id', sa.TEXT(), server_default=sa.text("'TNZN'"), nullable=False),
    sa.Column('vessel_id', sa.INTEGER(), nullable=False),
    sa.Column('vessel_name', sa.TEXT(), nullable=False),
    sa.Column('vessel_type', sa.TEXT(), nullable=False),
    sa.Column('jetty_name', sa.TEXT(), nullable=False),
    sa.Column('cargo_product', sa.TEXT(), nullable=True),
    sa.Column('cargo_volume', sa.REAL(), nullable=True),
    sa.Column('start_time', sa.DATETIME(), nullable=False),
    sa.Column('end_time', sa.DATETIME(), nullable=False),
    sa.Column('status', sa.TEXT(), server_default=sa.text("'SCHEDULED'"), nullable=False),
    sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['terminal_id'], ['terminals.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('jetties_old',
    sa.Column('id', sa.INTEGER(), nullable=True),
    sa.Column('terminal_id', sa.TEXT(), server_default=sa.text("'TNZN'"), nullable=False),
    sa.Column('name', sa.TEXT(), nullable=False),
    sa.Column('type', sa.TEXT(), nullable=False),
    sa.Column('vessel_type_restriction', sa.TEXT(), nullable=True),
    sa.Column('min_dwt', sa.REAL(), nullable=False),
    sa.Column('max_dwt', sa.REAL(), nullable=False),
    sa.Column('min_loa', sa.REAL(), nullable=True),
    sa.Column('max_loa', sa.REAL(), nullable=False),
    sa.Column('max_beam', sa.REAL(), nullable=False),
    sa.Column('max_draft', sa.REAL(), nullable=False),
    sa.Column('primary_use', sa.TEXT(), nullable=False),
    sa.Column('max_flow_rate', sa.REAL(), nullable=True),
    sa.Column('is_operational', sa.BOOLEAN(), server_default=sa.text('1'), nullable=True),
    sa.Column('created_at', sa.DATETIME(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
    sa.ForeignKeyConstraint(['terminal_id'], ['terminals.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###
