"""
Unit tests for TransactionService - Database Transaction Management
"""

import pytest
from unittest.mock import <PERSON><PERSON>, <PERSON><PERSON>ock, patch
from contextlib import contextmanager

from src.services.transaction_service import TransactionService


class TestTransactionService:
    """Test cases for TransactionService"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.mock_db = Mock()
        self.mock_vessel_repo = Mock()
        self.mock_assignment_repo = Mock()
        
        self.transaction_service = TransactionService(
            self.mock_db,
            self.mock_vessel_repo,
            self.mock_assignment_repo
        )
        
        self.terminal_id = "TNZN"
        self.mock_db.get_active_terminal_id.return_value = self.terminal_id
        
        # Mock database session
        self.mock_session = Mock()
        self.mock_db.get_session.return_value = self.mock_session
    
    def test_transaction_context_manager_success(self):
        """Test successful transaction context manager"""
        # Test
        with self.transaction_service.transaction() as session:
            assert session == self.mock_session
            # Simulate some operations
            pass
        
        # Assertions
        self.mock_session.commit.assert_called_once()
        self.mock_session.close.assert_called_once()
        self.mock_session.rollback.assert_not_called()
    
    def test_transaction_context_manager_rollback(self):
        """Test transaction rollback on exception"""
        # Test
        with pytest.raises(Exception):
            with self.transaction_service.transaction() as session:
                assert session == self.mock_session
                raise Exception("Test error")
        
        # Assertions
        self.mock_session.rollback.assert_called_once()
        self.mock_session.close.assert_called_once()
        self.mock_session.commit.assert_not_called()
    
    def test_schedule_vessel_atomic_success(self):
        """Test successful atomic vessel scheduling"""
        vessel_id = "V001"
        assignment_data = {
            'vessel_id': vessel_id,
            'vessel_name': 'Test Vessel',
            'jetty_name': 'Jetty 1',
            'start_time': '2024-01-01T10:00:00',
            'end_time': '2024-01-01T14:00:00',
            'status': 'SCHEDULED'
        }
        
        # Mock successful operations
        self.mock_assignment_repo.create_assignment.return_value = 1
        self.mock_db.get_nominations.return_value = [
            {'id': 1, 'runtime_vessel_id': vessel_id}
        ]
        
        # Test
        assignment_id = self.transaction_service.schedule_vessel_atomic(
            vessel_id, assignment_data, self.terminal_id
        )
        
        # Assertions
        assert assignment_id == 1
        self.mock_assignment_repo.create_assignment.assert_called_once_with(
            assignment_data, self.terminal_id
        )
        self.mock_session.commit.assert_called_once()
    
    def test_schedule_vessel_atomic_no_terminal(self):
        """Test atomic vessel scheduling with no terminal"""
        self.mock_db.get_active_terminal_id.return_value = None
        
        # Test
        with pytest.raises(ValueError, match="No active terminal found"):
            self.transaction_service.schedule_vessel_atomic("V001", {})
    
    def test_schedule_vessel_atomic_rollback_on_error(self):
        """Test atomic vessel scheduling rollback on error"""
        vessel_id = "V001"
        assignment_data = {'vessel_id': vessel_id}
        
        # Mock error in assignment creation
        self.mock_assignment_repo.create_assignment.side_effect = Exception("Database error")
        
        # Test
        with pytest.raises(Exception):
            self.transaction_service.schedule_vessel_atomic(
                vessel_id, assignment_data, self.terminal_id
            )
        
        # Assertions
        self.mock_session.rollback.assert_called_once()
    
    def test_unschedule_vessel_atomic_success(self):
        """Test successful atomic vessel unscheduling"""
        assignment_id = 1
        mock_assignment = {
            'id': assignment_id,
            'vessel_id': 'V001',
            'vessel_name': 'Test Vessel',
            'jetty_name': 'Jetty 1',
            'start_time': '2024-01-01T10:00:00',
            'end_time': '2024-01-01T14:00:00',
            'terminal_id': self.terminal_id
        }
        
        # Mock successful operations
        self.mock_assignment_repo.get_assignment_by_id.return_value = mock_assignment
        self.mock_assignment_repo.update_assignment.return_value = True
        self.mock_db.get_nominations.return_value = []
        
        # Test
        result = self.transaction_service.unschedule_vessel_atomic(
            assignment_id, self.terminal_id
        )
        
        # Assertions
        assert result is True
        self.mock_assignment_repo.get_assignment_by_id.assert_called_once_with(
            assignment_id, self.terminal_id
        )
        self.mock_assignment_repo.update_assignment.assert_called_once_with(
            assignment_id, {'status': 'CANCELLED'}
        )
        self.mock_session.commit.assert_called_once()
    
    def test_unschedule_vessel_atomic_assignment_not_found(self):
        """Test atomic vessel unscheduling when assignment not found"""
        assignment_id = 999
        
        # Mock assignment not found
        self.mock_assignment_repo.get_assignment_by_id.return_value = None
        
        # Test
        result = self.transaction_service.unschedule_vessel_atomic(
            assignment_id, self.terminal_id
        )
        
        # Assertions
        assert result is False
        self.mock_session.rollback.assert_called_once()
    
    def test_unschedule_vessel_atomic_update_failure(self):
        """Test atomic vessel unscheduling when update fails"""
        assignment_id = 1
        mock_assignment = {
            'id': assignment_id,
            'vessel_id': 'V001',
            'terminal_id': self.terminal_id
        }
        
        # Mock assignment found but update fails
        self.mock_assignment_repo.get_assignment_by_id.return_value = mock_assignment
        self.mock_assignment_repo.update_assignment.return_value = False
        
        # Test
        result = self.transaction_service.unschedule_vessel_atomic(
            assignment_id, self.terminal_id
        )
        
        # Assertions
        assert result is False
        self.mock_session.rollback.assert_called_once()
    
    def test_bulk_schedule_vessels_atomic_success(self):
        """Test successful bulk atomic vessel scheduling"""
        vessel_assignments = [
            {
                'vessel_id': 'V001',
                'assignment_data': {
                    'vessel_id': 'V001',
                    'jetty_name': 'Jetty 1',
                    'start_time': '2024-01-01T10:00:00',
                    'end_time': '2024-01-01T14:00:00'
                }
            },
            {
                'vessel_id': 'V002',
                'assignment_data': {
                    'vessel_id': 'V002',
                    'jetty_name': 'Jetty 2',
                    'start_time': '2024-01-01T15:00:00',
                    'end_time': '2024-01-01T19:00:00'
                }
            }
        ]
        
        # Mock successful assignment creation
        self.mock_assignment_repo.create_assignment.side_effect = [1, 2]
        
        # Test
        assignment_ids = self.transaction_service.bulk_schedule_vessels_atomic(
            vessel_assignments, self.terminal_id
        )
        
        # Assertions
        assert assignment_ids == [1, 2]
        assert self.mock_assignment_repo.create_assignment.call_count == 2
        self.mock_session.commit.assert_called_once()
    
    def test_bulk_schedule_vessels_atomic_partial_invalid_data(self):
        """Test bulk scheduling with some invalid vessel assignment data"""
        vessel_assignments = [
            {
                'vessel_id': 'V001',
                'assignment_data': {'vessel_id': 'V001', 'jetty_name': 'Jetty 1'}
            },
            {
                'vessel_id': None,  # Invalid - no vessel_id
                'assignment_data': {'jetty_name': 'Jetty 2'}
            },
            {
                'vessel_id': 'V003',
                'assignment_data': None  # Invalid - no assignment_data
            }
        ]
        
        # Mock assignment creation for valid data
        self.mock_assignment_repo.create_assignment.return_value = 1
        
        # Test
        assignment_ids = self.transaction_service.bulk_schedule_vessels_atomic(
            vessel_assignments, self.terminal_id
        )
        
        # Assertions - should only process the valid vessel assignment
        assert assignment_ids == [1]
        self.mock_assignment_repo.create_assignment.assert_called_once()
        self.mock_session.commit.assert_called_once()
    
    def test_replace_schedule_atomic_success(self):
        """Test successful atomic schedule replacement"""
        new_assignments = [
            {'vessel_id': 'V001', 'jetty_name': 'Jetty 1'},
            {'vessel_id': 'V002', 'jetty_name': 'Jetty 2'}
        ]
        
        created_assignments = [
            {'id': 1, 'vessel_id': 'V001', 'jetty_name': 'Jetty 1'},
            {'id': 2, 'vessel_id': 'V002', 'jetty_name': 'Jetty 2'}
        ]
        
        # Mock successful operations
        self.mock_assignment_repo.clear_assignments.return_value = True
        self.mock_assignment_repo.replace_assignments.return_value = created_assignments
        
        # Test
        result = self.transaction_service.replace_schedule_atomic(
            new_assignments, self.terminal_id
        )
        
        # Assertions
        assert result == created_assignments
        self.mock_assignment_repo.clear_assignments.assert_called_once_with(self.terminal_id)
        self.mock_assignment_repo.replace_assignments.assert_called_once_with(
            new_assignments, self.terminal_id
        )
        self.mock_session.commit.assert_called_once()
    
    def test_delete_vessel_and_assignments_atomic_success(self):
        """Test successful atomic vessel and assignments deletion"""
        vessel_id = "V001"
        vessel_assignments = [
            {'id': 1, 'vessel_id': vessel_id},
            {'id': 2, 'vessel_id': vessel_id}
        ]
        
        # Mock successful operations
        self.mock_assignment_repo.get_assignments_by_vessel.return_value = vessel_assignments
        self.mock_assignment_repo.delete_assignment.return_value = True
        self.mock_vessel_repo.delete_vessel.return_value = True
        
        # Test
        result = self.transaction_service.delete_vessel_and_assignments_atomic(
            vessel_id, self.terminal_id
        )
        
        # Assertions
        assert result is True
        self.mock_assignment_repo.get_assignments_by_vessel.assert_called_once_with(
            vessel_id, self.terminal_id
        )
        # Should delete both assignments
        assert self.mock_assignment_repo.delete_assignment.call_count == 2
        self.mock_vessel_repo.delete_vessel.assert_called_once_with(vessel_id, self.terminal_id)
        self.mock_session.commit.assert_called_once()
    
    def test_delete_vessel_and_assignments_atomic_vessel_deletion_failure(self):
        """Test atomic deletion when vessel deletion fails"""
        vessel_id = "V001"
        vessel_assignments = [{'id': 1, 'vessel_id': vessel_id}]
        
        # Mock assignment deletion success but vessel deletion failure
        self.mock_assignment_repo.get_assignments_by_vessel.return_value = vessel_assignments
        self.mock_assignment_repo.delete_assignment.return_value = True
        self.mock_vessel_repo.delete_vessel.return_value = False  # Vessel deletion fails
        
        # Test
        result = self.transaction_service.delete_vessel_and_assignments_atomic(
            vessel_id, self.terminal_id
        )
        
        # Assertions
        assert result is False
        self.mock_session.rollback.assert_called_once()
    
    def test_transaction_service_no_terminal_error_handling(self):
        """Test error handling when no terminal is found"""
        self.mock_db.get_active_terminal_id.return_value = None
        
        # Test schedule_vessel_atomic
        with pytest.raises(ValueError):
            self.transaction_service.schedule_vessel_atomic("V001", {})
        
        # Test replace_schedule_atomic
        with pytest.raises(ValueError):
            self.transaction_service.replace_schedule_atomic([])
        
        # Test bulk_schedule_vessels_atomic
        with pytest.raises(ValueError):
            self.transaction_service.bulk_schedule_vessels_atomic([])
        
        # Test unschedule_vessel_atomic - should return False
        result = self.transaction_service.unschedule_vessel_atomic(1)
        assert result is False
        
        # Test delete_vessel_and_assignments_atomic - should return False
        result = self.transaction_service.delete_vessel_and_assignments_atomic("V001")
        assert result is False
