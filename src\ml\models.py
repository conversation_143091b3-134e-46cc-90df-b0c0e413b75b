"""
ML Models and Data Structures

This module defines the data models used for ML predictions.
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
from datetime import timedelta
import numpy as np


@dataclass
class VesselFeatures:
    """Features extracted from vessel data for ML prediction"""
    # Vessel characteristics
    dwt: float
    loa: float  # Length overall
    beam: float
    draft: float
    vessel_type: str  # 'TANKER' or 'BARGE'
    
    # Cargo characteristics
    cargo_volume: float
    product_types: List[str]
    product_hazard_level: str  # 'standard', 'hazardous', 'highly_hazardous'
    
    # Operational characteristics
    is_first_visit: bool
    requires_vapor_return: bool
    requires_nitrogen_purge: bool
    multiple_products: bool
    connection_size: str  # e.g., '12"', '8"', '6"'
    
    # Environmental factors
    weather_risk_score: float
    season: str
    time_of_day: str  # 'day', 'night'
    
    # Business/Customer information (defaults at end)
    customer_name: Optional[str] = None
    operation_types: List[str] = field(default_factory=list)
    preferred_jetty: Optional[str] = None
    
    # Jetty characteristics (when available)
    jetty_id: Optional[str] = None
    max_flow_rate: Optional[float] = None
    jetty_product_compatible: Optional[bool] = None
    connection_size_match: Optional[bool] = None
    
    def to_feature_vector(self) -> Dict[str, Any]:
        """Convert to feature dictionary for ML model"""
        return {
            'dwt': self.dwt,
            'loa': self.loa,
            'beam': self.beam,
            'draft': self.draft,
            'vessel_type_tanker': 1 if self.vessel_type == 'TANKER' else 0,
            'cargo_volume': self.cargo_volume,
            'product_hazard_standard': 1 if self.product_hazard_level == 'standard' else 0,
            'product_hazard_hazardous': 1 if self.product_hazard_level == 'hazardous' else 0,
            'product_hazard_highly_hazardous': 1 if self.product_hazard_level == 'highly_hazardous' else 0,
            'is_first_visit': 1 if self.is_first_visit else 0,
            'requires_vapor_return': 1 if self.requires_vapor_return else 0,
            'requires_nitrogen_purge': 1 if self.requires_nitrogen_purge else 0,
            'multiple_products': 1 if self.multiple_products else 0,
            'weather_risk_score': self.weather_risk_score,
            'max_flow_rate': self.max_flow_rate or 0,
            'jetty_product_compatible': 1 if self.jetty_product_compatible else 0,
            'connection_size_match': 1 if self.connection_size_match else 0,
        }


@dataclass
class TimePrediction:
    """ML-generated time predictions for a vessel operation"""
    pre_pump_time: timedelta
    pump_time: timedelta
    post_pump_time: timedelta
    terminal_time: timedelta
    
    # Confidence scores (0-1)
    pre_pump_confidence: float = 0.0
    pump_confidence: float = 0.0
    post_pump_confidence: float = 0.0
    terminal_confidence: float = 0.0
    
    # Model metadata
    model_version: str = "unknown"
    prediction_timestamp: Optional[str] = None
    
    @property
    def total_predicted_time(self) -> timedelta:
        """Total time from all components"""
        return self.pre_pump_time + self.pump_time + self.post_pump_time
    
    @property
    def average_confidence(self) -> float:
        """Average confidence across all predictions"""
        return (self.pre_pump_confidence + self.pump_confidence + 
                self.post_pump_confidence + self.terminal_confidence) / 4


@dataclass
class PredictionRequest:
    """Request for ML time prediction"""
    vessel_id: str
    features: VesselFeatures
    jetty_id: Optional[str] = None
    override_jetty_selection: bool = False
    
    
@dataclass
class PredictionResponse:
    """Response from ML prediction service"""
    vessel_id: str
    jetty_id: Optional[str]
    predictions: TimePrediction
    compatible_jetties: List[str]
    recommendation_reason: str
    success: bool = True
    error_message: Optional[str] = None
