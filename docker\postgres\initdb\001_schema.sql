-- Initialize PostgreSQL schema for Jetty Planner (mirrors current SQLite schema)
-- This runs only on first initialization of the primary instance

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Terminals
CREATE TABLE IF NOT EXISTS terminals (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    location_lat DOUBLE PRECISION NOT NULL,
    location_lon DOUBLE PRECISION NOT NULL,
    total_capacity_cbm DOUBLE PRECISION NOT NULL,
    number_of_tanks INTEGER NOT NULL,
    draft_meters DOUBLE PRECISION NOT NULL,
    operational_since DATE NOT NULL,
    vessel_berths INTEGER NOT NULL DEFAULT 0,
    barge_berths INTEGER NOT NULL DEFAULT 0,
    timezone TEXT NOT NULL DEFAULT 'Europe/Brussels',
    currency TEXT NOT NULL DEFAULT 'EUR',
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Settings (composite PK: key, terminal_id)
CREATE TABLE IF NOT EXISTS settings (
    key TEXT NOT NULL,
    terminal_id TEXT NOT NULL REFERENCES terminals(id),
    value TEXT NOT NULL,
    category TEXT NOT NULL,
    is_sensitive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    PRIMARY KEY (key, terminal_id)
);

-- Assignments
CREATE TABLE IF NOT EXISTS assignments (
    id SERIAL PRIMARY KEY,
    terminal_id TEXT NOT NULL REFERENCES terminals(id),
    vessel_id TEXT NOT NULL,
    vessel_name TEXT NOT NULL,
    vessel_type TEXT NOT NULL,
    jetty_name TEXT NOT NULL,
    cargo_product TEXT,
    cargo_volume DOUBLE PRECISION,
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ NOT NULL,
    status TEXT NOT NULL DEFAULT 'SCHEDULED',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Vessels
CREATE TABLE IF NOT EXISTS vessels (
    id SERIAL PRIMARY KEY,
    terminal_id TEXT NOT NULL REFERENCES terminals(id),
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cargoes (linked to vessels)
CREATE TABLE IF NOT EXISTS cargoes (
    id SERIAL PRIMARY KEY,
    terminal_id TEXT NOT NULL REFERENCES terminals(id),
    vessel_id INTEGER NOT NULL REFERENCES vessels(id),
    product TEXT NOT NULL,
    volume DOUBLE PRECISION NOT NULL DEFAULT 0,
    is_loading BOOLEAN NOT NULL DEFAULT FALSE,
    connection_size TEXT,
    vapor_return BOOLEAN DEFAULT FALSE,
    nitrogen_purge BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Nominations
CREATE TABLE IF NOT EXISTS nominations (
    id SERIAL PRIMARY KEY,
    terminal_id TEXT NOT NULL REFERENCES terminals(id),
    runtime_vessel_id TEXT NOT NULL,
    name TEXT NOT NULL,
    vessel_type TEXT NOT NULL,
    length DOUBLE PRECISION,
    beam DOUBLE PRECISION,
    draft DOUBLE PRECISION,
    deadweight DOUBLE PRECISION,
    priority INTEGER DEFAULT 0,
    capacity DOUBLE PRECISION,
    width DOUBLE PRECISION,
    customer TEXT,
    status TEXT NOT NULL DEFAULT 'pending',
    eta TIMESTAMPTZ,
    etd TIMESTAMPTZ,
    mmsi TEXT,
    imo TEXT,
    cargoes JSONB,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS ix_nominations_terminal_status ON nominations(terminal_id, status);
CREATE INDEX IF NOT EXISTS ix_nominations_runtime_vessel_id ON nominations(runtime_vessel_id);

-- Jetties (enhanced spec)
CREATE TABLE IF NOT EXISTS jetties (
    id SERIAL PRIMARY KEY,
    terminal_id TEXT NOT NULL REFERENCES terminals(id),
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    vessel_type_restriction TEXT,
    min_dwt DOUBLE PRECISION NOT NULL,
    max_dwt DOUBLE PRECISION NOT NULL,
    min_loa DOUBLE PRECISION,
    max_loa DOUBLE PRECISION NOT NULL,
    max_beam DOUBLE PRECISION NOT NULL,
    max_draft DOUBLE PRECISION NOT NULL,
    primary_use TEXT NOT NULL,
    max_flow_rate DOUBLE PRECISION,
    is_operational BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Tanks
CREATE TABLE IF NOT EXISTS tanks (
    id SERIAL PRIMARY KEY,
    terminal_id TEXT NOT NULL REFERENCES terminals(id),
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    capacity DOUBLE PRECISION NOT NULL,
    current_level DOUBLE PRECISION NOT NULL DEFAULT 0,
    product_type TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Pumps
CREATE TABLE IF NOT EXISTS pumps (
    id SERIAL PRIMARY KEY,
    terminal_id TEXT NOT NULL REFERENCES terminals(id),
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    flow_rate DOUBLE PRECISION NOT NULL,
    status TEXT NOT NULL DEFAULT 'OPERATIONAL',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Surveyors
CREATE TABLE IF NOT EXISTS surveyors (
    id SERIAL PRIMARY KEY,
    terminal_id TEXT NOT NULL REFERENCES terminals(id),
    name TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'AVAILABLE',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Seed default terminal TNZN (Terneuzen)
INSERT INTO terminals (id, name, location_lat, location_lon, total_capacity_cbm, number_of_tanks, draft_meters,
                       operational_since, vessel_berths, barge_berths, timezone, currency, is_active)
VALUES ('TNZN', 'Evos Terneuzen', 51.3294, 3.8091, 537000, 42, 15.0, '2005-01-01', 3, 3, 'Europe/Brussels', 'EUR', TRUE)
ON CONFLICT (id) DO NOTHING;

-- Seed jetties for TNZN
INSERT INTO jetties (terminal_id, name, type, vessel_type_restriction, min_dwt, max_dwt, min_loa, max_loa, max_beam, max_draft, primary_use, max_flow_rate, is_operational)
VALUES
('TNZN', 'Jetty 1', 'VESSEL', 'seagoing', 1000, 60000, 57, 236, 34.0, 12.8, 'Multiple products', 2000, TRUE),
('TNZN', 'Jetty 2', 'BARGE', 'inland', 1000, 7000, 57, 135, 17.0, 4.5, 'Chemicals/Hydrocarbons', 800, TRUE),
('TNZN', 'Jetty 3', 'VESSEL', 'both', 1000, 15000, 85, 135, 22.0, 12.8, 'Minerals/Benzene', 1800, TRUE),
('TNZN', 'Jetty 4', 'BARGE', 'inland', 1000, 9000, 85, 135, 22.0, 4.4, 'Minerals only', 600, TRUE),
('TNZN', 'Jetty 5', 'VESSEL', 'seagoing', 1000, 150000, 105, 275, 50.0, 15.0, 'Minerals only', 4000, TRUE),
('TNZN', 'Jetty 6', 'VESSEL', 'both', 1000, 20000, 105, 150, 25.0, 9.9, 'Minerals/Butane', 1200, TRUE)
ON CONFLICT DO NOTHING;

-- Seed default settings for TNZN
INSERT INTO settings (key, terminal_id, value, category, is_sensitive)
VALUES
('terminal_name', 'TNZN', 'Evos Terneuzen', 'terminal', FALSE),
('terminal_location_lat', 'TNZN', '51.3294', 'terminal', FALSE),
('terminal_location_lon', 'TNZN', '3.8091', 'terminal', FALSE),
('time_zone', 'TNZN', 'Europe/Brussels', 'preferences', FALSE),
('date_format', 'TNZN', 'DD-MM-YYYY', 'preferences', FALSE),
('language', 'TNZN', 'nl-NL', 'preferences', FALSE),
('dark_mode', 'TNZN', 'false', 'preferences', FALSE),
('auto_refresh', 'TNZN', 'true', 'preferences', FALSE),
('refresh_interval', 'TNZN', '30', 'preferences', FALSE),
('aisstream_api_key', 'TNZN', '501e28080107bf1d5a3d4e10380cfb6af87cd357', 'api_keys', TRUE),
('weather_api_key', 'TNZN', '', 'api_keys', TRUE),
('claude_api_key', 'TNZN', '', 'api_keys', TRUE),
('weather_api_provider', 'TNZN', 'openmeteo', 'weather', FALSE),
('weather_display_units', 'TNZN', 'metric', 'weather', FALSE),
('weather_refresh_interval', 'TNZN', '15', 'weather', FALSE),
('show_wind_alerts', 'TNZN', 'true', 'weather', FALSE),
('wind_caution_threshold', 'TNZN', '12', 'weather', FALSE),
('wind_danger_threshold', 'TNZN', '17', 'weather', FALSE),
('show_12h_forecast', 'TNZN', 'true', 'weather', FALSE),
('solver_time_limit', 'TNZN', '60', 'solver', FALSE),
('solver_strategy', 'TNZN', 'AUTOMATIC', 'solver', FALSE),
('parallel_solving', 'TNZN', 'true', 'solver', FALSE),
('enable_email_notifications', 'TNZN', 'false', 'notifications', FALSE),
('notify_on_vessel_arrival', 'TNZN', 'true', 'notifications', FALSE),
('notify_on_vessel_departure', 'TNZN', 'true', 'notifications', FALSE),
('notify_on_assignment_change', 'TNZN', 'true', 'notifications', FALSE),
('notify_on_weather_alert', 'TNZN', 'true', 'notifications', FALSE)
ON CONFLICT DO NOTHING;


