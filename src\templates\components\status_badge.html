{% macro status_badge(status, type='vessel') %}
{# 
  Reusable status badge component
  Usage: {{ status_badge(vessel.status, 'vessel') }} or {{ status_badge(assignment.status, 'assignment') }}
  Parameters:
    - status: The status string (e.g., "SCHEDULED", "PLANNED")
    - type: Either 'vessel', 'assignment', or 'schedule' to determine styling
#}

{% set status_lower = status|lower %}
{% set status_display = status|title %}

{% if type == 'vessel' %}
  {% set badge_class = {
    'scheduled': 'bg-yellow-100 text-yellow-800',
    'approaching': 'bg-blue-100 text-blue-800',
    'arrived': 'bg-green-100 text-green-800',
    'at_berth': 'bg-green-200 text-green-900',
    'departed': 'bg-gray-100 text-gray-800',
    'cancelled': 'bg-red-100 text-red-800',
    'delayed': 'bg-orange-100 text-orange-800'
  } %}
{% elif type == 'assignment' %}
  {% set badge_class = {
    'planned': 'bg-yellow-100 text-yellow-800',
    'in_progress': 'bg-green-100 text-green-800',
    'completed': 'bg-blue-100 text-blue-800',
    'cancelled': 'bg-red-100 text-red-800',
    'on_hold': 'bg-orange-100 text-orange-800'
  } %}
{% elif type == 'schedule' %}
  {% set badge_class = {
    'draft': 'bg-gray-100 text-gray-800',
    'published': 'bg-green-100 text-green-800',
    'archived': 'bg-blue-100 text-blue-800'
  } %}
{% endif %}

{# Default styling if no match #}
{% set class = badge_class.get(status_lower, 'bg-gray-100 text-gray-800') %}

<span class="status-badge status-{{ status_lower }} inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ class }}" 
      data-status="{{ status_lower }}" 
      data-status-type="{{ type }}">
  {{ status_display }}
</span>
{% endmacro %}

{% macro status_legend(type='all') %}
{# 
  Reusable status legend component
  Usage: {{ status_legend() }} or {{ status_legend('vessel') }}
  Parameters:
    - type: 'all', 'vessel', 'assignment', or 'schedule' to determine which statuses to show
#}
<div class="status-legend-container">
  <button class="status-legend-toggle" id="toggle-status-legend">
    <i class="fas fa-info-circle"></i>
    <span>Status Legend</span>
  </button>
  
  <div class="status-legend" id="status-legend">
    {% if type == 'all' or type == 'vessel' %}
    <div class="status-legend-section">
      <h4>Vessel Status</h4>
      <div class="status-legend-items">
        {{ status_badge('SCHEDULED', 'vessel') }}
        {{ status_badge('APPROACHING', 'vessel') }}
        {{ status_badge('ARRIVED', 'vessel') }}
        {{ status_badge('AT_BERTH', 'vessel') }}
        {{ status_badge('DEPARTED', 'vessel') }}
        {{ status_badge('CANCELLED', 'vessel') }}
        {{ status_badge('DELAYED', 'vessel') }}
      </div>
    </div>
    {% endif %}
    
    {% if type == 'all' or type == 'assignment' %}
    <div class="status-legend-section">
      <h4>Assignment Status</h4>
      <div class="status-legend-items">
        {{ status_badge('PLANNED', 'assignment') }}
        {{ status_badge('IN_PROGRESS', 'assignment') }}
        {{ status_badge('COMPLETED', 'assignment') }}
        {{ status_badge('CANCELLED', 'assignment') }}
        {{ status_badge('ON_HOLD', 'assignment') }}
      </div>
    </div>
    {% endif %}
    
    {% if type == 'all' or type == 'schedule' %}
    <div class="status-legend-section">
      <h4>Schedule Status</h4>
      <div class="status-legend-items">
        {{ status_badge('DRAFT', 'schedule') }}
        {{ status_badge('PUBLISHED', 'schedule') }}
        {{ status_badge('ARCHIVED', 'schedule') }}
      </div>
    </div>
    {% endif %}
  </div>
</div>
{% endmacro %} 