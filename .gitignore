# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Environment variables
.env
.env.local

# Logs
*.log
logs/

# Data
data/
*.db
*.sqlite

# Docker
.dockerenv

# Generated files
*.pyc
.coverage
htmlcov/

# Overrides: include .env and data folder in private repos
!.env
!.env.local
!data/
!data/**
# Local artifacts
nul
.claude/

