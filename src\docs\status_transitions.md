# Status Transitions Documentation

This document defines the standardized status transitions for the Ghent Terminal Jetty Planning application. It serves as the reference for developers implementing status handling across the application.

## 1. Purpose

The status system serves several key functions:
- Tracking vessel movements through the terminal
- Monitoring assignment progress
- Enabling proper resource allocation
- Supporting reporting and analytics
- Ensuring consistent user interfaces

## 2. Status Types

### 2.1 Vessel Statuses

| Status | Description | Color Code |
|--------|-------------|------------|
| SCHEDULED | Vessel has been scheduled but has not yet started its journey | #6c757d (gray) |
| APPROACHING | Vessel is en route to the terminal | #007bff (blue) |
| ARRIVED | Vessel has arrived at the terminal but not yet docked | #17a2b8 (info) |
| DOCKED | Vessel is securely moored at the jetty | #28a745 (success) |
| LOADING | Vessel is being loaded with cargo | #fd7e14 (orange) |
| UNLOADING | Vessel is discharging cargo | #fd7e14 (orange) |
| DEPARTING | Vessel is preparing to leave the terminal | #6610f2 (purple) |
| DEPARTED | Vessel has left the terminal | #343a40 (dark) |
| DELAYED | Vessel's arrival or operations are delayed | #ffc107 (warning) |
| CANCELLED | Vessel's scheduled visit has been cancelled | #dc3545 (danger) |

### 2.2 Assignment Statuses

| Status | Description | Color Code |
|--------|-------------|------------|
| PENDING_APPROVAL | Assignment is waiting for approval | #ffc107 (warning) |
| ACTIVE | Assignment is currently in progress | #28a745 (success) |
| COMPLETED | Assignment has been successfully completed | #343a40 (dark) |

## 3. Status Transitions

### 3.1 Valid Vessel Status Transitions

```
SCHEDULED → APPROACHING → ARRIVED → DOCKED → LOADING/UNLOADING → DEPARTING → DEPARTED
    ↓            ↓           ↓           ↓             ↓              ↓
CANCELLED     DELAYED     DELAYED     DELAYED       DELAYED        DELAYED
```

#### Rules:
1. A vessel must follow the sequence defined above
2. DELAYED status can be applied at any point before DEPARTED
3. CANCELLED status can only be applied to SCHEDULED vessels
4. A vessel can toggle between LOADING and UNLOADING states while DOCKED
5. A DELAYED vessel can resume its previous sequence once the delay is resolved

### 3.2 Valid Assignment Status Transitions

```
PENDING_APPROVAL → ACTIVE → COMPLETED
```

#### Rules:
1. Assignments always begin in PENDING_APPROVAL status
2. Assignments can only be ACTIVE after approval
3. Assignments can only be marked COMPLETED when all required operations are done
4. Once COMPLETED, an assignment cannot return to a previous status

## 4. Status and Resource Relationships

### 4.1 Vessel-Jetty Relationships

| Vessel Status | Jetty Availability |
|---------------|-------------------|
| DOCKED, LOADING, UNLOADING | Jetty is OCCUPIED |
| All other statuses | Jetty is AVAILABLE |

### 4.2 Assignment-Resource Relationships

| Assignment Status | Resource Allocation |
|-------------------|---------------------|
| PENDING_APPROVAL | Resources are reserved but not allocated |
| ACTIVE | Resources are allocated and in use |
| COMPLETED | Resources are released |

## 5. Status Validation Guidelines

### 5.1 Input Validation

- All status values should be normalized to uppercase in the database
- UI components should display statuses in Title Case
- Status comparisons should be case-insensitive
- Invalid status transitions should be prevented and generate appropriate error messages

### 5.2 Transition Hooks

The application should implement hooks for status transitions to:
- Update related resources automatically
- Trigger notifications
- Log status changes for audit purposes
- Update scheduling and planning information

## 6. UI Display Standards

- All status indicators should use the color codes defined in this document
- Status badges should be consistent across all interfaces
- Filtering by status should be available in all resource list views
- Status legends should be provided where multiple statuses are displayed

## 7. Implementation Notes

### 7.1 Status Handling in Code

```python
# Example of case-insensitive status comparison
def is_valid_transition(current_status, new_status):
    current = current_status.upper()
    new = new_status.upper()
    
    transitions = {
        "SCHEDULED": ["APPROACHING", "CANCELLED", "DELAYED"],
        "APPROACHING": ["ARRIVED", "DELAYED"],
        # ... other transitions
    }
    
    return new in transitions.get(current, [])
```

### 7.2 Database Storage

- Store status values in uppercase
- Include timestamp for each status change
- Maintain status history for audit and reporting 