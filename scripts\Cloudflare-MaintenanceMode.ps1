# Cloudflare Workers Maintenance Mode Management
# Manages maintenance mode via Cloudflare Workers API

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("Enable", "Disable", "Status")]
    [string]$Action,
    
    [string]$Reason = "Scheduled maintenance",
    [string]$EstimatedDuration = "15-30 minutes"
)

$ErrorActionPreference = "Stop"

# Configuration - Update these values
$ZoneId = "your-cloudflare-zone-id"           # Get from Cloudflare Dashboard
$WorkerName = "evos-maintenance-worker"        # Your worker name
$Domain = "planner.evosgpt.eu"                # Your domain
$RoutePattern = "planner.evosgpt.eu/*"        # Route pattern

# API Configuration - Set these environment variables:
# $env:CLOUDFLARE_API_TOKEN = "your-api-token"
# $env:CLOUDFLARE_EMAIL = "<EMAIL>"

function Get-CloudflareHeaders {
    $ApiToken = $env:CLOUDFLARE_API_TOKEN
    $Email = $env:CLOUDFLARE_EMAIL
    
    if (-not $ApiToken -or -not $Email) {
        Write-Host "❌ Cloudflare API credentials not configured!" -ForegroundColor Red
        Write-Host "Set the following environment variables:" -ForegroundColor Yellow
        Write-Host "  `$env:CLOUDFLARE_API_TOKEN = 'your-api-token'" -ForegroundColor Cyan
        Write-Host "  `$env:CLOUDFLARE_EMAIL = '<EMAIL>'" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Get your API token from: https://dash.cloudflare.com/profile/api-tokens" -ForegroundColor Gray
        exit 1
    }
    
    return @{
        "X-Auth-Email" = $Email
        "Authorization" = "Bearer $ApiToken"
        "Content-Type" = "application/json"
    }
}

function Get-WorkerId {
    $Headers = Get-CloudflareHeaders
    $Uri = "https://api.cloudflare.com/v4/accounts/workers/scripts"
    
    try {
        $Response = Invoke-RestMethod -Uri $Uri -Headers $Headers -Method GET
        $Worker = $Response.result | Where-Object { $_.id -eq $WorkerName }
        
        if ($Worker) {
            return $Worker.id
        } else {
            Write-Host "❌ Worker '$WorkerName' not found!" -ForegroundColor Red
            Write-Host "Available workers:" -ForegroundColor Yellow
            $Response.result | ForEach-Object { Write-Host "  - $($_.id)" -ForegroundColor Gray }
            exit 1
        }
    } catch {
        Write-Host "❌ Error fetching workers: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

function Get-ExistingRoute {
    $Headers = Get-CloudflareHeaders
    $Uri = "https://api.cloudflare.com/v4/zones/$ZoneId/workers/routes"
    
    try {
        $Response = Invoke-RestMethod -Uri $Uri -Headers $Headers -Method GET
        return $Response.result | Where-Object { $_.pattern -eq $RoutePattern }
    } catch {
        Write-Host "❌ Error fetching routes: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

function Enable-MaintenanceMode {
    Write-Host "🔧 Enabling Cloudflare Workers maintenance mode..." -ForegroundColor Yellow
    
    # Check if route already exists
    $ExistingRoute = Get-ExistingRoute
    if ($ExistingRoute) {
        Write-Host "⚠️  Maintenance route already exists!" -ForegroundColor Yellow
        Write-Host "Route ID: $($ExistingRoute.id)" -ForegroundColor Gray
        return
    }
    
    # Get worker ID
    $WorkerId = Get-WorkerId
    
    # Create the route
    $Headers = Get-CloudflareHeaders
    $Uri = "https://api.cloudflare.com/v4/zones/$ZoneId/workers/routes"
    
    $Body = @{
        pattern = $RoutePattern
        script = $WorkerName
    } | ConvertTo-Json
    
    try {
        $Response = Invoke-RestMethod -Uri $Uri -Headers $Headers -Method POST -Body $Body
        
        if ($Response.success) {
            Write-Host "✅ Maintenance mode enabled successfully!" -ForegroundColor Green
            Write-Host ""
            Write-Host "🌐 Users will now see the maintenance page at:" -ForegroundColor Cyan
            Write-Host "   https://$Domain" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "📋 Route Details:" -ForegroundColor White
            Write-Host "   Pattern: $RoutePattern" -ForegroundColor Gray
            Write-Host "   Worker: $WorkerName" -ForegroundColor Gray
            Write-Host "   Route ID: $($Response.result.id)" -ForegroundColor Gray
            Write-Host ""
            Write-Host "📝 Maintenance Info:" -ForegroundColor White
            Write-Host "   Reason: $Reason" -ForegroundColor Gray
            Write-Host "   Estimated Duration: $EstimatedDuration" -ForegroundColor Gray
            
            # Log the action
            $LogFile = Join-Path (Split-Path -Parent $PSScriptRoot) "logs/cloudflare-maintenance.log"
            $LogsDir = Split-Path -Parent $LogFile
            if (-not (Test-Path $LogsDir)) {
                New-Item -ItemType Directory -Path $LogsDir -Force | Out-Null
            }
            
            $LogEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - ENABLED via Cloudflare Workers - Reason: $Reason, Duration: $EstimatedDuration, Route: $($Response.result.id)"
            Add-Content -Path $LogFile -Value $LogEntry
            
        } else {
            Write-Host "❌ Failed to enable maintenance mode!" -ForegroundColor Red
            Write-Host "Errors: $($Response.errors | ConvertTo-Json -Depth 2)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error enabling maintenance mode: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

function Disable-MaintenanceMode {
    Write-Host "🚀 Disabling Cloudflare Workers maintenance mode..." -ForegroundColor Green
    
    # Find existing route
    $ExistingRoute = Get-ExistingRoute
    if (-not $ExistingRoute) {
        Write-Host "ℹ️  No maintenance route found. Maintenance mode is not currently enabled." -ForegroundColor Blue
        return
    }
    
    # Confirm removal
    Write-Host "📋 Found maintenance route:" -ForegroundColor Gray
    Write-Host "   Pattern: $($ExistingRoute.pattern)" -ForegroundColor Gray
    Write-Host "   Worker: $($ExistingRoute.script)" -ForegroundColor Gray
    Write-Host "   Route ID: $($ExistingRoute.id)" -ForegroundColor Gray
    Write-Host ""
    
    $Confirm = Read-Host "Are you sure you want to disable maintenance mode? (Y/n)"
    if ($Confirm -match "^[Nn]") {
        Write-Host "Maintenance mode unchanged." -ForegroundColor Yellow
        return
    }
    
    # Delete the route
    $Headers = Get-CloudflareHeaders
    $Uri = "https://api.cloudflare.com/v4/zones/$ZoneId/workers/routes/$($ExistingRoute.id)"
    
    try {
        $Response = Invoke-RestMethod -Uri $Uri -Headers $Headers -Method DELETE
        
        if ($Response.success) {
            Write-Host "✅ Maintenance mode disabled successfully!" -ForegroundColor Green
            Write-Host ""
            Write-Host "🌐 The application is now accessible again at:" -ForegroundColor Cyan
            Write-Host "   https://$Domain" -ForegroundColor Cyan
            Write-Host ""
            Write-Host "🔍 You may want to verify the application is working correctly." -ForegroundColor Yellow
            
            # Log the action
            $LogFile = Join-Path (Split-Path -Parent $PSScriptRoot) "logs/cloudflare-maintenance.log"
            $LogEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - DISABLED via Cloudflare Workers - Route removed: $($ExistingRoute.id)"
            Add-Content -Path $LogFile -Value $LogEntry -ErrorAction SilentlyContinue
            
        } else {
            Write-Host "❌ Failed to disable maintenance mode!" -ForegroundColor Red
            Write-Host "Errors: $($Response.errors | ConvertTo-Json -Depth 2)" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error disabling maintenance mode: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

function Show-MaintenanceStatus {
    Write-Host "🔍 Cloudflare Workers Maintenance Status" -ForegroundColor Cyan
    Write-Host "=" * 45 -ForegroundColor Gray
    Write-Host ""
    
    # Check for existing route
    $ExistingRoute = Get-ExistingRoute
    
    if ($ExistingRoute) {
        Write-Host "🔧 STATUS: MAINTENANCE MODE ENABLED" -ForegroundColor Yellow -BackgroundColor DarkRed
        Write-Host ""
        Write-Host "📋 Route Details:" -ForegroundColor White
        Write-Host "   Pattern: $($ExistingRoute.pattern)" -ForegroundColor Gray
        Write-Host "   Worker: $($ExistingRoute.script)" -ForegroundColor Gray
        Write-Host "   Route ID: $($ExistingRoute.id)" -ForegroundColor Gray
        Write-Host ""
        Write-Host "🌐 Users currently see: Maintenance page (served from Cloudflare Edge)" -ForegroundColor Yellow
        
    } else {
        Write-Host "✅ STATUS: NORMAL OPERATION" -ForegroundColor Green -BackgroundColor DarkGreen
        Write-Host ""
        Write-Host "🌐 Users currently see: Normal application" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "🔗 URL: https://$Domain" -ForegroundColor Cyan
    Write-Host "⚙️  Worker: $WorkerName" -ForegroundColor Gray
    Write-Host "🆔 Zone ID: $ZoneId" -ForegroundColor Gray
    
    # Show recent logs
    $LogFile = Join-Path (Split-Path -Parent $PSScriptRoot) "logs/cloudflare-maintenance.log"
    if (Test-Path $LogFile) {
        Write-Host ""
        Write-Host "📋 Recent Cloudflare maintenance activity:" -ForegroundColor White
        $RecentLogs = Get-Content $LogFile -Tail 5 -ErrorAction SilentlyContinue
        if ($RecentLogs) {
            $RecentLogs | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
        } else {
            Write-Host "   No recent activity logged" -ForegroundColor Gray
        }
    }
    
    Write-Host ""
    Write-Host "🔧 Quick Actions:" -ForegroundColor White
    if ($ExistingRoute) {
        Write-Host "   .\scripts\Cloudflare-MaintenanceMode.ps1 -Action Disable" -ForegroundColor Cyan
    } else {
        Write-Host "   .\scripts\Cloudflare-MaintenanceMode.ps1 -Action Enable" -ForegroundColor Cyan
    }
}

# Validate configuration
if ($ZoneId -eq "your-cloudflare-zone-id") {
    Write-Host "❌ Configuration required!" -ForegroundColor Red
    Write-Host "Edit this script and update the configuration section:" -ForegroundColor Yellow
    Write-Host "  - ZoneId: Get from Cloudflare Dashboard" -ForegroundColor Cyan
    Write-Host "  - WorkerName: Name of your deployed worker" -ForegroundColor Cyan
    Write-Host "  - Domain: Your domain name" -ForegroundColor Cyan
    exit 1
}

# Execute the requested action
switch ($Action) {
    "Enable" { Enable-MaintenanceMode }
    "Disable" { Disable-MaintenanceMode }
    "Status" { Show-MaintenanceStatus }
}

Write-Host ""
Write-Host "💡 Pro tip: Set up environment variables for easier usage:" -ForegroundColor Gray
Write-Host "   `$env:CLOUDFLARE_API_TOKEN = 'your-token'" -ForegroundColor Gray
Write-Host "   `$env:CLOUDFLARE_EMAIL = 'your-email'" -ForegroundColor Gray
