Param(
    [string]$Branch = "main",
    [switch]$NoBuild
)

$ErrorActionPreference = 'Stop'

# Ensure we are at repo root
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location (Join-Path $scriptDir "..")

Write-Host "Updating Jetty Planner (branch: $Branch)" -ForegroundColor Cyan

# Pull latest code
if (Test-Path .git) {
    git fetch --all | cat
    git checkout $Branch | cat
    git pull --rebase | cat
} else {
    Write-Warning "No .git directory found; skipping git pull."
}

if ($NoBuild) {
    Write-Host "Skipping docker build as requested." -ForegroundColor Yellow
} else {
    Write-Host "Rebuilding and restarting docker compose (prod)" -ForegroundColor Yellow
    docker compose down | cat
    docker compose build | cat
}

docker compose up -d | cat

Write-Host "Update complete. Current status:" -ForegroundColor Green
docker compose ps | cat

