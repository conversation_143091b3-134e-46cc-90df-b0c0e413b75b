"""
Vessel Repository - Database Access Layer

Implements the Repository pattern for vessel data access,
providing a clean abstraction over database operations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
import logging

from src.database import Database
from src.models.vessel import VesselBase

logger = logging.getLogger(__name__)


class VesselRepository(ABC):
    """Abstract base class for vessel data access."""
    
    @abstractmethod
    def get_available_vessels(self, terminal_id: str) -> List[VesselBase]:
        """Get all vessels available for scheduling."""
        pass
    
    @abstractmethod
    def get_vessel_by_id(self, vessel_id: str, terminal_id: str) -> Optional[VesselBase]:
        """Get a specific vessel by ID."""
        pass
    
    @abstractmethod
    def save_vessel_nomination(self, vessel_data: Dict[str, Any], terminal_id: str) -> str:
        """Save a new vessel nomination."""
        pass
    
    @abstractmethod
    def delete_vessel(self, vessel_id: str, terminal_id: str) -> bool:
        """Delete a vessel."""
        pass
    
    @abstractmethod
    def get_vessel_count(self, terminal_id: str) -> int:
        """Get total count of available vessels."""
        pass


class DatabaseVesselRepository(VesselRepository):
    """Database implementation of vessel repository."""
    
    def __init__(self, database: Database):
        self.db = database
    
    def get_available_vessels(self, terminal_id: str) -> List[VesselBase]:
        """
        Get all vessels available for scheduling from database sources.
        
        Combines nominations and cancelled assignments into available vessels.
        """
        vessels = []
        
        try:
            # Get active nominations
            nominations = self.db.get_nominations(terminal_id, status="ACTIVE") or []
            logger.debug(f"Found {len(nominations)} active nominations")
            
            for nomination in nominations:
                try:
                    vessel = self._nomination_to_vessel(nomination)
                    vessels.append(vessel)
                except Exception as e:
                    logger.warning(f"Failed to convert nomination {nomination.get('id')}: {e}")
                    continue
            
            # Get cancelled assignments (unscheduled vessels)
            assignments = self.db.get_assignments(terminal_id) or []
            cancelled_assignments = [a for a in assignments if a.get('status', '').upper() == 'CANCELLED']
            logger.debug(f"Found {len(cancelled_assignments)} cancelled assignments")
            
            vessel_ids_seen = {vessel.id for vessel in vessels}
            
            for assignment in cancelled_assignments:
                try:
                    vessel_id = str(assignment.get('vessel_id', ''))
                    
                    # Skip invalid or duplicate vessel IDs
                    if vessel_id in vessel_ids_seen or not vessel_id or vessel_id == 'vessel_id':
                        continue
                    
                    vessel = self._assignment_to_vessel(assignment)
                    vessels.append(vessel)
                    vessel_ids_seen.add(vessel_id)
                    
                except Exception as e:
                    logger.warning(f"Failed to convert cancelled assignment {assignment.get('id')}: {e}")
                    continue
            
            logger.info(f"Repository found {len(vessels)} available vessels for terminal {terminal_id}")
            return vessels
            
        except Exception as e:
            logger.error(f"Error getting available vessels from repository: {e}")
            return []
    
    def get_vessel_by_id(self, vessel_id: str, terminal_id: str) -> Optional[VesselBase]:
        """Get a specific vessel by ID from all database sources."""
        try:
            # Check nominations first
            nominations = self.db.get_nominations(terminal_id) or []
            for nomination in nominations:
                if str(nomination.get('runtime_vessel_id')) == str(vessel_id):
                    return self._nomination_to_vessel(nomination)
            
            # Check cancelled assignments
            assignments = self.db.get_assignments(terminal_id) or []
            for assignment in assignments:
                if (assignment.get('status', '').upper() == 'CANCELLED' and 
                    str(assignment.get('vessel_id')) == str(vessel_id)):
                    return self._assignment_to_vessel(assignment)
            
            # Check database vessels
            try:
                vessel_id_int = int(vessel_id)
                vessel_data = self.db.get_vessel(vessel_id_int, terminal_id)
                if vessel_data:
                    return self._db_vessel_to_vessel(vessel_data)
            except (ValueError, TypeError):
                pass
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting vessel {vessel_id} from repository: {e}")
            return None
    
    def save_vessel_nomination(self, vessel_data: Dict[str, Any], terminal_id: str) -> str:
        """Save a new vessel nomination to database."""
        try:
            nomination_id = self.db.add_nomination({
                **vessel_data,
                'terminal_id': terminal_id,
                'status': 'ACTIVE'
            })
            
            logger.info(f"Repository saved nomination {nomination_id} for vessel {vessel_data.get('runtime_vessel_id')}")
            return vessel_data.get('runtime_vessel_id', str(nomination_id))
            
        except Exception as e:
            logger.error(f"Error saving vessel nomination to repository: {e}")
            raise
    
    def delete_vessel(self, vessel_id: str, terminal_id: str) -> bool:
        """Delete a vessel from database."""
        try:
            deleted = self.db.delete_vessel(vessel_id)
            if deleted:
                logger.info(f"Repository deleted vessel {vessel_id}")
            return deleted
            
        except Exception as e:
            logger.error(f"Error deleting vessel {vessel_id} from repository: {e}")
            return False
    
    def get_vessel_count(self, terminal_id: str) -> int:
        """Get total count of available vessels."""
        try:
            vessels = self.get_available_vessels(terminal_id)
            return len(vessels)
        except Exception as e:
            logger.error(f"Error getting vessel count from repository: {e}")
            return 0
    
    def _nomination_to_vessel(self, nomination: Dict[str, Any]) -> VesselBase:
        """Convert nomination to VesselBase object."""
        from src.models.vessel import VesselType, Cargo
        
        try:
            vessel_type_str = nomination.get('vessel_type', 'tanker').lower()
            vessel_type = VesselType(vessel_type_str) if vessel_type_str in ['tanker', 'barge', 'cargo'] else VesselType.tanker
            
            # Create cargoes
            cargoes = []
            for cargo_data in nomination.get('cargoes', []):
                try:
                    cargo = Cargo.from_dict({
                        'product': cargo_data.get('product', 'Unknown'),
                        'volume': float(cargo_data.get('volume', 0) or 0),
                        'is_loading': bool(cargo_data.get('is_loading', True))
                    })
                    cargoes.append(cargo)
                except Exception as e:
                    logger.warning(f"Failed to create cargo: {e}")
                    continue
            
            vessel = VesselBase(
                id=nomination.get('runtime_vessel_id'),
                name=nomination.get('name', 'Unknown Vessel'),
                vessel_type=vessel_type,
                length=float(nomination.get('length', 150.0) or 150.0),
                beam=float(nomination.get('beam', 25.0) or 25.0),
                draft=float(nomination.get('draft', 10.0) or 10.0),
                deadweight=float(nomination.get('deadweight', 50000.0) or 50000.0),
                cargoes=cargoes,
                status="APPROACHING",
                priority=int(nomination.get('priority', 1) or 1),
                capacity=nomination.get('capacity'),
                width=nomination.get('width'),
                customer=nomination.get('customer'),
                metadata={
                    'source': 'nomination',
                    'nomination_id': nomination.get('id'),
                    'mmsi': nomination.get('mmsi'),
                    'imo': nomination.get('imo'),
                    **(nomination.get('metadata') or {})
                }
            )
            
            # Set timing
            if nomination.get('eta'):
                vessel.eta = nomination['eta']
            if nomination.get('etd'):
                vessel.etd = nomination['etd']
            
            return vessel
            
        except Exception as e:
            logger.error(f"Error converting nomination to vessel: {e}")
            raise
    
    def _assignment_to_vessel(self, assignment: Dict[str, Any]) -> VesselBase:
        """Convert cancelled assignment to VesselBase object."""
        from src.models.vessel import VesselType, Cargo
        
        try:
            vessel_type_str = assignment.get('vessel_type', 'tanker').lower()
            vessel_type = VesselType(vessel_type_str) if vessel_type_str in ['tanker', 'barge', 'cargo'] else VesselType.tanker
            
            # Create cargo from assignment
            cargoes = []
            if assignment.get('cargo_product') and assignment.get('cargo_volume'):
                try:
                    cargo = Cargo.from_dict({
                        'product': assignment.get('cargo_product', 'Unknown'),
                        'volume': float(assignment.get('cargo_volume', 0) or 0),
                        'is_loading': True
                    })
                    cargoes.append(cargo)
                except Exception as e:
                    logger.warning(f"Failed to create cargo from assignment: {e}")
            
            vessel = VesselBase(
                id=assignment.get('vessel_id'),
                name=assignment.get('vessel_name', 'Unknown Vessel'),
                vessel_type=vessel_type,
                length=150.0,
                beam=25.0,
                draft=10.0,
                deadweight=50000.0,
                cargoes=cargoes,
                status="APPROACHING",
                priority=1,
                capacity=assignment.get('cargo_volume', 50000),
                metadata={
                    'source': 'unscheduled_assignment',
                    'original_assignment_id': assignment.get('id'),
                    'original_jetty': assignment.get('jetty_name')
                }
            )
            
            return vessel
            
        except Exception as e:
            logger.error(f"Error converting assignment to vessel: {e}")
            raise
    
    def _db_vessel_to_vessel(self, vessel_data: Dict[str, Any]) -> VesselBase:
        """Convert database vessel to VesselBase object."""
        from src.models.vessel import VesselType, Cargo
        
        try:
            vessel_type = VesselType(vessel_data.get('type', 'tanker').lower())
            
            # Get cargoes
            cargoes = []
            vessel_id = vessel_data.get('id')
            if vessel_id:
                try:
                    cargo_data_list = self.db.get_cargoes_by_vessel(vessel_id)
                    for cargo_data in cargo_data_list:
                        cargo = Cargo.from_dict({
                            'product': cargo_data.get('product', 'Unknown'),
                            'volume': float(cargo_data.get('volume', 0) or 0),
                            'is_loading': bool(cargo_data.get('is_loading', True))
                        })
                        cargoes.append(cargo)
                except Exception as e:
                    logger.warning(f"Failed to load cargoes for vessel {vessel_id}: {e}")
            
            vessel = VesselBase(
                id=str(vessel_data.get('id')),
                name=vessel_data.get('name', 'Unknown Vessel'),
                vessel_type=vessel_type,
                length=150.0,
                beam=25.0,
                draft=10.0,
                deadweight=50000.0,
                cargoes=cargoes,
                status=vessel_data.get('status', 'APPROACHING'),
                priority=1,
                capacity=vessel_data.get('total_cargo_volume', 50000),
                metadata={
                    'source': 'database_vessel',
                    'database_id': vessel_data.get('id')
                }
            )
            
            return vessel
            
        except Exception as e:
            logger.error(f"Error converting database vessel to vessel: {e}")
            raise


