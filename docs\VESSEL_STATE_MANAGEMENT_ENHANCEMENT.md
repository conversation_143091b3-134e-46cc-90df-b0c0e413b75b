# Vessel State Management Enhancement

## Industry Best Practices Analysis

### Current Simple State Model ❌
```
NOMINATED → ACTIVE → SCHEDULED → CANCELLED
```

### Recommended Industry Standard State Model ✅

## Comprehensive State Definitions

### 1. **Pre-Scheduling States**
| State | Description | Triggers | Next States |
|-------|-------------|----------|-------------|
| `DRAFT` | Form being filled | User starts nomination | `SUBMITTED`, `ABANDONED` |
| `SUBMITTED` | Awaiting initial review | Form submitted | `UNDER_REVIEW`, `REJECTED` |
| `UNDER_REVIEW` | Business rules validation | Auto/manual review | `APPROVED`, `REJECTED`, `NEEDS_INFO` |
| `NEEDS_INFO` | Additional info required | Validation issues | `UNDER_REVIEW`, `REJECTED` |
| `APPROVED` | Ready for optimization | Passes all checks | `SCHEDULED`, `CANCELLED` |

### 2. **Scheduling States**
| State | Description | Triggers | Next States |
|-------|-------------|----------|-------------|
| `SCHEDULED` | Assigned time/jetty | Optimization complete | `CONFIRMED`, `RESCHEDULED`, `CANCELLED` |
| `RESCHEDULED` | Schedule being modified | Manual/auto changes | `SCHEDULED`, `CANCELLED` |
| `CONFIRMED` | Vessel acknowledged | ETA confirmation | `APPROACHING`, `RESCHEDULED` |

### 3. **Operational States**
| State | Description | Triggers | Next States |
|-------|-------------|----------|-------------|
| `APPROACHING` | En route to terminal | Vessel tracking | `ARRIVED`, `DELAYED` |
| `DELAYED` | Behind schedule | ETA updates | `APPROACHING`, `RESCHEDULED` |
| `ARRIVED` | At terminal | AIS/manual update | `WAITING`, `BERTHED` |
| `WAITING` | Waiting for berth | Jetty not ready | `BERTHED`, `RESCHEDULED` |
| `BERTHED` | At assigned jetty | Mooring complete | `IN_PROGRESS` |

### 4. **Execution States**
| State | Description | Triggers | Next States |
|-------|-------------|----------|-------------|
| `IN_PROGRESS` | Operations active | Cargo ops started | `COMPLETED`, `SUSPENDED` |
| `SUSPENDED` | Operations paused | Weather/equipment | `IN_PROGRESS`, `COMPLETED` |
| `COMPLETED` | Operations finished | Cargo ops done | `DEPARTED` |
| `DEPARTED` | Left terminal | Vessel tracking | `ARCHIVED` |

### 5. **Terminal States**
| State | Description | Triggers | Next States |
|-------|-------------|----------|-------------|
| `CANCELLED` | Nomination cancelled | User/system action | `ARCHIVED` |
| `REJECTED` | Not approved | Business rules fail | `ARCHIVED` |
| `ARCHIVED` | Historical record | Final cleanup | None |

## Implementation Strategy

### Phase 1: Enhanced Core States (Immediate)
```python
class VesselStatus(Enum):
    # Pre-scheduling
    SUBMITTED = "SUBMITTED"      # Just submitted
    UNDER_REVIEW = "UNDER_REVIEW"  # Being validated
    APPROVED = "APPROVED"        # Ready for scheduling
    
    # Scheduling
    SCHEDULED = "SCHEDULED"      # Time/jetty assigned
    CONFIRMED = "CONFIRMED"      # Vessel confirmed ETA
    
    # Operational
    APPROACHING = "APPROACHING"   # En route
    ARRIVED = "ARRIVED"          # At terminal
    BERTHED = "BERTHED"          # At jetty
    IN_PROGRESS = "IN_PROGRESS"  # Operations active
    COMPLETED = "COMPLETED"      # Operations done
    DEPARTED = "DEPARTED"        # Left terminal
    
    # Terminal
    CANCELLED = "CANCELLED"      # Cancelled
    REJECTED = "REJECTED"        # Rejected
```

### Phase 2: Advanced States (Future)
- `RESCHEDULED`, `DELAYED`, `WAITING`, `SUSPENDED`
- `NEEDS_INFO`, `DRAFT`
- Integration with external systems (AIS, weather, etc.)

## State Transition Rules

### Validation Matrix
```python
ALLOWED_TRANSITIONS = {
    'SUBMITTED': ['UNDER_REVIEW', 'REJECTED'],
    'UNDER_REVIEW': ['APPROVED', 'REJECTED', 'NEEDS_INFO'],
    'APPROVED': ['SCHEDULED', 'CANCELLED'],
    'SCHEDULED': ['CONFIRMED', 'CANCELLED', 'APPROACHING'],
    'CONFIRMED': ['APPROACHING', 'SCHEDULED'],
    'APPROACHING': ['ARRIVED', 'DELAYED'],
    'ARRIVED': ['BERTHED', 'WAITING'],
    'BERTHED': ['IN_PROGRESS'],
    'IN_PROGRESS': ['COMPLETED', 'SUSPENDED'],
    'COMPLETED': ['DEPARTED'],
    # Terminal states
    'DEPARTED': ['ARCHIVED'],
    'CANCELLED': ['ARCHIVED'],
    'REJECTED': ['ARCHIVED']
}
```

## Business Logic Integration

### 1. **Automatic State Transitions**
```python
class StateTransitionService:
    def auto_transition_on_optimization(self, vessel_id):
        # APPROVED → SCHEDULED when optimizer assigns
        
    def auto_transition_on_ais_update(self, vessel_id, position):
        # APPROACHING → ARRIVED when in terminal area
        
    def auto_transition_on_time(self):
        # SCHEDULED → APPROACHING based on ETA
```

### 2. **State-Based Business Rules**
```python
def get_available_vessels_for_optimization():
    return vessels.filter(status__in=['APPROVED', 'CONFIRMED'])

def get_vessels_requiring_attention():
    return vessels.filter(status__in=['DELAYED', 'WAITING', 'NEEDS_INFO'])
```

## Database Schema Enhancement

### Enhanced Nominations Table
```sql
ALTER TABLE nominations ADD COLUMN sub_status VARCHAR(50);
ALTER TABLE nominations ADD COLUMN state_changed_at TIMESTAMP;
ALTER TABLE nominations ADD COLUMN state_changed_by VARCHAR(100);
ALTER TABLE nominations ADD COLUMN state_reason TEXT;

-- State transition history
CREATE TABLE nomination_state_history (
    id SERIAL PRIMARY KEY,
    nomination_id INTEGER REFERENCES nominations(id),
    old_status VARCHAR(50),
    new_status VARCHAR(50),
    sub_status VARCHAR(50),
    reason TEXT,
    changed_by VARCHAR(100),
    changed_at TIMESTAMP DEFAULT NOW()
);
```

## API Enhancements

### State Transition Endpoints
```python
@app.post("/api/nominations/{id}/transition")
async def transition_nomination_state(
    id: int, 
    transition: StateTransition
):
    # Validate transition is allowed
    # Update state with reason and audit
    # Trigger side effects (notifications, etc.)
```

### State-Based Filtering
```python
@app.get("/api/vessels")
async def get_vessels(
    status: List[str] = Query(None),
    stage: str = Query(None)  # pre-scheduling, scheduling, operational
):
    # Filter vessels by multiple states or stage
```

## Benefits of Enhanced State Management

### 1. **Operational Clarity**
- Clear understanding of where each vessel is in the process
- Better communication between departments
- Reduced confusion and errors

### 2. **Business Intelligence**
- Detailed analytics on process bottlenecks
- Performance metrics by state
- Predictive analytics on delays

### 3. **Integration Ready**
- Easy integration with external systems
- Webhook triggers on state changes
- Event-driven architecture support

### 4. **Compliance & Auditing**
- Complete audit trail of all changes
- Regulatory compliance support
- Historical reporting capabilities

### 5. **User Experience**
- Clear status indicators in UI
- Automated notifications
- Predictive scheduling

## Implementation Phases

### Phase 1 (Immediate) - Core States
- Implement basic enhanced states
- Add state transition validation
- Update UI to show detailed status

### Phase 2 (Short-term) - Business Logic
- Add automatic state transitions
- Implement business rule validation
- Add state-based notifications

### Phase 3 (Medium-term) - Advanced Features
- Add sub-statuses for detailed tracking
- Implement predictive state transitions
- Add external system integrations

### Phase 4 (Long-term) - AI/ML Integration
- Predictive delay detection
- Automated optimization based on states
- Machine learning for process improvement

## Migration Strategy

### Backward Compatibility
```python
# Legacy status mapping
LEGACY_STATUS_MAPPING = {
    'ACTIVE': 'APPROVED',
    'SCHEDULED': 'SCHEDULED', 
    'CANCELLED': 'CANCELLED'
}

def migrate_existing_nominations():
    # Convert old statuses to new system
    # Preserve existing functionality
```

This enhanced state management system provides the versatility you're asking about while following maritime industry best practices. It supports multiple states, complex transitions, and can grow with your system's needs.
