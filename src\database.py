"""
PostgreSQL database implementation for Jettyplanner.
This provides all database operations using SQLAlchemy and PostgreSQL.
"""

import os
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any

from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from .db.session import get_db_session, create_tables
from .db.models import (
    Terminal, Setting, Assignment, Vessel, Jetty, Tank, Pump, Surveyor,
    Cargo, AssignmentChange, Nomination, MLPredictionLog, PlanningMetrics,
    ChangeAnalysis, PerformanceAlert, VesselRegistry, VesselVisit, VesselAISData,
    ETAHistory
)
from .services.analytics_service import AnalyticsService

logger = logging.getLogger(__name__)


def _parse_datetime_robust(val):
    """Delegate to shared normalizer; keep name for backward compatibility."""
    try:
        from .utils.datetime_utils import normalize_iso_to_utc_microseconds
        return normalize_iso_to_utc_microseconds(val)
    except Exception:
        return None


class Database:
    """
    PostgreSQL database implementation for Jettyplanner.
    """

    def __init__(self):
        """Initialize the database and create tables if needed."""
        logger.info("Initializing PostgreSQL database")
        self.init_db()

    def get_session(self) -> Session:
        """Get a new database session."""
        return get_db_session()

    def init_db(self):
        """Initialize database tables and default data."""
        try:
            # Create all tables
            create_tables()
            
            with self.get_session() as session:
                # Ensure Terneuzen terminal exists
                terminal = session.query(Terminal).filter_by(id='TNZN').first()
                if not terminal:
                    terminal = Terminal(
                        id='TNZN',
                        name='Evos Terneuzen',
                        location_lat=51.3294,
                        location_lon=3.8091,
                        total_capacity_cbm=537000,
                        number_of_tanks=42,
                        draft_meters=15.0,
                        operational_since=datetime(2005, 1, 1),
                        vessel_berths=3,
                        barge_berths=3,
                        timezone='Europe/Brussels',
                        currency='EUR',
                        is_active=True
                    )
                    session.add(terminal)
                    session.commit()
                    logger.info("Created Terneuzen terminal")

                # Insert default jetties if they don't exist
                jetties_count = session.query(func.count(Jetty.id)).filter_by(terminal_id='TNZN').scalar()
                if jetties_count == 0:
                    jetties_data = [
                        ('Jetty 1', 'VESSEL', 'seagoing', 1000, 60000, 57, 236, 34.0, 12.8, 'Multiple products', 2000),
                        ('Jetty 2', 'BARGE', 'inland', 1000, 7000, 57, 135, 17.0, 4.5, 'Chemicals/Hydrocarbons', 800),
                        ('Jetty 3', 'VESSEL', 'both', 1000, 15000, 85, 135, 22.0, 12.8, 'Minerals/Benzene', 1800),
                        ('Jetty 4', 'BARGE', 'inland', 1000, 9000, 85, 135, 22.0, 4.4, 'Minerals only', 600),
                        ('Jetty 5', 'VESSEL', 'seagoing', 1000, 150000, 105, 275, 50.0, 15.0, 'Minerals only', 4000),
                        ('Jetty 6', 'VESSEL', 'both', 1000, 20000, 105, 150, 25.0, 9.9, 'Minerals/Butane', 1200)
                    ]
                    
                    for jetty_data in jetties_data:
                        jetty = Jetty(
                            terminal_id='TNZN',
                            name=jetty_data[0],
                            type=jetty_data[1],
                            vessel_type_restriction=jetty_data[2],
                            min_dwt=jetty_data[3],
                            max_dwt=jetty_data[4],
                            min_loa=jetty_data[5],
                            max_loa=jetty_data[6],
                            max_beam=jetty_data[7],
                            max_draft=jetty_data[8],
                            primary_use=jetty_data[9],
                            max_flow_rate=jetty_data[10],
                            is_operational=True
                        )
                        session.add(jetty)
                    
                    session.commit()
                    logger.info("Created default jetties")

                # Insert default settings if they don't exist
                settings_count = session.query(func.count(Setting.key)).filter_by(terminal_id='TNZN').scalar()
                if settings_count == 0:
                    default_settings = [
                        ('terminal_name', 'Evos Terneuzen', 'terminal', False),
                        ('terminal_location_lat', '51.3294', 'terminal', False),
                        ('terminal_location_lon', '3.8091', 'terminal', False),
                        ('time_zone', 'Europe/Brussels', 'preferences', False),
                        ('date_format', 'DD-MM-YYYY', 'preferences', False),
                        ('language', 'nl-NL', 'preferences', False),
                        ('dark_mode', 'false', 'preferences', False),
                        ('auto_refresh', 'true', 'preferences', False),
                        ('refresh_interval', '30', 'preferences', False),
                        ('aisstream_api_key', '501e28080107bf1d5a3d4e10380cfb6af87cd357', 'api_keys', True),
                        ('weather_api_key', '', 'api_keys', True),
                        ('claude_api_key', '', 'api_keys', True),
                        ('weather_api_provider', 'openmeteo', 'weather', False),
                        ('weather_display_units', 'metric', 'weather', False),
                        ('weather_refresh_interval', '15', 'weather', False),
                        ('show_wind_alerts', 'true', 'weather', False),
                        ('wind_caution_threshold', '12', 'weather', False),
                        ('wind_danger_threshold', '17', 'weather', False),
                        ('show_12h_forecast', 'true', 'weather', False),
                        ('solver_time_limit', '60', 'solver', False),
                        ('solver_strategy', 'AUTOMATIC', 'solver', False),
                        ('parallel_solving', 'true', 'solver', False),
                        ('enable_email_notifications', 'false', 'notifications', False),
                        ('notify_on_vessel_arrival', 'true', 'notifications', False),
                        ('notify_on_vessel_departure', 'true', 'notifications', False),
                        ('notify_on_assignment_change', 'true', 'notifications', False),
                        ('notify_on_weather_alert', 'true', 'notifications', False)
                    ]
                    
                    for key, value, category, is_sensitive in default_settings:
                        setting = Setting(
                            key=key,
                            terminal_id='TNZN',
                            value=value,
                            category=category,
                            is_sensitive=is_sensitive
                        )
                        session.add(setting)
                    
                    session.commit()
                    logger.info("Created default settings")

                # Seed tanks if none exist for terminal
                tanks_count = session.query(func.count(Tank.id)).filter_by(terminal_id='TNZN').scalar()
                if tanks_count == 0:
                    # Categories aligned with business rules: hydrocarbons/naphtha, benzene, acrylonitrile,
                    # propylene_oxide, minerals, butane, chemicals.
                    seed_tanks = [
                        # Hydrocarbons/Naphtha
                        ("TNK-001", "fixed_roof", 30000, 18000, "hydrocarbons"),
                        ("TNK-002", "fixed_roof", 30000, 12000, "hydrocarbons"),
                        ("TNK-003", "fixed_roof", 25000, 0, ""),  # empty, available
                        # Benzene
                        ("TNK-101", "floating_roof", 35000, 28000, "benzene"),
                        ("TNK-102", "floating_roof", 30000, 5000, "benzene"),
                        # Acrylonitrile (Jetty 1 only in rules)
                        ("TNK-201", "fixed_roof", 20000, 8000, "acrylonitrile"),
                        # Propylene Oxide (Jetty 1/2 per rules)
                        ("TNK-301", "fixed_roof", 20000, 10000, "propylene_oxide"),
                        # Minerals
                        ("TNK-401", "cone_roof", 80000, 60000, "minerals"),
                        ("TNK-402", "cone_roof", 80000, 20000, "minerals"),
                        ("TNK-403", "cone_roof", 90000, 0, ""),  # empty minerals tank shell
                        ("TNK-404", "cone_roof", 40000, 35000, "minerals"),
                        # Butane (Jetty 6 per rules)
                        ("TNK-501", "sphere", 15000, 9000, "butane"),
                        # General chemicals
                        ("TNK-601", "fixed_roof", 25000, 12000, "chemicals"),
                    ]

                    for name, ttype, capacity, level, product in seed_tanks:
                        tank = Tank(
                            terminal_id='TNZN',
                            name=name,
                            type=ttype,
                            capacity=float(capacity),
                            current_level=float(level),
                            # Use empty string to represent available/empty; frontend maps '' -> null
                            product_type=product or ""
                        )
                        session.add(tank)
                    session.commit()
                    logger.info("Seeded default tanks")
                    
            logger.info("Database initialization completed successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database: {str(e)}")
            raise

    # ---- ETA and Visit helpers ----
    def log_eta_update(
        self,
        *,
        source: str,
        eta: Optional[datetime],
        confidence: Optional[int] = None,
        vessel_registry_id: Optional[int] = None,
        visit_id: Optional[int] = None,
        runtime_vessel_id: Optional[str] = None,
        previous_eta: Optional[datetime] = None,
        context: Optional[str] = None,
    ) -> Optional[int]:
        """Log an ETA update event to ETAHistory.

        Provide at least one of vessel_registry_id, visit_id, or runtime_vessel_id.
        """
        if not (vessel_registry_id or visit_id or runtime_vessel_id):
            return None
        try:
            with self.get_session() as session:
                rec = ETAHistory(
                    vessel_registry_id=vessel_registry_id,
                    visit_id=visit_id,
                    runtime_vessel_id=runtime_vessel_id,
                    eta=eta,
                    previous_eta=previous_eta,
                    source=source,
                    confidence=confidence,
                    context=context,
                    created_at=datetime.utcnow(),
                )
                session.add(rec)
                session.commit()
                session.refresh(rec)
                return rec.id
        except Exception:
            return None

    def get_latest_visit_for_vessel(self, vessel_registry_id: int) -> Optional[Dict[str, Any]]:
        with self.get_session() as session:
            v = (
                session.query(VesselVisit)
                .filter_by(vessel_id=vessel_registry_id)
                .order_by(VesselVisit.created_at.desc())
                .first()
            )
            if not v:
                return None
            return {
                'id': v.id,
                'vessel_id': v.vessel_id,
                'eta': v.eta.isoformat() if v.eta else None,
                'calculated_eta': v.calculated_eta.isoformat() if v.calculated_eta else None,
                'eta_confidence': v.eta_confidence,
                'eta_source': v.eta_source,
                'status': v.status,
            }

    def get_eta_history(
        self,
        *,
        vessel_registry_id: Optional[int] = None,
        visit_id: Optional[int] = None,
        runtime_vessel_id: Optional[str] = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        with self.get_session() as session:
            q = session.query(ETAHistory)
            if vessel_registry_id:
                q = q.filter_by(vessel_registry_id=vessel_registry_id)
            if visit_id:
                q = q.filter_by(visit_id=visit_id)
            if runtime_vessel_id:
                q = q.filter_by(runtime_vessel_id=runtime_vessel_id)
            recs = q.order_by(ETAHistory.created_at.desc()).limit(limit).all()
            return [
                {
                    'id': r.id,
                    'vessel_registry_id': r.vessel_registry_id,
                    'visit_id': r.visit_id,
                    'runtime_vessel_id': r.runtime_vessel_id,
                    'eta': r.eta.isoformat() if r.eta else None,
                    'previous_eta': r.previous_eta.isoformat() if r.previous_eta else None,
                    'source': r.source,
                    'confidence': r.confidence,
                    'context': r.context,
                    'created_at': r.created_at.isoformat() if r.created_at else None,
                }
                for r in recs
            ]

    def calculate_daily_planning_metrics(self, target_date: datetime.date, terminal_id: str) -> bool:
        """Adapter for background service; calculates and stores daily metrics."""
        try:
            with self.get_session() as session:
                AnalyticsService(session).calculate_daily_metrics(target_date, terminal_id)
            return True
        except Exception:
            return False

    # Assignment methods
    def get_assignments(self, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all assignments for a terminal."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            assignments = session.query(Assignment).filter_by(terminal_id=terminal_id).order_by(Assignment.start_time).all()
            return [
                {
                    'id': a.id,
                    'terminal_id': a.terminal_id,
                    'vessel_id': a.vessel_id,
                    'vessel_name': a.vessel_name,
                    'vessel_type': a.vessel_type,
                    'jetty_name': a.jetty_name,
                    'cargo_product': a.cargo_product,
                    'cargo_volume': a.cargo_volume,
                    'start_time': a.start_time.isoformat() if a.start_time else None,
                    'end_time': a.end_time.isoformat() if a.end_time else None,
                    'status': a.status,
                    'vessel_db_id': a.vessel_db_id,
                    'visit_id': a.visit_id,
                    'nomination_reference': a.nomination_reference,
                    'assignment_type': a.assignment_type,
                    'lock_status': getattr(a, 'lock_status', 'UNLOCKED'),
                    'lock_reason': getattr(a, 'lock_reason', None),
                    'locked_by': getattr(a, 'locked_by', None),
                    'locked_at': a.locked_at.isoformat() if getattr(a, 'locked_at', None) else None,
                }
                for a in assignments
            ]

    def add_assignment(self, assignment: Dict[str, Any], terminal_id: Optional[str] = None) -> int:
        """Add a new assignment."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            new_assignment = Assignment(
                terminal_id=terminal_id,
                vessel_id=assignment['vessel_id'],
                vessel_name=assignment['vessel_name'],
                vessel_type=assignment['vessel_type'],
                jetty_name=assignment['jetty_name'],
                cargo_product=assignment.get('cargo_product'),
                cargo_volume=assignment.get('cargo_volume'),
                start_time=_parse_datetime_robust(assignment['start_time']),
                end_time=_parse_datetime_robust(assignment['end_time']),
                status=assignment.get('status', 'SCHEDULED'),
                vessel_db_id=assignment.get('vessel_db_id'),
                visit_id=assignment.get('visit_id'),
                nomination_reference=assignment.get('nomination_reference'),
                assignment_type=assignment.get('assignment_type', 'SCHEDULED'),
                lock_status=assignment.get('lock_status', 'UNLOCKED'),
                lock_reason=assignment.get('lock_reason'),
                locked_by=assignment.get('locked_by'),
                locked_at=_parse_datetime_robust(assignment.get('locked_at')) if assignment.get('locked_at') else None,
            )
            session.add(new_assignment)
            session.commit()
            session.refresh(new_assignment)
            return new_assignment.id

    def update_assignment(self, assignment_id: int, assignment: Dict[str, Any]) -> bool:
        """Update an existing assignment."""
        with self.get_session() as session:
            try:
                pk = int(assignment_id)
            except Exception:
                # Forcefully coerce to int or fail fast
                return False
            existing_assignment = session.get(Assignment, pk)
            if not existing_assignment:
                return False
                
            # Only update fields that are provided (partial update support)
            if 'vessel_id' in assignment:
                existing_assignment.vessel_id = assignment['vessel_id']
            if 'vessel_name' in assignment:
                existing_assignment.vessel_name = assignment['vessel_name']
            if 'vessel_type' in assignment:
                existing_assignment.vessel_type = assignment['vessel_type']
            if 'jetty_name' in assignment:
                existing_assignment.jetty_name = assignment['jetty_name']
            if 'cargo_product' in assignment:
                existing_assignment.cargo_product = assignment.get('cargo_product')
            if 'cargo_volume' in assignment:
                existing_assignment.cargo_volume = assignment.get('cargo_volume')
            # Normalize incoming times; if parsing fails, keep existing (avoid NULLs)
            if 'start_time' in assignment:
                new_start = _parse_datetime_robust(assignment['start_time'])
                if new_start is not None:
                    existing_assignment.start_time = new_start
            if 'end_time' in assignment:
                new_end = _parse_datetime_robust(assignment['end_time'])
                if new_end is not None:
                    existing_assignment.end_time = new_end
            if 'status' in assignment:
                existing_assignment.status = assignment.get('status', 'SCHEDULED')
            # New linkage fields
            if 'vessel_db_id' in assignment:
                existing_assignment.vessel_db_id = assignment.get('vessel_db_id')
            if 'visit_id' in assignment:
                existing_assignment.visit_id = assignment.get('visit_id')
            if 'nomination_reference' in assignment:
                existing_assignment.nomination_reference = assignment.get('nomination_reference')
            if 'assignment_type' in assignment:
                existing_assignment.assignment_type = assignment.get('assignment_type') or 'SCHEDULED'
            # Lock fields (optional)
            if 'lock_status' in assignment:
                existing_assignment.lock_status = assignment.get('lock_status') or 'UNLOCKED'
                # If locking (not UNLOCKED) and no explicit timestamp provided, set now
                if existing_assignment.lock_status != 'UNLOCKED' and not assignment.get('locked_at'):
                    from datetime import datetime as _dt
                    existing_assignment.locked_at = _dt.utcnow()
            if 'lock_reason' in assignment:
                existing_assignment.lock_reason = assignment.get('lock_reason')
            if 'locked_by' in assignment:
                existing_assignment.locked_by = assignment.get('locked_by')
            if 'locked_at' in assignment:
                parsed = _parse_datetime_robust(assignment.get('locked_at'))
                if parsed is not None:
                    existing_assignment.locked_at = parsed
            existing_assignment.updated_at = datetime.utcnow()
            
            session.commit()
            return True

    def get_assignment(self, assignment_id: int, terminal_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get a single assignment by ID."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
        
        with self.get_session() as session:
            try:
                pk = int(assignment_id)
            except Exception:
                return None
            
            assignment = session.get(Assignment, pk)
            if not assignment or assignment.terminal_id != terminal_id:
                return None
            
            return {
                'id': assignment.id,
                'terminal_id': assignment.terminal_id,
                'vessel_id': assignment.vessel_id,
                'vessel_name': assignment.vessel_name,
                'vessel_type': assignment.vessel_type,
                'jetty_name': assignment.jetty_name,
                'cargo_product': assignment.cargo_product,
                'cargo_volume': assignment.cargo_volume,
                'start_time': assignment.start_time,
                'end_time': assignment.end_time,
                'status': assignment.status,
                'vessel_db_id': assignment.vessel_db_id,
                'visit_id': assignment.visit_id,
                'nomination_reference': assignment.nomination_reference,
                'assignment_type': assignment.assignment_type,
                'created_at': assignment.created_at,
                'updated_at': assignment.updated_at,
                'lock_status': getattr(assignment, 'lock_status', 'UNLOCKED'),
                'lock_reason': getattr(assignment, 'lock_reason', None),
                'locked_by': getattr(assignment, 'locked_by', None),
                'locked_at': getattr(assignment, 'locked_at', None),
            }

    def delete_assignment(self, assignment_id: int) -> bool:
        """Delete an assignment."""
        with self.get_session() as session:
            try:
                pk = int(assignment_id)
            except Exception:
                return False
            assignment = session.get(Assignment, pk)
            if assignment:
                session.delete(assignment)
                session.commit()
                return True
            return False

    def clear_assignments(self, terminal_id: Optional[str] = None) -> bool:
        """Delete all assignments for a terminal."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            session.query(Assignment).filter_by(terminal_id=terminal_id).delete()
            session.commit()
            return True

    def replace_assignments(self, assignments: List[Dict[str, Any]], terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Replace all assignments for a terminal with the provided list.
        
        Returns: List of assignment dictionaries with database IDs included.
        """
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        created_assignments = []
        with self.get_session() as session:
            # Clear existing
            session.query(Assignment).filter_by(terminal_id=terminal_id).delete()
            
            # Insert new and collect database IDs
            for assignment_data in assignments:
                assignment = Assignment(
                    terminal_id=terminal_id,
                    vessel_id=assignment_data['vessel_id'],
                    vessel_name=assignment_data['vessel_name'],
                    vessel_type=assignment_data['vessel_type'],
                    jetty_name=assignment_data['jetty_name'],
                    cargo_product=assignment_data.get('cargo_product'),
                    cargo_volume=assignment_data.get('cargo_volume'),
                    start_time=_parse_datetime_robust(assignment_data['start_time']),
                    end_time=_parse_datetime_robust(assignment_data['end_time']),
                    status=assignment_data.get('status', 'SCHEDULED'),
                    vessel_db_id=assignment_data.get('vessel_db_id'),
                    visit_id=assignment_data.get('visit_id'),
                    nomination_reference=assignment_data.get('nomination_reference'),
                    assignment_type=assignment_data.get('assignment_type', 'SCHEDULED'),
                )
                session.add(assignment)
                session.flush()  # Ensure ID is assigned
                
                # Create result with database ID
                assignment_with_id = assignment_data.copy()
                assignment_with_id.setdefault('assignment_type', assignment.assignment_type)
                assignment_with_id['id'] = assignment.id
                created_assignments.append(assignment_with_id)
            
            session.commit()
            return created_assignments

    # Assignment change logging methods
    def log_assignment_change(self, assignment_id: int, old_start_time, old_end_time,
                             new_start_time, new_end_time, reason: str,
                             vessel_id: Optional[str] = None, vessel_name: Optional[str] = None,
                             jetty_name: Optional[str] = None, changed_by: Optional[str] = None,
                             terminal_id: Optional[str] = None) -> int:
        """Insert an assignment change entry for audit trail."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            change = AssignmentChange(
                terminal_id=terminal_id,
                assignment_id=assignment_id,
                vessel_id=vessel_id,
                vessel_name=vessel_name,
                jetty_name=jetty_name,
                old_start_time=old_start_time,
                old_end_time=old_end_time,
                new_start_time=new_start_time,
                new_end_time=new_end_time,
                reason=reason,
                changed_by=changed_by
            )
            session.add(change)
            session.commit()
            session.refresh(change)
            return change.id

    def get_assignment_changes(self, limit: int = 50, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get recent assignment changes."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            changes = (session.query(AssignmentChange)
                      .filter_by(terminal_id=terminal_id)
                      .order_by(AssignmentChange.changed_at.desc(), AssignmentChange.id.desc())
                      .limit(limit)
                      .all())
            
            return [
                {
                    'id': c.id,
                    'terminal_id': c.terminal_id,
                    'assignment_id': c.assignment_id,
                    'vessel_id': c.vessel_id,
                    'vessel_name': c.vessel_name,
                    'jetty_name': c.jetty_name,
                    'old_start_time': c.old_start_time.isoformat() if c.old_start_time else None,
                    'old_end_time': c.old_end_time.isoformat() if c.old_end_time else None,
                    'new_start_time': c.new_start_time.isoformat() if c.new_start_time else None,
                    'new_end_time': c.new_end_time.isoformat() if c.new_end_time else None,
                    'reason': c.reason,
                    'changed_by': c.changed_by,
                    'changed_at': c.changed_at.isoformat() if c.changed_at else None
                }
                for c in changes
            ]

    def find_assignments_for_status_transition(self, statuses: List[str], where_start_le: Optional[datetime] = None, 
                                             where_end_gt: Optional[datetime] = None, where_end_le: Optional[datetime] = None,
                                             limit: int = 100, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Find assignments that match criteria for status transitions."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            query = session.query(Assignment).filter_by(terminal_id=terminal_id)
            
            # Filter by status
            if statuses:
                query = query.filter(Assignment.status.in_(statuses))
            
            # Filter by start time
            if where_start_le is not None:
                query = query.filter(Assignment.start_time <= where_start_le)
            
            # Filter by end time (greater than)
            if where_end_gt is not None:
                query = query.filter(Assignment.end_time > where_end_gt)
                
            # Filter by end time (less than or equal)
            if where_end_le is not None:
                query = query.filter(Assignment.end_time <= where_end_le)
            
            # Order by start_time and limit
            assignments = query.order_by(Assignment.start_time).limit(limit).all()
            
            return [
                {
                    'id': a.id,
                    'terminal_id': a.terminal_id,
                    'vessel_id': a.vessel_id,
                    'vessel_name': a.vessel_name,
                    'vessel_type': a.vessel_type,
                    'jetty_name': a.jetty_name,
                    'cargo_product': a.cargo_product,
                    'cargo_volume': a.cargo_volume,
                    'start_time': a.start_time,
                    'end_time': a.end_time,
                    'status': a.status,
                    'vessel_db_id': a.vessel_db_id,
                    'visit_id': a.visit_id,
                    'nomination_reference': a.nomination_reference,
                    'assignment_type': a.assignment_type,
                }
                for a in assignments
            ]

    def acquire_advisory_lock(self, lock_key: int) -> bool:
        """Acquire a PostgreSQL advisory lock. Returns True if acquired, False if already held."""
        try:
            with self.get_session() as session:
                result = session.execute(func.pg_try_advisory_lock(lock_key)).scalar()
                return bool(result)
        except Exception as e:
            logger.warning(f"Failed to acquire advisory lock {lock_key}: {e}")
            return False

    def release_advisory_lock(self, lock_key: int) -> bool:
        """Release a PostgreSQL advisory lock."""
        try:
            with self.get_session() as session:
                result = session.execute(func.pg_advisory_unlock(lock_key)).scalar()
                return bool(result)
        except Exception as e:
            logger.warning(f"Failed to release advisory lock {lock_key}: {e}")
            return False

    def is_using_postgres(self) -> bool:
        """Check if the database backend is PostgreSQL."""
        return os.getenv('DB_BACKEND', '').lower() == 'postgres'

    def log_nominations_to_planned(self, assignments_with_ids: List[Dict[str, Any]], terminal_id: Optional[str] = None) -> int:
        """Log when nominations become planned assignments via optimization.
        
        Args:
            assignments_with_ids: List of assignment dicts that include database 'id' field
        """
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
        
        # For nomination to planned logging, we need to capture original nomination timestamps
        logged_count = 0
        with self.get_session() as session:
            for assignment_data in assignments_with_ids:
                try:
                    # Use the actual assignment database ID
                    assignment_id = assignment_data.get('id')
                    if not assignment_id:
                        logger.warning(f"Assignment data missing database ID: {assignment_data}")
                        continue
                    
                    # Try to find the original nomination data to capture timestamps
                    old_start_time = None
                    old_end_time = None
                    vessel_id = assignment_data.get('vessel_id')
                    
                    if vessel_id:
                        # Look for nomination with matching runtime_vessel_id
                        nomination = session.query(Nomination).filter_by(
                            terminal_id=terminal_id,
                            runtime_vessel_id=vessel_id
                        ).first()
                        
                        if nomination:
                            # Use nomination ETA as old start time
                            old_start_time = nomination.eta
                            # Use nomination ETD as old end time, fallback to ETA + estimated duration, then created_at
                            if nomination.etd:
                                old_end_time = nomination.etd
                            elif nomination.eta:
                                # Estimate ETD as ETA + 24 hours (reasonable default for terminal operations)
                                from datetime import timedelta
                                old_end_time = nomination.eta + timedelta(hours=24)
                            else:
                                old_end_time = nomination.created_at
                            
                            logger.debug(f"Found nomination timestamps for vessel {vessel_id}: ETA={old_start_time}, ETD={old_end_time}")
                        else:
                            logger.debug(f"No nomination found for vessel_id {vessel_id}, using fallback timestamps")
                            # Fallback: use current time as "nomination" time to avoid epoch timestamps
                            from datetime import datetime, timezone
                            fallback_time = datetime.now(timezone.utc)
                            old_start_time = fallback_time
                            old_end_time = fallback_time
                        
                    change = AssignmentChange(
                        terminal_id=terminal_id,
                        assignment_id=assignment_id,  # Use actual database assignment ID
                        vessel_id=assignment_data['vessel_id'],
                        vessel_name=assignment_data['vessel_name'],
                        jetty_name=assignment_data['jetty_name'],
                        old_start_time=old_start_time,  # Original nomination ETA
                        old_end_time=old_end_time,     # Original nomination ETD or created_at
                        new_start_time=_parse_datetime_robust(assignment_data['start_time']),
                        new_end_time=_parse_datetime_robust(assignment_data['end_time']),
                        reason="Vessel moved from nomination to planned via OR-tools optimization",
                        changed_by="System - OR-tools Optimizer"
                    )
                    session.add(change)
                    logged_count += 1
                except Exception as e:
                    vessel_id = assignment_data.get('vessel_id', 'unknown')
                    logger.warning(f"Failed to log nomination to planned transition for vessel {vessel_id}: {e}")
            
            if logged_count > 0:
                session.commit()
                logger.info(f"Logged {logged_count} vessels transitioning from nomination to planned")
            
            return logged_count

    # Vessel methods
    def get_vessels(self, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all vessels for a terminal."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            vessels = session.query(Vessel).filter_by(terminal_id=terminal_id).order_by(Vessel.name).all()
            result = []
            
            for vessel in vessels:
                # Get cargoes for this vessel
                cargoes = session.query(Cargo).filter_by(terminal_id=terminal_id, vessel_id=vessel.id).all()
                total_cargo_volume = sum(c.volume or 0 for c in cargoes)
                
                result.append({
                    'id': vessel.id,
                    'name': vessel.name,
                    'type': vessel.type,
                    'total_cargo_volume': total_cargo_volume,
                    'cargoes': [
                        {
                            'product': c.product,
                            'volume': c.volume,
                            'is_loading': c.is_loading
                        }
                        for c in cargoes
                    ]
                })
            
            return result

    def add_vessel_with_cargoes(self, vessel: Dict[str, Any], cargoes: List[Dict[str, Any]], 
                               terminal_id: Optional[str] = None) -> int:
        """Create a vessel in DB and insert its cargoes. Returns new vessel id."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            new_vessel = Vessel(
                terminal_id=terminal_id,
                name=vessel['name'],
                type=vessel['type']
            )
            session.add(new_vessel)
            session.flush()  # Get the ID without committing
            
            for cargo_data in cargoes or []:
                cargo = Cargo(
                    terminal_id=terminal_id,
                    vessel_id=new_vessel.id,
                    product=cargo_data.get('product', ''),
                    volume=float(cargo_data.get('volume', 0) or 0),
                    is_loading=bool(cargo_data.get('is_loading', False)),
                    connection_size=cargo_data.get('connection_size'),
                    vapor_return=bool(cargo_data.get('vapor_return', False)),
                    nitrogen_purge=bool(cargo_data.get('nitrogen_purge', False))
                )
                session.add(cargo)
            
            session.commit()
            session.refresh(new_vessel)
            return new_vessel.id

    def get_cargoes_by_vessel(self, vessel_id: int, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get cargoes for a specific vessel."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            cargoes = (session.query(Cargo)
                      .filter_by(terminal_id=terminal_id, vessel_id=vessel_id)
                      .order_by(Cargo.id)
                      .all())
            
            return [
                {
                    'id': c.id,
                    'product': c.product,
                    'volume': c.volume,
                    'is_loading': c.is_loading,
                    'connection_size': c.connection_size,
                    'vapor_return': c.vapor_return,
                    'nitrogen_purge': c.nitrogen_purge,
                    'created_at': c.created_at.isoformat() if c.created_at else None,
                    'updated_at': c.updated_at.isoformat() if c.updated_at else None
                }
                for c in cargoes
            ]

    def get_vessel(self, vessel_id: int, terminal_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get a single vessel by ID."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            try:
                pk = int(vessel_id)
            except Exception:
                return None
            
            vessel = session.query(Vessel).filter_by(id=pk, terminal_id=terminal_id).first()
            if not vessel:
                return None
            
            # Get cargoes for this vessel
            cargoes = session.query(Cargo).filter_by(terminal_id=terminal_id, vessel_id=vessel.id).all()
            total_cargo_volume = sum(c.volume or 0 for c in cargoes)
            
            return {
                'id': vessel.id,
                'name': vessel.name,
                'type': vessel.type,
                'terminal_id': vessel.terminal_id,
                'total_cargo_volume': total_cargo_volume,
                'cargoes': [
                    {
                        'product': c.product,
                        'volume': c.volume,
                        'is_loading': c.is_loading
                    }
                    for c in cargoes
                ],
                'created_at': vessel.created_at
            }

    def update_vessel(self, vessel_id: int, updates: Dict[str, Any], terminal_id: Optional[str] = None) -> bool:
        """Update an existing vessel."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            try:
                pk = int(vessel_id)
            except Exception:
                return False
            
            vessel = session.query(Vessel).filter_by(id=pk, terminal_id=terminal_id).first()
            if not vessel:
                return False
            
            # Update basic vessel fields
            if 'name' in updates:
                vessel.name = updates['name']
            if 'type' in updates:
                vessel.type = updates['type']
            # Note: Vessel model is simple and doesn't have status field in database
            # Status is typically managed in in-memory objects or separate tracking
            
            session.commit()
            return True

    def delete_vessel(self, vessel_id, terminal_id: Optional[str] = None) -> bool:
        """Delete a vessel and its cargoes, handling both database vessels (int IDs) and nomination vessels (string IDs).

        Returns True if a vessel row was deleted, False if not found.
        """
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
        with self.get_session() as session:
            # Try to find database vessel first (integer ID)
            vessel = None
            try:
                vessel_id_int = int(vessel_id)
                vessel = (session.query(Vessel)
                                  .filter_by(id=vessel_id_int, terminal_id=terminal_id)
                                  .first())
                if vessel:
                    # Remove dependent cargoes first for referential integrity
                    session.query(Cargo).filter_by(terminal_id=terminal_id, vessel_id=vessel.id).delete()
                    session.delete(vessel)
                    session.commit()
                    return True
            except (ValueError, TypeError):
                # Not an integer ID, continue to check for nomination vessels
                pass
            
            # If not found as database vessel, try to find as nomination vessel (string ID)
            nomination = (session.query(Nomination)
                               .filter_by(runtime_vessel_id=str(vessel_id), terminal_id=terminal_id)
                               .first())
            if nomination:
                session.delete(nomination)
                session.commit()
                return True
                
            return False

    # Vessel Registry (master) methods
    def find_or_create_vessel(self, *, imo: Optional[str] = None, mmsi: Optional[str] = None,
                               name: Optional[str] = None, vessel_data: Optional[Dict[str, Any]] = None) -> int:
        """Find a vessel by IMO/MMSI/name in the master registry or create a new one.

        Returns the registry vessel id.
        """
        vessel_data = vessel_data or {}
        with self.get_session() as session:
            query = session.query(VesselRegistry)
            # Strong match by IMO
            if imo:
                v = query.filter(VesselRegistry.imo == imo).first()
                if v:
                    return v.id
            # Next: MMSI
            if mmsi:
                v = query.filter(VesselRegistry.mmsi == mmsi).first()
                if v:
                    return v.id
            # Finally: name (exact, case-insensitive)
            if name:
                v = query.filter(func.lower(VesselRegistry.name) == func.lower(name)).first()
                if v:
                    return v.id

            v = VesselRegistry(
                imo=imo,
                mmsi=mmsi,
                name=name or vessel_data.get('name', 'UNKNOWN'),
                vessel_type=vessel_data.get('vessel_type') or vessel_data.get('type') or 'UNKNOWN',
                call_sign=vessel_data.get('call_sign'),
                vessel_subtype=vessel_data.get('vessel_subtype'),
                deadweight=vessel_data.get('deadweight'),
                gross_tonnage=vessel_data.get('gross_tonnage'),
                length_overall=vessel_data.get('length_overall'),
                beam=vessel_data.get('beam'),
                maximum_draft=vessel_data.get('maximum_draft'),
                flag_state=vessel_data.get('flag_state'),
                port_of_registry=vessel_data.get('port_of_registry'),
                owner=vessel_data.get('owner'),
                operator=vessel_data.get('operator'),
                manager=vessel_data.get('manager'),
                build_year=vessel_data.get('build_year'),
                shipyard=vessel_data.get('shipyard'),
                hull_number=vessel_data.get('hull_number'),
                status=vessel_data.get('status', 'ACTIVE'),
                is_blacklisted=bool(vessel_data.get('is_blacklisted', False)),
                data_source=vessel_data.get('data_source'),
                confidence_score=vessel_data.get('confidence_score', 100),
                last_ais_update=vessel_data.get('last_ais_update'),
                created_by=vessel_data.get('created_by'),
                notes=vessel_data.get('notes'),
                previous_names=vessel_data.get('previous_names'),
            )
            session.add(v)
            session.commit()
            session.refresh(v)
            return v.id

    def get_vessel_history(self, vessel_id: int) -> Dict[str, Any]:
        """Return basic vessel history: identity and counts of visits/ais records."""
        with self.get_session() as session:
            v = session.query(VesselRegistry).filter_by(id=vessel_id).first()
            if not v:
                return {}
            visits_count = session.query(func.count(VesselVisit.id)).filter_by(vessel_id=vessel_id).scalar() or 0
            ais_count = session.query(func.count(VesselAISData.id)).filter_by(vessel_id=vessel_id).scalar() or 0
            return {
                'id': v.id,
                'name': v.name,
                'imo': v.imo,
                'mmsi': v.mmsi,
                'status': v.status,
                'visits_count': visits_count,
                'ais_records_count': ais_count,
                'last_ais_update': v.last_ais_update.isoformat() if v.last_ais_update else None,
            }

    # Other entity methods
    # Nomination methods
    def add_nomination(self, nomination: Dict[str, Any], terminal_id: Optional[str] = None) -> int:
        """Persist a nomination and return its id."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
        with self.get_session() as session:
            new_nom = Nomination(
                terminal_id=terminal_id,
                runtime_vessel_id=nomination['runtime_vessel_id'],
                name=nomination['name'],
                vessel_type=nomination['vessel_type'],
                length=nomination.get('length'),
                beam=nomination.get('beam'),
                draft=nomination.get('draft'),
                deadweight=nomination.get('deadweight'),
                priority=nomination.get('priority', 0),
                capacity=nomination.get('capacity'),
                width=nomination.get('width'),
                customer=nomination.get('customer'),
                status=nomination.get('status', 'pending'),
                eta=nomination.get('eta'),
                etd=nomination.get('etd'),
                mmsi=nomination.get('mmsi'),
                imo=nomination.get('imo'),
                cargoes=nomination.get('cargoes'),
                extra_data=nomination.get('metadata'),
            )
            session.add(new_nom)
            session.commit()
            session.refresh(new_nom)
            return new_nom.id

    def get_nominations(self, terminal_id: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
        with self.get_session() as session:
            q = session.query(Nomination).filter_by(terminal_id=terminal_id)
            if status:
                q = q.filter_by(status=status)
            recs = q.order_by(Nomination.created_at.desc()).all()
            return [
                {
                    'id': n.id,
                    'terminal_id': n.terminal_id,
                    'runtime_vessel_id': n.runtime_vessel_id,
                    'name': n.name,
                    'vessel_type': n.vessel_type,
                    'length': n.length,
                    'beam': n.beam,
                    'draft': n.draft,
                    'deadweight': n.deadweight,
                    'priority': n.priority,
                    'capacity': n.capacity,
                    'width': n.width,
                    'customer': n.customer,
                    'status': n.status,
                    'eta': n.eta.isoformat() if n.eta else None,
                    'etd': n.etd.isoformat() if n.etd else None,
                    'mmsi': n.mmsi,
                    'imo': n.imo,
                    'cargoes': n.cargoes,
                    'metadata': n.extra_data,
                    'created_at': n.created_at.isoformat() if n.created_at else None,
                    'updated_at': n.updated_at.isoformat() if n.updated_at else None,
                }
                for n in recs
            ]

    def update_nomination(self, nomination_id: int, updates: Dict[str, Any]) -> bool:
        """Update a nomination with new data."""
        with self.get_session() as session:
            try:
                nomination = session.query(Nomination).filter_by(id=nomination_id).first()
                if not nomination:
                    return False
                
                # Update fields
                for key, value in updates.items():
                    if hasattr(nomination, key):
                        setattr(nomination, key, value)
                
                # Update timestamp
                nomination.updated_at = datetime.utcnow()
                
                session.commit()
                return True
                
            except Exception as e:
                session.rollback()
                raise e

    def update_nomination_by_runtime_id(self, runtime_vessel_id: str, updates: Dict[str, Any], terminal_id: Optional[str] = None) -> bool:
        """Update a nomination by its runtime vessel ID."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            try:
                nomination = session.query(Nomination).filter_by(
                    runtime_vessel_id=runtime_vessel_id,
                    terminal_id=terminal_id
                ).first()
                
                if not nomination:
                    return False
                
                # Update fields
                for key, value in updates.items():
                    if hasattr(nomination, key):
                        setattr(nomination, key, value)
                
                # Update timestamp
                nomination.updated_at = datetime.utcnow()
                
                session.commit()
                return True
                
            except Exception as e:
                session.rollback()
                raise e

    def delete_nomination(self, nomination_id: int) -> bool:
        """Delete a nomination."""
        with self.get_session() as session:
            try:
                nomination = session.query(Nomination).filter_by(id=nomination_id).first()
                if not nomination:
                    return False
                
                session.delete(nomination)
                session.commit()
                return True
                
            except Exception as e:
                session.rollback()
                raise e

    def delete_nomination_by_runtime_id(self, runtime_vessel_id: str, terminal_id: Optional[str] = None) -> bool:
        """Delete a nomination by its runtime vessel ID."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            try:
                nomination = session.query(Nomination).filter_by(
                    runtime_vessel_id=runtime_vessel_id,
                    terminal_id=terminal_id
                ).first()
                
                if not nomination:
                    return False
                
                session.delete(nomination)
                session.commit()
                return True
                
            except Exception as e:
                session.rollback()
                raise e

    def get_jetties(self, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all jetties for a terminal."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            jetties = session.query(Jetty).filter_by(terminal_id=terminal_id).order_by(Jetty.name).all()
            return [
                {
                    'id': j.id,
                    'name': j.name,
                    'type': j.type,
                    'vessel_type_restriction': j.vessel_type_restriction,
                    'min_dwt': j.min_dwt,
                    'max_dwt': j.max_dwt,
                    'min_loa': j.min_loa,
                    'max_loa': j.max_loa,
                    'max_beam': j.max_beam,
                    'max_draft': j.max_draft,
                    'primary_use': j.primary_use,
                    'max_flow_rate': j.max_flow_rate,
                    'is_operational': j.is_operational
                }
                for j in jetties
            ]

    def get_tanks(self, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all tanks for a terminal."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            tanks = session.query(Tank).filter_by(terminal_id=terminal_id).order_by(Tank.name).all()
            return [
                {
                    'id': t.id,
                    'name': t.name,
                    'type': t.type,
                    'capacity': t.capacity,
                    'current_level': t.current_level,
                    'product_type': t.product_type
                }
                for t in tanks
            ]

    def get_pumps(self, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all pumps for a terminal."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            pumps = session.query(Pump).filter_by(terminal_id=terminal_id).order_by(Pump.name).all()
            return [
                {
                    'id': p.id,
                    'name': p.name,
                    'type': p.type,
                    'flow_rate': p.flow_rate,
                    'status': p.status
                }
                for p in pumps
            ]

    def get_surveyors(self, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all surveyors for a terminal."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            surveyors = session.query(Surveyor).filter_by(terminal_id=terminal_id).order_by(Surveyor.name).all()
            return [
                {
                    'id': s.id,
                    'name': s.name,
                    'status': s.status
                }
                for s in surveyors
            ]

    # Update methods
    def update_tank_level(self, tank_id: int, new_level: float) -> bool:
        """Update tank level."""
        with self.get_session() as session:
            tank = session.query(Tank).filter_by(id=tank_id).first()
            if tank:
                tank.current_level = new_level
                tank.updated_at = datetime.utcnow()
                session.commit()
                return True
            return False

    def update_pump_status(self, pump_id: int, status: str) -> bool:
        """Update pump status."""
        with self.get_session() as session:
            pump = session.query(Pump).filter_by(id=pump_id).first()
            if pump:
                pump.status = status
                pump.updated_at = datetime.utcnow()
                session.commit()
                return True
            return False

    def update_surveyor_status(self, surveyor_id: int, status: str) -> bool:
        """Update surveyor status."""
        with self.get_session() as session:
            surveyor = session.query(Surveyor).filter_by(id=surveyor_id).first()
            if surveyor:
                surveyor.status = status
                surveyor.updated_at = datetime.utcnow()
                session.commit()
                return True
            return False

    # Settings methods
    def get_settings(self, category: Optional[str] = None, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all settings or settings for a specific category."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            query = session.query(Setting).filter_by(terminal_id=terminal_id)
            if category:
                query = query.filter_by(category=category)
            
            settings = query.all()
            return [
                {
                    'key': s.key,
                    'terminal_id': s.terminal_id,
                    'value': s.value,
                    'category': s.category,
                    'is_sensitive': s.is_sensitive
                }
                for s in settings
            ]

    def get_setting(self, key: str, terminal_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get a single setting by key."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            setting = session.query(Setting).filter_by(key=key, terminal_id=terminal_id).first()
            if setting:
                return {
                    'key': setting.key,
                    'terminal_id': setting.terminal_id,
                    'value': setting.value,
                    'category': setting.category,
                    'is_sensitive': setting.is_sensitive
                }
            return None

    def update_setting(self, key: str, value: str, terminal_id: Optional[str] = None) -> bool:
        """Update a setting value."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            setting = session.query(Setting).filter_by(key=key, terminal_id=terminal_id).first()
            if setting:
                setting.value = value
                setting.updated_at = datetime.utcnow()
                session.commit()
                return True
            return False

    def update_settings(self, settings: Dict[str, str], terminal_id: Optional[str] = None) -> bool:
        """Update multiple settings at once."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            for key, value in settings.items():
                setting = session.query(Setting).filter_by(key=key, terminal_id=terminal_id).first()
                if setting:
                    setting.value = value
                    setting.updated_at = datetime.utcnow()
            
            session.commit()
            return True

    # Terminal methods
    def get_active_terminal_id(self) -> str:
        """Get the currently active terminal ID."""
        with self.get_session() as session:
            terminal = session.query(Terminal).filter_by(is_active=True).first()
            return terminal.id if terminal else 'TNZN'

    def set_active_terminal(self, terminal_id: str) -> bool:
        """Set the active terminal."""
        with self.get_session() as session:
            # Deactivate all terminals
            session.query(Terminal).update({Terminal.is_active: False})
            
            # Activate the specified terminal
            terminal = session.query(Terminal).filter_by(id=terminal_id).first()
            if terminal:
                terminal.is_active = True
                session.commit()
                return True
            return False

    def get_terminals(self) -> List[Dict[str, Any]]:
        """Get all terminals."""
        with self.get_session() as session:
            terminals = session.query(Terminal).order_by(Terminal.name).all()
            return [
                {
                    'id': t.id,
                    'name': t.name,
                    'location_lat': t.location_lat,
                    'location_lon': t.location_lon,
                    'total_capacity_cbm': t.total_capacity_cbm,
                    'number_of_tanks': t.number_of_tanks,
                    'draft_meters': t.draft_meters,
                    'operational_since': t.operational_since.isoformat() if t.operational_since else None,
                    'vessel_berths': t.vessel_berths,
                    'barge_berths': t.barge_berths,
                    'timezone': t.timezone,
                    'currency': t.currency,
                    'is_active': t.is_active
                }
                for t in terminals
            ]

    def get_terminal(self, terminal_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific terminal by ID."""
        with self.get_session() as session:
            terminal = session.query(Terminal).filter_by(id=terminal_id).first()
            if terminal:
                return {
                    'id': terminal.id,
                    'name': terminal.name,
                    'location_lat': terminal.location_lat,
                    'location_lon': terminal.location_lon,
                    'total_capacity_cbm': terminal.total_capacity_cbm,
                    'number_of_tanks': terminal.number_of_tanks,
                    'draft_meters': terminal.draft_meters,
                    'operational_since': terminal.operational_since.isoformat() if terminal.operational_since else None,
                    'vessel_berths': terminal.vessel_berths,
                    'barge_berths': terminal.barge_berths,
                    'timezone': terminal.timezone,
                    'currency': terminal.currency,
                    'is_active': terminal.is_active
                }
            return None

    # Analytics Methods
    
    def log_ml_prediction(self, assignment_id: Optional[int], vessel_id: Optional[str], 
                         vessel_name: Optional[str], prediction_type: str, 
                         predicted_minutes: Optional[int], confidence_score: Optional[float],
                         terminal_id: Optional[str] = None) -> int:
        """Log ML prediction for later accuracy analysis."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            prediction = MLPredictionLog(
                assignment_id=assignment_id,
                vessel_id=vessel_id,
                vessel_name=vessel_name,
                prediction_type=prediction_type,
                predicted_minutes=predicted_minutes,
                confidence_score=confidence_score,
                terminal_id=terminal_id
            )
            session.add(prediction)
            session.commit()
            session.refresh(prediction)
            return prediction.id

    def update_ml_prediction_actual(self, prediction_id: int, actual_minutes: int) -> bool:
        """Update ML prediction with actual values and calculate accuracy."""
        with self.get_session() as session:
            prediction = session.query(MLPredictionLog).filter_by(id=prediction_id).first()
            if prediction and prediction.predicted_minutes is not None:
                prediction.actual_minutes = actual_minutes
                prediction.actual_timestamp = datetime.utcnow()
                
                # Calculate accuracy metrics
                predicted = prediction.predicted_minutes
                error = abs(actual_minutes - predicted)
                prediction.absolute_error_minutes = error
                
                # Accuracy as percentage (100% - error percentage, min 0%)
                if predicted > 0:
                    error_percentage = (error / predicted) * 100
                    prediction.accuracy_percentage = max(0, 100 - error_percentage)
                else:
                    prediction.accuracy_percentage = 0 if error > 0 else 100
                
                session.commit()
                return True
            return False

    def log_ml_predictions_for_assignment(self, assignment_id: int, vessel_id: str, vessel_name: str,
                                         predictions: Dict[str, float], confidences: Dict[str, float],
                                         terminal_id: Optional[str] = None) -> int:
        """Log ML predictions for an assignment for analytics."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        try:
            with self.get_session() as session:
                # Create ML prediction log entry
                prediction_log = MLPredictionLog(
                    terminal_id=terminal_id,
                    assignment_id=assignment_id,
                    vessel_id=vessel_id,
                    vessel_name=vessel_name,
                    model_version="1.0",  # Default version
                    prediction_type="assignment_duration",
                    predictions=predictions,
                    confidences=confidences,
                    actual_values=None,  # Will be filled later when actual data is available
                    accuracy_score=None  # Will be calculated later
                )
                session.add(prediction_log)
                session.commit()
                session.refresh(prediction_log)
                
                logger.debug(f"Logged ML predictions for assignment {assignment_id}")
                return prediction_log.id
                
        except Exception as e:
            logger.warning(f"Failed to log ML predictions for assignment {assignment_id}: {e}")
            return 0

    def log_change_analysis(self, assignment_id: int, change_type: str, reason_text: str,
                           original_value: Optional[str], new_value: Optional[str],
                           vessel_id: Optional[str] = None, vessel_name: Optional[str] = None,
                           changed_by: Optional[str] = None, terminal_id: Optional[str] = None) -> int:
        """Log detailed change analysis with categorization."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        # Auto-categorize the change
        change_category = self._categorize_change(reason_text)
        reason_category = self._categorize_reason(reason_text)
        
        # Calculate impact in minutes if possible
        change_impact = self._calculate_change_impact(original_value, new_value, change_type)
        
        with self.get_session() as session:
            analysis = ChangeAnalysis(
                assignment_id=assignment_id,
                change_type=change_type,
                change_category=change_category,
                reason_category=reason_category,
                reason_text=reason_text,
                original_value=original_value,
                new_value=new_value,
                change_impact_minutes=change_impact,
                vessel_id=vessel_id,
                vessel_name=vessel_name,
                terminal_id=terminal_id,
                changed_by=changed_by
            )
            session.add(analysis)
            session.commit()
            session.refresh(analysis)
            return analysis.id

    def get_ml_prediction_accuracy(self, days: int = 30, terminal_id: Optional[str] = None) -> Dict[str, Any]:
        """Get ML prediction accuracy metrics for the specified period."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        from_date = datetime.utcnow() - timedelta(days=days)
        
        with self.get_session() as session:
            predictions = (session.query(MLPredictionLog)
                          .filter(MLPredictionLog.terminal_id == terminal_id)
                          .filter(MLPredictionLog.prediction_timestamp >= from_date)
                          .filter(MLPredictionLog.actual_minutes.isnot(None))
                          .all())
            
            if not predictions:
                return {"total_predictions": 0, "accuracy_percentage": 0}
            
            # Calculate metrics by prediction type
            metrics = {}
            for pred_type in ['prepump', 'pump', 'postpump', 'terminal']:
                type_predictions = [p for p in predictions if p.prediction_type == pred_type]
                if type_predictions:
                    avg_accuracy = sum(p.accuracy_percentage for p in type_predictions) / len(type_predictions)
                    avg_error = sum(p.absolute_error_minutes for p in type_predictions) / len(type_predictions)
                    metrics[pred_type] = {
                        "count": len(type_predictions),
                        "accuracy_percentage": round(avg_accuracy, 1),
                        "average_error_minutes": round(avg_error, 1)
                    }
            
            # Overall metrics
            overall_accuracy = sum(p.accuracy_percentage for p in predictions) / len(predictions)
            overall_error = sum(p.absolute_error_minutes for p in predictions) / len(predictions)
            
            return {
                "total_predictions": len(predictions),
                "accuracy_percentage": round(overall_accuracy, 1),
                "average_error_minutes": round(overall_error, 1),
                "by_type": metrics,
                "period_days": days
            }

    def get_change_analysis(self, days: int = 30, terminal_id: Optional[str] = None) -> Dict[str, Any]:
        """Get change pattern analysis for the specified period."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        from_date = datetime.utcnow() - timedelta(days=days)
        
        with self.get_session() as session:
            changes = (session.query(ChangeAnalysis)
                      .filter(ChangeAnalysis.terminal_id == terminal_id)
                      .filter(ChangeAnalysis.changed_at >= from_date)
                      .all())
            
            if not changes:
                return {"total_changes": 0}
            
            # Analyze by category
            by_category = {}
            for category in ['internal_optimization', 'external_factor', 'ml_correction', 'unknown']:
                cat_changes = [c for c in changes if c.change_category == category]
                if cat_changes:
                    by_category[category] = {
                        "count": len(cat_changes),
                        "percentage": round((len(cat_changes) / len(changes)) * 100, 1)
                    }
            
            # Analyze by reason type
            by_reason = {}
            for reason in ['operational', 'vessel', 'commercial', 'terminal', 'regulatory', 'other']:
                reason_changes = [c for c in changes if c.reason_category == reason]
                if reason_changes:
                    by_reason[reason] = len(reason_changes)
            
            return {
                "total_changes": len(changes),
                "by_category": by_category,
                "by_reason": by_reason,
                "period_days": days,
                "changes_per_day": round(len(changes) / days, 2)
            }

    def create_performance_alert(self, alert_type: str, metric_name: str, 
                                current_value: float, threshold_value: float,
                                severity: str = 'warning', description: str = '',
                                terminal_id: Optional[str] = None) -> int:
        """Create a performance alert."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            alert = PerformanceAlert(
                alert_type=alert_type,
                metric_name=metric_name,
                current_value=current_value,
                threshold_value=threshold_value,
                severity=severity,
                description=description,
                terminal_id=terminal_id
            )
            session.add(alert)
            session.commit()
            session.refresh(alert)
            return alert.id

    def get_active_alerts(self, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get unresolved performance alerts."""
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        with self.get_session() as session:
            alerts = (session.query(PerformanceAlert)
                     .filter(PerformanceAlert.terminal_id == terminal_id)
                     .filter(PerformanceAlert.is_resolved == False)
                     .order_by(PerformanceAlert.created_at.desc())
                     .all())
            
            return [
                {
                    'id': a.id,
                    'alert_type': a.alert_type,
                    'metric_name': a.metric_name,
                    'current_value': a.current_value,
                    'threshold_value': a.threshold_value,
                    'severity': a.severity,
                    'description': a.description,
                    'created_at': a.created_at.isoformat()
                }
                for a in alerts
            ]

    def _categorize_change(self, reason_text: str) -> str:
        """Categorize change as internal, external, or ML correction."""
        if not reason_text:
            return 'unknown'
            
        reason_lower = reason_text.lower()
        
        external_keywords = [
            'weather', 'vessel breakdown', 'customer request', 'port congestion',
            'pilot availability', 'tide', 'crew change', 'documentation',
            'customs', 'environmental', 'safety inspection'
        ]
        
        optimization_keywords = [
            'resource optimization', 'efficiency improvement', 'schedule conflict',
            'equipment maintenance', 'tank availability', 'pipeline scheduling'
        ]
        
        if any(keyword in reason_lower for keyword in external_keywords):
            return 'external_factor'
        elif any(keyword in reason_lower for keyword in optimization_keywords):
            return 'internal_optimization'
        else:
            return 'unknown'

    def _categorize_reason(self, reason_text: str) -> str:
        """Categorize reason into operational categories."""
        if not reason_text:
            return 'other'
            
        reason_lower = reason_text.lower()
        
        categories = {
            'operational': ['weather', 'port congestion', 'berthing', 'tide', 'pilot'],
            'vessel': ['vessel breakdown', 'crew change', 'cargo preparation', 'documentation', 'eta'],
            'commercial': ['customer request', 'priority', 'cargo specification', 'commercial'],
            'terminal': ['equipment maintenance', 'tank availability', 'pipeline', 'safety', 'resource'],
            'regulatory': ['port authority', 'environmental', 'safety inspection', 'customs']
        }
        
        for category, keywords in categories.items():
            if any(keyword in reason_lower for keyword in keywords):
                return category
        
        return 'other'

    def _calculate_change_impact(self, original_value: Optional[str], 
                               new_value: Optional[str], change_type: str) -> Optional[int]:
        """Calculate impact of change in minutes."""
        if not original_value or not new_value or change_type not in ['start_time', 'end_time']:
            return None
            
        try:
            from dateutil import parser
            original_dt = parser.parse(original_value)
            new_dt = parser.parse(new_value)
            return int(abs((new_dt - original_dt).total_seconds() / 60))
        except Exception:
            return None

    # ML Prediction Logging Methods
    def log_ml_prediction(self, assignment_id: int, vessel_id: str, vessel_name: str,
                         prediction_type: str, predicted_minutes: int, 
                         confidence_score: float = None, terminal_id: str = None) -> Optional[int]:
        """
        Log an ML prediction for later accuracy tracking.
        
        Args:
            assignment_id: ID of the assignment
            vessel_id: ID of the vessel
            vessel_name: Name of the vessel
            prediction_type: Type of prediction ('prepump', 'pump', 'postpump', 'terminal')
            predicted_minutes: Predicted duration in minutes
            confidence_score: Confidence score of the prediction (0.0-1.0)
            terminal_id: ID of the terminal
            
        Returns:
            ID of the created prediction log entry
        """
        if terminal_id is None:
            terminal_id = self.get_active_terminal_id()
            
        try:
            with self.get_session() as session:
                prediction_log = MLPredictionLog(
                    assignment_id=assignment_id,
                    vessel_id=vessel_id,
                    vessel_name=vessel_name,
                    prediction_type=prediction_type,
                    predicted_minutes=predicted_minutes,
                    confidence_score=confidence_score,
                    terminal_id=terminal_id,
                    prediction_timestamp=datetime.utcnow()
                )
                
                session.add(prediction_log)
                session.commit()
                session.refresh(prediction_log)
                
                logger.info(f"Logged ML prediction for assignment {assignment_id}: {prediction_type} = {predicted_minutes}min")
                return prediction_log.id
                
        except Exception as e:
            logger.error(f"Error logging ML prediction: {e}")
            return None
    
    def log_ml_predictions_for_assignment(self, assignment_id: int, vessel_id: str, vessel_name: str,
                                        predictions: Dict[str, Any], confidences: Dict[str, float] = None,
                                        terminal_id: str = None) -> Dict[str, int]:
        """
        Log multiple ML predictions for an assignment.
        
        Args:
            assignment_id: ID of the assignment
            vessel_id: ID of the vessel  
            vessel_name: Name of the vessel
            predictions: Dictionary with prediction types and minutes
            confidences: Dictionary with confidence scores for each prediction type
            terminal_id: ID of the terminal
            
        Returns:
            Dictionary mapping prediction types to their log IDs
        """
        prediction_ids = {}
        confidences = confidences or {}
        
        prediction_mappings = {
            'ml_predicted_prepump_minutes': 'prepump',
            'ml_predicted_pump_minutes': 'pump', 
            'ml_predicted_postpump_minutes': 'postpump',
            'ml_predicted_terminal_minutes': 'terminal'
        }
        
        for pred_key, pred_type in prediction_mappings.items():
            if pred_key in predictions and predictions[pred_key] is not None:
                confidence = confidences.get(f'{pred_type}_confidence')
                pred_id = self.log_ml_prediction(
                    assignment_id=assignment_id,
                    vessel_id=vessel_id,
                    vessel_name=vessel_name,
                    prediction_type=pred_type,
                    predicted_minutes=predictions[pred_key],
                    confidence_score=confidence,
                    terminal_id=terminal_id
                )
                if pred_id:
                    prediction_ids[pred_type] = pred_id
        
        return prediction_ids

    # Analytics Methods
    def get_analytics_overview(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get analytics overview data for the specified date range."""
        terminal_id = self.get_active_terminal_id()
        
        with self.get_session() as session:
            # Get ML accuracy
            ml_predictions = (session.query(MLPredictionLog)
                            .filter(MLPredictionLog.terminal_id == terminal_id)
                            .filter(MLPredictionLog.prediction_timestamp >= start_date)
                            .filter(MLPredictionLog.prediction_timestamp <= end_date)
                            .filter(MLPredictionLog.accuracy_percentage.isnot(None))
                            .all())
            
            ml_accuracy = (sum(p.accuracy_percentage for p in ml_predictions) / len(ml_predictions)) if ml_predictions else 0
            
            # Get change frequency
            changes = (session.query(ChangeAnalysis)
                      .filter(ChangeAnalysis.terminal_id == terminal_id)
                      .filter(ChangeAnalysis.changed_at >= start_date)
                      .filter(ChangeAnalysis.changed_at <= end_date)
                      .all())
            
            days = max((end_date - start_date).days, 1)
            change_frequency = len(changes) / days if changes else 0
            
            # Get external vs internal ratio
            external_changes = [c for c in changes if c.change_category == 'external_factor']
            external_ratio = (len(external_changes) / len(changes) * 100) if changes else 0
            
            # Get planning metrics
            planning_metrics = (session.query(PlanningMetrics)
                              .filter(PlanningMetrics.terminal_id == terminal_id)
                              .filter(PlanningMetrics.date >= start_date.date())
                              .filter(PlanningMetrics.date <= end_date.date())
                              .all())
            
            if planning_metrics:
                avg_utilization = sum(m.schedule_utilization_percent or 0 for m in planning_metrics) / len(planning_metrics)
                avg_turnaround = sum(m.average_turnaround_hours or 0 for m in planning_metrics) / len(planning_metrics)
                avg_adherence = sum(m.throughput_efficiency or 0 for m in planning_metrics) / len(planning_metrics)
            else:
                avg_utilization = avg_turnaround = avg_adherence = 0
            
            has_data = len(ml_predictions) > 0 or len(changes) > 0 or len(planning_metrics) > 0
            
            return {
                "ml_accuracy": round(ml_accuracy, 1),
                "planning_efficiency": round(avg_utilization, 1),
                "change_frequency": round(change_frequency, 1),
                "external_ratio": round(external_ratio, 1),
                "avg_turnaround": round(avg_turnaround, 1),
                "schedule_adherence": round(avg_adherence, 1),
                "has_data": has_data
            }

    def get_ml_performance_data(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get ML prediction performance data for the specified date range."""
        terminal_id = self.get_active_terminal_id()
        
        with self.get_session() as session:
            predictions = (session.query(MLPredictionLog)
                         .filter(MLPredictionLog.terminal_id == terminal_id)
                         .filter(MLPredictionLog.prediction_timestamp >= start_date)
                         .filter(MLPredictionLog.prediction_timestamp <= end_date)
                         .order_by(MLPredictionLog.prediction_timestamp.asc())
                         .all())
            
            if not predictions:
                return {
                    "accuracy_trend": {"labels": [], "values": []},
                    "average_accuracy": 0,
                    "trend_direction": "stable",
                    "total_predictions": 0,
                    "has_data": False
                }
            
            # Group by day for trend
            daily_accuracy = {}
            for pred in predictions:
                if pred.accuracy_percentage is not None:
                    day = pred.prediction_timestamp.date()
                    if day not in daily_accuracy:
                        daily_accuracy[day] = []
                    daily_accuracy[day].append(pred.accuracy_percentage)
            
            labels = []
            values = []
            for day in sorted(daily_accuracy.keys()):
                labels.append(day.strftime("%m/%d"))
                values.append(sum(daily_accuracy[day]) / len(daily_accuracy[day]))
            
            avg_accuracy = sum(p.accuracy_percentage for p in predictions if p.accuracy_percentage) / max(len([p for p in predictions if p.accuracy_percentage]), 1)
            
            # Determine trend
            if len(values) >= 2:
                trend_direction = "up" if values[-1] > values[0] else ("down" if values[-1] < values[0] else "stable")
            else:
                trend_direction = "stable"
            
            return {
                "accuracy_trend": {"labels": labels, "values": values},
                "average_accuracy": round(avg_accuracy, 1),
                "trend_direction": trend_direction,
                "total_predictions": len(predictions),
                "has_data": True
            }

    def get_change_analysis_data(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get change analysis data for the specified date range."""
        terminal_id = self.get_active_terminal_id()
        
        with self.get_session() as session:
            changes = (session.query(ChangeAnalysis)
                      .filter(ChangeAnalysis.terminal_id == terminal_id)
                      .filter(ChangeAnalysis.changed_at >= start_date)
                      .filter(ChangeAnalysis.changed_at <= end_date)
                      .all())
            
            if not changes:
                return {
                    "reason_distribution": {"labels": [], "values": []},
                    "external_ratio": 0,
                    "change_frequency": 0,
                    "trend_direction": "stable",
                    "total_changes": 0,
                    "has_data": False
                }
            
            # Count by reason category
            reason_counts = {}
            external_count = 0
            
            for change in changes:
                reason = change.reason_category or "other"
                reason_counts[reason] = reason_counts.get(reason, 0) + 1
                
                if change.change_category == 'external_factor':
                    external_count += 1
            
            labels = list(reason_counts.keys())
            values = list(reason_counts.values())
            
            days = max((end_date - start_date).days, 1)
            change_frequency = len(changes) / days
            external_ratio = (external_count / len(changes) * 100) if changes else 0
            
            # Generate daily change counts for heatmap
            daily_changes = {}
            for change in changes:
                day = change.changed_at.date()
                daily_changes[day] = daily_changes.get(day, 0) + 1
            
            # Fill in missing days with zero counts
            daily_change_list = []
            current_date = start_date.date()
            end_date_only = end_date.date()
            
            while current_date <= end_date_only:
                daily_change_list.append({
                    "date": current_date.isoformat(),
                    "count": daily_changes.get(current_date, 0)
                })
                current_date += timedelta(days=1)
            
            return {
                "reason_distribution": {"labels": labels, "values": values},
                "external_ratio": round(external_ratio, 1),
                "change_frequency": round(change_frequency, 1),
                "trend_direction": "stable",  # Would need historical comparison
                "total_changes": len(changes),
                "daily_changes": daily_change_list,
                "has_data": True
            }

    def get_efficiency_metrics(self, start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get planning efficiency metrics for the specified date range."""
        terminal_id = self.get_active_terminal_id()
        
        with self.get_session() as session:
            metrics = (session.query(PlanningMetrics)
                      .filter(PlanningMetrics.terminal_id == terminal_id)
                      .filter(PlanningMetrics.date >= start_date.date())
                      .filter(PlanningMetrics.date <= end_date.date())
                      .order_by(PlanningMetrics.date.asc())
                      .all())
            
            if not metrics:
                return {
                    "utilization_trend": {"labels": [], "values": []},
                    "schedule_utilization": 0,
                    "average_turnaround": 0,
                    "schedule_adherence": 0,
                    "total_assignments": 0,
                    "has_data": False
                }
            
            labels = []
            values = []
            total_assignments = 0
            total_turnaround = 0
            total_adherence = 0
            
            for metric in metrics:
                labels.append(metric.date.strftime("%m/%d"))
                values.append(metric.schedule_utilization_percent or 0)
                total_assignments += metric.total_assignments or 0
                total_turnaround += metric.average_turnaround_hours or 0
                total_adherence += metric.throughput_efficiency or 0
            
            avg_utilization = sum(values) / len(values) if values else 0
            avg_turnaround = total_turnaround / len(metrics) if metrics else 0
            avg_adherence = total_adherence / len(metrics) if metrics else 0
            
            return {
                "utilization_trend": {"labels": labels, "values": values},
                "schedule_utilization": round(avg_utilization, 1),
                "average_turnaround": round(avg_turnaround, 1),
                "schedule_adherence": round(avg_adherence, 1),
                "total_assignments": total_assignments,
                "has_data": True
            }


# Global database instance will be created on first use
db = None

def get_database():
    """Get or create the global database instance"""
    global db
    if db is None:
        db = Database()
    return db
