from typing import Optional, Dict, Any
from sqlalchemy.orm import Session

from ..db.models import VesselRegistry
from ..database import Database


class VesselRegistryService:
    def __init__(self, db: Optional[Database] = None):
        self.db = db or Database()

    def find_or_create_vessel(self, *, session: Optional[Session] = None,
                              imo: Optional[str] = None, mmsi: Optional[str] = None,
                              name: Optional[str] = None, **vessel_data: Any) -> int:
        # Minimal skeleton; full logic to be implemented in Phase 2 methods
        with self.db.get_session() as db:
            q = db.query(VesselRegistry)
            if imo:
                v = q.filter_by(imo=imo).first()
                if v:
                    return v.id
            if mmsi and not imo:
                v = q.filter_by(mmsi=mmsi).first()
                if v:
                    return v.id
            if name and not (imo or mmsi):
                v = q.filter(VesselRegistry.name.ilike(name)).first()
                if v:
                    return v.id
            v = VesselRegistry(name=name or vessel_data.get('name', 'UNKNOWN'),
                               imo=imo, mmsi=mmsi, **{k: v for k, v in vessel_data.items() if k in {
                                   'call_sign', 'vessel_type', 'vessel_subtype', 'deadweight', 'gross_tonnage',
                                   'length_overall', 'beam', 'maximum_draft', 'flag_state', 'port_of_registry',
                                   'owner', 'operator', 'manager', 'build_year', 'shipyard', 'hull_number',
                                   'status', 'is_blacklisted', 'data_source', 'confidence_score', 'last_ais_update',
                                   'created_by', 'notes', 'previous_names'
                               }})
            db.add(v)
            db.commit()
            db.refresh(v)
            return v.id

    def update_vessel_from_ais(self, vessel_id: int, ais_data: Dict[str, Any]) -> bool:
        # Placeholder: update selected fields based on AIS
        with self.db.get_session() as db:
            v = db.query(VesselRegistry).filter_by(id=vessel_id).first()
            if not v:
                return False
            if 'draught' in ais_data:
                v.maximum_draft = ais_data['draught'] or v.maximum_draft
            v.last_ais_update = ais_data.get('timestamp') or v.last_ais_update
            db.commit()
            return True

    def merge_vessel_records(self, primary_id: int, duplicate_id: int) -> bool:
        # Placeholder: business rules later
        if primary_id == duplicate_id:
            return False
        with self.db.get_session() as db:
            primary = db.query(VesselRegistry).filter_by(id=primary_id).first()
            dup = db.query(VesselRegistry).filter_by(id=duplicate_id).first()
            if not primary or not dup:
                return False
            # In a full impl, re-point visits/ais/assignments; for now delete dup
            db.delete(dup)
            db.commit()
            return True

    def get_vessel_history(self, vessel_id: int) -> Dict[str, Any]:
        # Placeholder: will include visits, performance
        with self.db.get_session() as db:
            vessel = db.query(VesselRegistry).filter_by(id=vessel_id).first()
            if not vessel:
                return {}
            return {
                'id': vessel.id,
                'name': vessel.name,
                'imo': vessel.imo,
                'mmsi': vessel.mmsi,
                'status': vessel.status,
                'visits_count': len(vessel.visits or []),
            }


