# Evos Terminal Jetty Planner - User Manual

## Table of Contents
1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Vessel Nomination Workflow](#vessel-nomination-workflow)
4. [Schedule Optimization](#schedule-optimization)
5. [Monitoring and Management](#monitoring-and-management)
6. [Troubleshooting](#troubleshooting)

---

## Introduction

The **Evos Terminal Jetty Planner** is an intelligent vessel scheduling system that helps optimize jetty assignments for terminal operations. This manual guides you through the complete workflow from creating vessel nominations to generating optimized schedules.

### Key Features
- **Smart Vessel Nomination**: Create nominations with AIS integration and ML-powered predictions
- **Intelligent Optimization**: AI-driven schedule optimization with multiple strategies
- **Real-time Tracking**: Live vessel positions and ETA calculations
- **Performance Analytics**: Comprehensive metrics and reporting

---

## Getting Started

### System Overview
The system operates in a four-stage workflow:
1. **Nomination** - Register vessels and cargo details
2. **Prediction** - Generate ML-powered time estimates
3. **Optimization** - Create optimal jetty schedules
4. **Execution** - Monitor and manage operations

### Navigation
- **Dashboard**: Overview of current operations
- **Nominated Vessels**: View and manage pending vessels
- **Nomination**: Create new vessel nominations
- **Schedule**: View and manage optimized schedules
- **Optimize**: Run optimization algorithms

---

## Vessel Nomination Workflow

The vessel nomination process is the foundation of effective schedule optimization. Follow these steps to create accurate nominations.

### Step 1: Vessel Information

#### Option A: Search Existing Vessels (Recommended)
1. **Access the Nomination Page**
   - Click "Add Nomination" from the Nominated Vessels page
   - Or navigate to `/nomination` directly

2. **Use the AIS Map**
   - The interactive map shows real-time vessel positions
   - Click on vessel markers to auto-populate search
   - Vessels are color-coded by status and type

3. **Search for Vessels**
   - Enter vessel name, MMSI, or IMO in the search box
   - Use filters to narrow results:
     - ✓ **Only liquid cargo**: Filters for tankers and chemical carriers
     - **Type contains**: Search for specific vessel types (e.g., "tanker", "oil")
     - ✓ **Extended search**: Include vessels outside the immediate area
   - Click "Search" to find matching vessels

4. **Select a Vessel**
   - Review search results showing:
     - Vessel name and type
     - Current position and status
     - Physical specifications (length, beam, draft)
     - MMSI/IMO numbers
   - Click on the desired vessel to auto-populate form fields

#### Option B: Manual Entry
1. **Toggle Manual Entry**
   - Click "Enter Vessel Details Manually" if vessel not found in AIS
   
2. **Complete Required Fields**
   - **Vessel Name**: Official registered name
   - **Vessel Type**: Select "Vessel (seagoing)" or "Barge"
   - **Length Overall**: Maximum 235m (terminal limit)
   - **Beam**: Maximum 34m (terminal limit)
   - **Draft**: Maximum 12.8m (terminal limit)
   - **Deadweight**: Maximum 60,000 tonnes
   - **ETA**: Estimated time of arrival
   - **Customer**: Select from dropdown list

3. **Additional Information**
   - **Notes**: Special requirements, first visit details, etc.

### Step 2: Cargo Information

#### Configure Cargo Details
1. **Product Type**
   - Select from comprehensive product list
   - Common products: NAPHTA, BENZEEN E, PROPYLEENOXIDE
   - Product selection affects tank compatibility

2. **Volume and Operation**
   - **Volume (m³)**: Cargo quantity (100-50,000 m³)
   - **Operation Type**: 
     - **Export (Ex)**: Loading from terminal to vessel
     - **Import (In)**: Discharge from vessel to terminal

3. **Technical Specifications**
   - **Connection Size**: Select appropriate ANSI connection (4"-12")
   - **Vapor Return**: Check if required for safety
   - **Nitrogen Purging**: Check if required for cleaning

4. **Tank Selection**
   - System automatically shows compatible tanks based on:
     - Operation type (loading/unloading)
     - Product compatibility
     - Tank availability and capacity
   - Select appropriate source/destination tanks
   - Multiple tank selection is supported

5. **Multiple Cargoes**
   - Click "Add Another Cargo" for multi-product operations
   - Each cargo can have different specifications

### Step 3: ML Predictions & Recommendations

#### Model Selection
1. **Choose Prediction Model**
   - Select from available ML models in dropdown
   - Default model is automatically selected
   - View model details including type, filename, and description

2. **Upload New Model (Optional)**
   - Drag & drop .zip or .joblib files
   - Upload progress is shown with status updates
   - New models become available immediately after upload

#### Generate Predictions
1. **Calculate Predictions**
   - Click "Calculate Predictions" to generate ML estimates
   - System analyzes:
     - Vessel characteristics
     - Cargo requirements
     - Historical performance data
     - Current terminal conditions

2. **Review Predictions**
   - **Total Terminal Time**: ML-predicted duration
   - **Confidence Indicators**: Visual confidence bars
   - **Model Information**: Active model and prediction timestamp

#### Jetty Recommendations
1. **Automated Recommendations**
   - System suggests compatible jetties based on:
     - Vessel size and type restrictions
     - Cargo compatibility
     - Current availability
     - Historical performance

2. **Jetty Selection**
   - **Preferred Jetty**: Optional manual selection
   - **Compatibility Matrix**: Shows which jetties can handle the vessel
   - **Suitability Scores**: ML-calculated recommendation scores

#### Business Rules Validation
The system automatically validates against operational rules:
- ✓ **Vessel Dimensions**: Within jetty limits
- ✓ **Draft Restrictions**: Tidal and depth constraints
- ✓ **Product Compatibility**: Jetty-product matching
- ⚠ **Capacity Limits**: Tank and throughput constraints
- ❌ **Safety Restrictions**: Regulatory compliance

### Step 4: Review & Submit

#### Final Review
1. **Vessel Summary**
   - Verify all vessel details are correct
   - Check ETA and customer information

2. **Cargo Summary**
   - Confirm product types and volumes
   - Verify tank assignments

3. **ML Predictions**
   - Review predicted terminal times
   - Check jetty recommendations

4. **Submit Nomination**
   - Click "Create Unscheduled Vessel"
   - System creates nomination record
   - Vessel appears in "Nominated Vessels" list

---

## Schedule Optimization

Once nominations are created, use the optimization engine to generate efficient schedules.

### Accessing Optimization

1. **Navigate to Optimize Page**
   - Use main navigation menu
   - Or click "Run Optimization" from schedule page

### Optimization Strategy Selection

#### Preset Strategies
Choose from four pre-configured optimization strategies:

1. **🚀 Maximum Throughput**
   - **Best for**: High-demand periods, peak season
   - **Focus**: Process maximum vessels in shortest time
   - **Trade-offs**: May increase costs but maximizes revenue

2. **💰 Cost Efficiency**
   - **Best for**: Budget-conscious operations, low-margin periods
   - **Focus**: Minimize demurrage and operational expenses
   - **Trade-offs**: May reduce throughput but controls costs

3. **🏗️ Infrastructure Efficiency**
   - **Best for**: Maintenance periods, resource optimization
   - **Focus**: Maximize jetty utilization and facility efficiency
   - **Trade-offs**: Balances throughput and costs

4. **⚖️ Balanced**
   - **Best for**: Normal operations, mixed priorities
   - **Focus**: Even consideration of all factors
   - **Trade-offs**: Good all-around performance

#### AI Assistant
1. **Get Recommendations**
   - Click "Ask AI Assistant" for personalized advice
   - Describe your current situation and priorities
   - AI analyzes conditions and suggests optimal strategy

2. **Interactive Guidance**
   - Ask questions about optimization strategies
   - Get explanations of trade-offs
   - Receive specific recommendations

### Fine-Tuning Parameters

#### Time Parameters
- **Planning Horizon**: 1-30 days (default: 7 days)
- **Time Granularity**: 1-6 hours (default: 1 hour)

#### Optimization Weights
Adjust relative importance of different factors:

- **Throughput Weight** (0-20): Prioritize volume processing
- **Demurrage Cost Weight** (0-20): Minimize vessel waiting time
- **Customer Priority Weight** (0-20): Favor high-priority customers
- **Utilization Weight** (0-20): Balance jetty usage

#### Advanced Options
- **Force Assign All Vessels**: Ensure every vessel gets assigned
- **Custom Preset Management**: Save current settings as new preset

### Running Optimization

1. **Start Optimization**
   - Click "Run Optimization"
   - System processes all nominated vessels
   - Progress indicators show optimization stages

2. **Monitor Progress**
   - Real-time status updates
   - Optimization log shows detailed steps
   - Estimated completion time

3. **Review Results**
   - **Objective Value**: Overall optimization score
   - **Assignments**: Number of vessels scheduled
   - **Total Throughput**: Volume processed
   - **Jetty Utilization**: Efficiency metrics

### Understanding Results

#### Schedule Metrics
- **Objective Value**: Higher is better (optimization score)
- **Assignment Count**: Vessels successfully scheduled
- **Total Throughput**: Cubic meters processed
- **Average Utilization**: Jetty efficiency percentage

#### Utilization Charts
- Visual representation of jetty usage
- Color coding: Green (low), Yellow (medium), Red (high)
- Identify bottlenecks and optimization opportunities

#### Optimization History
- Track previous optimization runs
- Compare different parameter settings
- Reapply successful configurations

---

## Monitoring and Management

### Nominated Vessels Dashboard

#### Overview Features
- **Real-time AIS Map**: Live vessel positions
- **Service Status**: AIS, Tidal, Lock, and Tracking systems
- **Vessel List**: Comprehensive nomination tracking

#### Vessel Filtering
- **ETA Range**: Next 6h, 12h, 24h, or 48h
- **Proximity**: Terminal, 2-hour zone, 4-hour zone, tracked
- **Status**: EN_ROUTE, APPROACHING, ARRIVED, WAITING

#### Smart ETA Tracking
For each vessel, monitor:
- **Original ETA**: Customer-provided estimate
- **Smart ETA**: ML-enhanced calculation including:
  - Real-time position and speed
  - Tidal conditions and restrictions
  - Lock status and wait times
  - Historical performance data

#### Detailed Analysis
Click on any vessel to view:
- **Position & Movement**: Current location, speed, course
- **ETA Breakdown**: Component analysis of time factors
- **Tidal Information**: Water levels and navigation windows
- **Lock Status**: Operational status and delays

### Schedule Management

#### Schedule View
- **Gantt Chart**: Visual timeline of assignments
- **Jetty Overview**: Utilization by berth
- **Vessel Status**: Current position in workflow

#### Assignment Management
- **Modify Assignments**: Adjust timing and jetty allocation
- **Status Updates**: Track vessel progress
- **Change Logging**: Audit trail of modifications

#### Performance Monitoring
- **Real-time Metrics**: KPI dashboard
- **Alerts**: Automated notifications for issues
- **Reporting**: Historical performance analysis

---

## Troubleshooting

### Common Issues

#### Vessel Not Found in AIS Search
**Symptoms**: Search returns no results for known vessel
**Solutions**:
1. Try different search terms (name variations, MMSI, IMO)
2. Enable "Extended search" option
3. Check vessel type filters
4. Use manual entry if vessel not in AIS database

#### ML Predictions Not Generated
**Symptoms**: "Calculate Predictions" fails or shows errors
**Solutions**:
1. Verify all required vessel fields are completed
2. Check cargo information is valid
3. Ensure at least one ML model is available
4. Try uploading a new model if needed

#### Optimization Fails
**Symptoms**: Optimization stops with error or no results
**Solutions**:
1. Check that vessels are in correct status (EN_ROUTE, APPROACHING, etc.)
2. Verify jetty compatibility with vessel dimensions
3. Reduce planning horizon or increase time granularity
4. Try "Force Assign All" option
5. Reset schedule and try again

#### ETA Calculations Inaccurate
**Symptoms**: Smart ETA differs significantly from expected
**Solutions**:
1. Verify vessel AIS data is current
2. Check tidal and lock status services
3. Update vessel draft and specifications
4. Review historical performance data

### System Status Indicators

#### Service Health
Monitor these key services:
- **🟢 AIS Stream**: Real-time vessel positions
- **🟢 Tidal Data**: RWS Waterwebservices connection
- **🟢 Lock Status**: RWS & BGV APIs
- **🟢 Ship Tracking**: ETA calculation engine

#### Data Quality
- **Position Accuracy**: GPS precision indicators
- **Update Frequency**: Last AIS update timestamps
- **Confidence Scores**: ML prediction reliability

### Getting Help

#### Built-in Assistance
- **AI Assistant**: Interactive help for optimization strategies
- **Preset Guide**: Detailed strategy explanations
- **Status Legend**: ETA calculation methodology
- **Tooltips**: Contextual help throughout interface

#### Support Resources
- **System Logs**: Detailed operation logging
- **Performance Alerts**: Automated issue detection
- **Change History**: Audit trail for troubleshooting

---

## Best Practices

### Nomination Quality
1. **Use AIS Data**: Always search existing vessels first
2. **Verify Specifications**: Double-check vessel dimensions
3. **Accurate ETAs**: Provide realistic arrival estimates
4. **Complete Cargo Details**: Include all technical requirements

### Optimization Strategy
1. **Start with Balanced**: Good default for most situations
2. **Monitor Results**: Track optimization effectiveness
3. **Adjust Gradually**: Make incremental parameter changes
4. **Save Successful Configurations**: Create custom presets

### System Maintenance
1. **Regular Optimization**: Run daily or as vessels arrive
2. **Monitor Service Status**: Check AIS and API connections
3. **Update Models**: Upload new ML models periodically
4. **Review Performance**: Analyze historical metrics

---

This manual provides comprehensive guidance for the complete nomination-to-optimization workflow. For additional support or advanced features, consult the system administrator or refer to the technical documentation.

