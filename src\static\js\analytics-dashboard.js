/**
 * Analytics Dashboard JavaScript
 * Handles chart visualizations, KPI updates, and data fetching for the analytics page
 */

// Global variables
let analyticsData = {};
let charts = {};
let currentDateRange = '30'; // Default to 30 days

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeAnalytics();
    setDefaultDateRange();
    setupEventListeners();
    refreshAnalytics();
});

/**
 * Set up event listeners for analytics controls
 */
function setupEventListeners() {
    // Date range selector
    const dateRangeSelect = document.getElementById('dateRange');
    if (dateRangeSelect) {
        dateRangeSelect.addEventListener('change', updateDateRange);
    }
    
    // Refresh button
    const refreshBtn = document.getElementById('refreshAnalyticsBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshAnalytics);
    }
}

/**
 * Initialize analytics dashboard
 */
function initializeAnalytics() {
    console.log('Initializing analytics dashboard...');
    
    // Set up Chart.js defaults
    if (typeof Chart !== 'undefined') {
        Chart.defaults.font.family = 'Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif';
        Chart.defaults.color = '#666';
        Chart.defaults.borderColor = '#ddd';
        Chart.defaults.backgroundColor = 'rgba(0, 59, 111, 0.1)';
    }
}

/**
 * Set default date range
 */
function setDefaultDateRange() {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 30);
    
    document.getElementById('startDate').value = formatDateForInput(startDate);
    document.getElementById('endDate').value = formatDateForInput(endDate);
}

/**
 * Format date for HTML date input
 */
function formatDateForInput(date) {
    return date.toISOString().split('T')[0];
}

/**
 * Update date range based on dropdown selection
 */
function updateDateRange() {
    const select = document.getElementById('dateRange');
    const customInputs = document.getElementById('customDateInputs');
    const value = select.value;
    
    if (value === 'custom') {
        customInputs.style.display = 'flex';
        customInputs.style.alignItems = 'center';
        customInputs.style.gap = '10px';
    } else {
        customInputs.style.display = 'none';
        
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - parseInt(value));
        
        document.getElementById('startDate').value = formatDateForInput(startDate);
        document.getElementById('endDate').value = formatDateForInput(endDate);
        
        currentDateRange = value;
    }
}

/**
 * Refresh all analytics data and charts
 */
async function refreshAnalytics() {
    console.log('Refreshing analytics data...');
    showLoadingState();
    
    try {
        const dateRange = getDateRange();
        
        // Fetch all analytics data in parallel
        const [overview, mlPerformance, changeData, efficiency] = await Promise.all([
            fetchAnalyticsOverview(dateRange),
            fetchMLPerformance(dateRange),
            fetchChangeAnalysis(dateRange),
            fetchEfficiencyMetrics(dateRange)
        ]);
        
        // Store data globally
        analyticsData = {
            overview,
            mlPerformance,
            changeData,
            efficiency
        };
        
        // Update KPI cards
        updateKPICards();
        
        // Update charts
        updateCharts();
        
        hideLoadingState();
        
    } catch (error) {
        console.error('Error refreshing analytics:', error);
        showErrorState();
    }
}

/**
 * Get current date range from form
 */
function getDateRange() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    return {
        start_date: startDate,
        end_date: endDate
    };
}

/**
 * Fetch analytics overview data
 */
async function fetchAnalyticsOverview(dateRange) {
    const params = new URLSearchParams(dateRange);
    const response = await fetch(`/api/analytics/overview?${params}`);
    
    if (!response.ok) {
        throw new Error(`Overview API error: ${response.status}`);
    }
    
    const result = await response.json();
    return result.data || result;
}

/**
 * Fetch ML performance data
 */
async function fetchMLPerformance(dateRange) {
    const params = new URLSearchParams(dateRange);
    const response = await fetch(`/api/analytics/ml-performance?${params}`);
    
    if (!response.ok) {
        throw new Error(`ML Performance API error: ${response.status}`);
    }
    
    const result = await response.json();
    return result.data || result;
}

/**
 * Fetch change analysis data
 */
async function fetchChangeAnalysis(dateRange) {
    const params = new URLSearchParams(dateRange);
    const response = await fetch(`/api/analytics/changes?${params}`);
    
    if (!response.ok) {
        throw new Error(`Change Analysis API error: ${response.status}`);
    }
    
    const result = await response.json();
    return result.data || result;
}

/**
 * Fetch efficiency metrics
 */
async function fetchEfficiencyMetrics(dateRange) {
    const params = new URLSearchParams(dateRange);
    const response = await fetch(`/api/analytics/efficiency?${params}`);
    
    if (!response.ok) {
        throw new Error(`Efficiency Metrics API error: ${response.status}`);
    }
    
    const result = await response.json();
    return result.data || result;
}

/**
 * Update KPI cards with latest data
 */
function updateKPICards() {
    const { overview, mlPerformance, changeData, efficiency } = analyticsData;
    
    // Check if we have any data at all
    const hasAnyData = overview?.has_data || mlPerformance?.has_data || 
                       changeData?.has_data || efficiency?.has_data;
    
    if (!hasAnyData) {
        // Show "No data" for all KPIs
        updateKPICard('ml-accuracy', 'No data', 'ml-accuracy-trend', 'stable', 'No data available');
        updateKPICard('planning-efficiency', 'No data', 'efficiency-trend', 'stable', 'No data available');
        updateKPICard('change-frequency', 'No data', 'change-trend', 'stable', 'No data available');
        updateKPICard('external-ratio', 'No data', 'external-trend', 'stable', 'No data available');
        updateKPICard('avg-turnaround', 'No data', 'turnaround-trend', 'stable', 'No data available');
        updateKPICard('schedule-adherence', 'No data', 'adherence-trend', 'stable', 'No data available');
        return;
    }
    
    // ML Accuracy
    if (mlPerformance?.has_data) {
        updateKPICard('ml-accuracy', formatPercentage(mlPerformance.average_accuracy), 
                      'ml-accuracy-trend', mlPerformance.trend_direction);
    } else {
        updateKPICard('ml-accuracy', 'No data', 'ml-accuracy-trend', 'stable', 'No predictions recorded');
    }
    
    // Planning Efficiency
    if (efficiency?.has_data) {
        updateKPICard('planning-efficiency', formatPercentage(efficiency.schedule_utilization), 
                      'efficiency-trend', 'stable');
    } else {
        updateKPICard('planning-efficiency', 'No data', 'efficiency-trend', 'stable', 'No efficiency metrics');
    }
    
    // Change Frequency
    if (changeData?.has_data) {
        updateKPICard('change-frequency', formatNumber(changeData.change_frequency, 1), 
                      'change-trend', changeData.trend_direction);
    } else {
        updateKPICard('change-frequency', 'No data', 'change-trend', 'stable', 'No changes recorded');
    }
    
    // External Changes Ratio
    if (changeData?.has_data) {
        updateKPICard('external-ratio', formatPercentage(changeData.external_ratio), 
                      'external-trend', 'stable');
    } else {
        updateKPICard('external-ratio', 'No data', 'external-trend', 'stable', 'No changes recorded');
    }
    
    // Average Turnaround
    if (efficiency?.has_data) {
        updateKPICard('avg-turnaround', formatNumber(efficiency.average_turnaround, 1) + 'h', 
                      'turnaround-trend', 'stable');
    } else {
        updateKPICard('avg-turnaround', 'No data', 'turnaround-trend', 'stable', 'No turnaround data');
    }
    
    // Schedule Adherence
    if (efficiency?.has_data) {
        updateKPICard('schedule-adherence', formatPercentage(efficiency.schedule_adherence), 
                      'adherence-trend', 'stable');
    } else {
        updateKPICard('schedule-adherence', 'No data', 'adherence-trend', 'stable', 'No adherence data');
    }
}

/**
 * Update individual KPI card
 */
function updateKPICard(valueId, value, trendId, trendDirection, trendText = null) {
    const valueElement = document.getElementById(valueId);
    const trendElement = document.getElementById(trendId);
    
    if (valueElement) {
        valueElement.textContent = value;
        
        // Add styling for "No data" state
        if (value === 'No data') {
            valueElement.style.color = '#999';
            valueElement.style.fontSize = '1.5em';
        } else {
            valueElement.style.color = '#2c5282';
            valueElement.style.fontSize = '2.2em';
        }
    }
    
    if (trendElement) {
        const trendIcon = getTrendIcon(trendDirection);
        const trendClass = getTrendClass(trendDirection);
        const displayText = trendText || getTrendText(trendDirection);
        
        trendElement.innerHTML = `<i class="${trendIcon}"></i> ${displayText}`;
        trendElement.className = `kpi-trend ${trendClass}`;
    }
}

/**
 * Get trend icon based on direction
 */
function getTrendIcon(direction) {
    switch (direction) {
        case 'up': return 'fas fa-arrow-up';
        case 'down': return 'fas fa-arrow-down';
        case 'stable': return 'fas fa-minus';
        default: return 'fas fa-circle';
    }
}

/**
 * Get trend CSS class based on direction
 */
function getTrendClass(direction) {
    switch (direction) {
        case 'up': return 'trend-up';
        case 'down': return 'trend-down';
        case 'stable': return 'trend-stable';
        default: return 'trend-stable';
    }
}

/**
 * Get trend text based on direction
 */
function getTrendText(direction) {
    switch (direction) {
        case 'up': return 'Trending up';
        case 'down': return 'Trending down';
        case 'stable': return 'Stable';
        default: return 'No data';
    }
}

/**
 * Update all charts
 */
function updateCharts() {
    updateMLAccuracyChart();
    updateChangeReasonsChart();
    updateEfficiencyChart();
    updateChangeHeatmapChart();
}

/**
 * Update ML accuracy trend chart
 */
function updateMLAccuracyChart() {
    const ctx = document.getElementById('mlAccuracyChart');
    if (!ctx) return;
    
    // Destroy existing chart if it exists
    if (charts.mlAccuracy) {
        charts.mlAccuracy.destroy();
    }
    
    const data = analyticsData.mlPerformance?.accuracy_trend;
    const hasData = analyticsData.mlPerformance?.has_data;
    
    if (!hasData || !data || data.labels.length === 0) {
        // Show "No data" message
        ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
        ctx.getContext('2d').font = '16px Roboto';
        ctx.getContext('2d').fillStyle = '#666';
        ctx.getContext('2d').textAlign = 'center';
        ctx.getContext('2d').fillText('No ML prediction data available', ctx.width / 2, ctx.height / 2);
        return;
    }
    
    charts.mlAccuracy = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: [{
                label: 'ML Accuracy %',
                data: data.values,
                borderColor: '#003b6f',
                backgroundColor: 'rgba(0, 59, 111, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}

/**
 * Update change reasons pie chart
 */
function updateChangeReasonsChart() {
    const ctx = document.getElementById('changeReasonsChart');
    if (!ctx) return;
    
    if (charts.changeReasons) {
        charts.changeReasons.destroy();
    }
    
    const data = analyticsData.changeData?.reason_distribution;
    const hasData = analyticsData.changeData?.has_data;
    
    if (!hasData || !data || data.labels.length === 0) {
        // Show "No data" message
        ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
        ctx.getContext('2d').font = '16px Roboto';
        ctx.getContext('2d').fillStyle = '#666';
        ctx.getContext('2d').textAlign = 'center';
        ctx.getContext('2d').fillText('No change data available', ctx.width / 2, ctx.height / 2);
        return;
    }
    
    charts.changeReasons = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.labels,
            datasets: [{
                data: data.values,
                backgroundColor: [
                    '#003b6f',
                    '#2c5282',
                    '#3182ce',
                    '#4299e1',
                    '#63b3ed',
                    '#90cdf4'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

/**
 * Update efficiency trend chart
 */
function updateEfficiencyChart() {
    const ctx = document.getElementById('efficiencyChart');
    if (!ctx) return;
    
    if (charts.efficiency) {
        charts.efficiency.destroy();
    }
    
    const data = analyticsData.efficiency?.utilization_trend;
    const hasData = analyticsData.efficiency?.has_data;
    
    if (!hasData || !data || data.labels.length === 0) {
        // Show "No data" message
        ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
        ctx.getContext('2d').font = '16px Roboto';
        ctx.getContext('2d').fillStyle = '#666';
        ctx.getContext('2d').textAlign = 'center';
        ctx.getContext('2d').fillText('No efficiency data available', ctx.width / 2, ctx.height / 2);
        return;
    }
    
    charts.efficiency = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.labels,
            datasets: [{
                label: 'Utilization %',
                data: data.values,
                backgroundColor: '#17a2b8',
                borderColor: '#138496',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            }
        }
    });
}

/**
 * Update change frequency heatmap
 */
function updateChangeHeatmapChart() {
    const ctx = document.getElementById('changeHeatmapChart');
    if (!ctx) return;
    
    if (charts.changeHeatmap) {
        charts.changeHeatmap.destroy();
    }
    
    const hasData = analyticsData.changeData?.has_data;
    
    if (!hasData || !analyticsData.changeData?.total_changes) {
        // Show "No data" message
        ctx.getContext('2d').clearRect(0, 0, ctx.width, ctx.height);
        ctx.getContext('2d').font = '16px Roboto';
        ctx.getContext('2d').fillStyle = '#666';
        ctx.getContext('2d').textAlign = 'center';
        ctx.getContext('2d').fillText('No change frequency data available', ctx.width / 2, ctx.height / 2);
        return;
    }
    
    // Create a proper heatmap-style chart using daily change frequency data
    const changeData = analyticsData.changeData;
    const heatmapData = generateHeatmapFromChangeData(changeData);
    
    charts.changeHeatmap = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: heatmapData.labels,
            datasets: [{
                label: 'Changes per Day',
                data: heatmapData.values,
                backgroundColor: heatmapData.colors,
                borderColor: '#003b6f',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            return `${context.parsed.y} changes`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                        callback: function(value) {
                            return Math.floor(value);
                        }
                    },
                    title: {
                        display: true,
                        text: 'Number of Changes'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Date'
                    }
                }
            }
        }
    });
}

/**
 * Generate heatmap data from change analysis data
 */
function generateHeatmapFromChangeData(changeData) {
    const labels = [];
    const values = [];
    const colors = [];
    
    // If we have real daily change data, use it
    if (changeData?.daily_changes && changeData.daily_changes.length > 0) {
        changeData.daily_changes.forEach(day => {
            labels.push(new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            values.push(day.count);
            
            // Color intensity based on change count (heat map effect)
            const intensity = Math.min(day.count / 10, 1); // Normalize to 0-1
            const alpha = 0.3 + (intensity * 0.7); // 0.3 to 1.0 alpha
            colors.push(`rgba(0, 59, 111, ${alpha})`);
        });
    } else {
        // Generate mock data for the last 14 days
        for (let i = 13; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            
            const changeCount = Math.floor(Math.random() * 8); // 0-8 changes per day
            values.push(changeCount);
            
            // Color intensity based on change count
            const intensity = Math.min(changeCount / 8, 1);
            const alpha = 0.3 + (intensity * 0.7);
            colors.push(`rgba(0, 59, 111, ${alpha})`);
        }
    }
    
    return { labels, values, colors };
}

// Mock data generation functions (for when APIs aren't ready)

function generateMockAccuracyData() {
    const labels = [];
    const values = [];
    
    for (let i = 29; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
        values.push(Math.random() * 20 + 70); // 70-90% accuracy
    }
    
    return { labels, values };
}

function generateMockChangeReasons() {
    return {
        labels: ['Operational', 'Vessel-Related', 'Commercial', 'Terminal', 'Regulatory', 'Other'],
        values: [25, 20, 15, 18, 12, 10]
    };
}

function generateMockEfficiencyData() {
    const labels = [];
    const values = [];
    
    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('en-US', { weekday: 'short' }));
        values.push(Math.random() * 20 + 75); // 75-95% efficiency
    }
    
    return { labels, values };
}

function generateMockHeatmapData() {
    const labels = [];
    const values = [];
    
    for (let i = 13; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('en-US', { month: 'numeric', day: 'numeric' }));
        values.push(Math.floor(Math.random() * 8)); // 0-8 changes per day
    }
    
    return { labels, values };
}

// Utility functions

function formatPercentage(value) {
    return Math.round(value) + '%';
}

function formatNumber(value, decimals = 0) {
    return Number(value).toFixed(decimals);
}

function showLoadingState() {
    // Show loading spinners in KPI cards
    const kpiValues = document.querySelectorAll('.kpi-value');
    kpiValues.forEach(el => {
        el.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    });
    
    const trends = document.querySelectorAll('.kpi-trend');
    trends.forEach(el => {
        el.innerHTML = '<i class="fas fa-circle"></i> Loading...';
        el.className = 'kpi-trend trend-stable';
    });
}

function hideLoadingState() {
    console.log('Analytics data loaded successfully');
}

function showErrorState() {
    const kpiValues = document.querySelectorAll('.kpi-value');
    kpiValues.forEach(el => {
        el.textContent = 'Error';
    });
    
    const trends = document.querySelectorAll('.kpi-trend');
    trends.forEach(el => {
        el.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error loading data';
        el.className = 'kpi-trend trend-down';
    });
    
    console.error('Failed to load analytics data');
}