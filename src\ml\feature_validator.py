"""
ML Feature Validator and Missing Feature Handler

This module provides comprehensive feature validation and intelligent missing feature handling
for the ML prediction service, ensuring reliable predictions.
"""

import logging
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import asdict
import numpy as np

from .models import VesselFeatures

logger = logging.getLogger(__name__)


class MLFeatureValidator:
    """
    Validates and handles missing features for ML predictions
    """
    # Ensure heavy vessel identity mapping is initialized only once per process
    _identity_mapping_initialized: bool = False
    
    def __init__(self):
        # Expected features from the gradient boosting model
        self.expected_features = [
            'product_quantity', 'vessel_type', 'operation_type', 
            'product_type', 'vessel_name', 'customer_name', 'location'
        ]
        
        # Critical features that must be provided or intelligently inferred
        self.critical_features = [
            'product_quantity', 'vessel_type', 'product_type'
        ]
        
        # Initialize vessel identity mapping system
        self._init_vessel_identity_mapping()
        
        # Feature encodings from the trained model
        self.feature_encodings = {
            'vessel_type': {'Barge': 0, 'Vessel': 1},
            'operation_type': {'Ex': 0, 'In': 1},  # Ex=Export/Loading, In=Import/Discharge
            'product_type': {
                'NAPHTA': 0, 'BENZEEN E': 1, 'PROPYLEENOXIDE': 2, 'SBP 100/140': 3,
                'CHEMFEED NAPHTA': 4, 'GTL FLUID G100': 5, '1-HEXEEN': 6, 'GAS CONDENSATES': 7,
                'BENZENE HEARTCUT': 8, 'PYGAS UNHYDROGENATED': 9, 'Pygas': 10, 'NAPHTA FRSR': 11,
                'ACRYLONITRIL': 12, 'BENZENE HEARTCUT-BHC (ZR)': 13, 'NAPHTHA CATALYTIC REFORMED': 14,
                'LIGHT NAPHTA': 15, 'NESTE CIRCULAR MIDDLE DISTILLATE': 16, 
                'HEAVY NAPHTA (petroleum) HYDROCRACKED': 17, 'NAPHTA - GASOLINE': 18,
                'NAPHTA (Hydro,Heavy)': 19, 'GTL SARALINE 185V': 20, 'NATURAL GAS CONDENSATES': 21,
                'DSD 482.02 DEVELOPMENTAL POLYOL': 22
            },
            'customer_name': {
                'Trafigura PTE LTD': 0,
                'Shell Trading Rotterdam BV': 1,
                'Dow Benelux BV': 2,
                'Chevron B.V.': 3,
                'Trinseo netherlands BV': 4,
                'LYB B.V. on behalf of Covestro PO LLC': 5,
                'Lyondell Chemie Nederland B.V.': 6,
                'BA Trading BV': 7,
                'Basell Polyolefine GmbH': 8
            },
            'location': {'Jetty 6': 0, 'jetty 2': 1, 'jetty 1': 2, 'jetty 5': 3, 'Jetty 4': 4}
        }
        
        # Preprocessing info from the trained model
        self.preprocessing_info = {
            'product_quantity': {'type': 'robust_scaling', 'median': 1965.57, 'iqr': 3177.56},
            'vessel_type': {'type': 'centering', 'median': 0.0},
            'operation_type': {'type': 'robust_scaling', 'median': 0.0, 'iqr': 1.0},
            'product_type': {'type': 'robust_scaling', 'median': 1.5, 'iqr': 9.0},
            'vessel_name': {'type': 'robust_scaling', 'median': 74.0, 'iqr': 98.75},
            'customer_name': {'type': 'robust_scaling', 'median': 2.0, 'iqr': 2.0},
            'location': {'type': 'robust_scaling', 'median': 1.0, 'iqr': 2.0}
        }
    
    def validate_and_prepare_features(self, vessel_features: VesselFeatures) -> Tuple[Dict[str, Any], List[str]]:
        """
        Validate vessel features and prepare them for ML model prediction
        
        Args:
            vessel_features: Rich VesselFeatures object
            
        Returns:
            Tuple of (prepared_features_dict, warnings_list)
        """
        warnings = []
        prepared_features = {}
        
        # Convert VesselFeatures to dict for easier handling
        features_dict = asdict(vessel_features)
        
        # Map each expected feature
        for feature_name in self.expected_features:
            try:
                value, warning = self._map_single_feature(feature_name, features_dict)
                prepared_features[feature_name] = value
                if warning:
                    warnings.append(warning)
            except Exception as e:
                logger.error(f"Error mapping feature {feature_name}: {e}")
                # Use intelligent default
                prepared_features[feature_name] = self._get_safe_default(feature_name, features_dict)
                warnings.append(f"Error mapping {feature_name}, used safe default")
        
        return prepared_features, warnings
    
    def _map_single_feature(self, feature_name: str, vessel_features: Dict[str, Any]) -> Tuple[Any, Optional[str]]:
        """
        Map a single feature from vessel data to model format
        
        Returns:
            Tuple of (feature_value, warning_message)
        """
        warning = None
        
        if feature_name == 'product_quantity':
            # Direct mapping from cargo volume
            return float(vessel_features.get('cargo_volume', 2000.0)), None
        
        elif feature_name == 'vessel_type':
            # Map vessel type to historical encoding
            vessel_type = vessel_features.get('vessel_type', 'TANKER')
            if vessel_type == 'TANKER':
                return 1, None  # Vessel
            elif vessel_type == 'BARGE':
                return 0, None  # Barge
            else:
                warning = f"Unknown vessel type '{vessel_type}', defaulting to Vessel"
                return 1, warning
        
        elif feature_name == 'operation_type':
            # Use direct operation types when available
            operation_types = vessel_features.get('operation_types', [])
            if operation_types:
                # Use the first operation type if multiple
                op_type = operation_types[0]
                if op_type == 'Ex':
                    return 0, None  # Ex (Export/Loading)
                elif op_type == 'In':
                    return 1, None  # In (Import/Discharge)
            
            # Fallback: Infer from operational characteristics
            requires_vapor_return = vessel_features.get('requires_vapor_return', False)
            product_hazard = vessel_features.get('product_hazard_level', 'standard')
            
            if requires_vapor_return or product_hazard in ['hazardous', 'highly_hazardous']:
                return 0, "Inferred Export from hazard characteristics"  # Ex (Export/Loading)
            else:
                return 1, "Inferred Import as default"  # In (Import/Discharge)
        
        elif feature_name == 'product_type':
            # Map product types to historical encoding
            product_types = vessel_features.get('product_types', [])
            product_hazard = vessel_features.get('product_hazard_level', 'standard')
            
            if not product_types:
                # Use hazard level to infer product type
                if product_hazard == 'highly_hazardous':
                    return 2, "No product specified, inferred PROPYLEENOXIDE from hazard level"  # PROPYLEENOXIDE
                elif product_hazard == 'hazardous':
                    return 1, "No product specified, inferred BENZEEN E from hazard level"  # BENZEEN E
                else:
                    return 0, "No product specified, defaulted to NAPHTA"  # NAPHTA
            
            # Try to match product types to known encodings
            for product in product_types:
                product_upper = product.upper()
                if product_upper in self.feature_encodings['product_type']:
                    return self.feature_encodings['product_type'][product_upper], None
                
                # Fuzzy matching for common variations
                if 'NAPHTA' in product_upper or 'NAPHTHA' in product_upper:
                    return 0, f"Mapped '{product}' to NAPHTA"
                elif 'BENZENE' in product_upper:
                    return 1, f"Mapped '{product}' to BENZEEN E"
                elif 'PROPYLENE' in product_upper:
                    return 2, f"Mapped '{product}' to PROPYLEENOXIDE"
            
            # Default based on hazard level
            if product_hazard == 'highly_hazardous':
                return 2, f"Unknown product '{product_types[0]}', inferred from hazard level"
            elif product_hazard == 'hazardous':
                return 1, f"Unknown product '{product_types[0]}', inferred from hazard level"
            else:
                return 0, f"Unknown product '{product_types[0]}', defaulted to NAPHTA"
        
        elif feature_name == 'vessel_name':
            # Use sophisticated vessel identity mapping (Phase 3 ML Feature Alignment)
            vessel_id = self._map_vessel_to_historical_identity(vessel_features)
            
            # Log the mapping for debugging
            dwt = vessel_features.get('dwt', 25000)
            vessel_type = vessel_features.get('vessel_type', 'TANKER')
            customer_name = vessel_features.get('customer_name', 'Unknown')
            size_class = self._classify_vessel_size(dwt)
            
            logger.debug(f"Vessel identity mapping: {vessel_type} {size_class} (DWT: {dwt:,.0f}) "
                        f"Customer: {customer_name} -> vessel_id: {vessel_id}")
            
            return vessel_id, f"Mapped to historical vessel pattern (ID: {vessel_id})"
        
        elif feature_name == 'customer_name':
            # Use direct customer name when available
            customer_name = vessel_features.get('customer_name')
            if customer_name and customer_name in self.feature_encodings['customer_name']:
                return self.feature_encodings['customer_name'][customer_name], None
            
            # Fallback: Infer customer type from product and operation characteristics
            product_hazard = vessel_features.get('product_hazard_level', 'standard')
            requires_vapor_return = vessel_features.get('requires_vapor_return', False)
            vessel_type = vessel_features.get('vessel_type', 'TANKER')
            
            if customer_name:
                warning = f"Unknown customer '{customer_name}', inferring from characteristics"
            else:
                warning = "No customer provided, inferring from characteristics"
            
            if product_hazard == 'highly_hazardous':
                return 2, warning  # Dow Benelux BV equivalent
            elif requires_vapor_return:
                return 1, warning  # Shell Trading equivalent
            elif vessel_type == 'BARGE':
                return 6, warning  # Lyondell equivalent
            else:
                return 0, warning  # Trafigura equivalent
        
        elif feature_name == 'location':
            # First check for preferred jetty from nomination form
            preferred_jetty = vessel_features.get('preferred_jetty')
            if preferred_jetty:
                # Map preferred jetty to location encoding
                jetty_mapping = {
                    'jetty1': 2,  # jetty 1
                    'jetty2': 1,  # jetty 2  
                    'jetty3': 1,  # jetty 2 (fallback, jetty 3 not in training)
                    'jetty4': 4,  # Jetty 4
                    'jetty5': 3,  # jetty 5
                    'jetty6': 0   # Jetty 6
                }
                if preferred_jetty in jetty_mapping:
                    return jetty_mapping[preferred_jetty], f"Using preferred jetty: {preferred_jetty}"
            
            # Map jetty information
            jetty_id = vessel_features.get('jetty_id')
            max_flow_rate = vessel_features.get('max_flow_rate') or 0
            
            if jetty_id:
                # Try to map jetty ID to location encoding
                jetty_str = str(jetty_id).lower()
                for loc_name, loc_id in self.feature_encodings['location'].items():
                    if jetty_str in loc_name.lower():
                        return loc_id, None
            
            # Use flow rate to infer jetty (handle None case)
            if max_flow_rate and max_flow_rate >= 4000:
                return 3, "Inferred jetty 5 from high flow rate"  # jetty 5
            elif max_flow_rate and max_flow_rate >= 2000:
                return 2, "Inferred jetty 1 from medium flow rate"  # jetty 1
            else:
                return 1, "Defaulted to jetty 2"  # jetty 2
        
        # Should not reach here
        return 0, f"Unknown feature {feature_name}, used default"
    
    def _get_safe_default(self, feature_name: str, vessel_features: Dict[str, Any]) -> Any:
        """Get a safe default value for a feature"""
        defaults = {
            'product_quantity': 2000.0,  # Reasonable cargo volume
            'vessel_type': 1,  # Vessel (most common)
            'operation_type': 1,  # In (Import/Discharge)
            'product_type': 0,  # NAPHTA (most common)
            'vessel_name': 50,  # Mid-range vessel ID
            'customer_name': 0,  # Default customer
            'location': 1  # jetty 2 (default)
        }
        return defaults.get(feature_name, 0)
    
    def _init_vessel_identity_mapping(self):
        """
        Initialize the vessel identity mapping system based on historical patterns.
        
        This implements the Phase 3 strategy from ML_FEATURE_ALIGNMENT_PLAN.md:
        - Create vessel classes based on size/type/customer/product combinations
        - Map new vessels to similar historical vessels
        - Handle the vessel_name feature (100% importance) intelligently
        """
        
        # Vessel size classes based on DWT ranges observed in terminal operations
        self.vessel_size_classes = {
            'small': (0, 5000),      # Small barges, coastal vessels
            'medium': (5000, 15000), # Medium tankers, large barges
            'large': (15000, 60000), # Large tankers, small product carriers
            'vlarge': (60000, float('inf'))  # VLCCs, large product carriers
        }
        
        # Historical vessel identity mappings based on training data patterns
        # These represent the 0-221 vessel_name encodings from the trained model
        self.historical_vessel_identities = {
            # Format: (vessel_type, size_class, customer_id, product_type_id) -> vessel_id_range
            
            # Trafigura vessels (customer_id=0) - Major trader, diverse products
            ('TANKER', 'small', 0, 0): (10, 25),    # Small tankers, NAPHTA
            ('TANKER', 'medium', 0, 0): (35, 55),   # Medium tankers, NAPHTA
            ('TANKER', 'large', 0, 0): (65, 85),    # Large tankers, NAPHTA
            ('TANKER', 'medium', 0, 1): (90, 105),  # Medium tankers, BENZEEN E
            ('BARGE', 'small', 0, 0): (5, 15),      # Small barges, NAPHTA
            ('BARGE', 'medium', 0, 0): (20, 35),    # Medium barges, NAPHTA
            
            # Shell Trading (customer_id=1) - Major oil company
            ('TANKER', 'medium', 1, 0): (110, 125), # Medium tankers, NAPHTA
            ('TANKER', 'large', 1, 0): (130, 150),  # Large tankers, NAPHTA
            ('TANKER', 'large', 1, 7): (155, 165),  # Large tankers, GAS CONDENSATES
            ('TANKER', 'vlarge', 1, 0): (170, 185), # Very large tankers, NAPHTA
            
            # Dow Benelux (customer_id=2) - Chemicals specialist
            ('TANKER', 'medium', 2, 1): (40, 55),   # Medium tankers, BENZEEN E
            ('TANKER', 'medium', 2, 2): (60, 75),   # Medium tankers, PROPYLEENOXIDE
            ('TANKER', 'large', 2, 2): (80, 95),    # Large tankers, PROPYLEENOXIDE
            ('TANKER', 'medium', 2, 12): (100, 115), # Medium tankers, ACRYLONITRIL
            
            # Chevron (customer_id=3) - Oil company
            ('TANKER', 'large', 3, 0): (120, 135),  # Large tankers, NAPHTA
            ('TANKER', 'medium', 3, 4): (140, 155), # Medium tankers, CHEMFEED NAPHTA
            
            # Trinseo (customer_id=4) - Styrenic specialist
            ('TANKER', 'medium', 4, 1): (45, 60),   # Medium tankers, BENZEEN E
            ('TANKER', 'medium', 4, 8): (65, 80),   # Medium tankers, BENZENE HEARTCUT
            
            # LYB/Covestro (customer_id=5) - Polyurethane chemicals
            ('TANKER', 'medium', 5, 2): (70, 85),   # Medium tankers, PROPYLEENOXIDE
            ('TANKER', 'large', 5, 2): (90, 105),   # Large tankers, PROPYLEENOXIDE
            
            # Lyondell (customer_id=6) - Petrochemicals
            ('TANKER', 'large', 6, 0): (110, 125),  # Large tankers, NAPHTA
            ('TANKER', 'medium', 6, 1): (75, 90),   # Medium tankers, BENZEEN E
            ('BARGE', 'medium', 6, 0): (25, 40),    # Medium barges, NAPHTA
            
            # BA Trading (customer_id=7) - Trading company
            ('TANKER', 'medium', 7, 0): (50, 65),   # Medium tankers, NAPHTA
            ('TANKER', 'medium', 7, 1): (70, 85),   # Medium tankers, BENZEEN E
            
            # Basell Polyolefine (customer_id=8) - Polyolefins
            ('TANKER', 'large', 8, 0): (130, 145),  # Large tankers, NAPHTA
            ('TANKER', 'medium', 8, 4): (95, 110),  # Medium tankers, CHEMFEED NAPHTA
        }
        
        # Default vessel identity ranges for unknown combinations
        self.default_vessel_ranges = {
            ('TANKER', 'small'): (15, 30),
            ('TANKER', 'medium'): (45, 75),
            ('TANKER', 'large'): (100, 140),
            ('TANKER', 'vlarge'): (160, 200),
            ('BARGE', 'small'): (5, 20),
            ('BARGE', 'medium'): (25, 45),
            ('BARGE', 'large'): (50, 70),
        }
        
        # Log only once to avoid excessive noise during optimization loops
        if not MLFeatureValidator._identity_mapping_initialized:
            logger.debug("Initialized sophisticated vessel identity mapping system with historical patterns")
            MLFeatureValidator._identity_mapping_initialized = True
    
    def _classify_vessel_size(self, dwt: float) -> str:
        """Classify vessel into size category based on DWT"""
        for size_class, (min_dwt, max_dwt) in self.vessel_size_classes.items():
            if min_dwt <= dwt < max_dwt:
                return size_class
        return 'vlarge'  # Default for very large vessels
    
    def _map_vessel_to_historical_identity(self, vessel_features: Dict[str, Any]) -> int:
        """
        Map a vessel to a historical vessel identity using sophisticated matching.
        
        This implements the hybrid approach described in the ML Feature Alignment Plan:
        - Use vessel characteristics + customer + product to create pseudo-identity
        - Map to similar historical vessels in the same operational class
        
        Args:
            vessel_features: Dictionary of vessel features
            
        Returns:
            Vessel identity ID in range 0-221 (matching training data)
        """
        
        # Extract key characteristics
        dwt = vessel_features.get('dwt', 25000)
        vessel_type = vessel_features.get('vessel_type', 'TANKER').upper()
        customer_name = vessel_features.get('customer_name')
        product_types = vessel_features.get('product_types', [])
        is_first_visit = vessel_features.get('is_first_visit', False)
        
        # Classify vessel size
        size_class = self._classify_vessel_size(dwt)
        
        # Get customer encoding
        customer_id = 0  # Default to Trafigura
        if customer_name and customer_name in self.feature_encodings['customer_name']:
            customer_id = self.feature_encodings['customer_name'][customer_name]
        
        # Get primary product type encoding
        product_type_id = 0  # Default to NAPHTA
        if product_types:
            primary_product = product_types[0].upper()
            if primary_product in self.feature_encodings['product_type']:
                product_type_id = self.feature_encodings['product_type'][primary_product]
        
        # Create vessel class key
        vessel_class_key = (vessel_type, size_class, customer_id, product_type_id)
        
        # Try to find exact match in historical patterns
        if vessel_class_key in self.historical_vessel_identities:
            vessel_range = self.historical_vessel_identities[vessel_class_key]
            base_vessel_id = vessel_range[0] + (vessel_range[1] - vessel_range[0]) // 2
            
            # Add variation based on specific characteristics
            variation = 0
            if is_first_visit:
                variation += 5  # First visits get higher complexity factor
            
            # Add variation based on DWT within size class
            size_min, size_max = self.vessel_size_classes[size_class]
            if size_max != float('inf'):
                dwt_ratio = (dwt - size_min) / (size_max - size_min)
                variation += int(dwt_ratio * 10)  # 0-10 variation based on size within class
            
            vessel_id = base_vessel_id + variation
            
            # Ensure within valid range and historical range
            vessel_id = max(vessel_range[0], min(vessel_range[1], vessel_id))
            
            logger.debug(f"Mapped vessel to historical identity: {vessel_class_key} -> {vessel_id} "
                        f"(range: {vessel_range}, variation: {variation})")
            
            return vessel_id
        
        # Fallback: Use vessel type and size only
        fallback_key = (vessel_type, size_class)
        if fallback_key in self.default_vessel_ranges:
            vessel_range = self.default_vessel_ranges[fallback_key]
            base_vessel_id = vessel_range[0] + (vessel_range[1] - vessel_range[0]) // 2
            
            # Add customer and product variation
            variation = (customer_id * 3) + (product_type_id % 5)
            if is_first_visit:
                variation += 8
                
            vessel_id = base_vessel_id + variation
            vessel_id = max(vessel_range[0], min(vessel_range[1], vessel_id))
            
            logger.debug(f"Used fallback vessel mapping: {fallback_key} -> {vessel_id} "
                        f"(customer: {customer_id}, product: {product_type_id})")
            
            return vessel_id
        
        # Ultimate fallback: Use the old DWT-based approach but improved
        dwt_based_id = int(dwt / 1000)
        if vessel_type == 'BARGE':
            dwt_based_id = min(dwt_based_id, 20)  # Barges typically smaller vessel IDs
        else:
            dwt_based_id = min(dwt_based_id + 30, 200)  # Tankers get higher base IDs
            
        if is_first_visit:
            dwt_based_id += 15
            
        # Ensure within 0-221 range
        vessel_id = max(0, min(221, dwt_based_id))
        
        logger.warning(f"Used ultimate fallback vessel mapping: DWT {dwt} -> {vessel_id} "
                      f"({vessel_type}, first_visit: {is_first_visit})")
        
        return vessel_id
    
    def apply_preprocessing(self, features: Dict[str, Any]) -> np.ndarray:
        """
        Apply the same preprocessing as used during model training
        
        Args:
            features: Dictionary of feature values
            
        Returns:
            Preprocessed feature array ready for model
        """
        processed_values = []
        
        for feature_name in self.expected_features:
            value = features[feature_name]
            preprocessing = self.preprocessing_info.get(feature_name, {})
            
            if preprocessing.get('type') == 'robust_scaling':
                median = preprocessing.get('median', 0)
                iqr = preprocessing.get('iqr', 1)
                processed_value = (value - median) / iqr if iqr != 0 else 0
            elif preprocessing.get('type') == 'centering':
                median = preprocessing.get('median', 0)
                processed_value = value - median
            else:
                processed_value = value
            
            processed_values.append(processed_value)
        
        return np.array(processed_values).reshape(1, -1)
    
    def validate_critical_features(self, vessel_features: VesselFeatures) -> Dict[str, Any]:
        """
        Validate that critical features can be properly extracted
        
        Returns:
            Validation report with is_valid flag and any issues
        """
        issues = []
        features_dict = asdict(vessel_features)
        
        # Check cargo volume
        if features_dict.get('cargo_volume', 0) <= 0:
            issues.append("cargo_volume must be positive for product_quantity")
        
        # Check vessel type (case-insensitive)
        vessel_type = features_dict.get('vessel_type')
        try:
            vt = (str(vessel_type or '')).strip().upper()
        except Exception:
            vt = ''
        if vt in ['TANKER', 'BARGE']:
            # Normalize back into the features dict for downstream use
            features_dict['vessel_type'] = vt
        else:
            issues.append(f"vessel_type '{vessel_type}' not recognized (should be TANKER or BARGE)")
        
        # Check product information
        product_types = features_dict.get('product_types', [])
        product_hazard = features_dict.get('product_hazard_level', 'standard')
        if not product_types and product_hazard == 'standard':
            issues.append("No product information available (product_types empty and hazard level standard)")
        
        return {
            'is_valid': len(issues) == 0,
            'issues': issues,
            'feature_count': len(self.expected_features),
            'critical_feature_count': len(self.critical_features)
        }
    
    def get_feature_info(self) -> Dict[str, Any]:
        """Get information about expected features"""
        return {
            'expected_features': self.expected_features,
            'critical_features': self.critical_features,
            'feature_encodings': self.feature_encodings,
            'preprocessing_types': {
                name: info.get('type', 'none') 
                for name, info in self.preprocessing_info.items()
            }
        }
