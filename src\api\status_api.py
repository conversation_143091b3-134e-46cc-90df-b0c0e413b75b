"""
Status API Module

This module provides API endpoints for status-related operations.
"""
from typing import Dict, List, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel

from src.utils.status_utils import (
    normalize_status,
    is_valid_vessel_status,
    is_valid_assignment_status,
    is_valid_vessel_transition,
    is_valid_assignment_transition,
    get_valid_next_statuses,
    VALID_VESSEL_STATUSES,
    VALID_ASSIGNMENT_STATUSES,
    VALID_VESSEL_TRANSITIONS,
    VALID_ASSIGNMENT_TRANSITIONS
)

router = APIRouter(prefix="/api/status", tags=["status"])

class StatusTransitionCheck(BaseModel):
    """Request body for checking if a status transition is valid"""
    current_status: str
    new_status: str


@router.get("/vessel-statuses", response_model=List[str])
async def get_vessel_statuses():
    """Get all valid vessel statuses"""
    return list(VALID_VESSEL_STATUSES)


@router.get("/assignment-statuses", response_model=List[str])
async def get_assignment_statuses():
    """Get all valid assignment statuses"""
    return list(VALID_ASSIGNMENT_STATUSES)


@router.get("/vessel-transitions", response_model=Dict[str, List[str]])
async def get_vessel_transitions():
    """Get all valid vessel status transitions"""
    # Convert set values to lists for JSON serialization
    return {k: list(v) for k, v in VALID_VESSEL_TRANSITIONS.items()}


@router.get("/assignment-transitions", response_model=Dict[str, List[str]])
async def get_assignment_transitions():
    """Get all valid assignment status transitions"""
    # Convert set values to lists for JSON serialization
    return {k: list(v) for k, v in VALID_ASSIGNMENT_TRANSITIONS.items()}


@router.get("/vessel-next-statuses/{current_status}", response_model=List[str])
async def get_vessel_next_statuses(current_status: str):
    """Get all valid next statuses for a vessel based on current status"""
    normalized = normalize_status(current_status)
    if not is_valid_vessel_status(normalized):
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid vessel status: {current_status}"
        )
    
    return get_valid_next_statuses(normalized, is_vessel=True)


@router.get("/assignment-next-statuses/{current_status}", response_model=List[str])
async def get_assignment_next_statuses(current_status: str):
    """Get all valid next statuses for an assignment based on current status"""
    normalized = normalize_status(current_status)
    if not is_valid_assignment_status(normalized):
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid assignment status: {current_status}"
        )
    
    return get_valid_next_statuses(normalized, is_vessel=False)


@router.post("/check-vessel-transition", response_model=Dict[str, bool])
async def check_vessel_transition(data: StatusTransitionCheck):
    """
    Check if a vessel status transition is valid
    
    Returns:
        Dict with 'valid' key indicating if transition is valid
    """
    current_status = normalize_status(data.current_status)
    new_status = normalize_status(data.new_status)
    
    is_valid, _ = is_valid_vessel_transition(current_status, new_status)
    
    return {"valid": is_valid}


@router.post("/check-assignment-transition", response_model=Dict[str, bool])
async def check_assignment_transition(data: StatusTransitionCheck):
    """
    Check if an assignment status transition is valid
    
    Returns:
        Dict with 'valid' key indicating if transition is valid
    """
    current_status = normalize_status(data.current_status)
    new_status = normalize_status(data.new_status)
    
    is_valid, _ = is_valid_assignment_transition(current_status, new_status)
    
    return {"valid": is_valid}


@router.get("/normalize-status/{status}", response_model=str)
async def normalize_status_endpoint(status: str):
    """
    Normalize a status string to standardized format.
    
    Args:
        status: The status string to normalize
        
    Returns:
        Normalized status string
    """
    return normalize_status(status) 