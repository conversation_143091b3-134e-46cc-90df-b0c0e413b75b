# Start Complete Jetty Planner Stack
# Usage: .\scripts\Start-FullStack.ps1

param(
    [switch]$Detached = $true,
    [switch]$Build = $false
)

Write-Host "🚀 Starting Jetty Planner Full Stack..." -ForegroundColor Green
Write-Host ""

# Build if requested
if ($Build) {
    Write-Host "🔨 Building application..." -ForegroundColor Yellow
    docker-compose build --no-cache jetty-planning-app
    Write-Host ""
}

# Start all services
$composeArgs = @("up")
if ($Detached) {
    $composeArgs += "-d"
}

Write-Host "Starting services..." -ForegroundColor Yellow
& docker-compose @composeArgs

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to start services" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow

# Wait for PostgreSQL
$maxAttempts = 30
$attempt = 0
do {
    Start-Sleep -Seconds 2
    $attempt++
    $pgHealth = docker inspect jetty-postgres --format='{{.State.Health.Status}}' 2>$null
    Write-Host "  PostgreSQL - Attempt $attempt/$maxAttempts - Status: $pgHealth" -ForegroundColor Cyan
} while ($pgHealth -ne "healthy" -and $attempt -lt $maxAttempts)

if ($pgHealth -ne "healthy") {
    Write-Host "❌ PostgreSQL failed to start" -ForegroundColor Red
    docker-compose logs postgres
    exit 1
}

# Wait for Application
$attempt = 0
do {
    Start-Sleep -Seconds 2
    $attempt++
    $appHealth = docker inspect jetty-planning-app --format='{{.State.Health.Status}}' 2>$null
    Write-Host "  Application - Attempt $attempt/$maxAttempts - Status: $appHealth" -ForegroundColor Cyan
} while ($appHealth -ne "healthy" -and $attempt -lt $maxAttempts)

Write-Host ""
Write-Host "✅ Jetty Planner Full Stack is ready!" -ForegroundColor Green
Write-Host ""

Write-Host "🌐 Service URLs:" -ForegroundColor Yellow
Write-Host "  Application:      http://localhost:7000" -ForegroundColor White
Write-Host "  pgAdmin:          http://localhost:8800" -ForegroundColor White
Write-Host "    - Email:        <EMAIL>" -ForegroundColor Gray
Write-Host "    - Password:     admin123" -ForegroundColor Gray
Write-Host "  Backup Monitor:   http://localhost:8081" -ForegroundColor White
Write-Host ""

Write-Host "🗄️  Database Info:" -ForegroundColor Yellow
Write-Host "  Host:             localhost" -ForegroundColor White
Write-Host "  Port:             7432" -ForegroundColor White
Write-Host "  Database:         planner" -ForegroundColor White
Write-Host "  User:             postgres" -ForegroundColor White
Write-Host ""

Write-Host "📊 Container Status:" -ForegroundColor Yellow
docker ps --filter "name=jetty-" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

Write-Host ""
Write-Host "💾 Backup Schedule:" -ForegroundColor Yellow
Write-Host "  Daily:            2:00 AM (kept for 30 days)" -ForegroundColor White
Write-Host "  Weekly:           Sundays (kept for 8 weeks)" -ForegroundColor White
Write-Host "  Monthly:          1st of month (kept for 6 months)" -ForegroundColor White
Write-Host ""

Write-Host "🔧 Useful Commands:" -ForegroundColor Yellow
Write-Host "  Stop all:         docker-compose down" -ForegroundColor White
Write-Host "  View logs:        docker-compose logs -f [service]" -ForegroundColor White
Write-Host "  Manual backup:    docker exec jetty-postgres-backup /backup.sh" -ForegroundColor White
