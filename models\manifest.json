{"name": "gradient_boosting_20250820_120027.joblib", "friendly_name": "gradient_boosting_20250820_120027", "algorithm": "gradient_boosting", "target_column": "terminal_time", "feature_columns": ["product_quantity", "vessel_type", "operation_type", "product_type", "vessel_name", "customer_name", "location"], "performance": {"mse": 408682.6530421011, "rmse": 639.2829209685655, "mae": 418.06876592995883, "r2": 0.6091596844881857, "mape": 30.862706232279706, "weighted_mape": 26.055344913442646, "mase": 0.6089401668637395, "has_zero_values": 0, "zero_value_percent": 0.0}, "timestamp": "20250820_120027", "framework": {"python": "3.13.3", "sklearn": "1.7.1", "xgboost": "3.0.4"}, "notes": ""}