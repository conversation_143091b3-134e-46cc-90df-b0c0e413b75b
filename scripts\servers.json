{"Servers": {"1": {"Name": "Jetty Planner Database", "Group": "Servers", "Host": "postgres", "Port": 5432, "MaintenanceDB": "planner", "Username": "postgres", "SSLMode": "prefer", "SSLCert": "<STORAGE_DIR>/.postgresql/postgresql.crt", "SSLKey": "<STORAGE_DIR>/.postgresql/postgresql.key", "SSLCompression": 0, "Timeout": 10, "UseSSHTunnel": 0, "TunnelPort": "22", "TunnelAuthentication": 0, "SharedPreload": "connection pooling, performance insights"}}}