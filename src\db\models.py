from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Float, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.types import JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy import Index

Base = declarative_base()


class Terminal(Base):
    __tablename__ = "terminals"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    location_lat = Column(Float, nullable=False)
    location_lon = Column(Float, nullable=False)
    total_capacity_cbm = Column(Float, nullable=False)
    number_of_tanks = Column(Integer, nullable=False)
    draft_meters = Column(Float, nullable=False)
    operational_since = Column(DateTime, nullable=False)
    vessel_berths = Column(Integer, nullable=False, default=0)
    barge_berths = Column(Integer, nullable=False, default=0)
    timezone = Column(String, nullable=False, default='Europe/Brussels')
    currency = Column(String, nullable=False, default='EUR')
    is_active = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    settings = relationship("Setting", back_populates="terminal")
    assignments = relationship("Assignment", back_populates="terminal")
    vessels = relationship("Vessel", back_populates="terminal")
    jetties = relationship("Jetty", back_populates="terminal")
    tanks = relationship("Tank", back_populates="terminal")
    pumps = relationship("Pump", back_populates="terminal")
    surveyors = relationship("Surveyor", back_populates="terminal")
    cargoes = relationship("Cargo", back_populates="terminal")
    nominations = relationship("Nomination", back_populates="terminal")
    assignment_changes = relationship("AssignmentChange", back_populates="terminal")
    ml_predictions = relationship("MLPredictionLog", back_populates="terminal")
    planning_metrics = relationship("PlanningMetrics", back_populates="terminal")
    change_analysis = relationship("ChangeAnalysis", back_populates="terminal")
    performance_alerts = relationship("PerformanceAlert", back_populates="terminal")


class Setting(Base):
    __tablename__ = "settings"

    key = Column(String, primary_key=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), primary_key=True)
    value = Column(Text, nullable=False)
    category = Column(String, nullable=False)
    is_sensitive = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    terminal = relationship("Terminal", back_populates="settings")


class Assignment(Base):
    __tablename__ = "assignments"

    id = Column(Integer, primary_key=True, autoincrement=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    vessel_id = Column(String, nullable=False)
    vessel_name = Column(String, nullable=False)
    vessel_type = Column(String, nullable=False)
    jetty_name = Column(String, nullable=False)
    cargo_product = Column(String)
    cargo_volume = Column(Float)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=False)
    status = Column(String, nullable=False, default='SCHEDULED')

    # ETA tracking for assignments
    original_eta = Column(DateTime)  # Original ETA when assignment was created
    calculated_eta = Column(DateTime)  # System-calculated ETA at time of assignment
    eta_confidence = Column(Integer, nullable=False, default=50)  # Confidence score (0-100)
    eta_source = Column(String(20), nullable=False, default='user')  # Source of ETA
    # Enhanced vessel linkage
    vessel_db_id = Column(Integer, ForeignKey("vessel_registry.id"), nullable=True)
    visit_id = Column(Integer, ForeignKey("vessel_visits.id"), nullable=True)
    nomination_reference = Column(String)
    assignment_type = Column(String, nullable=False, default='SCHEDULED')  # SCHEDULED, EMERGENCY, MAINTENANCE
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Locking fields
    lock_status = Column(String(20), nullable=False, default='UNLOCKED')  # UNLOCKED, SOFT_LOCKED, HARD_LOCKED, TIME_LOCKED
    lock_reason = Column(Text, nullable=True)
    locked_by = Column(String(100), nullable=True)
    locked_at = Column(DateTime, nullable=True)

    # Relationships
    terminal = relationship("Terminal", back_populates="assignments")
    vessel_registry = relationship("VesselRegistry", back_populates="assignments", foreign_keys=[vessel_db_id])
    vessel_visit = relationship("VesselVisit", back_populates="assignments", foreign_keys=[visit_id])


class Vessel(Base):
    __tablename__ = "vessels"

    id = Column(Integer, primary_key=True, autoincrement=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    terminal = relationship("Terminal", back_populates="vessels")
    cargoes = relationship("Cargo", back_populates="vessel")


class Jetty(Base):
    __tablename__ = "jetties"

    id = Column(Integer, primary_key=True, autoincrement=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    vessel_type_restriction = Column(String)  # 'seagoing', 'inland', or 'both'
    min_dwt = Column(Float, nullable=False)
    max_dwt = Column(Float, nullable=False)
    min_loa = Column(Float)  # minimum for inland barges only
    max_loa = Column(Float, nullable=False)
    max_beam = Column(Float, nullable=False)
    max_draft = Column(Float, nullable=False)
    primary_use = Column(String, nullable=False)  # primary product categories
    max_flow_rate = Column(Float)
    is_operational = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    terminal = relationship("Terminal", back_populates="jetties")


class Tank(Base):
    __tablename__ = "tanks"

    id = Column(Integer, primary_key=True, autoincrement=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    capacity = Column(Float, nullable=False)
    current_level = Column(Float, nullable=False, default=0)
    product_type = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    terminal = relationship("Terminal", back_populates="tanks")


class Pump(Base):
    __tablename__ = "pumps"

    id = Column(Integer, primary_key=True, autoincrement=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    flow_rate = Column(Float, nullable=False)
    status = Column(String, nullable=False, default='OPERATIONAL')
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    terminal = relationship("Terminal", back_populates="pumps")


class Surveyor(Base):
    __tablename__ = "surveyors"

    id = Column(Integer, primary_key=True, autoincrement=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    name = Column(String, nullable=False)
    status = Column(String, nullable=False, default='AVAILABLE')
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    terminal = relationship("Terminal", back_populates="surveyors")


class Cargo(Base):
    __tablename__ = "cargoes"

    id = Column(Integer, primary_key=True, autoincrement=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    vessel_id = Column(Integer, ForeignKey("vessels.id"), nullable=False)
    product = Column(String, nullable=False)
    volume = Column(Float, nullable=False, default=0)
    is_loading = Column(Boolean, nullable=False, default=False)
    connection_size = Column(String)
    vapor_return = Column(Boolean, default=False)
    nitrogen_purge = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    terminal = relationship("Terminal", back_populates="cargoes")
    vessel = relationship("Vessel", back_populates="cargoes")


class AssignmentChange(Base):
    __tablename__ = "assignment_changes"

    id = Column(Integer, primary_key=True, autoincrement=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    assignment_id = Column(Integer, nullable=True)
    vessel_id = Column(String)
    vessel_name = Column(String)
    jetty_name = Column(String)
    old_start_time = Column(DateTime)
    old_end_time = Column(DateTime)
    new_start_time = Column(DateTime)
    new_end_time = Column(DateTime)
    reason = Column(Text, nullable=False)
    changed_by = Column(String)
    changed_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    terminal = relationship("Terminal", back_populates="assignment_changes")


class Nomination(Base):
    __tablename__ = "nominations"

    id = Column(Integer, primary_key=True, autoincrement=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    # Stable app-level IDs to correlate runtime vessels (e.g., NV001)
    runtime_vessel_id = Column(String, nullable=False)

    name = Column(String, nullable=False)
    vessel_type = Column(String, nullable=False)
    length = Column(Float)
    beam = Column(Float)
    draft = Column(Float)
    deadweight = Column(Float)
    priority = Column(Integer, default=0)
    capacity = Column(Float)
    width = Column(Float)
    customer = Column(String)

    status = Column(String, nullable=False, default='pending')

    # Standardized ETA fields
    eta = Column(DateTime)  # User-specified/customer-provided ETA
    etd = Column(DateTime)  # User-specified/customer-provided ETD
    calculated_eta = Column(DateTime)  # System-calculated ETA based on AIS/conditions
    eta_confidence = Column(Integer, nullable=False, default=50)  # Confidence score (0-100)
    eta_source = Column(String(20), nullable=False, default='user')  # Source of ETA

    mmsi = Column(String)
    imo = Column(String)

    cargoes = Column(JSON)  # list of {product, volume, is_loading, ...}
    extra_data = Column(JSON)  # JSON data for nominations

    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    terminal = relationship("Terminal", back_populates="nominations")


class MLPredictionLog(Base):
    """Track ML prediction accuracy and performance over time"""
    __tablename__ = "ml_predictions_log"

    id = Column(Integer, primary_key=True, autoincrement=True)
    assignment_id = Column(Integer, nullable=True)  # May not have assignment yet when prediction is made
    vessel_id = Column(String, nullable=True)
    vessel_name = Column(String, nullable=True)
    prediction_type = Column(String, nullable=False)  # 'prepump', 'pump', 'postpump', 'terminal'
    predicted_minutes = Column(Integer, nullable=True)
    actual_minutes = Column(Integer, nullable=True)  # Filled in later when actual data available
    confidence_score = Column(Float, nullable=True)
    prediction_timestamp = Column(DateTime, default=datetime.utcnow)
    actual_timestamp = Column(DateTime, nullable=True)  # When actual value was recorded
    accuracy_percentage = Column(Float, nullable=True)  # Calculated when actual is available
    absolute_error_minutes = Column(Integer, nullable=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    terminal = relationship("Terminal", back_populates="ml_predictions")


class PlanningMetrics(Base):
    """Daily/weekly planning efficiency and performance metrics"""
    __tablename__ = "planning_metrics"

    id = Column(Integer, primary_key=True, autoincrement=True)
    date = Column(DateTime, nullable=False)  # Date for this metrics period
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    total_assignments = Column(Integer, default=0)
    optimized_assignments = Column(Integer, default=0)
    manual_changes = Column(Integer, default=0)
    schedule_utilization_percent = Column(Float, nullable=True)
    average_turnaround_hours = Column(Float, nullable=True)
    throughput_efficiency = Column(Float, nullable=True)
    total_vessels_processed = Column(Integer, default=0)
    idle_time_hours = Column(Float, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    terminal = relationship("Terminal", back_populates="planning_metrics")


class ChangeAnalysis(Base):
    """Extended change tracking with categorization and impact analysis"""
    __tablename__ = "change_analysis"

    id = Column(Integer, primary_key=True, autoincrement=True)
    assignment_id = Column(Integer, nullable=False)
    change_type = Column(String, nullable=False)  # 'start_time', 'end_time', 'jetty', 'vessel'
    change_category = Column(String, nullable=True)  # 'internal_optimization', 'external_factor', 'ml_correction'
    reason_category = Column(String, nullable=True)  # 'operational', 'vessel', 'commercial', 'terminal', 'regulatory', 'other'
    reason_text = Column(Text, nullable=True)
    original_value = Column(String, nullable=True)
    new_value = Column(String, nullable=True)
    change_impact_minutes = Column(Integer, nullable=True)
    change_frequency_score = Column(Float, nullable=True)  # How often this type of change occurs
    vessel_id = Column(String, nullable=True)
    vessel_name = Column(String, nullable=True)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=False)
    changed_by = Column(String, nullable=True)
    changed_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    terminal = relationship("Terminal", back_populates="change_analysis")


class PerformanceAlert(Base):
    """Automated alerts for performance degradation and anomalies"""
    __tablename__ = "performance_alerts"

    id = Column(Integer, primary_key=True, autoincrement=True)
    alert_type = Column(String, nullable=False)  # 'ml_accuracy_drop', 'high_change_frequency', 'low_utilization', etc.
    metric_name = Column(String, nullable=True)
    threshold_value = Column(Float, nullable=True)
    current_value = Column(Float, nullable=True)
    severity = Column(String, default='info')  # 'info', 'warning', 'critical'
    description = Column(Text, nullable=True)
    is_resolved = Column(Boolean, default=False)
    terminal_id = Column(String, ForeignKey("terminals.id"), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    resolved_at = Column(DateTime, nullable=True)
    resolved_by = Column(String, nullable=True)
    
    # Relationships
    terminal = relationship("Terminal", back_populates="performance_alerts")


# New vessel registry master table (separate from per-terminal Vessel)
class VesselRegistry(Base):
    __tablename__ = "vessel_registry"

    id = Column(Integer, primary_key=True, autoincrement=True)

    # Unique Identifiers
    imo = Column(String(10), unique=True)
    mmsi = Column(String(9), unique=True)
    call_sign = Column(String(10))

    # Vessel Identity
    name = Column(String(255), nullable=False)
    previous_names = Column(JSON)  # store as list of strings
    vessel_type = Column(String(50), nullable=False)
    vessel_subtype = Column(String(50))

    # Physical Characteristics
    deadweight = Column(Float)
    gross_tonnage = Column(Float)
    length_overall = Column(Float)
    beam = Column(Float)
    maximum_draft = Column(Float)

    # Registration & Ownership
    flag_state = Column(String(3))
    port_of_registry = Column(String(100))
    owner = Column(String(255))
    operator = Column(String(255))
    manager = Column(String(255))

    # Build Information
    build_year = Column(Integer)
    shipyard = Column(String(255))
    hull_number = Column(String(50))

    # Operational Status
    status = Column(String(20), default='ACTIVE')
    is_blacklisted = Column(Boolean, default=False)

    # Data Sources & Confidence
    data_source = Column(String(50))
    confidence_score = Column(Integer, default=100)
    last_ais_update = Column(DateTime)

    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = Column(String(100))
    notes = Column(Text)

    # Relationships
    visits = relationship("VesselVisit", back_populates="vessel", cascade="all, delete-orphan")
    ais_data = relationship("VesselAISData", back_populates="vessel", cascade="all, delete-orphan")
    assignments = relationship("Assignment", back_populates="vessel_registry")


class VesselVisit(Base):
    __tablename__ = "vessel_visits"

    id = Column(Integer, primary_key=True, autoincrement=True)
    vessel_id = Column(Integer, ForeignKey("vessel_registry.id", ondelete="CASCADE"))
    terminal_id = Column(String, ForeignKey("terminals.id"))

    # Visit Identification
    visit_number = Column(Integer)
    external_reference = Column(String(100))

    # Visit Timeline - Standardized ETA fields
    eta = Column(DateTime)  # User-specified/customer-provided ETA (renamed from estimated_arrival)
    actual_arrival = Column(DateTime)  # Actual recorded arrival time
    calculated_eta = Column(DateTime)  # System-calculated ETA based on AIS/conditions
    eta_confidence = Column(Integer, nullable=False, default=50)  # Confidence score (0-100)
    eta_source = Column(String(20), nullable=False, default='user')  # Source of ETA
    estimated_departure = Column(DateTime)  # User-specified ETD
    actual_departure = Column(DateTime)  # Actual recorded departure time

    # Visit Purpose
    operation_type = Column(String(20))
    cargo_types = Column(JSON)
    total_cargo_volume = Column(Float)

    # Visit Status
    status = Column(String(20), default='PLANNED')
    berth_assignments = Column(JSON)

    # Business Information
    customer = Column(String(255))
    agent = Column(String(255))
    priority = Column(Integer, default=1)

    # Performance Metrics
    actual_berth_time = Column(Integer)  # store minutes
    actual_operation_time = Column(Integer)
    total_port_time = Column(Integer)

    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    notes = Column(Text)

    # Relationships
    vessel = relationship("VesselRegistry", back_populates="visits")
    terminal = relationship("Terminal")
    assignments = relationship("Assignment", back_populates="vessel_visit")


class VesselAISData(Base):
    __tablename__ = "vessel_ais_data"

    id = Column(Integer, primary_key=True, autoincrement=True)
    vessel_id = Column(Integer, ForeignKey("vessel_registry.id", ondelete="CASCADE"))

    # AIS Position Data
    mmsi = Column(String(9), nullable=False)
    latitude = Column(Float)
    longitude = Column(Float)
    course = Column(Float)
    speed = Column(Float)
    heading = Column(Integer)

    # Vessel Status from AIS
    navigation_status = Column(Integer)
    rate_of_turn = Column(Integer)
    position_accuracy = Column(Integer)

    # Dynamic Data
    draught = Column(Float)
    destination = Column(String(100))
    eta_raw = Column(Integer)
    eta_parsed = Column(DateTime)

    # Data Quality
    timestamp = Column(DateTime, nullable=False)
    age_seconds = Column(Integer)
    signal_quality = Column(Integer)

    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    source = Column(String(50))

    # Relationships
    vessel = relationship("VesselRegistry", back_populates="ais_data")


class ETAHistory(Base):
    __tablename__ = "eta_history"

    id = Column(Integer, primary_key=True, autoincrement=True)

    # Associations (one of these is typically set)
    vessel_registry_id = Column(Integer, ForeignKey("vessel_registry.id", ondelete="SET NULL"), nullable=True)
    visit_id = Column(Integer, ForeignKey("vessel_visits.id", ondelete="SET NULL"), nullable=True)
    runtime_vessel_id = Column(String, nullable=True)  # e.g., NV### nomination id

    # ETA data
    eta = Column(DateTime, nullable=True)
    previous_eta = Column(DateTime, nullable=True)
    source = Column(String(30), nullable=False, default='user')  # user, ais_calculated, ml_predicted, system
    confidence = Column(Integer, nullable=True)  # 0-100
    context = Column(Text, nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow)

    __table_args__ = (
        Index('ix_eta_history_vessel_registry_id', 'vessel_registry_id'),
        Index('ix_eta_history_visit_id', 'visit_id'),
        Index('ix_eta_history_runtime_vessel_id', 'runtime_vessel_id'),
        Index('ix_eta_history_created_at', 'created_at'),
    )
