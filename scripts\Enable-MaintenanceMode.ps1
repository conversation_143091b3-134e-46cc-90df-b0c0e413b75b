# Enable Maintenance Mode for Jetty Planner
# Creates the maintenance flag file to trigger nginx maintenance mode

param(
    [string]$Reason = "Scheduled maintenance",
    [string]$EstimatedDuration = "15-30 minutes"
)

$ErrorActionPreference = "Stop"

$ProjectRoot = Split-Path -Parent $PSScriptRoot
$FlagFile = Join-Path $ProjectRoot "maintenance.flag"
$LogFile = Join-Path $ProjectRoot "logs/maintenance.log"

function Write-MaintenanceLog {
    param([string]$Message)
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "$Timestamp - $Message"
    Write-Host $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry -ErrorAction SilentlyContinue
}

try {
    Write-Host "🔧 Enabling Maintenance Mode for Evos Jetty Planner..." -ForegroundColor Yellow
    Write-Host ""
    
    # Create logs directory if it doesn't exist
    $LogsDir = Split-Path -Parent $LogFile
    if (-not (Test-Path $LogsDir)) {
        New-Item -ItemType Directory -Path $LogsDir -Force | Out-Null
    }
    
    # Check if already in maintenance mode
    if (Test-Path $FlagFile) {
        Write-Host "⚠️  Maintenance mode is already enabled!" -ForegroundColor Yellow
        Write-Host "Flag file exists at: $FlagFile"
        
        $Content = Get-Content $FlagFile -Raw -ErrorAction SilentlyContinue
        if ($Content) {
            Write-Host ""
            Write-Host "Current maintenance info:"
            Write-Host $Content
        }
        
        $Confirm = Read-Host "`nDo you want to update the maintenance info? (y/N)"
        if ($Confirm -notmatch "^[Yy]") {
            Write-Host "Maintenance mode unchanged." -ForegroundColor Green
            exit 0
        }
    }
    
    # Create maintenance flag file with metadata
    $MaintenanceInfo = @"
Maintenance Mode Enabled
========================
Timestamp: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Reason: $Reason
Estimated Duration: $EstimatedDuration
Enabled By: $env:USERNAME
Computer: $env:COMPUTERNAME

This file triggers nginx to serve the maintenance page.
Remove this file to disable maintenance mode.
"@
    
    $MaintenanceInfo | Out-File -FilePath $FlagFile -Encoding UTF8
    Write-MaintenanceLog "MAINTENANCE ENABLED - Reason: $Reason, Duration: $EstimatedDuration"
    
    Write-Host "✅ Maintenance mode enabled successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Users will now see the maintenance page at:" -ForegroundColor Cyan
    Write-Host "   https://planner.evosgpt.eu"
    Write-Host ""
    Write-Host "📁 Flag file created at:" -ForegroundColor Gray
    Write-Host "   $FlagFile"
    Write-Host ""
    Write-Host "🔧 To disable maintenance mode, run:" -ForegroundColor Yellow
    Write-Host "   .\scripts\Disable-MaintenanceMode.ps1"
    Write-Host ""
    Write-Host "📝 Maintenance Details:" -ForegroundColor White
    Write-Host "   Reason: $Reason"
    Write-Host "   Estimated Duration: $EstimatedDuration"
    Write-Host "   Enabled by: $env:USERNAME at $(Get-Date -Format 'HH:mm:ss')"
    
} catch {
    Write-Host "❌ Error enabling maintenance mode: $($_.Exception.Message)" -ForegroundColor Red
    Write-MaintenanceLog "ERROR enabling maintenance mode: $($_.Exception.Message)"
    exit 1
}
