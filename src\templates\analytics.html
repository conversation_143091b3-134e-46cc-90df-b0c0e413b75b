{% extends "base.html" %}

{% block title %}Analytics - Terneuzen Terminal Jetty Planning{% endblock %}

{% block head %}
    <!-- Chart.js for visualizations -->
    <script src="/static/vendor/js/chartjs-3.9.1.min.js"></script>
    <style>
        /* Typography aligned with app theme */
        :root {
            --font-sans: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-heading: 'Blinker', var(--font-sans);
            --color-primary: #003b6f;
            --color-muted: #6c757d;
        }
        .analytics-dashboard, .date-filter, .chart-container, .kpi-card, .analytics-summary {
            font-family: var(--font-sans);
        }
        .analytics-summary h2,
        .kpi-card h3,
        .chart-container h3 {
            font-family: var(--font-heading);
            letter-spacing: .2px;
        }
        .kpi-card h3 { font-weight: 600; }
        .kpi-value { font-weight: 700; }

        /* Analytics-specific styles */
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .kpi-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid var(--color-primary);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .kpi-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .kpi-card h3 {
            color: var(--color-primary);
            font-size: 1.1em;
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .kpi-value {
            font-size: 2.2em;
            color: #2c5282;
            margin: 10px 0;
        }
        
        .kpi-subtitle {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .kpi-trend {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.85em;
            margin-top: 8px;
        }
        
        .trend-up { color: #28a745; }
        .trend-down { color: #dc3545; }
        .trend-stable { color: var(--color-muted); }
        
        .charts-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .chart-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .chart-container h3 {
            color: var(--color-primary);
            margin: 0 0 20px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .chart-container canvas { max-height: 300px; }
        
        .date-filter {
            background: white;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .date-filter label {
            font-weight: 600;
            color: var(--color-primary);
        }
        
        .date-filter input, .date-filter select {
            padding: 8px 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.9em;
            font-family: var(--font-sans);
        }
        
        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #666;
        }
        
        .no-data {
            text-align: center;
            color: #666;
            padding: 40px 20px;
        }
        
        .analytics-summary {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #17a2b8;
        }
        
        .analytics-summary h2 {
            color: var(--color-primary);
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .analytics-summary p {
            margin: 0;
            color: #555;
            line-height: 1.6;
        }
    </style>
{% endblock %}

{% block header %}Terminal Analytics{% endblock %}

{% block content %}
    <!-- Analytics Summary -->
    <div class="analytics-summary">
        <h2><i class="fas fa-info-circle"></i> Analytics Overview</h2>
        <p>Track ML prediction accuracy, planning efficiency, and operational patterns to optimize terminal performance. Use the date filters to analyze specific time periods.</p>
    </div>

    <!-- Date Filter Controls -->
    <div class="date-filter">
        <label for="dateRange">Date Range:</label>
        <select id="dateRange">
            <option value="7">Last 7 days</option>
            <option value="30" selected>Last 30 days</option>
            <option value="90">Last 90 days</option>
            <option value="custom">Custom Range</option>
        </select>
        
        <div id="customDateInputs" style="display: none;">
            <input type="date" id="startDate">
            <span>to</span>
            <input type="date" id="endDate">
        </div>
        
        <button id="refreshAnalyticsBtn" class="btn btn-primary">
            <i class="fas fa-sync-alt"></i> Refresh
        </button>
    </div>

    <!-- Analytics Dashboard -->
    <div class="analytics-dashboard">
        <!-- KPI Cards -->
        <div class="analytics-grid">
            <!-- ML Accuracy Card -->
            <div class="kpi-card">
                <h3><i class="fas fa-brain"></i> ML Accuracy</h3>
                <div class="kpi-value" id="ml-accuracy">--</div>
                <div class="kpi-subtitle">Average prediction accuracy</div>
                <div class="kpi-trend" id="ml-accuracy-trend">
                    <i class="fas fa-circle"></i> Loading...
                </div>
            </div>
            
            <!-- Planning Efficiency Card -->
            <div class="kpi-card">
                <h3><i class="fas fa-chart-line"></i> Planning Efficiency</h3>
                <div class="kpi-value" id="planning-efficiency">--</div>
                <div class="kpi-subtitle">Schedule utilization</div>
                <div class="kpi-trend" id="efficiency-trend">
                    <i class="fas fa-circle"></i> Loading...
                </div>
            </div>
            
            <!-- Change Frequency Card -->
            <div class="kpi-card">
                <h3><i class="fas fa-exchange-alt"></i> Changes per Day</h3>
                <div class="kpi-value" id="change-frequency">--</div>
                <div class="kpi-subtitle">Average daily changes</div>
                <div class="kpi-trend" id="change-trend">
                    <i class="fas fa-circle"></i> Loading...
                </div>
            </div>
            
            <!-- External vs Internal Changes -->
            <div class="kpi-card">
                <h3><i class="fas fa-balance-scale"></i> External Changes</h3>
                <div class="kpi-value" id="external-ratio">--</div>
                <div class="kpi-subtitle">External vs internal ratio</div>
                <div class="kpi-trend" id="external-trend">
                    <i class="fas fa-circle"></i> Loading...
                </div>
            </div>
            
            <!-- Average Turnaround -->
            <div class="kpi-card">
                <h3><i class="fas fa-clock"></i> Avg Turnaround</h3>
                <div class="kpi-value" id="avg-turnaround">--</div>
                <div class="kpi-subtitle">Hours per vessel</div>
                <div class="kpi-trend" id="turnaround-trend">
                    <i class="fas fa-circle"></i> Loading...
                </div>
            </div>
            
            <!-- Schedule Adherence -->
            <div class="kpi-card">
                <h3><i class="fas fa-check-circle"></i> Schedule Adherence</h3>
                <div class="kpi-value" id="schedule-adherence">--</div>
                <div class="kpi-subtitle">Plans executed as scheduled</div>
                <div class="kpi-trend" id="adherence-trend">
                    <i class="fas fa-circle"></i> Loading...
                </div>
            </div>
        </div>
        
        <!-- Charts Section -->
        <div class="charts-section">
            <!-- ML Performance Chart -->
            <div class="chart-container">
                <h3><i class="fas fa-brain"></i> ML Performance Trends</h3>
                <canvas id="mlAccuracyChart"></canvas>
            </div>
            
            <!-- Change Pattern Analysis -->
            <div class="chart-container">
                <h3><i class="fas fa-pie-chart"></i> Change Reasons Distribution</h3>
                <canvas id="changeReasonsChart"></canvas>
            </div>
            
            <!-- Planning Efficiency Trends -->
            <div class="chart-container">
                <h3><i class="fas fa-chart-line"></i> Planning Efficiency</h3>
                <canvas id="efficiencyChart"></canvas>
            </div>
            
            <!-- Change Impact Heatmap -->
            <div class="chart-container">
                <h3><i class="fas fa-calendar-alt"></i> Change Frequency Heatmap</h3>
                <canvas id="changeHeatmapChart"></canvas>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script src="/static/js/analytics-dashboard.js"></script>
{% endblock %}