# PostgreSQL Backup and <PERSON>ore Script for Jetty Planner
# Usage: .\backup-restore.ps1 [-Action] backup|restore|list [-BackupFile filename]

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("backup", "restore", "list", "cleanup")]
    [string]$Action,
    
    [Parameter()]
    [string]$BackupFile,
    
    [Parameter()]
    [int]$KeepDays = 30
)

# Load environment variables
if (Test-Path ".\.env") {
    Get-Content ".\.env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
}

# Database connection settings
$DB_HOST = if ($env:DB_HOST) { $env:DB_HOST } else { "host.docker.internal" }
$DB_PORT = if ($env:DB_PORT) { $env:DB_PORT } else { "4432" }
$DB_NAME = if ($env:DB_NAME) { $env:DB_NAME } else { "planner" }
$DB_USER = if ($env:DB_USER) { $env:DB_USER } else { "postgres" }
$DB_PASSWORD = if ($env:DB_PASSWORD) { $env:DB_PASSWORD } else { "" }

# Backup directory
$BackupDir = ".\backups"
if (!(Test-Path $BackupDir)) {
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
}

switch ($Action) {
    "backup" {
        Write-Host "Creating PostgreSQL backup..." -ForegroundColor Green
        
        $Timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
        $BackupFileName = "jetty_planner_backup_$Timestamp.sql"
        $BackupPath = Join-Path $BackupDir $BackupFileName
        
        # Create backup using pg_dump
        $env:PGPASSWORD = $DB_PASSWORD
        & docker run --rm --network="jetty-network" -v "${PWD}/backups:/backups" postgres:16 `
            pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME `
            --verbose --clean --if-exists --create `
            --format=plain --encoding=UTF8 `
            --file="/backups/$BackupFileName"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Backup created successfully: $BackupFileName" -ForegroundColor Green
            Write-Host "  Location: $BackupPath" -ForegroundColor Gray
            Write-Host "  Size: $([math]::Round((Get-Item $BackupPath).Length / 1MB, 2)) MB" -ForegroundColor Gray
        } else {
            Write-Host "✗ Backup failed!" -ForegroundColor Red
            exit 1
        }
    }
    
    "restore" {
        if (-not $BackupFile) {
            Write-Host "Error: -BackupFile parameter is required for restore action" -ForegroundColor Red
            exit 1
        }
        
        $BackupPath = Join-Path $BackupDir $BackupFile
        if (!(Test-Path $BackupPath)) {
            Write-Host "Error: Backup file not found: $BackupPath" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "Restoring PostgreSQL backup: $BackupFile" -ForegroundColor Yellow
        Write-Host "⚠️  WARNING: This will overwrite the current database!" -ForegroundColor Red
        $Confirm = Read-Host "Type 'yes' to continue"
        
        if ($Confirm -ne "yes") {
            Write-Host "Restore cancelled." -ForegroundColor Yellow
            exit 0
        }
        
        # Restore backup using psql
        $env:PGPASSWORD = $DB_PASSWORD
        & docker run --rm --network="jetty-network" -v "${PWD}/backups:/backups" postgres:16 `
            psql -h $DB_HOST -p $DB_PORT -U $DB_USER `
            --file="/backups/$BackupFile"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Database restored successfully from: $BackupFile" -ForegroundColor Green
        } else {
            Write-Host "✗ Restore failed!" -ForegroundColor Red
            exit 1
        }
    }
    
    "list" {
        Write-Host "Available backups in $BackupDir:" -ForegroundColor Green
        
        $Backups = Get-ChildItem -Path $BackupDir -Filter "*.sql" | Sort-Object LastWriteTime -Descending
        
        if ($Backups.Count -eq 0) {
            Write-Host "No backup files found." -ForegroundColor Yellow
        } else {
            Write-Host ""
            Write-Host "Filename                                Size      Date" -ForegroundColor Cyan
            Write-Host "─────────────────────────────────────── ────────  ─────────────" -ForegroundColor Gray
            
            foreach ($Backup in $Backups) {
                $Size = [math]::Round($Backup.Length / 1MB, 1).ToString("F1") + " MB"
                $Date = $Backup.LastWriteTime.ToString("yyyy-MM-dd HH:mm")
                Write-Host ("{0,-40} {1,8}  {2}" -f $Backup.Name, $Size, $Date)
            }
        }
    }
    
    "cleanup" {
        Write-Host "Cleaning up old backups (older than $KeepDays days)..." -ForegroundColor Yellow
        
        $CutoffDate = (Get-Date).AddDays(-$KeepDays)
        $OldBackups = Get-ChildItem -Path $BackupDir -Filter "*.sql" | Where-Object { $_.LastWriteTime -lt $CutoffDate }
        
        if ($OldBackups.Count -eq 0) {
            Write-Host "No old backups to clean up." -ForegroundColor Green
        } else {
            Write-Host "Found $($OldBackups.Count) old backup(s) to remove:" -ForegroundColor Yellow
            foreach ($Backup in $OldBackups) {
                Write-Host "  - $($Backup.Name) ($($Backup.LastWriteTime.ToString('yyyy-MM-dd')))"
            }
            
            $Confirm = Read-Host "Remove these files? (yes/no)"
            if ($Confirm -eq "yes") {
                $OldBackups | Remove-Item -Force
                Write-Host "✓ Cleaned up $($OldBackups.Count) old backup(s)." -ForegroundColor Green
            } else {
                Write-Host "Cleanup cancelled." -ForegroundColor Yellow
            }
        }
    }
}

# Clear sensitive environment variables
$env:PGPASSWORD = $null