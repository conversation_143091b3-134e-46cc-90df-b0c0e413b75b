#!/usr/bin/env python3
"""
Migration script to add vessel registry tables and update assignments table.
This implements Phase 1 of the vessel registry plan.
"""

import sys
import os
sys.path.append('.')

# Load environment variables first
from dotenv import load_dotenv
load_dotenv()

from src.database import Database
from sqlalchemy import text
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate_assignments_table():
    """Add new vessel registry columns to assignments table."""
    logger.info("Migrating assignments table...")
    
    # Use the database instance to detect backend
    db = Database()
    
    # Let's try both approaches - first try PostgreSQL syntax, fall back to SQLite
    try:
        if db._is_postgres():
            logger.info("Detected PostgreSQL - trying PostgreSQL migration")
            db = Database()
            with db.get_session() as session:
                try:
                    # Try PostgreSQL syntax
                    session.execute(text("""
                        ALTER TABLE assignments 
                        ADD COLUMN IF NOT EXISTS vessel_db_id INTEGER,
                        ADD COLUMN IF NOT EXISTS visit_id INTEGER,
                        ADD COLUMN IF NOT EXISTS nomination_reference VARCHAR(50),
                        ADD COLUMN IF NOT EXISTS assignment_type VARCHAR(20) DEFAULT 'SCHEDULED'
                    """))
                    session.commit()
                    logger.info("PostgreSQL assignments table migrated successfully")
                    return
                    
                except Exception as pg_error:
                    logger.warning(f"PostgreSQL syntax failed: {pg_error}")
                    session.rollback()
                    # Fall through to SQLite approach
        
        logger.info("Using SQLite migration approach")
        # Try SQLite approach (works for both SQLite and some PostgreSQL setups)
        db = Database()
        with db.get_session() as session:
            # Check what columns exist
            try:
                result = session.execute(text("SELECT vessel_db_id FROM assignments LIMIT 1"))
                logger.info("vessel_db_id column already exists")
            except:
                logger.info("Adding vessel_db_id column")
                session.execute(text("ALTER TABLE assignments ADD COLUMN vessel_db_id INTEGER"))
            
            try:
                result = session.execute(text("SELECT visit_id FROM assignments LIMIT 1"))
                logger.info("visit_id column already exists") 
            except:
                logger.info("Adding visit_id column")
                session.execute(text("ALTER TABLE assignments ADD COLUMN visit_id INTEGER"))
            
            try:
                result = session.execute(text("SELECT nomination_reference FROM assignments LIMIT 1"))
                logger.info("nomination_reference column already exists")
            except:
                logger.info("Adding nomination_reference column")
                session.execute(text("ALTER TABLE assignments ADD COLUMN nomination_reference VARCHAR(50)"))
            
            try:
                result = session.execute(text("SELECT assignment_type FROM assignments LIMIT 1"))
                logger.info("assignment_type column already exists")
            except:
                logger.info("Adding assignment_type column")
                session.execute(text("ALTER TABLE assignments ADD COLUMN assignment_type VARCHAR(20) DEFAULT 'SCHEDULED'"))
            
            session.commit()
            logger.info("Assignments table migrated successfully")
            
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise

def create_vessel_registry_tables():
    """Create the new vessel registry tables."""
    logger.info("Creating vessel registry tables...")
    
    # Import the models to ensure tables are created
    from src.db.models import VesselRegistry, VesselVisit, VesselAISData
    from src.db.session import create_tables
    
    try:
        # This will create all tables that don't exist yet
        create_tables()
        logger.info("Vessel registry tables created successfully")
        
    except Exception as e:
        logger.error(f"Failed to create vessel registry tables: {e}")
        raise

def verify_migration():
    """Verify the migration was successful."""
    logger.info("Verifying migration...")
    
    db = Database()
    
    if db._is_postgres():
        db = Database()
        with db.get_session() as session:
            try:
                # Test new columns
                result = session.execute(text("""
                    SELECT vessel_db_id, visit_id, nomination_reference, assignment_type 
                    FROM assignments LIMIT 1
                """))
                logger.info("✓ Assignments table columns verified")
                
                # Test new tables
                session.execute(text("SELECT COUNT(*) FROM vessel_registry"))
                session.execute(text("SELECT COUNT(*) FROM vessel_visits"))
                session.execute(text("SELECT COUNT(*) FROM vessel_ais_data"))
                logger.info("✓ Vessel registry tables verified")
                
            except Exception as e:
                logger.error(f"PostgreSQL verification failed: {e}")
                raise
    else:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            try:
                # Test new columns
                cursor.execute("""
                    SELECT vessel_db_id, visit_id, nomination_reference, assignment_type 
                    FROM assignments LIMIT 1
                """)
                logger.info("✓ Assignments table columns verified")
                
                # Test new tables
                cursor.execute("SELECT COUNT(*) FROM vessel_registry")
                cursor.execute("SELECT COUNT(*) FROM vessel_visits") 
                cursor.execute("SELECT COUNT(*) FROM vessel_ais_data")
                logger.info("✓ Vessel registry tables verified")
                
            except Exception as e:
                logger.error(f"SQLite verification failed: {e}")
                raise

def main():
    """Run the complete migration."""
    logger.info("Starting vessel registry migration...")
    
    try:
        # Step 1: Create new vessel registry tables
        create_vessel_registry_tables()
        
        # Step 2: Add new columns to assignments table
        migrate_assignments_table()
        
        # Step 3: Verify everything works
        verify_migration()
        
        logger.info("✅ Migration completed successfully!")
        logger.info("The vessel registry system is now ready to use.")
        
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        logger.error("Please check the error above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()