"""
Dynamic ETA Update Service
Continuously updates vessel ETAs based on real-time AIS data, weather conditions, and traffic
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import psycopg2
from psycopg2.extras import RealDictCursor

from ..database import Database
from ..models.vessel import VesselBase
from ..services.ship_tracking_service import ShipTrackingService
from ..api.weather_api import OpenMeteoApiClient

logger = logging.getLogger(__name__)

@dataclass
class ETAUpdate:
    """Represents an ETA update for a vessel"""
    vessel_id: str
    vessel_name: str
    old_eta: Optional[datetime]
    new_calculated_eta: datetime
    confidence: int
    source: str
    factors: List[str]  # Factors that influenced the update
    significance: float  # How significant the change is (0-1)

class DynamicETAService:
    """Service for real-time ETA updates and monitoring"""
    
    def __init__(self, 
                 database: Database,
                 ship_tracking_service: Optional[ShipTrackingService] = None,
                 weather_api: Optional[OpenMeteoApiClient] = None,
                 update_interval_minutes: int = 15):
        """
        Initialize the Dynamic ETA Service
        
        Args:
            database: Database connection
            ship_tracking_service: Service for AIS data
            weather_api: Weather API client
            update_interval_minutes: How often to update ETAs
        """
        self.database = database
        self.ship_tracking_service = ship_tracking_service
        self.weather_api = weather_api
        self.update_interval_minutes = update_interval_minutes
        
        # Configuration
        self.significant_change_threshold_hours = 0.5  # 30 minutes
        self.max_eta_horizon_hours = 72  # Only update ETAs within 72 hours
        self.min_confidence_for_update = 60  # Only update if confidence >= 60%
        
        # Tracking
        self.last_update_time = None
        self.update_count = 0
        self.significant_updates = 0
        
    async def start_monitoring(self):
        """Start the continuous ETA monitoring loop"""
        logger.info(f"Starting dynamic ETA monitoring (interval: {self.update_interval_minutes} minutes)")
        
        while True:
            try:
                await self.update_all_vessel_etas()
                await asyncio.sleep(self.update_interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error in ETA monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def update_all_vessel_etas(self) -> List[ETAUpdate]:
        """Update ETAs for all active vessels"""
        logger.debug("Starting ETA update cycle")
        
        try:
            # Get all active vessels with ETAs
            vessels = await self._get_active_vessels_with_etas()
            
            if not vessels:
                logger.debug("No active vessels with ETAs found")
                return []
            
            logger.info(f"Updating ETAs for {len(vessels)} vessels")
            
            updates = []
            for vessel in vessels:
                try:
                    update = await self._calculate_updated_eta(vessel)
                    if update:
                        updates.append(update)
                        
                except Exception as e:
                    logger.warning(f"Failed to update ETA for vessel {vessel.get('name', 'Unknown')}: {e}")
            
            # Apply significant updates to database
            significant_updates = [u for u in updates if u.significance >= 0.3]
            if significant_updates:
                await self._apply_eta_updates(significant_updates)
                self.significant_updates += len(significant_updates)
            
            self.update_count += len(updates)
            self.last_update_time = datetime.now(timezone.utc)
            
            logger.info(f"ETA update cycle complete: {len(updates)} calculated, {len(significant_updates)} applied")
            return updates
            
        except Exception as e:
            logger.error(f"Failed to update vessel ETAs: {e}")
            return []
    
    async def _get_active_vessels_with_etas(self) -> List[Dict]:
        """Get all active vessels that have ETAs and are within update horizon"""
        try:
            with self.database.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute("""
                        SELECT 
                            id, runtime_vessel_id, name, vessel_type,
                            eta, calculated_eta, eta_confidence, eta_source,
                            length, beam, draft, deadweight,
                            created_at, updated_at
                        FROM nominations 
                        WHERE status = 'ACTIVE' 
                        AND eta IS NOT NULL
                        AND eta <= %s
                        ORDER BY eta;
                    """, (datetime.now(timezone.utc) + timedelta(hours=self.max_eta_horizon_hours),))
                    
                    return cursor.fetchall()
                    
        except Exception as e:
            logger.error(f"Failed to get active vessels: {e}")
            return []
    
    async def _calculate_updated_eta(self, vessel: Dict) -> Optional[ETAUpdate]:
        """Calculate updated ETA for a single vessel"""
        vessel_id = vessel['runtime_vessel_id']
        vessel_name = vessel['name']
        current_eta = vessel['eta']
        current_calculated_eta = vessel['calculated_eta']
        
        factors = []
        confidence = 50  # Base confidence
        
        # Start with current ETA as baseline
        new_eta = current_eta
        
        # Factor 1: AIS-based position and speed updates
        if self.ship_tracking_service:
            try:
                ais_eta = await self._calculate_ais_based_eta(vessel)
                if ais_eta:
                    new_eta = ais_eta
                    confidence += 20
                    factors.append("AIS position/speed")
                    
            except Exception as e:
                logger.debug(f"AIS ETA calculation failed for {vessel_name}: {e}")
        
        # Factor 2: Weather impact
        if self.weather_api:
            try:
                weather_delay = await self._calculate_weather_delay(vessel, new_eta)
                if weather_delay:
                    new_eta += timedelta(hours=weather_delay)
                    confidence += 10
                    factors.append(f"Weather delay: {weather_delay:.1f}h")
                    
            except Exception as e:
                logger.debug(f"Weather calculation failed for {vessel_name}: {e}")
        
        # Factor 3: Traffic and congestion
        try:
            traffic_delay = await self._calculate_traffic_delay(vessel, new_eta)
            if traffic_delay:
                new_eta += timedelta(hours=traffic_delay)
                confidence += 5
                factors.append(f"Traffic delay: {traffic_delay:.1f}h")
                
        except Exception as e:
            logger.debug(f"Traffic calculation failed for {vessel_name}: {e}")
        
        # Factor 4: Historical accuracy adjustment
        try:
            accuracy_adjustment = await self._calculate_accuracy_adjustment(vessel)
            if accuracy_adjustment:
                new_eta += timedelta(hours=accuracy_adjustment)
                confidence += 10
                factors.append(f"Historical adjustment: {accuracy_adjustment:.1f}h")
                
        except Exception as e:
            logger.debug(f"Accuracy adjustment failed for {vessel_name}: {e}")
        
        # Calculate significance of change
        if current_calculated_eta:
            time_diff = abs((new_eta - current_calculated_eta).total_seconds() / 3600)
        else:
            time_diff = abs((new_eta - current_eta).total_seconds() / 3600)
        
        significance = min(1.0, time_diff / self.significant_change_threshold_hours)
        
        # Only create update if confidence is high enough and change is meaningful
        if confidence >= self.min_confidence_for_update and significance > 0.1:
            return ETAUpdate(
                vessel_id=vessel_id,
                vessel_name=vessel_name,
                old_eta=current_calculated_eta or current_eta,
                new_calculated_eta=new_eta,
                confidence=min(95, confidence),  # Cap at 95%
                source="dynamic_update",
                factors=factors,
                significance=significance
            )
        
        return None
    
    async def _calculate_ais_based_eta(self, vessel: Dict) -> Optional[datetime]:
        """Calculate ETA based on current AIS position and speed"""
        # This would integrate with actual AIS data
        # For now, simulate with some logic
        
        vessel_id = vessel['runtime_vessel_id']
        current_eta = vessel['eta']
        
        # Simulate AIS-based calculation
        # In real implementation, this would:
        # 1. Get current vessel position from AIS
        # 2. Calculate distance to destination
        # 3. Use current speed to estimate arrival time
        # 4. Account for route constraints (channels, locks, etc.)
        
        # For simulation, add some realistic variation
        import random
        variation_hours = random.uniform(-0.5, 0.5)  # ±30 minutes
        
        return current_eta + timedelta(hours=variation_hours)
    
    async def _calculate_weather_delay(self, vessel: Dict, eta: datetime) -> Optional[float]:
        """Calculate weather-related delays"""
        # This would integrate with weather API
        # For now, simulate based on vessel type and season
        
        vessel_type = vessel.get('vessel_type', 'TANKER')
        
        # Simulate weather impact
        import random
        if vessel_type == 'TANKER':
            # Tankers are more affected by weather
            return random.uniform(0, 1.0)  # 0-1 hour delay
        else:
            return random.uniform(0, 0.5)  # 0-30 min delay
    
    async def _calculate_traffic_delay(self, vessel: Dict, eta: datetime) -> Optional[float]:
        """Calculate traffic and congestion delays"""
        # This would analyze current traffic patterns
        # For now, simulate based on time of day and vessel size
        
        # Simulate traffic based on ETA time
        eta_hour = eta.hour
        
        # Rush hours have more delays
        if 6 <= eta_hour <= 9 or 16 <= eta_hour <= 19:
            import random
            return random.uniform(0.2, 0.8)  # 12-48 minutes
        
        return 0.0
    
    async def _calculate_accuracy_adjustment(self, vessel: Dict) -> Optional[float]:
        """Calculate adjustment based on historical ETA accuracy"""
        # This would analyze historical ETA vs actual arrival patterns
        # For now, simulate based on vessel characteristics
        
        vessel_type = vessel.get('vessel_type', 'TANKER')
        eta_source = vessel.get('eta_source', 'user')
        
        # Simulate historical bias
        if eta_source == 'user':
            # User ETAs tend to be optimistic
            import random
            return random.uniform(0.1, 0.3)  # 6-18 minutes later
        
        return 0.0
    
    async def _apply_eta_updates(self, updates: List[ETAUpdate]):
        """Apply ETA updates to the database"""
        if not updates:
            return
        
        try:
            with self.database.get_connection() as conn:
                with conn.cursor() as cursor:
                    for update in updates:
                        cursor.execute("""
                            UPDATE nominations 
                            SET 
                                calculated_eta = %s,
                                eta_confidence = %s,
                                eta_source = %s,
                                updated_at = %s
                            WHERE runtime_vessel_id = %s;
                        """, (
                            update.new_calculated_eta,
                            update.confidence,
                            update.source,
                            datetime.now(timezone.utc),
                            update.vessel_id
                        ))
                        
                        logger.info(f"Updated ETA for {update.vessel_name}: "
                                  f"{update.old_eta} → {update.new_calculated_eta} "
                                  f"(confidence: {update.confidence}%, factors: {', '.join(update.factors)})")
                    
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"Failed to apply ETA updates: {e}")
            raise
    
    def get_service_stats(self) -> Dict:
        """Get service statistics"""
        return {
            'last_update_time': self.last_update_time,
            'total_updates': self.update_count,
            'significant_updates': self.significant_updates,
            'update_interval_minutes': self.update_interval_minutes,
            'significant_change_threshold_hours': self.significant_change_threshold_hours,
            'max_eta_horizon_hours': self.max_eta_horizon_hours,
            'min_confidence_for_update': self.min_confidence_for_update
        }

    async def force_update_vessel_eta(self, vessel_id: str) -> Optional[ETAUpdate]:
        """Force an immediate ETA update for a specific vessel"""
        try:
            vessels = await self._get_active_vessels_with_etas()
            vessel = next((v for v in vessels if v['runtime_vessel_id'] == vessel_id), None)

            if not vessel:
                logger.warning(f"Vessel {vessel_id} not found for ETA update")
                return None

            update = await self._calculate_updated_eta(vessel)
            if update and update.significance > 0.1:  # Apply even small changes for forced updates
                await self._apply_eta_updates([update])
                logger.info(f"Forced ETA update applied for vessel {vessel_id}")
                return update

            return None

        except Exception as e:
            logger.error(f"Failed to force update ETA for vessel {vessel_id}: {e}")
            return None
