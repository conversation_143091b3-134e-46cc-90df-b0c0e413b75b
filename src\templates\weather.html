{% extends "base.html" %}
{% block title %}Weather - Terneuzen Terminal Jetty Planning{% endblock %}
{% block head %}
    <!-- Favicon and Browser Icons -->
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', path='/icons/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', path='/icons/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', path='/icons/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ url_for('static', path='/site.webmanifest') }}">
    <link rel="mask-icon" href="{{ url_for('static', path='/icons/safari-pinned-tab.svg') }}" color="#003b6f">
    <meta name="msapplication-TileColor" content="#003b6f">
    <meta name="theme-color" content="#ffffff">
    <style>
        .weather-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            grid-template-rows: auto auto;
            gap: 1.5rem;
            padding: 1.5rem;
            max-width: 1800px;
            margin: 0 auto;
        }

        .weather-grid-bottom {
            grid-column: 1 / -1;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
        }

        @media (max-width: 1400px) {
            .weather-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
            }

            .weather-grid-bottom {
                grid-template-columns: 1fr;
            }
        }

        .weather-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
        }

        .weather-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .weather-header h2 {
            margin: 0;
            color: #003b6f;
            font-size: 1.25rem;
        }

        .weather-icon {
            font-size: 2.5rem;
            color: #2196f3;
        }

        .weather-main {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .weather-temp {
            font-size: 3rem;
            font-weight: 300;
            margin-right: 1rem;
        }

        .weather-description {
            font-size: 1.25rem;
            color: #6c757d;
        }

        .weather-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .weather-detail {
            display: flex;
            align-items: center;
        }

        .weather-detail i {
            width: 1.5rem;
            margin-right: 0.5rem;
            color: #2196f3;
        }

        .forecast-container {
            display: flex;
            overflow-x: auto;
            gap: 1rem;
            padding: 1rem 0;
            scrollbar-width: thin;
            scrollbar-color: #2196f3 #f0f0f0;
        }

        .forecast-container::-webkit-scrollbar {
            height: 8px;
        }

        .forecast-container::-webkit-scrollbar-track {
            background: #f0f0f0;
            border-radius: 4px;
        }

        .forecast-container::-webkit-scrollbar-thumb {
            background-color: #2196f3;
            border-radius: 4px;
        }

        .forecast-hour {
            min-width: 100px;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }

        .forecast-hour:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .forecast-time {
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #003b6f;
        }

        .forecast-icon {
            font-size: 2rem;
            margin: 0.5rem 0;
            color: #2196f3;
        }

        .forecast-hour.night .forecast-icon {
            color: #3f51b5;
        }

        .forecast-temp {
            font-size: 1.5rem;
            font-weight: 300;
            margin: 0.5rem 0;
        }

        .forecast-wind {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0.5rem 0;
            font-size: 0.9rem;
        }

        .forecast-wind i {
            margin-right: 0.25rem;
            color: #2196f3;
        }

        .forecast-description {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .forecast-alert {
            color: #f44336;
            font-weight: 500;
            font-size: 0.9rem;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.25rem;
        }

        /* Chart styles */
        .chart-container {
            width: 100%;
            height: 400px;
            margin-bottom: 1rem;
            position: relative;
            border: 1px solid #eee;
            border-radius: 8px;
            padding: 1rem;
            background-color: #fff;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 4px;
        }

        /* Toggle buttons */
        .view-toggle {
            display: flex;
            gap: 0.5rem;
        }

        .toggle-btn {
            background: #f0f0f0;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85rem;
            transition: all 0.2s;
        }

        .toggle-btn:hover {
            background: #e0e0e0;
        }

        .toggle-btn.active {
            background: #2196f3;
            color: white;
        }

        .forecast-day {
            min-width: 150px;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
        }

        .forecast-date {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .forecast-temp-max {
            font-weight: 500;
        }

        .forecast-temp-min {
            color: #6c757d;
        }

        .forecast-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            width: 100%;
        }

        .forecast-loading i {
            font-size: 2rem;
            color: #2196f3;
            margin-bottom: 1rem;
        }

        .weather-alerts {
            margin-top: 1.5rem;
        }

        .alert {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 4px;
        }

        .alert-title {
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .alert-title i {
            color: #ff9800;
        }

        .alert-body {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .weather-source {
            margin-top: 1.5rem;
            text-align: right;
            font-size: 0.8rem;
            color: #6c757d;
        }

        .weather-source a {
            color: #2196f3;
            text-decoration: none;
        }

        .weather-source a:hover {
            text-decoration: underline;
        }

        .refresh-button {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 50%;
            width: 3rem;
            height: 3rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: all 0.2s;
        }

        .refresh-button:hover {
            background: #1976d2;
            transform: scale(1.1);
        }

        .refresh-button i {
            font-size: 1.25rem;
        }

        .wind-direction {
            display: inline-block;
            transform-origin: center;
        }

        .impact-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .impact-low {
            background-color: #4caf50;
        }

        .impact-moderate {
            background-color: #ff9800;
        }

        .impact-high {
            background-color: #f44336;
        }

        .impact-none {
            background-color: #4caf50;
        }

        .impact-unknown {
            background-color: #9e9e9e;
        }

        .operational-impact {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 4px;
            background-color: #f5f5f5;
        }

        .operational-impact h3 {
            margin-top: 0;
            color: #003b6f;
            font-size: 1rem;
        }

        .operational-impact p {
            margin-bottom: 0.5rem;
        }

        .daily-forecast {
            margin-top: 1rem;
        }

        .daily-forecast h3 {
            margin-top: 0;
            color: #003b6f;
            font-size: 1rem;
        }

        .daily-forecast-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 0.5rem;
            table-layout: fixed;
        }

        .daily-forecast-table th,
        .daily-forecast-table td {
            padding: 0.75rem 0.5rem;
            text-align: center;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .daily-forecast-table th {
            font-weight: 500;
            color: #003b6f;
            background-color: #f0f7ff;
        }

        .daily-forecast-table tr:hover {
            background-color: #f9f9f9;
        }

        .daily-forecast-table .date-column {
            width: 20%;
            text-align: left;
        }

        .daily-forecast-table .temp-column {
            width: 15%;
        }

        .daily-forecast-table .wind-column {
            width: 20%;
        }

        .daily-forecast-table .thunder-column {
            width: 15%;
        }

        .daily-forecast-table .impact-column {
            width: 30%;
        }
    </style>
{% endblock %}
{% block header %}Weather Conditions{% endblock %}
{% block user_actions %}
    <button class="btn btn-primary" id="refresh-weather">
        <i class="fas fa-sync-alt"></i>
        Refresh Weather
    </button>
{% endblock %}
{% block content %}
            <div class="weather-grid">
                <!-- First row: Current Weather and Forecast -->
                <div class="weather-card">
                    <div class="weather-header">
                        <h2>Current Weather</h2>
                        <div class="weather-icon">
                            <i class="fas fa-cloud-sun" id="current-weather-icon"></i>
                        </div>
                    </div>
                    <div class="weather-main">
                        <div class="weather-temp" id="current-temp">--°C</div>
                        <div class="weather-description" id="current-description">Loading weather data...</div>
                    </div>
                    <div class="weather-details">
                        <div class="weather-detail">
                            <i class="fas fa-wind"></i>
                            <span id="current-wind">-- m/s</span>
                        </div>
                        <div class="weather-detail">
                            <i class="fas fa-compass"></i>
                            <span id="current-wind-dir">--</span>
                            <span class="wind-direction" id="wind-arrow">↑</span>
                        </div>
                        <div class="weather-detail">
                            <i class="fas fa-tint"></i>
                            <span id="current-humidity">--%</span>
                        </div>
                        <div class="weather-detail">
                            <i class="fas fa-eye"></i>
                            <span id="current-visibility">-- km</span>
                        </div>
                    </div>
                    <div class="operational-impact">
                        <h3>Operational Impact</h3>
                        <p>
                            <span class="impact-indicator" id="wind-impact-indicator"></span>
                            <strong>Wind Impact:</strong> <span id="wind-impact-message">Loading...</span>
                        </p>
                        <p>
                            <span class="impact-indicator" id="thunderstorm-impact-indicator"></span>
                            <strong>Thunderstorm Impact:</strong> <span id="thunderstorm-impact-message">Loading...</span>
                        </p>
                    </div>
                    <div class="weather-source">
                        <div id="last-updated">Last updated: --</div>
                        Source: <a href="https://open-meteo.com/" target="_blank">Open-Meteo</a> (KNMI data)
                    </div>
                </div>

                <!-- 12-Hour Forecast Card -->
                <div class="weather-card">
                    <div class="weather-header">
                        <h2>Next 12 Hours Forecast</h2>
                        <div class="view-toggle">
                            <button class="toggle-btn active" id="graph-view-btn">Graph View</button>
                            <button class="toggle-btn" id="cards-view-btn">Cards View</button>
                        </div>
                    </div>

                    <!-- Graph View -->
                    <div class="hourly-forecast-graph" id="hourly-forecast-graph">
                        <div class="chart-container">
                            <canvas id="hourly-chart"></canvas>
                        </div>
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: rgba(33, 150, 243, 0.5);"></div>
                                <span>Temperature (°C)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: rgba(255, 152, 0, 0.5);"></div>
                                <span>Wind Speed (m/s)</span>
                            </div>
                        </div>
                    </div>

                    <!-- Cards View (hidden by default) -->
                    <div class="hourly-forecast" id="hourly-forecast" style="display: none;">
                        <div class="forecast-container" id="hourly-forecast-container">
                            <div class="forecast-loading">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p>Loading hourly forecast...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bottom row with 3 cards -->
                <div class="weather-grid-bottom">
                    <!-- Tides (RWS) Card -->
                    <div class="weather-card">
                        <div class="weather-header">
                            <h2>Tides (RWS)</h2>
                        </div>
                        <div class="weather-details" id="tide-summary">
                            <div class="weather-detail">
                                <i class="fas fa-water"></i>
                                <span><strong>Current level:</strong> <span id="tide-current-level">Loading...</span></span>
                            </div>
                            <div class="weather-detail">
                                <i class="fas fa-arrow-up"></i>
                                <span><strong>Next High:</strong> <span id="tide-next-high">Loading...</span></span>
                            </div>
                            <div class="weather-detail">
                                <i class="fas fa-arrow-down"></i>
                                <span><strong>Next Low:</strong> <span id="tide-next-low">Loading...</span></span>
                            </div>
                        </div>
                        <div class="chart-container" style="margin-top: 1rem;">
                            <canvas id="tides-chart"></canvas>
                        </div>
                        <div class="weather-source">
                            Source: <a href="https://waterinfo.rws.nl/#/kaart/gegevens" target="_blank">RWS Waterwebservices</a>
                        </div>
                    </div>
                    <!-- 5-Day Operational Forecast Card -->
                    <div class="weather-card">
                        <div class="weather-header">
                            <h2>5-Day Operational Forecast</h2>
                        </div>
                        <div class="daily-forecast" id="daily-forecast">
                            <table class="daily-forecast-table">
                                <thead>
                                    <tr>
                                        <th class="date-column">Date</th>
                                        <th class="temp-column">Max Temp</th>
                                        <th class="wind-column">Wind</th>
                                        <th class="thunder-column">Thunder</th>
                                        <th class="impact-column">Impact</th>
                                    </tr>
                                </thead>
                                <tbody id="forecast-table-body">
                                    <tr>
                                        <td colspan="5" style="text-align: center;">Loading forecast data...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Weather Alerts Card -->
                    <div class="weather-card">
                        <div class="weather-header">
                            <h2>Weather Alerts</h2>
                        </div>
                        <div class="weather-alerts" id="weather-alerts">
                            <!-- Alerts will be added here -->
                            <div id="no-alerts-message">No active weather alerts at this time.</div>
                        </div>
                    </div>

                    <!-- Operational Guidelines Card -->
                    <div class="weather-card">
                        <div class="weather-header">
                            <h2>Operational Guidelines</h2>
                        </div>
                        <div class="weather-details">
                            <div class="weather-detail" style="grid-column: 1 / -1;">
                                <i class="fas fa-ship"></i>
                                <strong>Vessel Operations:</strong>
                            </div>
                            <div class="weather-detail" style="grid-column: 1 / -1; margin-left: 1.5rem;">
                                <span class="impact-indicator impact-low"></span>
                                <span>Wind < 12 m/s (25 knots): Normal operations</span>
                            </div>
                            <div class="weather-detail" style="grid-column: 1 / -1; margin-left: 1.5rem;">
                                <span class="impact-indicator impact-moderate"></span>
                                <span>Wind 12-17 m/s (25-33 knots): Caution during approach</span>
                            </div>
                            <div class="weather-detail" style="grid-column: 1 / -1; margin-left: 1.5rem;">
                                <span class="impact-indicator impact-high"></span>
                                <span>Wind > 17 m/s (33 knots): Operations restricted</span>
                            </div>

                            <div class="weather-detail" style="grid-column: 1 / -1; margin-top: 1rem;">
                                <i class="fas fa-tachometer-alt"></i>
                                <strong>Pumping Operations:</strong>
                            </div>
                            <div class="weather-detail" style="grid-column: 1 / -1; margin-left: 1.5rem;">
                                <span class="impact-indicator impact-high"></span>
                                <span>Thunderstorms: All pumping operations suspended</span>
                            </div>
                            <div class="weather-detail" style="grid-column: 1 / -1; margin-left: 1.5rem;">
                                <span class="impact-indicator impact-none"></span>
                                <span>No thunderstorms: Normal pumping operations</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <button class="refresh-button" id="refresh-weather-fab">
                <i class="fas fa-sync-alt"></i>
            </button>
{% endblock %}
{% block scripts %}
    <script src="/static/vendor/js/chartjs-3.9.1.min.js"></script>
    <script src="/static/vendor/js/chartjs-annotation-2.1.0.min.js"></script>
    <script nonce="{{ nonce }}">
        // We'll handle Chart.js plugin registration in the chart creation function

        // Global variable for the chart
        let hourlyChart = null;
        let tideChart = null;

        document.addEventListener('DOMContentLoaded', function() {
            loadWeatherData();

            // Set up refresh button
            document.getElementById('refresh-weather').addEventListener('click', function() {
                refreshWeather();
            });

            // Set up view toggle buttons
            document.getElementById('graph-view-btn').addEventListener('click', function() {
                document.getElementById('graph-view-btn').classList.add('active');
                document.getElementById('cards-view-btn').classList.remove('active');
                document.getElementById('hourly-forecast-graph').style.display = 'block';
                document.getElementById('hourly-forecast').style.display = 'none';
            });

            document.getElementById('cards-view-btn').addEventListener('click', function() {
                document.getElementById('cards-view-btn').classList.add('active');
                document.getElementById('graph-view-btn').classList.remove('active');
                document.getElementById('hourly-forecast').style.display = 'block';
                document.getElementById('hourly-forecast-graph').style.display = 'none';
            });

            // Wire floating action button
            const refreshFab = document.getElementById('refresh-weather-fab');
            if (refreshFab) refreshFab.addEventListener('click', refreshWeather);
        });

        function refreshWeather() {
            // Show loading indicators
            document.getElementById('current-temp').textContent = '--°C';
            document.getElementById('current-description').textContent = 'Loading weather data...';
            document.getElementById('current-wind').textContent = '-- m/s';
            document.getElementById('current-wind-dir').textContent = '--';
            document.getElementById('current-humidity').textContent = '--%';
            document.getElementById('current-visibility').textContent = '-- km';
            document.getElementById('wind-impact-message').textContent = 'Loading...';
            document.getElementById('thunderstorm-impact-message').textContent = 'Loading...';
            document.getElementById('forecast-table-body').innerHTML = '<tr><td colspan="5" style="text-align: center;">Loading forecast data...</td></tr>';
            document.getElementById('hourly-forecast-container').innerHTML = '<div class="forecast-loading"><i class="fas fa-spinner fa-spin"></i><p>Loading hourly forecast...</p></div>';
            document.getElementById('weather-alerts').innerHTML = '<div id="no-alerts-message">Loading alerts...</div>';
            // Tide placeholders
            const tideCurrent = document.getElementById('tide-current-level');
            const tideHigh = document.getElementById('tide-next-high');
            const tideLow = document.getElementById('tide-next-low');
            if (tideCurrent) tideCurrent.textContent = 'Loading...';
            if (tideHigh) tideHigh.textContent = 'Loading...';
            if (tideLow) tideLow.textContent = 'Loading...';

            // Reset chart if it exists
            if (hourlyChart) {
                hourlyChart.destroy();
                hourlyChart = null;
            }
            if (tideChart) {
                tideChart.destroy();
                tideChart = null;
            }

            // Load fresh data
            loadWeatherData();
        }

        async function loadWeatherData() {
            try {
                // Get current weather
                const currentResponse = await fetch('/api/weather');
                const currentData = await currentResponse.json();

                // Update current weather display
                updateCurrentWeather(currentData);

                // Get hourly forecast for next 12 hours
                const hourlyResponse = await fetch('/api/weather?forecast=true&hours=12');
                const hourlyData = await hourlyResponse.json();

                // Update hourly forecast display
                updateHourlyForecast(hourlyData.forecast);

                // Get 5-day forecast
                const forecastResponse = await fetch('/api/weather?forecast=true&days=5');
                const forecastData = await forecastResponse.json();

                // Update forecast display
                updateForecast(forecastData);

                // Update alerts - use the warnings from the 5-day forecast as they're more comprehensive
                updateAlerts(forecastData.warnings);
                
                // Load tidal data (RWS)
                let tidalCurrent = null;
                let tidalForecast = null;
                try {
                    const tCurResp = await fetch('/api/tidal/current?station=VLISSGN');
                    if (tCurResp.ok) tidalCurrent = await tCurResp.json();
                } catch (e) { /* ignore */ }
                try {
                    const tForResp = await fetch('/api/tidal/forecast?station=VLISSGN&hours=24');
                    if (tForResp.ok) tidalForecast = await tForResp.json();
                } catch (e) { /* ignore */ }
                updateTides(tidalCurrent, tidalForecast);

            } catch (error) {
                console.error('Error loading weather data:', error);
            }
        }

        function updateCurrentWeather(data) {
            const current = data.current_weather;
            const operations = data.operations_status;

            // Update basic weather info
            document.getElementById('current-temp').textContent = `${Math.round(current.temperature)}°C`;
            document.getElementById('current-description').textContent = current.conditions;
            document.getElementById('current-wind').textContent = `${current.wind_speed.toFixed(1)} m/s`;

            // Wind direction
            const windDirection = current.wind_direction;
            document.getElementById('current-wind-dir').textContent = getWindDirectionText(windDirection);
            document.getElementById('wind-arrow').style.transform = `rotate(${windDirection}deg)`;

            // Other details
            document.getElementById('current-humidity').textContent = `${current.humidity}%`;
            document.getElementById('current-visibility').textContent = `${(current.visibility / 1000).toFixed(1)} km`;

            // Update last updated time
            const lastUpdated = new Date(current.timestamp);
            document.getElementById('last-updated').textContent = `Laatst bijgewerkt: ${lastUpdated.toLocaleTimeString('nl-NL', { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false })}`;

            // Update weather icon
            updateWeatherIcon('current-weather-icon', current.conditions);

            // Update operational impact indicators
            updateImpactIndicator('wind-impact-indicator', current.wind_impact);
            document.getElementById('wind-impact-message').textContent = current.wind_message;

            updateImpactIndicator('thunderstorm-impact-indicator', current.thunderstorm_impact);
            document.getElementById('thunderstorm-impact-message').textContent = current.thunderstorm_message;
        }

        function updateHourlyForecast(hourlyForecast) {
            const container = document.getElementById('hourly-forecast-container');

            // Clear existing content
            container.innerHTML = '';

            // If no forecast data
            if (!hourlyForecast || hourlyForecast.length === 0) {
                container.innerHTML = '<div class="forecast-loading"><p>No hourly forecast data available</p></div>';
                return;
            }

            // Add a card for each hour
            hourlyForecast.forEach(hour => {
                // Parse time
                const time = new Date(hour.time);
                const formattedTime = time.toLocaleTimeString('nl-NL', { hour: '2-digit', minute: '2-digit', hour12: false });

                // Create forecast hour card
                const hourCard = document.createElement('div');
                hourCard.className = `forecast-hour ${hour.is_day ? 'day' : 'night'}`;

                // Determine wind impact
                let windImpactClass = 'low';
                if (hour.wind_speed >= 17.0) {
                    windImpactClass = 'high';
                } else if (hour.wind_speed >= 12.0) {
                    windImpactClass = 'moderate';
                }

                // Determine if thunderstorm
                const hasThunderstorm = hour.weather_code >= 95 && hour.weather_code <= 99;

                // Create content
                hourCard.innerHTML = `
                    <div class="forecast-time">${formattedTime}</div>
                    <div class="forecast-icon">
                        <i class="${getWeatherIcon(hour.weather_code, hour.is_day)}"></i>
                    </div>
                    <div class="forecast-temp">${Math.round(hour.temperature)}°C</div>
                    <div class="forecast-description">${hour.description}</div>
                    <div class="forecast-wind">
                        <i class="fas fa-wind"></i> ${hour.wind_speed.toFixed(1)} m/s
                    </div>
                    ${hasThunderstorm ?
                        '<div class="forecast-alert"><i class="fas fa-bolt" style="color: #f44336;"></i> Thunder</div>' :
                        ''}
                    <div class="impact-indicator impact-${hasThunderstorm ? 'high' : windImpactClass}"
                         style="margin-top: 0.5rem; width: 16px; height: 16px;"></div>
                `;

                container.appendChild(hourCard);
            });

            // Create/update the chart
            createHourlyChart(hourlyForecast);
        }

        function createHourlyChart(hourlyForecast) {
            // Get the canvas element
            const ctx = document.getElementById('hourly-chart').getContext('2d');

            // If no forecast data
            if (!hourlyForecast || hourlyForecast.length === 0) {
                return;
            }

            // Prepare data for the chart
            const labels = [];
            const tempData = [];
            const windData = [];

            hourlyForecast.forEach((hour) => {
                // Format time for labels
                const time = new Date(hour.time);
                const formattedTime = time.toLocaleTimeString('nl-NL', { hour: '2-digit', hour12: false });
                labels.push(formattedTime);

                // Temperature data
                tempData.push(hour.temperature);

                // Wind data
                windData.push(hour.wind_speed);
            });

            // Destroy existing chart if it exists
            if (hourlyChart) {
                hourlyChart.destroy();
            }

            // Create the chart
            hourlyChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Temperature (°C)',
                            data: tempData,
                            borderColor: 'rgba(33, 150, 243, 1)',
                            backgroundColor: 'rgba(33, 150, 243, 0.5)',
                            fill: false,
                            tension: 0.4,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Wind Speed (m/s)',
                            data: windData,
                            borderColor: 'rgba(255, 152, 0, 1)',
                            backgroundColor: 'rgba(255, 152, 0, 0.5)',
                            fill: false,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Temperature (°C)'
                            },
                            grid: {
                                drawOnChartArea: true
                            },
                            ticks: {
                                color: 'rgba(33, 150, 243, 1)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Wind Speed (m/s)'
                            },
                            grid: {
                                drawOnChartArea: false
                            },
                            ticks: {
                                color: 'rgba(255, 152, 0, 1)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Time'
                            }
                        }
                    }
                }
            });

            // Add plugins for threshold lines
            const moderateWindY = 12;
            const highWindY = 17;

            // Add text for the threshold lines
            hourlyChart.options.plugins.tooltip = {
                callbacks: {
                    afterBody: function(context) {
                        const index = context[0].dataIndex;
                        const hour = hourlyForecast[index];

                        let result = [];
                        if (hour.description) {
                            result.push(hour.description);
                        }

                        // Add wind impact
                        let windImpact = 'Low';
                        if (hour.wind_speed >= 17.0) {
                            windImpact = 'High';
                        } else if (hour.wind_speed >= 12.0) {
                            windImpact = 'Moderate';
                        }
                        result.push(`Wind Impact: ${windImpact}`);

                        // Add thunderstorm warning
                        const hasThunderstorm = hour.weather_code >= 95 && hour.weather_code <= 99;
                        if (hasThunderstorm) {
                            result.push('⚡ Thunderstorm Warning!');
                        }

                        return result;
                    }
                }
            };

            // Create a custom plugin to draw the threshold lines
            const thresholdLinesPlugin = {
                id: 'thresholdLines',
                afterDraw: function(chart) {
                const ctx = chart.ctx;
                const yAxis = chart.scales.y;

                // Use the wind speed y-axis (y1) for the threshold lines
                const windAxis = chart.scales.y1;

                // Draw moderate wind threshold line
                const moderateY = windAxis.getPixelForValue(moderateWindY);
                ctx.save();
                ctx.beginPath();
                ctx.moveTo(chart.chartArea.left, moderateY);
                ctx.lineTo(chart.chartArea.right, moderateY);
                ctx.strokeStyle = 'rgba(255, 152, 0, 0.7)';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.stroke();

                // Add moderate wind label
                ctx.fillStyle = 'rgba(255, 152, 0, 1)';
                ctx.font = '12px Arial';
                ctx.textAlign = 'right';
                ctx.fillText('Moderate Wind (12 m/s)', chart.chartArea.right - 10, moderateY - 5);

                // Draw high wind threshold line
                const highY = windAxis.getPixelForValue(highWindY);
                ctx.beginPath();
                ctx.moveTo(chart.chartArea.left, highY);
                ctx.lineTo(chart.chartArea.right, highY);
                ctx.strokeStyle = 'rgba(244, 67, 54, 0.7)';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.stroke();

                // Add high wind label
                ctx.fillStyle = 'rgba(244, 67, 54, 1)';
                ctx.font = '12px Arial';
                ctx.fillText('High Wind (17 m/s)', chart.chartArea.right - 10, highY - 5);

                ctx.restore();
                }
            };

            // Register the plugin
            Chart.register(thresholdLinesPlugin);
        }

        function getWeatherIcon(code, isDay) {
            // Map WMO weather codes to Font Awesome icons
            // https://www.nodc.noaa.gov/archive/arc0021/0002199/1.1/data/0-data/HTML/WMO-CODE/WMO4677.HTM
            const dayIcons = {
                0: 'fas fa-sun', // Clear sky
                1: 'fas fa-sun', // Mainly clear
                2: 'fas fa-cloud-sun', // Partly cloudy
                3: 'fas fa-cloud', // Overcast
                45: 'fas fa-smog', // Fog
                48: 'fas fa-smog', // Depositing rime fog
                51: 'fas fa-cloud-rain', // Light drizzle
                53: 'fas fa-cloud-rain', // Moderate drizzle
                55: 'fas fa-cloud-rain', // Dense drizzle
                56: 'fas fa-cloud-rain', // Light freezing drizzle
                57: 'fas fa-cloud-rain', // Dense freezing drizzle
                61: 'fas fa-cloud-rain', // Slight rain
                63: 'fas fa-cloud-rain', // Moderate rain
                65: 'fas fa-cloud-showers-heavy', // Heavy rain
                66: 'fas fa-cloud-rain', // Light freezing rain
                67: 'fas fa-cloud-showers-heavy', // Heavy freezing rain
                71: 'fas fa-snowflake', // Slight snow fall
                73: 'fas fa-snowflake', // Moderate snow fall
                75: 'fas fa-snowflake', // Heavy snow fall
                77: 'fas fa-snowflake', // Snow grains
                80: 'fas fa-cloud-rain', // Slight rain showers
                81: 'fas fa-cloud-showers-heavy', // Moderate rain showers
                82: 'fas fa-cloud-showers-heavy', // Violent rain showers
                85: 'fas fa-snowflake', // Slight snow showers
                86: 'fas fa-snowflake', // Heavy snow showers
                95: 'fas fa-bolt', // Thunderstorm
                96: 'fas fa-bolt', // Thunderstorm with slight hail
                99: 'fas fa-bolt', // Thunderstorm with heavy hail
            };

            const nightIcons = {
                0: 'fas fa-moon', // Clear sky
                1: 'fas fa-moon', // Mainly clear
                2: 'fas fa-cloud-moon', // Partly cloudy
                3: 'fas fa-cloud', // Overcast
                // Rest are the same as day icons
            };

            // Use night icons for certain codes if it's night, otherwise use day icons
            if (!isDay && nightIcons[code]) {
                return nightIcons[code];
            }

            return dayIcons[code] || 'fas fa-cloud';
        }

        function updateForecast(data) {
            const forecast = data.forecast;
            const tableBody = document.getElementById('forecast-table-body');

            // Clear existing rows
            tableBody.innerHTML = '';

            // Add a row for each day
            forecast.forEach(day => {
                const row = document.createElement('tr');

                // Format date
                const date = new Date(day.date);
                const formattedDate = date.toLocaleDateString('nl-NL', { weekday: 'short', month: 'short', day: 'numeric' });

                // Determine impact level - thunderstorm takes precedence over wind
                const hasThunderstorm = day.has_thunderstorm === true;
                const windImpact = day.wind_impact || 'low';
                const impactClass = hasThunderstorm ? 'high' : windImpact;
                const impactText = hasThunderstorm ? 'High (Thunder)' : capitalizeFirstLetter(windImpact) + ' (Wind)';

                // Create cells
                row.innerHTML = `
                    <td class="date-column">${formattedDate}</td>
                    <td class="temp-column">${Math.round(day.max_temp)}°C</td>
                    <td class="wind-column">
                        <strong>${day.max_wind ? day.max_wind.toFixed(1) : '0.0'}</strong> m/s
                    </td>
                    <td class="thunder-column">
                        ${hasThunderstorm ?
                            '<i class="fas fa-bolt" style="color: #f44336; font-size: 1.2rem;"></i> <span style="font-weight: 500;">Yes</span>' :
                            '<i class="fas fa-check" style="color: #4caf50;"></i> No'}
                    </td>
                    <td class="impact-column">
                        <span class="impact-indicator impact-${impactClass}"></span>
                        <span style="font-weight: 500;">${impactText}</span>
                    </td>
                `;

                tableBody.appendChild(row);
            });
        }

        function updateAlerts(warnings) {
            const alertsContainer = document.getElementById('weather-alerts');

            // Clear existing alerts
            alertsContainer.innerHTML = '';

            // If no warnings, show message
            if (!warnings || warnings.length === 0) {
                alertsContainer.innerHTML = '<div id="no-alerts-message">No active weather alerts at this time.</div>';
                return;
            }

            // Add each warning
            warnings.forEach(warning => {
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert';

                // Determine icon based on type
                let icon = 'exclamation-triangle';
                if (warning.type === 'wind') {
                    icon = 'wind';
                } else if (warning.type === 'storm') {
                    icon = 'bolt';
                }

                // Format time if available
                let timeInfo = '';
                if (warning.start_time) {
                    const startTime = new Date(warning.start_time);
                    timeInfo = `<div class="alert-time">${startTime.toLocaleString()}</div>`;
                }

                alertDiv.innerHTML = `
                    <div class="alert-title">
                        <i class="fas fa-${icon}"></i>
                        ${warning.title}
                    </div>
                    ${timeInfo}
                    <div class="alert-body">
                        <p>${warning.description}</p>
                        <p><strong>Operational Impact:</strong> ${warning.operational_impact}</p>
                    </div>
                `;

                alertsContainer.appendChild(alertDiv);
            });
        }

        function updateWeatherIcon(iconId, conditions) {
            const iconElement = document.getElementById(iconId);
            let iconClass = 'fa-cloud-sun'; // default

            // Map conditions to Font Awesome icons
            const conditionsLower = conditions.toLowerCase();
            if (conditionsLower.includes('clear') || conditionsLower.includes('sunny')) {
                iconClass = 'fa-sun';
            } else if (conditionsLower.includes('partly cloudy')) {
                iconClass = 'fa-cloud-sun';
            } else if (conditionsLower.includes('cloudy') || conditionsLower.includes('overcast')) {
                iconClass = 'fa-cloud';
            } else if (conditionsLower.includes('rain') || conditionsLower.includes('drizzle')) {
                iconClass = 'fa-cloud-rain';
            } else if (conditionsLower.includes('thunder') || conditionsLower.includes('lightning')) {
                iconClass = 'fa-bolt';
            } else if (conditionsLower.includes('snow')) {
                iconClass = 'fa-snowflake';
            } else if (conditionsLower.includes('fog') || conditionsLower.includes('mist')) {
                iconClass = 'fa-smog';
            }

            // Update the icon class
            iconElement.className = `fas ${iconClass}`;
        }

        function updateImpactIndicator(indicatorId, impact) {
            const indicator = document.getElementById(indicatorId);
            indicator.className = 'impact-indicator impact-' + impact;
        }

        function getWindDirectionText(degrees) {
            const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
            const index = Math.round(degrees / 22.5) % 16;
            return directions[index];
        }

        function capitalizeFirstLetter(string) {
            return string.charAt(0).toUpperCase() + string.slice(1);
        }

        function updateTides(current, forecast) {
            const curEl = document.getElementById('tide-current-level');
            const highEl = document.getElementById('tide-next-high');
            const lowEl = document.getElementById('tide-next-low');

            // Current level
            if (current && current.water_level != null) {
                curEl.textContent = `${current.water_level.toFixed(2)} m NAP`;
            } else if (curEl) {
                curEl.textContent = 'Unavailable';
            }

            // Next highs/lows
            if (forecast && forecast.next_high) {
                const t = new Date(forecast.next_high.time);
                highEl.textContent = `${t.toLocaleTimeString('nl-NL', {hour: '2-digit', minute: '2-digit', hour12: false})} (${Number(forecast.next_high.water_level).toFixed(2)} m)`;
            } else if (highEl) {
                highEl.textContent = '—';
            }
            if (forecast && forecast.next_low) {
                const t = new Date(forecast.next_low.time);
                lowEl.textContent = `${t.toLocaleTimeString('nl-NL', {hour: '2-digit', minute: '2-digit', hour12: false})} (${Number(forecast.next_low.water_level).toFixed(2)} m)`;
            } else if (lowEl) {
                lowEl.textContent = '—';
            }

            // Chart
            if (forecast && Array.isArray(forecast.forecast) && forecast.forecast.length > 0) {
                createTideChart(forecast.forecast, forecast.navigation_levels || {});
            } else if (tideChart) {
                tideChart.destroy();
                tideChart = null;
            }
        }

        function createTideChart(series, navLevels) {
            const ctx = document.getElementById('tides-chart').getContext('2d');
            const labels = [];
            const values = [];
            series.forEach(p => {
                const t = new Date(p.time);
                labels.push(t.toLocaleTimeString('nl-NL', {hour: '2-digit', minute: '2-digit', hour12: false}));
                values.push(p.water_level);
            });

            if (tideChart) tideChart.destroy();

            // Compute dynamic y-axis bounds including navigation thresholds
            const allValues = values.slice();
            if (navLevels && typeof navLevels.minimum_safe === 'number') {
                allValues.push(navLevels.minimum_safe);
            }
            if (navLevels && typeof navLevels.optimal_window === 'number') {
                allValues.push(navLevels.optimal_window);
            }
            const minVal = Math.min(...allValues);
            const maxVal = Math.max(...allValues);
            const range = maxVal - minVal;
            const padding = range === 0 ? 0.25 : range * 0.1; // add 10% padding (or fallback)

            tideChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Water level (m NAP)',
                            data: values,
                            borderColor: 'rgba(54, 162, 235, 1)',
                            backgroundColor: 'rgba(54, 162, 235, 0.35)',
                            fill: false,
                            tension: 0.35,
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: { mode: 'index', intersect: false },
                    scales: {
                        y: {
                            beginAtZero: false,
                            suggestedMin: minVal - padding,
                            suggestedMax: maxVal + padding,
                            title: { display: true, text: 'm above NAP' }
                        },
                        x: { title: { display: true, text: 'Time' } }
                    }
                }
            });

            // Threshold lines plugin for navigation levels
            const thresholds = [];
            if (navLevels && typeof navLevels.minimum_safe === 'number') {
                thresholds.push({ value: navLevels.minimum_safe, color: 'rgba(255, 152, 0, 0.8)', label: 'Minimum Safe' });
            }
            if (navLevels && typeof navLevels.optimal_window === 'number') {
                thresholds.push({ value: navLevels.optimal_window, color: 'rgba(76, 175, 80, 0.8)', label: 'Optimal' });
            }

            const tideThresholdLines = {
                id: 'tideThresholdLines',
                afterDraw: function(chart) {
                    if (!thresholds.length) return;
                    const ctx = chart.ctx;
                    const yAxis = chart.scales.y;
                    ctx.save();
                    thresholds.forEach(th => {
                        const y = yAxis.getPixelForValue(th.value);
                        ctx.beginPath();
                        ctx.moveTo(chart.chartArea.left, y);
                        ctx.lineTo(chart.chartArea.right, y);
                        ctx.strokeStyle = th.color;
                        ctx.lineWidth = 1.5;
                        ctx.setLineDash([4, 4]);
                        ctx.stroke();
                        ctx.fillStyle = th.color;
                        ctx.font = '12px Arial';
                        ctx.textAlign = 'right';
                        ctx.fillText(`${th.label} (${th.value.toFixed(2)} m)`, chart.chartArea.right - 8, y - 4);
                    });
                    ctx.restore();
                }
            };
            Chart.register(tideThresholdLines);
        }
    </script>
{% endblock %}
