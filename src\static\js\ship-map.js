/*
 * ShipMap - Lightweight Leaflet wrapper for showing AIS ships on a map
 * Usage:
 *   const shipMap = new ShipMap('ais-map', { onShipClick: (ship) => {...} });
 *   shipMap.updateShips(shipsArray);
 *
 * shipsArray element format (as returned by /api/tracking/ships):
 *   {
 *     mmsi: '123456789',
 *     vessel_name: 'Vessel',
 *     current_position: { latitude, longitude, speed, course, timestamp },
 *     distance_to_terminal: Number | null
 *   }
 */

class ShipMap {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.onShipClick = options.onShipClick || null;
        this.markersByMmsi = new Map();
        this.lastCourseByMmsi = new Map();
        this.nominatedMmsiSet = new Set();
        this.selectedMmsi = null;
        this.lockMarkersByCode = new Map();
        this.initialized = false;
        this._initMap(options.center, options.zoom);
    }

    _initMap(center, zoom) {
        const defaultCenter = center || [51.34543250288062, 3.751466718019277]; // EVOS Terneuzen
        const defaultZoom = zoom || 9;

        const el = document.getElementById(this.containerId);
        if (!el) {
            console.warn(`ShipMap: container #${this.containerId} not found`);
            return;
        }

        // Initialize Leaflet map
        this.map = L.map(this.containerId, {
            zoomControl: true,
            attributionControl: true,
        }).setView(defaultCenter, defaultZoom);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            maxZoom: 19,
            attribution: '&copy; OpenStreetMap contributors'
        }).addTo(this.map);

        // Terminal marker (center)
        this.terminalMarker = L.circleMarker(defaultCenter, {
            radius: 6,
            color: '#0ea5e9',
            fillColor: '#0ea5e9',
            fillOpacity: 0.9
        }).addTo(this.map).bindTooltip('Terneuzen Terminal');

        this.initialized = true;
    }

    _createShipIcon(courseDeg, isSelected = false, isNominated = false) {
        // Directional icon with robust fallbacks (works even if Font Awesome fails to load)
        const rotation = courseDeg || 0;
        const color = isSelected ? '#ef4444' : (isNominated ? '#7c3aed' : '#0d9488');
        const html = `<div class="ship-glyph" style="transform: rotate(${rotation}deg); color:${color}; font-size:18px; width:18px; height:18px; line-height:18px; text-align:center;">
            <i class="fas fa-location-arrow fa-solid fa-location-arrow" style="display:inline-block;"></i>
            <span style="display:inline-block; font-weight:700;">▲</span>
        </div>`;
        return L.divIcon({ html, className: 'ship-icon', iconSize: [18, 18], iconAnchor: [9, 9] });
    }

    _createLockIcon(status) {
        const s = (status || '').toLowerCase();
        let color = '#6b7280'; // default gray
        if (s === 'open') color = '#16a34a';
        else if (s === 'closed' || s === 'emergency_closed') color = '#dc2626';
        else if (s === 'maintenance' || s === 'scheduled') color = '#f59e0b';
        const html = `<div style="color:${color}; font-size:18px;">
            <i class="fas fa-lock"></i>
        </div>`;
        return L.divIcon({ html, className: 'lock-icon', iconSize: [18, 18], iconAnchor: [9, 9] });
    }

    focusOnShip(mmsi, openPopup = true) {
        if (!this.initialized) return;
        const marker = this.markersByMmsi.get(String(mmsi));
        if (marker) {
            const latlng = marker.getLatLng();
            this.map.setView(latlng, Math.max(this.map.getZoom(), 12), { animate: true });
            if (openPopup) {
                marker.openPopup();
            }
        }
    }

    updateShips(ships) {
        if (!this.initialized) return;
        const seen = new Set();

        (ships || []).forEach(ship => {
            const mmsi = String(ship.mmsi || '');
            const pos = ship.current_position;
            if (!mmsi || !pos || pos.latitude == null || pos.longitude == null) return;

            seen.add(mmsi);

            const latlng = [pos.latitude, pos.longitude];
            const title = ship.vessel_name || `MMSI ${mmsi}`;
            const speedText = (pos.speed !== null && pos.speed !== undefined && isFinite(Number(pos.speed)))
                ? `${Number(pos.speed).toFixed(1)} kn`
                : '—';
            const courseText = (pos.course !== null && pos.course !== undefined && isFinite(Number(pos.course)))
                ? `${Math.round(Number(pos.course))}°`
                : '—';
            const timeText = pos.timestamp ? new Date(pos.timestamp).toLocaleString('nl-NL') : '';

            const popup = `
                <div style="min-width:180px">
                    <strong>${title}</strong><br/>
                    MMSI: ${mmsi}<br/>
                    ${speedText} | ${courseText}<br/>
                    ${timeText}
                </div>`;

            let marker = this.markersByMmsi.get(mmsi);
            const isSelected = this.selectedMmsi && this.selectedMmsi === mmsi;
            const isNominated = this.nominatedMmsiSet.has(mmsi);
            const course = Number(pos.course) || 0;
            this.lastCourseByMmsi.set(mmsi, course);
            if (!marker) {
                marker = L.marker(latlng, { icon: this._createShipIcon(course, isSelected, isNominated) }).addTo(this.map);
                marker.bindPopup(popup);
                marker.on('click', () => {
                    if (this.onShipClick) this.onShipClick(ship);
                });
                this.markersByMmsi.set(mmsi, marker);
            } else {
                marker.setLatLng(latlng);
                marker.setIcon(this._createShipIcon(course, isSelected, isNominated));
                marker.setPopupContent(popup);
            }
        });

        // Remove markers that disappeared
        Array.from(this.markersByMmsi.keys()).forEach(mmsi => {
            if (!seen.has(mmsi)) {
                const marker = this.markersByMmsi.get(mmsi);
                if (marker) {
                    this.map.removeLayer(marker);
                }
                this.markersByMmsi.delete(mmsi);
            }
        });
    }

    setSelectedShip(mmsi) {
        this.selectedMmsi = mmsi ? String(mmsi) : null;
        // Update marker icons to reflect selection state
        this.markersByMmsi.forEach((marker, key) => {
            const isSelected = this.selectedMmsi && this.selectedMmsi === key;
            const isNominated = this.nominatedMmsiSet.has(key);
            const course = this.lastCourseByMmsi.get(key) || 0;
            marker.setIcon(this._createShipIcon(course, isSelected, isNominated));
        });
    }

    setNominated(mmsiArrayOrSet) {
        // Accept array or Set of MMSI strings/numbers
        const nextSet = new Set();
        (mmsiArrayOrSet || []).forEach(m => {
            if (m != null) nextSet.add(String(m));
        });
        this.nominatedMmsiSet = nextSet;
        // Refresh icons to reflect nomination highlighting
        this.setSelectedShip(this.selectedMmsi);
    }

    updateLocks(locks) {
        if (!this.initialized) return;
        const seen = new Set();
        (locks || []).forEach(lock => {
            const code = String(lock.code || lock.key || lock.name || '');
            const loc = lock.location || {};
            const lat = loc.latitude;
            const lon = loc.longitude;
            if (!code || lat == null || lon == null) return;
            seen.add(code);
            const latlng = [lat, lon];
            const title = lock.name || code;
            const status = (lock.status || '').toLowerCase();
            const popup = `
                <div style="min-width:200px">
                    <strong>${title}</strong><br/>
                    Code: ${code}<br/>
                    Status: ${status || 'unknown'}<br/>
                    ${lock.chambers != null ? `Chambers: ${lock.chambers}<br/>` : ''}
                    ${lock.max_dimensions ? `Max: L ${lock.max_dimensions.length || '-'}m, B ${lock.max_dimensions.beam || '-'}m, T ${lock.max_dimensions.draft || '-'}m` : ''}
                </div>`;
            let marker = this.lockMarkersByCode.get(code);
            if (!marker) {
                marker = L.marker(latlng, { icon: this._createLockIcon(status) }).addTo(this.map);
                marker.bindPopup(popup).bindTooltip(title);
                this.lockMarkersByCode.set(code, marker);
            } else {
                marker.setLatLng(latlng);
                marker.setIcon(this._createLockIcon(status));
                marker.setPopupContent(popup);
            }
        });
        // Remove old lock markers
        Array.from(this.lockMarkersByCode.keys()).forEach(code => {
            if (!seen.has(code)) {
                const marker = this.lockMarkersByCode.get(code);
                if (marker) this.map.removeLayer(marker);
                this.lockMarkersByCode.delete(code);
            }
        });
    }
}

window.ShipMap = ShipMap;


