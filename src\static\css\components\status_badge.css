/* Status Badge Component Styles */

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: capitalize;
    white-space: nowrap;
    color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    cursor: default;
}

/* Vessel Status Colors */
.status-badge.scheduled { background-color: #3498db; }
.status-badge.approaching { background-color: #9b59b6; }
.status-badge.arrived { background-color: #1abc9c; }
.status-badge.docked { background-color: #2ecc71; }
.status-badge.loading { background-color: #e67e22; }
.status-badge.unloading { background-color: #f1c40f; color: #333; }
.status-badge.departing { background-color: #e91e63; }
.status-badge.departed { background-color: #95a5a6; }
.status-badge.delayed { background-color: #e74c3c; }
.status-badge.cancelled { background-color: #7f8c8d; }

/* Assignment Status Colors */
.status-badge.active { background-color: #2ecc71; }
.status-badge.completed { background-color: #95a5a6; }
.status-badge.pending-approval { background-color: #e67e22; }

/* Status Badge with Icon */
.status-badge .icon {
    margin-right: 4px;
    font-size: 0.8rem;
}

/* Status Badge with Counter */
.status-badge .count {
    margin-left: 5px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    padding: 1px 6px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Status Legend */
.status-legend {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 100;
    width: 250px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    padding: 10px;
    margin-top: 5px;
    border: 1px solid #eaeaea;
}

.status-legend.active {
    display: block;
}

.status-legend-title {
    font-weight: 600;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eaeaea;
    font-size: 0.9rem;
}

.status-legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.status-legend-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
}

.status-legend-label {
    font-size: 0.85rem;
    color: #333;
}

.status-legend-toggle {
    cursor: pointer;
    margin-left: 5px;
    opacity: 0.5;
    transition: opacity 0.2s;
    font-size: 0.85rem;
}

.status-legend-toggle:hover {
    opacity: 1;
}

/* Vessel Legend Colors */
.status-legend-indicator.scheduled { background-color: #3498db; }
.status-legend-indicator.approaching { background-color: #9b59b6; }
.status-legend-indicator.arrived { background-color: #1abc9c; }
.status-legend-indicator.docked { background-color: #2ecc71; }
.status-legend-indicator.loading { background-color: #e67e22; }
.status-legend-indicator.unloading { background-color: #f1c40f; }
.status-legend-indicator.departing { background-color: #e91e63; }
.status-legend-indicator.departed { background-color: #95a5a6; }
.status-legend-indicator.delayed { background-color: #e74c3c; }
.status-legend-indicator.cancelled { background-color: #7f8c8d; }

/* Assignment Legend Colors */
.status-legend-indicator.active { background-color: #2ecc71; }
.status-legend-indicator.completed { background-color: #95a5a6; }
.status-legend-indicator.pending-approval { background-color: #e67e22; }

/* Responsive adjustments */
@media (max-width: 768px) {
    .status-legend {
        width: 200px;
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 2px 6px;
    }
} 