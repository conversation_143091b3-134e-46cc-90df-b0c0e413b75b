"""
Real-time Schedule Adjustment Service
Automatically adjusts schedules when ETAs change significantly
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

from ..database import Database
from ..optimization.scheduler import JettyScheduler
from ..models.terminal import Terminal
from ..models.vessel import VesselBase
from ..services.dynamic_eta_service import DynamicETAService, ETAUpdate

logger = logging.getLogger(__name__)

class ScheduleAdjustmentTrigger(Enum):
    """Reasons for schedule adjustment"""
    SIGNIFICANT_ETA_CHANGE = "significant_eta_change"
    MULTIPLE_ETA_CHANGES = "multiple_eta_changes"
    CRITICAL_VESSEL_DELAY = "critical_vessel_delay"
    OPTIMIZATION_IMPROVEMENT = "optimization_improvement"
    MANUAL_REQUEST = "manual_request"

@dataclass
class ScheduleAdjustment:
    """Represents a schedule adjustment event"""
    trigger: ScheduleAdjustmentTrigger
    affected_vessels: List[str]
    eta_changes: List[ETAUpdate]
    old_schedule_score: Optional[float]
    new_schedule_score: Optional[float]
    adjustment_time: datetime
    success: bool
    details: str

class RealtimeScheduleService:
    """Service for real-time schedule adjustments based on ETA changes"""
    
    def __init__(self, 
                 database: Database,
                 dynamic_eta_service: DynamicETAService,
                 terminal: Terminal,
                 adjustment_threshold_hours: float = 1.0,
                 min_improvement_threshold: float = 0.05):
        """
        Initialize Real-time Schedule Service
        
        Args:
            database: Database connection
            dynamic_eta_service: Service for ETA updates
            terminal: Terminal configuration
            adjustment_threshold_hours: Minimum ETA change to trigger adjustment
            min_improvement_threshold: Minimum schedule improvement to apply changes
        """
        self.database = database
        self.dynamic_eta_service = dynamic_eta_service
        self.terminal = terminal
        self.adjustment_threshold_hours = adjustment_threshold_hours
        self.min_improvement_threshold = min_improvement_threshold
        
        # Tracking
        self.last_schedule_time = None
        self.adjustment_count = 0
        self.successful_adjustments = 0
        self.pending_eta_changes: List[ETAUpdate] = []
        
        # Configuration
        self.batch_adjustment_window_minutes = 10  # Wait 10 min to batch changes
        self.max_adjustments_per_hour = 6  # Limit frequency
        self.critical_vessel_priorities = set()  # High-priority vessels
        
    async def start_monitoring(self):
        """Start monitoring for schedule adjustments"""
        logger.info("Starting real-time schedule adjustment monitoring")
        
        while True:
            try:
                await self._check_for_schedule_adjustments()
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                logger.error(f"Error in schedule adjustment monitoring: {e}")
                await asyncio.sleep(60)
    
    async def handle_eta_update(self, eta_update: ETAUpdate):
        """Handle a new ETA update and determine if schedule adjustment is needed"""
        logger.debug(f"Handling ETA update for vessel {eta_update.vessel_name}")
        
        # Add to pending changes
        self.pending_eta_changes.append(eta_update)
        
        # Check if immediate adjustment is needed
        if await self._should_trigger_immediate_adjustment(eta_update):
            await self._trigger_schedule_adjustment(
                ScheduleAdjustmentTrigger.CRITICAL_VESSEL_DELAY,
                [eta_update]
            )
        else:
            # Schedule batched adjustment
            logger.debug(f"ETA change queued for batched adjustment: {eta_update.vessel_name}")
    
    async def _check_for_schedule_adjustments(self):
        """Check if any pending ETA changes warrant schedule adjustment"""
        if not self.pending_eta_changes:
            return
        
        # Check if we should process batched changes
        oldest_change = min(self.pending_eta_changes, key=lambda x: x.significance)
        time_since_oldest = (datetime.now(timezone.utc) - oldest_change.old_eta).total_seconds() / 60
        
        if time_since_oldest >= self.batch_adjustment_window_minutes:
            # Process batched changes
            significant_changes = [
                change for change in self.pending_eta_changes 
                if change.significance >= 0.5  # Only significant changes
            ]
            
            if significant_changes:
                await self._trigger_schedule_adjustment(
                    ScheduleAdjustmentTrigger.MULTIPLE_ETA_CHANGES,
                    significant_changes
                )
            
            # Clear processed changes
            self.pending_eta_changes.clear()
    
    async def _should_trigger_immediate_adjustment(self, eta_update: ETAUpdate) -> bool:
        """Determine if an ETA update should trigger immediate schedule adjustment"""
        
        # Critical vessel check
        if eta_update.vessel_id in self.critical_vessel_priorities:
            return eta_update.significance >= 0.3
        
        # Large ETA change check
        if eta_update.significance >= 0.8:  # Very significant change
            return True
        
        # High confidence, significant change
        if eta_update.confidence >= 85 and eta_update.significance >= 0.6:
            return True
        
        return False
    
    async def _trigger_schedule_adjustment(self, 
                                         trigger: ScheduleAdjustmentTrigger, 
                                         eta_changes: List[ETAUpdate]) -> Optional[ScheduleAdjustment]:
        """Trigger a schedule adjustment"""
        
        # Rate limiting check
        if not await self._check_adjustment_rate_limit():
            logger.warning("Schedule adjustment rate limit exceeded, skipping")
            return None
        
        logger.info(f"Triggering schedule adjustment: {trigger.value} affecting {len(eta_changes)} vessels")
        
        try:
            # Get current schedule
            current_vessels = await self._get_current_vessels()
            if not current_vessels:
                logger.warning("No vessels found for schedule adjustment")
                return None
            
            # Calculate current schedule score
            current_scheduler = JettyScheduler(
                terminal=self.terminal,
                vessels=current_vessels,
                start_time=datetime.now(timezone.utc),
                time_horizon_hours=48
            )
            
            current_schedule = current_scheduler.optimize()
            current_score = current_schedule.total_score if current_schedule else 0
            
            # Update vessel ETAs with new information
            updated_vessels = await self._apply_eta_changes_to_vessels(current_vessels, eta_changes)
            
            # Calculate new schedule
            new_scheduler = JettyScheduler(
                terminal=self.terminal,
                vessels=updated_vessels,
                start_time=datetime.now(timezone.utc),
                time_horizon_hours=48
            )
            
            new_schedule = new_scheduler.optimize()
            new_score = new_schedule.total_score if new_schedule else 0
            
            # Determine if adjustment should be applied
            score_improvement = (new_score - current_score) / abs(current_score) if current_score != 0 else 0
            should_apply = score_improvement >= self.min_improvement_threshold
            
            adjustment = ScheduleAdjustment(
                trigger=trigger,
                affected_vessels=[change.vessel_id for change in eta_changes],
                eta_changes=eta_changes,
                old_schedule_score=current_score,
                new_schedule_score=new_score,
                adjustment_time=datetime.now(timezone.utc),
                success=should_apply,
                details=f"Score change: {score_improvement:.3f} ({'applied' if should_apply else 'rejected'})"
            )
            
            if should_apply:
                # Apply the new schedule
                await self._apply_schedule_adjustment(new_schedule, adjustment)
                self.successful_adjustments += 1
                logger.info(f"Schedule adjustment applied: {score_improvement:.3f} improvement")
            else:
                logger.info(f"Schedule adjustment rejected: {score_improvement:.3f} improvement (below threshold)")
            
            self.adjustment_count += 1
            self.last_schedule_time = datetime.now(timezone.utc)
            
            return adjustment
            
        except Exception as e:
            logger.error(f"Schedule adjustment failed: {e}")
            return ScheduleAdjustment(
                trigger=trigger,
                affected_vessels=[change.vessel_id for change in eta_changes],
                eta_changes=eta_changes,
                old_schedule_score=None,
                new_schedule_score=None,
                adjustment_time=datetime.now(timezone.utc),
                success=False,
                details=f"Error: {str(e)}"
            )
    
    async def _check_adjustment_rate_limit(self) -> bool:
        """Check if we're within rate limits for schedule adjustments"""
        if not self.last_schedule_time:
            return True
        
        # Check hourly rate limit
        one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)
        if self.last_schedule_time > one_hour_ago:
            recent_adjustments = self.adjustment_count  # Simplified - would track per hour in production
            return recent_adjustments < self.max_adjustments_per_hour
        
        return True
    
    async def _get_current_vessels(self) -> List[VesselBase]:
        """Get current active vessels for scheduling"""
        try:
            with self.database.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT 
                            runtime_vessel_id, name, vessel_type,
                            eta, calculated_eta, eta_confidence, eta_source,
                            length, beam, draft, deadweight
                        FROM nominations 
                        WHERE status = 'ACTIVE'
                        AND eta IS NOT NULL;
                    """)
                    
                    vessels = []
                    for row in cursor.fetchall():
                        vessel = VesselBase(
                            id=row[0],
                            name=row[1],
                            vessel_type=row[2],
                            eta=row[3],
                            calculated_eta=row[4],
                            eta_confidence=row[5] or 50,
                            eta_source=row[6] or 'user',
                            length=row[7] or 0,
                            beam=row[8] or 0,
                            draft=row[9] or 0,
                            deadweight=row[10] or 0
                        )
                        vessels.append(vessel)
                    
                    return vessels
                    
        except Exception as e:
            logger.error(f"Failed to get current vessels: {e}")
            return []
    
    async def _apply_eta_changes_to_vessels(self, 
                                          vessels: List[VesselBase], 
                                          eta_changes: List[ETAUpdate]) -> List[VesselBase]:
        """Apply ETA changes to vessel list"""
        eta_change_map = {change.vessel_id: change for change in eta_changes}
        
        updated_vessels = []
        for vessel in vessels:
            if vessel.id in eta_change_map:
                change = eta_change_map[vessel.id]
                # Create updated vessel with new ETA
                updated_vessel = VesselBase(
                    id=vessel.id,
                    name=vessel.name,
                    vessel_type=vessel.vessel_type,
                    eta=vessel.eta,
                    calculated_eta=change.new_calculated_eta,
                    eta_confidence=change.confidence,
                    eta_source=change.source,
                    length=vessel.length,
                    beam=vessel.beam,
                    draft=vessel.draft,
                    deadweight=vessel.deadweight
                )
                updated_vessels.append(updated_vessel)
            else:
                updated_vessels.append(vessel)
        
        return updated_vessels
    
    async def _apply_schedule_adjustment(self, new_schedule, adjustment: ScheduleAdjustment):
        """Apply the new schedule to the system"""
        try:
            # In a real system, this would:
            # 1. Update the assignments table
            # 2. Notify affected parties
            # 3. Update any external systems
            # 4. Log the change for audit
            
            logger.info(f"Applied schedule adjustment affecting {len(adjustment.affected_vessels)} vessels")
            
            # For now, just log the change
            with self.database.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        INSERT INTO schedule_adjustments 
                        (trigger_type, affected_vessels, adjustment_time, success, details)
                        VALUES (%s, %s, %s, %s, %s)
                        ON CONFLICT DO NOTHING;
                    """, (
                        adjustment.trigger.value,
                        ','.join(adjustment.affected_vessels),
                        adjustment.adjustment_time,
                        adjustment.success,
                        adjustment.details
                    ))
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"Failed to apply schedule adjustment: {e}")
    
    async def force_schedule_adjustment(self, reason: str = "manual") -> Optional[ScheduleAdjustment]:
        """Force a manual schedule adjustment"""
        logger.info(f"Forcing manual schedule adjustment: {reason}")
        
        # Use any pending ETA changes
        eta_changes = self.pending_eta_changes.copy()
        self.pending_eta_changes.clear()
        
        return await self._trigger_schedule_adjustment(
            ScheduleAdjustmentTrigger.MANUAL_REQUEST,
            eta_changes
        )
    
    def get_service_stats(self) -> Dict:
        """Get service statistics"""
        return {
            'last_schedule_time': self.last_schedule_time,
            'total_adjustments': self.adjustment_count,
            'successful_adjustments': self.successful_adjustments,
            'pending_eta_changes': len(self.pending_eta_changes),
            'adjustment_threshold_hours': self.adjustment_threshold_hours,
            'min_improvement_threshold': self.min_improvement_threshold,
            'success_rate': (self.successful_adjustments / self.adjustment_count * 100) if self.adjustment_count > 0 else 0
        }
