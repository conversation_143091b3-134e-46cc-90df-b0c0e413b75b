# Backup Restoration Guide - Jetty Planner

## 🚨 Emergency Restoration Procedures

### Quick Reference Commands
```powershell
# Stop the application first
docker stop jetty-planning-app

# Restore from backup (replace with actual backup filename)
docker exec jetty-postgres-backup restore /backups/jetty_planner_backup_20250904.sql

# Restart application
docker start jetty-planning-app
```

---

## 📋 Types of Failures & Solutions

### 1. **Data Corruption / Accidental Data Loss**
**Symptoms:** Missing records, corrupted data, accidental deletions
**Solution:** Restore from most recent clean backup

### 2. **Database Server Failure**
**Symptoms:** PostgreSQL won't start, database files corrupted
**Solution:** Full database restoration

### 3. **Application Issues After Data Changes**
**Symptoms:** App errors after schema changes, migration failures
**Solution:** Restore to point before changes

### 4. **Complete System Failure**
**Symptoms:** Server crashed, Docker volumes lost
**Solution:** Full system restoration

---

## 🔍 Step 1: Assess the Situation

### A. Identify the Problem
```powershell
# Check if database is accessible
docker exec jetty-planning-app python -c "from src.database import engine; print('DB OK' if engine else 'DB FAILED')"

# Check recent logs for errors
docker logs jetty-planning-app --tail 50

# Check database container status
docker ps --filter "name=postgres"
```

### B. Determine Recovery Point
```powershell
# List available backups
Get-ChildItem C:\Users\<USER>\Jettyplanner\backups\ -Name "*.sql" | Sort-Object

# Or browse via web interface
# https://planner.evosgpt.eu/backups/
```

### C. Choose the Right Backup
- **Latest backup**: For recent corruption/accidental changes
- **Pre-change backup**: If problems started after specific changes
- **Weekly backup**: For major rollback scenarios
- **Monthly backup**: For disaster recovery

---

## ⚡ Step 2: Emergency Stop Procedures

### Stop Application Services
```powershell
# Stop the main application
docker stop jetty-planning-app

# Verify application is stopped
docker ps --filter "name=jetty-planning-app"

# Optional: Stop related services if needed
# docker stop jetty-backup-web  # Only if interfering with restore
```

### Create Emergency Backup (if possible)
```powershell
# If database is still accessible, create emergency backup first
docker exec jetty-postgres-backup backup-emergency

# This creates a backup with timestamp before restoration
```

---

## 🔧 Step 3: Database Restoration

### Method A: Using Backup Container (Recommended)
```powershell
# Navigate to project directory
cd C:\Users\<USER>\Jettyplanner

# Method 1: Direct restore (if backup container has restore capability)
docker exec jetty-postgres-backup restore /backups/your_backup_file.sql

# Method 2: Using PostgreSQL client in backup container
docker exec -it jetty-postgres-backup psql -h $POSTGRES_HOST -U $POSTGRES_USER -d postgres -c "DROP DATABASE IF EXISTS planner;"
docker exec -it jetty-postgres-backup psql -h $POSTGRES_HOST -U $POSTGRES_USER -d postgres -c "CREATE DATABASE planner;"
docker exec -it jetty-postgres-backup psql -h $POSTGRES_HOST -U $POSTGRES_USER -d planner -f /backups/your_backup_file.sql
```

### Method B: Direct PostgreSQL Restore
```powershell
# If you have direct database access
# First, get database connection details from your .env file

# Drop and recreate database (⚠️ DESTRUCTIVE!)
psql -h localhost -p 4432 -U postgres -c "DROP DATABASE IF EXISTS planner;"
psql -h localhost -p 4432 -U postgres -c "CREATE DATABASE planner;"

# Restore from backup
psql -h localhost -p 4432 -U postgres -d planner -f "C:\Users\<USER>\Jettyplanner\backups\your_backup_file.sql"
```

### Method C: Docker Volume Restore (for complete failure)
```powershell
# If PostgreSQL container/volume is completely lost
# Stop and remove damaged containers
docker stop postgres-container-name
docker rm postgres-container-name
docker volume rm postgres-volume-name

# Recreate PostgreSQL container
docker-compose up -d postgres

# Wait for PostgreSQL to start, then restore
Start-Sleep -Seconds 30
# Run Method A or B above
```

---

## ✅ Step 4: Verification & Testing

### A. Verify Database Integrity
```powershell
# Check if database is accessible
docker exec jetty-planning-app python -c "
from src.database import engine
from sqlalchemy import text
with engine.connect() as conn:
    result = conn.execute(text('SELECT COUNT(*) FROM nominations;'))
    print(f'Nominations count: {result.scalar()}')
"

# Check critical tables exist
docker exec jetty-planning-app python -c "
from src.database import engine
from sqlalchemy import text
tables = ['nominations', 'vessels', 'terminals', 'schedules']
with engine.connect() as conn:
    for table in tables:
        result = conn.execute(text(f'SELECT COUNT(*) FROM {table};'))
        print(f'{table}: {result.scalar()} records')
"
```

### B. Start Application Services
```powershell
# Start the main application
docker start jetty-planning-app

# Wait for startup
Start-Sleep -Seconds 15

# Check application health
docker ps --filter "name=jetty-planning-app"
curl http://localhost:7000/health  # If health endpoint exists
```

### C. Functional Testing
```powershell
# Test web interface
# Navigate to: http://localhost:7000
# Or: https://planner.evosgpt.eu

# Check key functionality:
# 1. Dashboard loads
# 2. Data is visible
# 3. Can create/edit records
# 4. No error messages
```

---

## 🔄 Step 5: Post-Restoration Tasks

### Update Application State
```powershell
# Run any necessary migrations (if restoring older backup)
docker exec jetty-planning-app alembic upgrade head

# Clear application caches if any
docker exec jetty-planning-app python -c "
# Add any cache clearing logic here
print('Cache cleared')
"
```

### Document the Incident
```powershell
# Create incident log
$Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
$LogEntry = "[$Timestamp] Database restored from backup: backup_filename.sql"
Add-Content -Path "C:\Users\<USER>\Jettyplanner\logs\restore-log.txt" -Value $LogEntry
```

### Restart Backup Schedule
```powershell
# Ensure backup services are running
docker-compose -f docker-compose.backup.yml up -d

# Verify backup schedule is active
docker logs jetty-postgres-backup --tail 5
```

---

## 🆘 Emergency Scenarios

### Scenario 1: "I accidentally deleted critical data"
```powershell
# 1. Stop application immediately
docker stop jetty-planning-app

# 2. Choose backup from before deletion
# Check: https://planner.evosgpt.eu/backups/

# 3. Restore using Method A above
docker exec jetty-postgres-backup restore /backups/backup_before_deletion.sql

# 4. Restart and verify
docker start jetty-planning-app
```

### Scenario 2: "Application won't start after update"
```powershell
# 1. Check if it's a database schema issue
docker logs jetty-planning-app

# 2. If migration failed, restore pre-update backup
docker exec jetty-postgres-backup restore /backups/backup_before_update.sql

# 3. Rollback application version if needed
# docker pull jettyplanner:previous-version
```

### Scenario 3: "Complete server crash"
```powershell
# 1. Reinstall Docker and restore project files from git
git clone https://github.com/Timverhoogt/Jettyplanner.git
cd Jettyplanner

# 2. Copy backup files to new system
# (from external storage or backup location)

# 3. Start fresh containers and restore database
docker-compose up -d
docker exec jetty-postgres-backup restore /backups/latest_backup.sql
```

---

## 📱 Quick Recovery Checklist

- [ ] **Stop application** (`docker stop jetty-planning-app`)
- [ ] **Identify backup file** (browse `/backups/` or web interface)
- [ ] **Create emergency backup** (if database accessible)
- [ ] **Restore database** (`docker exec jetty-postgres-backup restore`)
- [ ] **Verify restoration** (check table counts, key data)
- [ ] **Start application** (`docker start jetty-planning-app`)
- [ ] **Test functionality** (web interface, critical features)
- [ ] **Document incident** (log what happened and steps taken)
- [ ] **Monitor** (watch for recurring issues)

---

## 🚨 When to Call for Help

**Immediate help needed if:**
- Database restore fails with errors
- Application won't start after restoration
- Data still appears corrupted after restore
- Backup files are corrupted/unreadable
- Multiple restore attempts fail

**Contact information:**
- Check application logs: `docker logs jetty-planning-app`
- Review backup logs: `docker logs jetty-postgres-backup`
- System status: `docker ps -a`
- Create support bundle with logs and error messages

---

## 💡 Prevention Tips

1. **Test restores regularly** (monthly test restoration to verify backups work)
2. **Monitor backup health** (check backup container status daily)
3. **Keep multiple backup copies** (local + offsite)
4. **Document changes** (note major updates/migrations)
5. **Verify backup integrity** (check backup file sizes and dates)

Remember: **Practice makes perfect!** Test restoration procedures during maintenance windows to ensure you're prepared for real emergencies.
