# Optimization System Alignment Analysis

## 🔍 Current Issues Found

### 1. Parameter Mismatch Between UI and Implementation

**UI Sliders** → **API Parameters** → **Scheduler Implementation**
- ✅ Throughput Weight → `weight_throughput` → `self.weight_throughput` ✓ ALIGNED
- ✅ Demurrage Weight → `weight_demurrage` → `self.weight_demurrage` ✓ ALIGNED  
- ✅ Priority Weight → `weight_priority` → `self.weight_priority` ✓ ALIGNED
- ❌ **Utilization Weight** → `weight_weather` → **NOT IMPLEMENTED** ❌ BROKEN
- ✅ Planning Horizon → `horizon_days` → `horizon_days` ✓ ALIGNED
- ✅ Time Granularity → `time_granularity_hours` → `time_granularity_hours` ✓ ALIGNED

### 2. Missing Implementation in JettyScheduler

The scheduler has these weight attributes:
```python
self.weight_throughput = 10.0  # ✓ Used in objective
self.weight_demurrage = 5.0    # ✓ Used in objective
self.weight_priority = 3.0     # ✓ Used in objective
self.weight_contract = 8.0     # ❌ NOT exposed in UI
```

But missing:
- `weight_weather` or `weight_utilization` - **NOT IMPLEMENTED AT ALL**

### 3. Semantic Confusion: "Utilization" vs "Weather"

**UI Label**: "Utilization Weight" 
- Description: "Higher values prioritize balanced jetty utilization and infrastructure efficiency"

**API Parameter**: `weight_weather`
- Suggests weather-based optimization

**Business Rules**: Both concepts are important!
- **Weather**: Wind speed limits (>14 m/s stops operations, >17 m/s stops mooring)
- **Utilization**: Balanced jetty usage for infrastructure efficiency

## 🏭 Business Rules Alignment Analysis

### Current Objective Function vs. Evos Terminal Requirements

#### ✅ Currently Implemented & Aligned:
1. **Throughput Optimization** - Matches business need to maximize vessel processing
2. **Demurrage Minimization** - Critical for cost control (business rule PS-1, 4.1)
3. **Priority Handling** - Important for customer service

#### ❌ Missing Critical Factors from Business Rules:

1. **Weather Constraints** (Section 4.1)
   ```
   Rule W-3: Stop operations if wind_speed > 14 m/s
   Rule W-4: Stop loading if wind_speed > 17 m/s  
   Rule W-5: Preemptive disconnection if forecast > 21 m/s
   ```
   **Current Status**: Not implemented in optimization

2. **Product-Specific Safety** (Section 4.3)
   ```
   Rule PS-1: Hazardous products need special handling
   Rule PS-3: Nitrogen purging adds preparation time
   ```
   **Current Status**: Not considered in optimization

3. **Flow Rate Restrictions** (Section 4.2, 5)
   ```
   Rule FR-1: Static accumulator products start at ≤1 m/s
   Rule FR-2: Max 4 simultaneous tanks during startup
   ```
   **Current Status**: Not factored into terminal time prediction

4. **Vessel-Jetty Compatibility** (Section 3.1-3.3)
   ```
   Hard constraints: DWT, LOA, beam, draft limits
   Product compatibility matrix
   ```
   **Current Status**: Partially implemented but could be enhanced

## 🎯 Optimization Strategy Alignment

### Preset Strategies vs. Business Reality:

1. **🚀 Maximum Throughput**: 
   - ✅ Good for high-demand periods
   - ❌ Should consider weather windows
   - ❌ Should factor in product flow restrictions

2. **💰 Cost Efficiency**:
   - ✅ Demurrage minimization is correct
   - ❌ Should consider weather delays that increase costs
   - ❌ Should account for product-specific preparation time

3. **🏗️ Infrastructure Efficiency**:
   - ❌ NO IMPLEMENTATION - This is the "Utilization Weight" that's broken
   - Should optimize jetty usage distribution
   - Should consider connection compatibility

4. **⚖️ Balanced**:
   - ✅ Conceptually correct
   - ❌ Missing weather and infrastructure factors

## 📋 Specific Technical Issues

### 1. Broken Parameter Chain
```
UI: "Utilization Weight" → API: weight_weather → Scheduler: DOES NOT EXIST
```

### 2. Missing Objective Terms
Current objective only has:
- Throughput terms
- Demurrage terms  
- Priority terms
- Force assignment terms
- Optional jetty balancing

Missing:
- Weather/safety terms
- Product complexity terms
- Flow rate efficiency terms
- Infrastructure utilization terms

### 3. Hard Constraints Not Fully Implemented
Business rules define many hard constraints that should make certain assignments impossible, not just less optimal.

## 🔧 Recommendations

### Immediate Fixes (Critical):
1. **Fix Utilization Weight Parameter**
   - Add `weight_utilization` to JettyScheduler
   - Implement jetty balancing objective term
   - Update API to use consistent parameter names

2. **Implement Weather Factor**
   - Add `weight_weather` to JettyScheduler  
   - Create weather risk scoring based on wind forecast
   - Penalize assignments during high-wind periods

### Medium-term Enhancements:
1. **Product-Specific Factors**
   - Add complexity scoring for hazardous products
   - Factor in preparation time requirements
   - Consider flow rate restrictions

2. **Enhanced Hard Constraints**
   - Implement full compatibility matrix
   - Add weather-based operational constraints
   - Validate against business rules

### Long-term Improvements:
1. **ML Integration Enhancement**
   - Use business rules for feature engineering
   - Incorporate weather forecast data
   - Product-specific terminal time prediction

## 🚨 Critical Action Items

1. **URGENT**: Fix the broken Utilization Weight parameter
2. **HIGH**: Implement weather consideration in optimization
3. **MEDIUM**: Enhance hard constraints based on business rules
4. **LOW**: Add product-specific optimization factors

