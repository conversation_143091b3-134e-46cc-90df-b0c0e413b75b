{% extends "base.html" %}

{% block title %}Assistant - Terneuzen Terminal Jetty Planning{% endblock %}

{% block head %}
<style>
/* Additional styles for chat interface */
.chat-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    background-color: #f5f7fa;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
}

.message {
    max-width: 80%;
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 1rem;
    position: relative;
    line-height: 1.5;
}

.message.user {
    background-color: #e3f2fd;
    color: #1976d2;
    align-self: flex-end;
    margin-left: auto;
    border-bottom-right-radius: 0.25rem;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message.assistant {
    background-color: white;
    color: var(--dark-color);
    align-self: flex-start;
    border-bottom-left-radius: 0.25rem;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.chat-input {
    display: flex;
    padding: 1rem;
    background-color: white;
    border-top: 1px solid #e0e0e0;
}

.chat-input input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.chat-input button {
    margin-left: 0.5rem;
}

.typing-indicator {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem 1rem;
    background-color: white;
    border-radius: 1rem;
    width: fit-content;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.typing-indicator span {
    height: 8px;
    width: 8px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    margin: 0 2px;
    animation: typing 1.4s infinite both;
}

.typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typing {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
    100% {
        transform: translateY(0);
    }
}

.message table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.message table th,
.message table td {
    padding: 0.5rem;
    border: 1px solid #e0e0e0;
    text-align: left;
}

.message table th {
    background-color: #f5f5f5;
    font-weight: 500;
}

.message b {
    font-weight: 500;
    color: var(--primary-teal);
}

/* Markdown content styles */
.message .content h2,
.message .content h3,
.message .content h4 {
    color: var(--dark-teal);
    margin: 0.25rem 0 0.5rem 0;
}

.message .content p {
    margin: 0.25rem 0 0.75rem 0;
}

.message .content ul,
.message .content ol {
    margin: 0.25rem 0 0.75rem 1.25rem;
}

.message .content pre {
    background: #0f172a0d;
    border: 1px solid #e5e7eb;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.5rem 0 0.75rem 0;
}

.message .content code {
    background: #0f172a14;
    border: 1px solid #e5e7eb;
    border-radius: 0.25rem;
    padding: 0.1rem 0.3rem;
    font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.95em;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.copy-btn {
    border: 1px solid #e5e7eb;
    background: #ffffff;
    border-radius: 0.35rem;
    padding: 0.15rem 0.4rem;
    cursor: pointer;
}

/* Welcome message styling */
.welcome-message {
    width: 100%;
}

.welcome-message h3 {
    margin-top: 0;
    color: var(--primary-teal);
    font-size: 1.3rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.welcome-message p {
    margin-bottom: 1rem;
}

.feature-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    transition: transform 0.2s ease;
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 5px rgba(0,0,0,0.1);
}

.feature-item i {
    font-size: 1.25rem;
    color: var(--primary-teal);
    width: 1.5rem;
    text-align: center;
}

.prompt-question {
    font-weight: 500;
    margin-top: 1.25rem;
    color: var(--dark-teal);
    font-size: 1.1rem;
}

/* Assistant info and API status */
.assistant-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.api-status {
    font-size: 0.8rem;
    padding: 0.15rem 0.5rem;
    border-radius: 0.25rem;
    background-color: #e0e0e0;
}

.api-status.live {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.api-status.mock {
    background-color: #fff3e0;
    color: #e65100;
}

.api-status.error {
    background-color: #ffebee;
    color: #c62828;
}
</style>
{% endblock %}

{% block user_actions %}
    <button class="btn btn-secondary" id="clear-chat">
        <i class="fas fa-trash"></i>
        Clear Chat
    </button>
{% endblock %}

{% block header %}Jetty Planning Assistant{% endblock %}

{% block content %}
<div class="card" style="height: calc(100vh - 100px);">
    <div class="card-header">
        <h3>Jetty Planning Assistant</h3>
        <div class="assistant-info">
            <span>Powered by Claude 3.5</span>
            <span class="api-status" id="api-status">Checking API status...</span>
        </div>
    </div>
    <div class="card-body" style="padding: 0; overflow: hidden; display: flex; flex-direction: column;">
        <div class="chat-container">
            <div class="chat-messages" id="chat-messages">
                <!-- Assistant welcome message -->
                <div class="message assistant">
                    <div class="welcome-message">
                        <h3><i class="fas fa-robot"></i> Welcome to the Terneuzen Terminal Assistant!</h3>
                        <p>I'm here to help you manage your petrochemical terminal operations. You can ask me about:</p>
                        
                        <div class="feature-list">
                            <div class="feature-item">
                                <i class="fas fa-calendar-alt"></i>
                                <span>Current schedule and jetty availability</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-ship"></i>
                                <span>Vessel information and status</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-cloud-sun"></i>
                                <span>Weather conditions and forecasts</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-cogs"></i>
                                <span>Schedule optimization</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-industry"></i>
                                <span>Terminal resources and utilization</span>
                            </div>
                        </div>
                        
                        <p class="prompt-question">How can I assist you today?</p>
                    </div>
                </div>
            </div>
            
            <div class="chat-input">
                <input type="text" id="chat-input" placeholder="Type your message here...">
                <button class="btn btn-primary" id="send-button">
                    <i class="fas fa-paper-plane"></i>
                    Send
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        // Chat elements
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-button');
        const clearButton = document.getElementById('clear-chat');
        
        // Chat history
        const chatHistory = [];
        
        // Function to add a message to the chat
        function addMessage(text, isUser = false) {
            const message = document.createElement('div');
            message.className = `message ${isUser ? 'user' : 'assistant'}`;
            const content = document.createElement('div');
            content.className = 'content';
            // Render markdown for assistant messages; user stays plain text
            if (isUser) {
                content.textContent = text;
            } else {
                content.innerHTML = renderMarkdown(text);
                enhanceCodeBlocks(content);
            }
            message.appendChild(content);
            
            chatMessages.appendChild(message);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            // Add to history
            chatHistory.push({
                text: text,
                isUser: isUser,
                time: new Date()
            });
        }
        
        // Function to show typing indicator
        function showTypingIndicator() {
            const indicator = document.createElement('div');
            indicator.className = 'typing-indicator';
            indicator.id = 'typing-indicator';
            indicator.innerHTML = `
                <span></span>
                <span></span>
                <span></span>
            `;
            chatMessages.appendChild(indicator);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // Function to remove typing indicator
        function removeTypingIndicator() {
            const indicator = document.getElementById('typing-indicator');
            if (indicator) {
                indicator.remove();
            }
        }
        
        // Function to handle user input
        async function handleUserInput() {
            const text = chatInput.value.trim();
            if (text === '') return;
            
            // Add user message
            addMessage(text, true);
            
            // Clear input
            chatInput.value = '';
            
            // Show typing indicator
            showTypingIndicator();
            
            try {
                // Send request to the assistant API
                const response = await fetch('/api/assistant', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: text
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`API error: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Remove typing indicator
                removeTypingIndicator();
                
                // Add assistant response
                addMessage(data.response);
            } catch (error) {
                console.error('Error calling assistant API:', error);
                
                // Remove typing indicator
                removeTypingIndicator();
                
                // Add error message
                addMessage(`Sorry, I encountered an error: ${error.message}. Please try again later.`);
            }
        }
        
        // Event listeners
        sendButton.addEventListener('click', handleUserInput);
        
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleUserInput();
            }
        });
        
        clearButton.addEventListener('click', function() {
            // Clear all messages except the welcome message
            while (chatMessages.children.length > 1) {
                chatMessages.removeChild(chatMessages.lastChild);
            }
            
            // Clear history
            chatHistory.length = 0;
        });
        
        // Add active class to current nav link
        const currentPath = window.location.pathname;
        document.querySelectorAll('.nav-link').forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
        
        // Check API status
        checkApiStatus();
        
        // Function to check if the assistant is using real API or mock responses
        async function checkApiStatus() {
            const apiStatusElement = document.getElementById('api-status');
            if (!apiStatusElement) return;
            
            try {
                // Make a test request to the assistant API
                const response = await fetch('/api/assistant/status', {
                    method: 'GET'
                });
                
                if (!response.ok) {
                    // If we get an error on the status endpoint, try the main assistant endpoint
                    const testResponse = await fetch('/api/assistant', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            message: "API status check"
                        })
                    });
                    
                    if (!testResponse.ok) {
                        throw new Error(`API error: ${testResponse.status}`);
                    }
                    
                    const data = await testResponse.json();
                    
                    // Check if response contains mock data indicators
                    const isMock = detectMockResponse(data.response);
                    
                    if (isMock) {
                        apiStatusElement.textContent = "Using Mock Data";
                        apiStatusElement.className = "api-status mock";
                    } else {
                        apiStatusElement.textContent = "Live API";
                        apiStatusElement.className = "api-status live";
                    }
                } else {
                    // Parse the status response
                    const statusData = await response.json();
                    
                    if (statusData.mode === "mock") {
                        apiStatusElement.textContent = "Using Mock Data";
                        apiStatusElement.className = "api-status mock";
                    } else if (statusData.mode === "live") {
                        apiStatusElement.textContent = "Live API";
                        apiStatusElement.className = "api-status live";
                    } else {
                        apiStatusElement.textContent = "API Status Unknown";
                        apiStatusElement.className = "api-status";
                    }
                }
            } catch (error) {
                console.error('Error checking API status:', error);
                apiStatusElement.textContent = "API Error";
                apiStatusElement.className = "api-status error";
                
                // Since the real API would likely respond properly,
                // an error probably means we're using mock data
                setTimeout(() => {
                    apiStatusElement.textContent = "Using Mock Data";
                    apiStatusElement.className = "api-status mock";
                }, 2000);
            }
        }
        
        // Function to detect if a response is from mock data
        function detectMockResponse(response) {
            // Look for patterns that appear in mock responses
            const mockPatterns = [
                "I've optimized the jetty schedule based on your request",
                "TESTTANKER1",
                "TESTTANKER2",
                "TESTBARGE1"
            ];
            
            return mockPatterns.some(pattern => response.includes(pattern));
        }

        // --- Minimal markdown renderer (safe subset) ---
        function renderMarkdown(md) {
            if (!md) return '';

            // Escape HTML first for safety
            const escapeHtml = (str) => str
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/\"/g, '&quot;')
                .replace(/'/g, '&#39;');

            // Preserve code fences before other transforms
            const fencePattern = /```([a-zA-Z0-9_-]+)?\n([\s\S]*?)```/g;
            let codeBlocks = [];
            md = md.replace(fencePattern, (m, lang, code) => {
                const idx = codeBlocks.length;
                codeBlocks.push({ lang: (lang || '').toLowerCase(), code: escapeHtml(code) });
                return `%%CODEBLOCK${idx}%%`;
            });

            // Headings ###, ##
            md = md.replace(/^###\s+(.*)$/gm, '<h3>$1</h3>');
            md = md.replace(/^##\s+(.*)$/gm, '<h2>$1</h2>');

            // Bold and italics
            md = md.replace(/\*\*(.+?)\*\*/g, '<b>$1</b>');
            md = md.replace(/\*(.+?)\*/g, '<i>$1</i>');

            // Inline code
            md = md.replace(/`([^`]+)`/g, '<code>$1</code>');

            // Links
            md = md.replace(/\[([^\]]+)\]\(([^)\s]+)(?:\s+"([^"]+)")?\)/g,
                (m, text, url) => `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`);

            // Tables (very simple parser: header, divider, rows)
            md = md.replace(/^(\|?.*\|.*)\n\s*\|?\s*-{3,}.*\n([\s\S]*?)(?=\n\n|$)/gm, (m, headerLine, body) => {
                const toCells = (line) => line.trim().replace(/^\|/, '').replace(/\|$/, '').split('|').map(c => c.trim());
                const headers = toCells(headerLine);
                const rows = body.trim().split(/\n+/).map(l => toCells(l));
                let html = '<table><thead><tr>' + headers.map(h => `<th>${h}</th>`).join('') + '</tr></thead><tbody>';
                html += rows.map(r => '<tr>' + r.map(c => `<td>${c}</td>`).join('') + '</tr>').join('');
                html += '</tbody></table>';
                return html;
            });

            // Lists
            // Ordered
            md = md.replace(/(^\d+\.\s.*(?:\n\d+\.\s.*)*)/gm, (m) => {
                const items = m.split(/\n/).map(l => l.replace(/^\d+\.\s/, '').trim()).filter(Boolean);
                return '<ol>' + items.map(i => `<li>${i}</li>`).join('') + '</ol>';
            });
            // Unordered
            md = md.replace(/(^[-*]\s.*(?:\n[-*]\s.*)*)/gm, (m) => {
                const items = m.split(/\n/).map(l => l.replace(/^[-*]\s/, '').trim()).filter(Boolean);
                return '<ul>' + items.map(i => `<li>${i}</li>`).join('') + '</ul>';
            });

            // Paragraphs (lines that are not tags already)
            md = md.split(/\n{2,}/).map(block => {
                if (/^\s*<\/?(h2|h3|ul|ol|table|pre|blockquote)/.test(block)) return block;
                if (block.startsWith('%%CODEBLOCK')) return block; // placeholder
                const trimmed = block.trim();
                if (!trimmed) return '';
                return `<p>${trimmed.replace(/\n/g, '<br>')}</p>`;
            }).join('');

            // Restore code blocks with copy buttons
            codeBlocks.forEach((b, i) => {
                const langLabel = b.lang ? b.lang : 'code';
                const codeHtml = `<div class="code-header"><span>${langLabel}</span><button class="copy-btn" data-copy-ref="code-${i}">Copy</button></div><pre><code id="code-${i}" class="language-${b.lang}">${b.code}</code></pre>`;
                md = md.replace(`%%CODEBLOCK${i}%%`, codeHtml);
            });

            return md;
        }

        function enhanceCodeBlocks(root) {
            root.querySelectorAll('.copy-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const ref = btn.getAttribute('data-copy-ref');
                    const el = root.querySelector(`#${ref}`);
                    if (!el) return;
                    const text = el.textContent || '';
                    navigator.clipboard.writeText(text).then(() => {
                        btn.textContent = 'Copied';
                        setTimeout(() => (btn.textContent = 'Copy'), 1500);
                    }).catch(() => {
                        btn.textContent = 'Failed';
                        setTimeout(() => (btn.textContent = 'Copy'), 1500);
                    });
                });
            });
        }
    });
</script>
{% endblock %}