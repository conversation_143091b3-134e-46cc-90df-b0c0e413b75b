/**
 * Unified Toast Notification System
 * Provides consistent toast messaging across the entire application
 */

class ToastManager {
    constructor() {
        this.container = null;
        this.ensureContainer();
    }

    ensureContainer() {
        if (this.container) return this.container;
        
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container';
            document.body.appendChild(container);
        }
        
        this.container = container;
        return container;
    }

    show(message, type = 'info', options = {}) {
        const {
            timeout = 5000,
            closable = true,
            persistent = false
        } = options;

        this.ensureContainer();

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.setAttribute('role', 'status');
        toast.setAttribute('aria-live', 'polite');

        // Create icon
        const icon = document.createElement('div');
        icon.className = 'toast-icon';
        
        const iconClass = this.getIconClass(type);
        icon.innerHTML = `<i class="fas ${iconClass}"></i>`;

        // Create message
        const messageElement = document.createElement('div');
        messageElement.className = 'toast-message';
        messageElement.textContent = message;

        // Create close button (if closable)
        let closeButton = null;
        if (closable) {
            closeButton = document.createElement('button');
            closeButton.className = 'toast-close';
            closeButton.innerHTML = '<i class="fas fa-times"></i>';
            closeButton.setAttribute('aria-label', 'Close notification');
            closeButton.addEventListener('click', () => this.remove(toast));
        }

        // Assemble toast
        toast.appendChild(icon);
        toast.appendChild(messageElement);
        if (closeButton) {
            toast.appendChild(closeButton);
        }

        // Add to container
        this.container.appendChild(toast);

        // Animate in
        requestAnimationFrame(() => {
            toast.classList.add('show');
        });

        // Auto-remove (if not persistent)
        if (!persistent && timeout > 0) {
            setTimeout(() => {
                this.remove(toast);
            }, timeout);
        }

        return toast;
    }

    remove(toast) {
        if (!toast || !toast.parentNode) return;

        toast.classList.add('fade-out');
        toast.classList.remove('show');
        
        // Remove after animation
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    getIconClass(type) {
        switch (type) {
            case 'success':
                return 'fa-check-circle';
            case 'error':
                return 'fa-exclamation-triangle';
            case 'warning':
                return 'fa-exclamation-circle';
            case 'info':
            default:
                return 'fa-info-circle';
        }
    }

    // Convenience methods
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    error(message, options = {}) {
        return this.show(message, 'error', options);
    }

    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    info(message, options = {}) {
        return this.show(message, 'info', options);
    }

    // Clear all toasts
    clear() {
        if (this.container) {
            const toasts = this.container.querySelectorAll('.toast');
            toasts.forEach(toast => this.remove(toast));
        }
    }
}

// Create global instance
const toast = new ToastManager();

// Global convenience functions for backwards compatibility
window.showToast = (message, type = 'info', options = {}) => toast.show(message, type, options);
window.showSuccessToast = (message, options = {}) => toast.success(message, options);
window.showErrorToast = (message, options = {}) => toast.error(message, options);
window.showWarningToast = (message, options = {}) => toast.warning(message, options);
window.showInfoToast = (message, options = {}) => toast.info(message, options);

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ToastManager, toast };
}
