<!-- Status Legend Component -->
<div class="status-legend-container">
  <div class="status-legend-toggle">
    <i class="fas fa-info-circle"></i> Status Legend
  </div>
  <div class="status-legend">
    <div class="legend-section">
      <h4>Vessel Status</h4>
      <div class="legend-items">
        <div class="legend-item">
          <span class="status-badge status-scheduled">Scheduled</span>
          <span class="legend-description">Vessel is scheduled but not yet arrived</span>
        </div>
        <div class="legend-item">
          <span class="status-badge status-approaching">Approaching</span>
          <span class="legend-description">Vessel is en route to the terminal</span>
        </div>
        <div class="legend-item">
          <span class="status-badge status-waiting">Waiting</span>
          <span class="legend-description">Vessel has arrived but is waiting for berth</span>
        </div>
        <div class="legend-item">
          <span class="status-badge status-berthed">Berthed</span>
          <span class="legend-description">Vessel is at jetty but operation not started</span>
        </div>
        <div class="legend-item">
          <span class="status-badge status-loading">Loading</span>
          <span class="legend-description">Cargo is being loaded onto vessel</span>
        </div>
        <div class="legend-item">
          <span class="status-badge status-unloading">Unloading</span>
          <span class="legend-description">Cargo is being unloaded from vessel</span>
        </div>
        <div class="legend-item">
          <span class="status-badge status-completed">Completed</span>
          <span class="legend-description">All operations have been completed</span>
        </div>
        <div class="legend-item">
          <span class="status-badge status-departed">Departed</span>
          <span class="legend-description">Vessel has left the terminal</span>
        </div>
      </div>
    </div>
    
    <div class="legend-section">
      <h4>Assignment Status</h4>
      <div class="legend-items">
        <div class="legend-item">
          <span class="status-badge status-planned">Planned</span>
          <span class="legend-description">Assignment is scheduled but not started</span>
        </div>
        <div class="legend-item">
          <span class="status-badge status-active">Active</span>
          <span class="legend-description">Assignment is currently in progress</span>
        </div>
        <div class="legend-item">
          <span class="status-badge status-completed">Completed</span>
          <span class="legend-description">Assignment has been completed</span>
        </div>
        <div class="legend-item">
          <span class="status-badge status-cancelled">Cancelled</span>
          <span class="legend-description">Assignment has been cancelled</span>
        </div>
      </div>
    </div>
    
    <div class="legend-section">
      <h4>Scheduling State</h4>
      <div class="legend-items">
        <div class="legend-item">
          <span class="status-badge">Standard</span>
          <span class="legend-description">Vessel has assignments</span>
        </div>
        <div class="legend-item">
          <span class="status-badge unscheduled">Unscheduled</span>
          <span class="legend-description">Vessel has no assignments</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Status Legend Toggle Script -->
<script nonce="{{ nonce }}">
  document.addEventListener('DOMContentLoaded', function() {
    const legendToggle = document.querySelector('.status-legend-toggle');
    const legend = document.querySelector('.status-legend');
    
    // Check if user preference for legend visibility is stored
    const legendVisible = localStorage.getItem('statusLegendVisible') === 'true';
    
    // Set initial state based on user preference or default to hidden
    if (legendVisible) {
      legend.classList.add('visible');
    }
    
    // Toggle legend visibility
    legendToggle.addEventListener('click', function() {
      legend.classList.toggle('visible');
      // Store user preference
      localStorage.setItem('statusLegendVisible', legend.classList.contains('visible'));
    });
  });
</script> 