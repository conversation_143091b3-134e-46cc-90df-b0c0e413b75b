#!/bin/sh

# Simple PostgreSQL backup script
# Runs continuously and creates backups on schedule

DB_HOST="postgres"
DB_PORT="5432"
DB_NAME="planner"
DB_USER="postgres"

echo "PostgreSQL Backup Service Starting..."
echo "Target: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"

# Function to create backup
create_backup() {
    TIMESTAMP=$(date +%Y-%m-%d_%H-%M-%S)
    BACKUP_FILE="jetty_planner_backup_${TIMESTAMP}.sql"
    
    echo "Creating backup: $BACKUP_FILE"
    
    pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
            --verbose --clean --if-exists --create \
            --format=plain --encoding=UTF8 \
            --file="/backups/$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        echo "✓ Backup created successfully: $BACKUP_FILE"
        gzip "/backups/$BACKUP_FILE"
        echo "✓ Backup compressed: ${BACKUP_FILE}.gz"
    else
        echo "✗ Backup failed!"
        return 1
    fi
}

# Function to cleanup old backups
cleanup_old_backups() {
    echo "Cleaning up backups older than 30 days..."
    find /backups -name "*.sql.gz" -type f -mtime +30 -delete
    echo "✓ Cleanup completed"
}

# Test database connection
echo "Testing database connection..."
pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER"

if [ $? -eq 0 ]; then
    echo "✓ Database connection successful"
    
    # Create initial backup
    create_backup
    
    # Run scheduled backups (every 24 hours)
    while true; do
        echo "Waiting for next backup cycle (24 hours)..."
        sleep 86400  # 24 hours
        
        echo "Starting scheduled backup..."
        create_backup
        cleanup_old_backups
    done
else
    echo "✗ Database connection failed!"
    echo "Retrying in 60 seconds..."
    sleep 60
    exec "$0" "$@"
fi