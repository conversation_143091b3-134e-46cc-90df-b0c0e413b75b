"""
Tests for Assignment Status Job

Test the automatic assignment status transition functionality.
"""

import unittest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone, timedelta

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.services.assignment_status_job import AssignmentStatusJob
from src.utils.status_utils import is_valid_assignment_transition


class TestAssignmentStatusJob(unittest.TestCase):
    """Test assignment status job functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_db = Mock()
        self.job = AssignmentStatusJob(self.mock_db)
        
        # Mock environment variables to enable the job
        with patch.dict(os.environ, {'ENABLE_STATUS_CRON': 'true'}):
            self.job = AssignmentStatusJob(self.mock_db)
    
    def test_status_transitions_validation(self):
        """Test that status transitions work correctly"""
        # Test valid transitions for job scenarios
        valid, msg = is_valid_assignment_transition('SCHEDULED', 'IN_PROGRESS')
        self.assertTrue(valid, f"SCHEDULED -> IN_PROGRESS should be valid: {msg}")
        
        valid, msg = is_valid_assignment_transition('APPROVED', 'IN_PROGRESS')
        self.assertTrue(valid, f"APPROVED -> IN_PROGRESS should be valid: {msg}")
        
        valid, msg = is_valid_assignment_transition('PENDING_APPROVAL', 'IN_PROGRESS')
        self.assertTrue(valid, f"PENDING_APPROVAL -> IN_PROGRESS should be valid: {msg}")
        
        valid, msg = is_valid_assignment_transition('IN_PROGRESS', 'COMPLETED')
        self.assertTrue(valid, f"IN_PROGRESS -> COMPLETED should be valid: {msg}")
        
        valid, msg = is_valid_assignment_transition('APPROVED', 'COMPLETED')
        self.assertTrue(valid, f"APPROVED -> COMPLETED should be valid: {msg}")
        
        valid, msg = is_valid_assignment_transition('SCHEDULED', 'COMPLETED')
        self.assertTrue(valid, f"SCHEDULED -> COMPLETED should be valid: {msg}")
    
    async def test_transition_to_in_progress(self):
        """Test transition to IN_PROGRESS logic"""
        now = datetime.now(timezone.utc)
        
        # Mock assignment that should transition to IN_PROGRESS
        mock_assignment = {
            'id': 1,
            'status': 'SCHEDULED',
            'vessel_id': 'TEST001',
            'vessel_name': 'Test Vessel',
            'jetty_name': 'Jetty A',
            'start_time': now - timedelta(minutes=30),
            'end_time': now + timedelta(hours=2)
        }
        
        # Mock database responses
        self.mock_db.find_assignments_for_status_transition.return_value = [mock_assignment]
        self.mock_db.update_assignment.return_value = True
        self.mock_db.log_assignment_change.return_value = 123
        
        # Run the transition
        stats = await self.job._transition_to_in_progress(now)
        
        # Verify results
        self.assertEqual(stats['attempted'], 1)
        self.assertEqual(stats['transitioned_to_in_progress'], 1)
        self.assertEqual(stats['skipped_invalid'], 0)
        self.assertEqual(stats['errors'], 0)
        
        # Verify database calls
        self.mock_db.find_assignments_for_status_transition.assert_called_once_with(
            statuses=['SCHEDULED', 'APPROVED', 'PENDING_APPROVAL'],
            where_start_le=now,
            where_end_gt=now,
            limit=100
        )
        self.mock_db.update_assignment.assert_called_once_with(1, {'status': 'IN_PROGRESS'})
        self.mock_db.log_assignment_change.assert_called_once()
    
    async def test_transition_to_completed(self):
        """Test transition to COMPLETED logic"""
        now = datetime.now(timezone.utc)
        
        # Mock assignment that should transition to COMPLETED
        mock_assignment = {
            'id': 2,
            'status': 'IN_PROGRESS',
            'vessel_id': 'TEST002',
            'vessel_name': 'Test Vessel 2',
            'jetty_name': 'Jetty B',
            'start_time': now - timedelta(hours=3),
            'end_time': now - timedelta(minutes=30)
        }
        
        # Mock database responses
        self.mock_db.find_assignments_for_status_transition.return_value = [mock_assignment]
        self.mock_db.update_assignment.return_value = True
        self.mock_db.log_assignment_change.return_value = 124
        
        # Run the transition
        stats = await self.job._transition_to_completed(now)
        
        # Verify results
        self.assertEqual(stats['attempted'], 1)
        self.assertEqual(stats['transitioned_to_completed'], 1)
        self.assertEqual(stats['skipped_invalid'], 0)
        self.assertEqual(stats['errors'], 0)
        
        # Verify database calls
        self.mock_db.find_assignments_for_status_transition.assert_called_once_with(
            statuses=['IN_PROGRESS', 'APPROVED', 'SCHEDULED'],
            where_end_le=now,
            limit=100
        )
        self.mock_db.update_assignment.assert_called_once_with(2, {'status': 'COMPLETED'})
        self.mock_db.log_assignment_change.assert_called_once()
    
    async def test_full_job_run_with_postgres_lock(self):
        """Test full job run with PostgreSQL advisory lock"""
        # Mock PostgreSQL database
        self.mock_db.is_using_postgres.return_value = True
        self.mock_db.acquire_advisory_lock.return_value = True
        self.mock_db.release_advisory_lock.return_value = True
        
        # Mock no assignments to transition
        self.mock_db.find_assignments_for_status_transition.return_value = []
        
        # Run the job
        result = await self.job.run_transition_job()
        
        # Verify lock operations
        self.mock_db.acquire_advisory_lock.assert_called_once()
        self.mock_db.release_advisory_lock.assert_called_once()
        
        # Verify result
        self.assertEqual(result['status'], 'completed')
        self.assertIn('duration_seconds', result)
        self.assertIn('stats', result)
    
    async def test_job_run_skipped_when_lock_not_acquired(self):
        """Test job run is skipped when advisory lock cannot be acquired"""
        # Mock PostgreSQL database with lock not available
        self.mock_db.is_using_postgres.return_value = True
        self.mock_db.acquire_advisory_lock.return_value = False
        
        # Run the job
        result = await self.job.run_transition_job()
        
        # Verify result
        self.assertEqual(result['status'], 'skipped')
        self.assertEqual(result['reason'], 'lock_not_acquired')
        
        # Verify lock was attempted but not released (since not acquired)
        self.mock_db.acquire_advisory_lock.assert_called_once()
        self.mock_db.release_advisory_lock.assert_not_called()
    
    def test_job_disabled_by_config(self):
        """Test job respects enabled/disabled configuration"""
        with patch.dict(os.environ, {'ENABLE_STATUS_CRON': 'false'}):
            disabled_job = AssignmentStatusJob(self.mock_db)
            self.assertFalse(disabled_job.enabled)
    
    def test_get_job_status(self):
        """Test job status reporting"""
        status = self.job.get_job_status()
        
        self.assertIn('enabled', status)
        self.assertIn('batch_size', status)
        self.assertIn('lock_key', status)
        self.assertIn('last_run_time', status)
        self.assertIn('last_run_duration_seconds', status)
        self.assertIn('last_run_stats', status)


if __name__ == '__main__':
    import asyncio
    
    # Run async tests
    async def run_async_tests():
        suite = unittest.TestSuite()
        loader = unittest.TestLoader()
        
        # Load all test methods
        test_case = TestAssignmentStatusJob()
        test_case.setUp()
        
        # Run each async test
        await test_case.test_transition_to_in_progress()
        await test_case.test_transition_to_completed()
        await test_case.test_full_job_run_with_postgres_lock()
        await test_case.test_job_run_skipped_when_lock_not_acquired()
        
        print("All async tests passed!")
    
    # Run sync tests
    unittest.main(verbosity=2, exit=False)
    
    # Run async tests
    asyncio.run(run_async_tests())
