# Missing Feature Handler - Integration Guide

This guide explains how to use the `MissingFeatureHandler` class in external programs to handle missing features when using exported ML models from the Pump Time Model system.

## Overview

The `MissingFeatureHandler` provides a standalone solution for:
- Loading model metadata from exported models
- Handling missing features with intelligent defaults
- Validating critical business features
- Applying preprocessing steps consistently

## Quick Start

```python
from utils.missing_feature_handler import MissingFeatureHandler
import joblib

# Load your exported model
model_package = joblib.load('path/to/your/model.joblib')
model = model_package['model']

# Initialize the handler
handler = MissingFeatureHandler()

# Load model metadata (feature requirements, preprocessing info)
handler.load_model_metadata('path/to/your/model.joblib')

# Prepare input data with missing feature handling
input_data = {
    'operation_type': 'Loading',
    'product_type': 'Gasoline',
    'product_quantity': 15000,
    'vessel_type': 'Vessel'
    # Note: missing features like 'is_weekend', 'hour_of_day' etc. will be handled automatically
}

processed_df = handler.prepare_input_data(input_data)
X_processed = handler.apply_preprocessing(processed_df)

# Make prediction
prediction = model.predict(X_processed)[0]
print(f"Predicted pump time: {prediction:.1f} minutes")
```

## Detailed Usage

### 1. Basic Setup

```python
from utils.missing_feature_handler import MissingFeatureHandler
import logging

# Optional: Setup custom logger
logger = logging.getLogger('my_app')
handler = MissingFeatureHandler(logger=logger)

# Load model metadata
try:
    metadata = handler.load_model_metadata('model.joblib')
    print(f"Loaded model: {metadata.get('algorithm')}")
    print(f"Required features: {len(handler.feature_columns)}")
except ValueError as e:
    print(f"Error loading model: {e}")
```

### 2. Feature Information

```python
# Get information about required features
feature_info = handler.get_feature_info()
print("Feature Requirements:")
print(f"- Total features: {feature_info['feature_count']}")
print(f"- Critical features: {feature_info['critical_features']}")
print(f"- Categorical features: {feature_info['categorical_features']}")
print(f"- Scaled features: {feature_info['scaled_features']}")
```

### 3. Input Validation

```python
# Validate critical features before processing
input_data = {
    'operation_type': 'Loading',
    'product_quantity': 15000
    # Missing: product_type, vessel_type (critical features)
}

validation = handler.validate_critical_features(input_data)
if not validation['is_valid']:
    print("Validation errors:")
    for error in validation['errors']:
        print(f"- {error}")
    # Handle errors before proceeding
else:
    print("Input validation passed")
```

### 4. Batch Processing

```python
# Process multiple predictions
batch_inputs = [
    {'operation_type': 'Loading', 'product_type': 'Gasoline', 'product_quantity': 15000, 'vessel_type': 'Vessel'},
    {'operation_type': 'Discharging', 'product_type': 'Diesel', 'product_quantity': 20000, 'vessel_type': 'Barge'},
    # ... more inputs
]

results = []
for input_data in batch_inputs:
    try:
        processed_df = handler.prepare_input_data(input_data)
        X_processed = handler.apply_preprocessing(processed_df)
        prediction = model.predict(X_processed)[0]
        
        results.append({
            'input': input_data,
            'prediction': float(prediction),
            'status': 'success'
        })
    except Exception as e:
        results.append({
            'input': input_data,
            'error': str(e),
            'status': 'failed'
        })

print(f"Processed {len(results)} predictions")
```

### 5. Convenience Function

For simple use cases, use the convenience function:

```python
from utils.missing_feature_handler import load_and_predict

# One-line prediction with full missing feature handling
result = load_and_predict(
    model_path='path/to/model.joblib',
    input_data={
        'operation_type': 'Loading',
        'product_type': 'Gasoline',
        'product_quantity': 15000,
        'vessel_type': 'Vessel'
    }
)

print(f"Prediction: {result['prediction']:.1f} minutes")
print(f"Model used: {result['model_used']}")
print(f"Features processed: {result['processed_features']}")
```

## Feature Defaults

The handler applies intelligent defaults for missing features:

### Critical Features (Must be provided)
- `operation_type`: Loading/Discharging
- `product_type`: Type of product being handled
- `product_quantity`: Amount in tons
- `vessel_type`: Vessel/Barge/etc.

### Temporal Features (Auto-defaulted)
- `day_of_week`: 1 (Monday)
- `hour_of_day`: 12 (Noon)
- `month`: 1 (January)
- `is_weekend`: 0 (Weekday)
- `is_business_hours`: 1 (Business hours)

### Other Features (Auto-defaulted)
- `vessel_name`: 'Unknown'
- `customer_name`: 'Unknown'
- `location`: 'Unknown'
- `berth_number`: 1

### Smart Defaults
- **Product Category**: Inferred from product_type (gasoline → 'gasoline', diesel → 'diesel', etc.)
- **Time-related features**: Default to 0
- **Numerical features**: Default to 0
- **Categorical features**: Default to 'Unknown' or first known category

## Error Handling

```python
try:
    # Load model metadata
    handler.load_model_metadata('model.joblib')
    
    # Prepare input (may raise ValueError for missing critical features)
    processed_df = handler.prepare_input_data(input_data)
    
    # Apply preprocessing (may raise ValueError for missing required columns)
    X_processed = handler.apply_preprocessing(processed_df)
    
    # Make prediction
    prediction = model.predict(X_processed)[0]
    
except ValueError as e:
    print(f"Input validation error: {e}")
except FileNotFoundError as e:
    print(f"Model file not found: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Integration Examples

### Web API Integration

```python
from flask import Flask, request, jsonify
from utils.missing_feature_handler import MissingFeatureHandler
import joblib

app = Flask(__name__)

# Initialize once at startup
handler = MissingFeatureHandler()
handler.load_model_metadata('models/production_model.joblib')
model = joblib.load('models/production_model.joblib')['model']

@app.route('/predict', methods=['POST'])
def predict():
    try:
        input_data = request.json
        
        # Validate and process input
        processed_df = handler.prepare_input_data(input_data)
        X_processed = handler.apply_preprocessing(processed_df)
        
        # Make prediction
        prediction = model.predict(X_processed)[0]
        
        return jsonify({
            'prediction': float(prediction),
            'status': 'success',
            'input_features': input_data
        })
    
    except ValueError as e:
        return jsonify({
            'error': str(e),
            'status': 'validation_failed'
        }), 400
    except Exception as e:
        return jsonify({
            'error': 'Internal server error',
            'status': 'error'
        }), 500
```

### Scheduled Batch Processing

```python
import pandas as pd
from utils.missing_feature_handler import MissingFeatureHandler
import joblib

def process_daily_predictions(input_file: str, output_file: str):
    # Load model and handler
    handler = MissingFeatureHandler()
    handler.load_model_metadata('models/daily_model.joblib')
    model = joblib.load('models/daily_model.joblib')['model']
    
    # Load input data
    df = pd.read_csv(input_file)
    results = []
    
    # Process each row
    for _, row in df.iterrows():
        try:
            input_data = row.to_dict()
            processed_df = handler.prepare_input_data(input_data, validate_critical=False)
            X_processed = handler.apply_preprocessing(processed_df)
            prediction = model.predict(X_processed)[0]
            
            results.append({
                **input_data,
                'predicted_pump_time': prediction,
                'status': 'success'
            })
        except Exception as e:
            results.append({
                **input_data,
                'predicted_pump_time': None,
                'status': f'error: {str(e)}'
            })
    
    # Save results
    results_df = pd.DataFrame(results)
    results_df.to_csv(output_file, index=False)
    print(f"Processed {len(results)} predictions to {output_file}")

# Run daily processing
process_daily_predictions('daily_inputs.csv', 'daily_predictions.csv')
```

## Best Practices

1. **Load model metadata once**: Load model metadata during initialization, not for each prediction
2. **Validate critical features**: Always validate that critical business features are provided
3. **Handle errors gracefully**: Wrap prediction calls in try-catch blocks
4. **Log missing features**: Monitor which features are frequently missing to improve data collection
5. **Test with edge cases**: Test with minimal input data to ensure defaults work correctly
6. **Version compatibility**: Ensure the handler version matches your model export version

## Troubleshooting

### Common Issues

**Issue**: `ValueError: Critical feature 'product_quantity' is missing`
**Solution**: Ensure all critical features are provided in input_data

**Issue**: `ValueError: Missing required feature columns: ['some_feature']`
**Solution**: Check if model metadata loaded correctly and all required features are available

**Issue**: Model predictions seem inaccurate
**Solution**: Check logs for which features were defaulted - too many defaults may affect accuracy

### Debug Mode

```python
import logging
logging.basicConfig(level=logging.DEBUG)

handler = MissingFeatureHandler()
# Will show detailed logs of default application and preprocessing steps
```

This handler provides a complete solution for using exported models in external systems while maintaining the same missing feature handling logic as the main application.