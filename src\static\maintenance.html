<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evos - Under Maintenance</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Blinker:wght@300;400;600&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .maintenance-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem 2rem;
            max-width: 600px;
            margin: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            font-family: 'Blinker', sans-serif;
            font-size: 3rem;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .maintenance-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        h1 {
            font-family: 'Blinker', sans-serif;
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2c3e50;
        }
        
        p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            color: #555;
        }
        
        .status-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            border-left: 4px solid #667eea;
        }
        
        .status-info h3 {
            font-family: 'Blinker', sans-serif;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .contact-info {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #eee;
        }
        
        .contact-info p {
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }
        
        .refresh-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }
        
        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .estimated-time {
            font-weight: 500;
            color: #667eea;
        }
        
        @media (max-width: 768px) {
            .maintenance-container {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }
            
            .logo {
                font-size: 2.5rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            p {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="logo">Evos</div>
        <div class="maintenance-icon">🔧</div>
        
        <h1>Under Maintenance</h1>
        <p>We're currently performing scheduled maintenance to improve your experience.</p>
        
        <div class="status-info">
            <h3>What's happening?</h3>
            <p>Our team is working on system updates and improvements. The Jetty Planner application will be back online shortly.</p>
            <p class="estimated-time">Estimated completion: Usually within 15-30 minutes</p>
        </div>
        
        <p>Thank you for your patience while we make things better for you.</p>
        
        <button class="refresh-btn" onclick="location.reload()">Check Again</button>
        
        <div class="contact-info">
            <p><strong>Need immediate assistance?</strong></p>
            <p>Please contact our support team if this is urgent.</p>
        </div>
    </div>
    
    <script>
        // Auto-refresh every 2 minutes to check if maintenance is complete
        setTimeout(function() {
            location.reload();
        }, 120000);
    </script>
</body>
</html>
