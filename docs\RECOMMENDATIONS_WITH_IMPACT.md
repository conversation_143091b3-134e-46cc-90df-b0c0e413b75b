# Jetty Planner — Recommendations and Impact Analysis

This document summarizes recommended improvements, their rationale, and expected consequences. It reflects your deployment via Nginx + Cloudflare Tunnel (Zero Trust), Dockerized Postgres, and your goal to enable terminal planners to experiment with and trust an OR-Tools–driven schedule.

## Goals Recap

- Empower planners to optimally schedule vessels, iterate on scenarios, and understand solver decisions.
- Keep the plan of record in the system; enable adding nominations and re-optimizing quickly.
- Provide explainability for decisions (to users and customers).
- Allow colleagues to test functionality, find gaps, and improve iteratively.

---

## 1) Database Port Consistency (Docker mapping 7432:5432)

- What you have:
  - Docker mapping: `7432:5432` (host:container). Inside the Postgres container the server listens on `5432`.
  - `.env` uses `DB_PORT=5432` (standard Postgres port).
  - `run.py` logs mention `4432` in advisory output (artifact).

- Recommendation:
  - Define which context your app runs in:
    - Inside the same Docker network (e.g., app container) → connect to service name (e.g., `postgres`) on port `5432`.
    - On the host (outside Docker) → connect to `localhost:7432` (the published host port).
  - Keep `.env` values environment-specific:
    - For containerized app: `DB_HOST=postgres`, `DB_PORT=5432`.
    - For host-run app: `DB_HOST=127.0.0.1`, `DB_PORT=7432`.
  - Fix the advisory messages in `run.py` to avoid `4432` and explain the mapping.

- Expected consequences:
  - Eliminates confusion and failed connections due to mismatched ports.
  - No functional change to the app itself; only clearer configuration and docs.
  - If you switch between host-run and container-run, you’ll only need to flip `DB_HOST/DB_PORT` appropriately.

---

## 2) Background Scheduler (APScheduler) — Single Instance vs. Multi-Instance

- What you have:
  - APScheduler started in `startup_event` (in-process). If you scale to multiple Uvicorn workers or multiple app replicas, you may get duplicate job runs.

- Options:
  1. Single-instance only (keep as-is, ensure one worker)
     - How: Run a single Uvicorn worker (e.g., `--workers 1`), one app replica.
     - Impact:
       - Simple, zero code changes.
       - No duplicate jobs.
       - Limits horizontal scaling of the API process.
  2. Dedicated scheduler process/container (recommended for clarity)
     - How:
       - Add an env flag, e.g., `SCHEDULER_ENABLED=true` and change the app to only start APScheduler when this flag is set.
       - Run a separate lightweight container/command for the scheduler (same image) with `SCHEDULER_ENABLED=true` and API containers with `SCHEDULER_ENABLED=false`.
     - Impact:
       - Prevents duplicate runs across API replicas.
       - Clear separation of concerns; easier to operate.
       - Slight ops complexity (one more service).
       - If the flag is misconfigured (disabled everywhere), no status transitions will run — add monitoring/alerts.
  3. Leader election via Postgres advisory lock (advanced)
     - How:
       - Before starting APScheduler, acquire a Postgres advisory lock (e.g., `pg_try_advisory_lock(bigint)`); if lock acquired, start; otherwise, skip.
       - Optionally use `SQLAlchemyJobStore` to centralize job definitions.
     - Impact:
       - Supports multiple replicas safely without a separate scheduler service.
       - Slight code complexity and DB dependency for leadership.

- Minimal code change sketch (Option 2):
  - In `startup_event`, wrap scheduler start:
    - `if os.getenv('SCHEDULER_ENABLED', 'false').lower() in ('1','true','yes'):` then start; else skip.
  - In Compose: add a `scheduler` service with the same image and `SCHEDULER_ENABLED=true`.

- User-facing impact:
  - None in UI/UX. Backend reliability improves (no double-processing). If misconfigured (scheduler off), automatic status transitions won’t run; manual actions still work.

---

## 3) Global State Management (`global_state`)

- What you have:
  - `global_state` dict for terminal, vessels, schedule, clients. This is per-process memory. With multiple workers/replicas, states may diverge.

- Recommendations (staged):
  1. Short-term hardening
     - Treat all “plan of record” data as DB-sourced; ensure `get_data()` pulls from DB when needed and does not rely on stale memory.
     - Keep long-lived clients (AIS, weather) in memory — that’s fine.
     - Document single-worker expectation if you do nothing else.
  2. Medium-term: DB-backed state + lightweight cache
     - Move any mutable state (e.g., current schedule, nominations) out of in-memory dicts into DB tables.
     - Use an in-memory cache (per-process) with TTL for expensive reads; invalidate on writes.
     - Impact: consistent behavior across workers/replicas; small refactor in `get_data()` and services.
  3. Long-term: External cache (Redis)
     - Introduce Redis for low-latency shared caches, locks, and pub/sub invalidation.
     - Impact: best consistency across replicas; adds infra component.

- Expected consequences:
  - Short-term: No user-visible changes. Reduced risk of “it works on one pod, not on another”.
  - Medium-term: Small performance cost per request (more DB reads), offset by caching; major consistency and debuggability gains.
  - Long-term: Operationally stronger for scale-out; complexity increase is justified if/when you scale replicas.

---

## 4) CORS and Host Binding (Nginx + Cloudflare)

- What you have:
  - `allow_origins=['*']`, `allow_credentials=True`; `.env` has `API_HOST=127.0.0.1` with `PRODUCTION=true`.

- Recommendations:
  - Bind to `0.0.0.0` in production (app remains accessible only through Nginx). Ensure Uvicorn runs with `--proxy-headers` so client IPs are correct.
  - Set explicit CORS origins to `https://planner.evosgpt.eu` (and any internal admin domain). Keep `allow_credentials=False` unless you need cross-site cookies.
  - Ensure Nginx forwards `X-Forwarded-Proto`, `X-Real-IP`, upgrades for WebSockets (AIS/streaming).

- Expected consequences:
  - Improved security posture. If you forget to include a dev origin, local testing of the web UI might fail CORS (add `http://localhost:7000` etc. in dev).

---

## 5) Secrets Management and Rotation

- What you have:
  - Real-looking API keys and DB credentials in `.env`.

- Recommendations:
  - Rotate exposed credentials immediately.
  - Move secrets to environment-specific files not committed (e.g., `.env.local`, Docker secrets, or a vault). Keep `.env.example` sanitized.
  - Audit logs for misuse where applicable (Cloudflare/API provider dashboards).

- Expected consequences:
  - No user-facing change. Strongly reduces risk. Slight ops overhead for managing secrets.

---

## 6) Gantt UI as Primary Planning View

- What you have:
  - `schedule.html` Gantt with rich styling. A picture of the current overview is in `/data/`.

- Recommendations:
  - Make the Gantt the default landing page (or add a prominent link/button from the dashboard) to replace the picture-based workflow.
  - Add “scenario presets” (already partially supported): users can load presets and re-run optimization quickly.
  - Add “save scenario” and “compare scenarios” (diff view of assignments and throughput/demurrage measures).

- Expected consequences:
  - Big UX win for planners; immediate value. Minimal risk; just routing/menu tweaks and persistence for scenarios.

---

## 7) Explainability and Reporting

- Recommendations:
  - Produce an “Optimization Report” artifact per run:
    - Inputs: vessels, constraints toggles, weights.
    - Outputs: objective value components, constraint violations resolved, conflicts, and locked items preserved.
    - Store in DB; expose in UI under Analytics/Logs.
  - Add per-assignment notes and rationale fields (e.g., “assigned to Jetty 3 due to draft constraint and flow rate”); show on hover in Gantt.

- Expected consequences:
  - Builds user and customer trust; aids debugging. Moderate effort; no adverse impact to existing flows.

---

## 8) Observability

- Recommendations:
  - Structured logs with request IDs; propagate through background jobs.
  - Expose health endpoints (`/health`, `/ready`) and add Nginx/Cloudflare probes.
  - Metrics: count assignments created/updated, solver runs, job success/failure, AIS connectivity. Consider Prometheus-compatible endpoints.

- Expected consequences:
  - Easier troubleshooting and SLA tracking. Minimal runtime overhead.

---

## 9) Testing Enhancements

- Recommendations:
  - Add API contract tests for assignment endpoints and terminal endpoints.
  - Add an integration test to simulate the scheduler job once (guarded to not require real APScheduler timer).
  - Ensure migration tests validate Alembic heads.

- Expected consequences:
  - Higher confidence during iteration. Slight CI runtime increase.

---

## Concrete Next Steps (Low-Risk, High-Value)

1. Clarify DB connection guidance and fix `run.py` advisory messages; document host vs. container ports.
2. Gate APScheduler behind `SCHEDULER_ENABLED` and run a dedicated scheduler container.
3. Lock down CORS to `https://planner.evosgpt.eu` and bind `0.0.0.0` in prod.
4. Rotate secrets; move real values out of `.env` (keep `.env.example`).
5. Make Gantt the default landing page and add a “Save Scenario” prototype.
6. Add an “Optimization Report” persisted record after each run and link it from the Analytics page.

These steps will not break user flows; they reduce operational risk and improve planner experience.

---

## Notes on Cloudflare Tunnel + Zero Trust

- Ensure WebSocket upgrade headers are preserved (AIS and any RT features):
  - Nginx: `proxy_set_header Upgrade $http_upgrade;` and `proxy_set_header Connection "upgrade";`.
- If using Cloudflare Access, validate required auth headers/tokens at Nginx and avoid trusting client-provided identity headers directly.
- Consider enabling HSTS and ensuring `X-Forwarded-Proto` is honored so app generates `https` URLs where relevant.

---

If you want, I can patch:
- `run.py` messages for DB port clarity,
- `fastapi_app.py` to conditionally start APScheduler via `SCHEDULER_ENABLED`, and
- CORS origins narrowed for production.

Let me know your deployment constraints (single vs. multiple API replicas, and whether the app runs inside Docker alongside Postgres) and I will implement the corresponding patch set.

