# PostgreSQL Backup System for Jetty Planner

## Overview

The Jetty Planner backup system provides automated, reliable PostgreSQL backups with intelligent retention policies, monitoring, and easy restore procedures.

## Features

- **Automated Daily Backups** at 2:00 AM
- **Intelligent Retention**: Daily (30 days), Weekly (8 weeks), Monthly (6 months)
- **Compression**: Backups are compressed to save space
- **Web Interface**: Browse and download backups via web browser
- **Health Monitoring**: Automated backup validation and alerting
- **Easy Restore**: Simple scripts for database restoration

## Quick Start

### 1. Start the Backup System

```powershell
# Start backup services
docker compose -f docker-compose.backup.yml up -d

# Verify services are running
docker ps --filter "name=jetty"
```

### 2. Create Manual Backup

```powershell
# Using PowerShell script (recommended)
.\scripts\backup-restore.ps1 -Action backup

# Or using Docker directly
docker exec jetty-postgres-backup backup
```

### 3. Browse Backups

Visit http://localhost:8081 to:
- View backup status
- Browse available backup files
- Download backup files

## Backup Schedule

| Type | Frequency | Retention | Purpose |
|------|-----------|-----------|---------|
| **Daily** | 2:00 AM | 30 days | Recent changes and quick recovery |
| **Weekly** | Sundays | 8 weeks | Medium-term recovery points |
| **Monthly** | 1st of month | 6 months | Long-term archival |

## Manual Operations

### Create Immediate Backup

```powershell
# Using PowerShell script
.\scripts\backup-restore.ps1 -Action backup

# Using Docker command
docker exec jetty-postgres-backup backup
```

### List Available Backups

```powershell
.\scripts\backup-restore.ps1 -Action list
```

### Restore Database

```powershell
# Restore from specific backup file
.\scripts\backup-restore.ps1 -Action restore -BackupFile "jetty_planner_backup_2024-01-15_14-30-00.sql"
```

**⚠️ WARNING**: Restore operations will overwrite the current database!

### Manual Cleanup

```powershell
# Clean up old backups (dry run)
.\scripts\backup-cleanup.ps1 -DryRun

# Actually perform cleanup
.\scripts\backup-cleanup.ps1
```

## Monitoring and Health Checks

### Check Backup Health

```powershell
# Monitor backup status
.\scripts\backup-monitor.ps1

# Verbose monitoring
.\scripts\backup-monitor.ps1 -Verbose
```

### Configure Email Alerts

```powershell
.\scripts\backup-monitor.ps1 -AlertEmail "<EMAIL>" `
                             -SmtpServer "smtp.gmail.com" `
                             -SmtpPort 587 `
                             -SmtpUser "<EMAIL>" `
                             -SmtpPassword "app-password"
```

## Configuration

### Environment Variables

The backup system uses the same database connection settings as your main application:

```bash
# Database connection (from .env)
DB_HOST=host.docker.internal
DB_PORT=4432
DB_NAME=planner
DB_USER=postgres
DB_PASSWORD=your-password

# Backup-specific settings (optional)
BACKUP_KEEP_DAYS=30
BACKUP_KEEP_WEEKS=8
BACKUP_KEEP_MONTHS=6
```

### Custom Retention Policies

Edit `docker-compose.backup.yml` to adjust retention:

```yaml
environment:
  - BACKUP_KEEP_DAYS=60      # Keep daily backups for 60 days
  - BACKUP_KEEP_WEEKS=12     # Keep weekly backups for 12 weeks
  - BACKUP_KEEP_MONTHS=12    # Keep monthly backups for 12 months
```

## File Locations

| Path | Description |
|------|-------------|
| `./backups/` | Backup files storage |
| `./scripts/backup-restore.ps1` | Main backup/restore script |
| `./scripts/backup-cleanup.ps1` | Cleanup and retention script |
| `./scripts/backup-monitor.ps1` | Health monitoring script |
| `./docker-compose.backup.yml` | Backup services configuration |

## Backup File Naming

Backup files follow this naming convention:
```
jetty_planner_backup_YYYY-MM-DD_HH-MM-SS.sql
```

Example: `jetty_planner_backup_2024-01-15_02-00-15.sql`

## Troubleshooting

### Common Issues

#### Backup Service Won't Start

```powershell
# Check logs
docker logs jetty-postgres-backup

# Verify database connection
docker exec jetty-postgres-backup pg_isready -h $DB_HOST -p $DB_PORT
```

#### Connection Refused

1. Ensure PostgreSQL is running
2. Check database connection settings in `.env`
3. Verify network connectivity between containers

#### Backup Files Are Empty

1. Check PostgreSQL user permissions
2. Verify database name is correct
3. Check Docker volume mounts

#### Restore Fails

1. Ensure target database exists
2. Check PostgreSQL user has CREATE privileges
3. Verify backup file is not corrupted

### Logs and Debugging

```powershell
# View backup service logs
docker logs jetty-postgres-backup

# View web interface logs
docker logs jetty-backup-web

# Manual backup with verbose output
docker exec jetty-postgres-backup pg_dump --verbose -h $DB_HOST -U $DB_USER $DB_NAME
```

## Security Considerations

1. **Backup Encryption**: Consider encrypting backup files for sensitive data
2. **Access Control**: Backup web interface (port 8081) should be firewall-protected in production
3. **Credentials**: Database passwords are stored in environment variables
4. **File Permissions**: Backup files are readable by Docker containers only

## Performance Impact

- **Backup Duration**: Typically 30-60 seconds for average databases
- **Storage Usage**: ~1-2MB per backup (with compression)
- **System Load**: Minimal impact during backup creation
- **Network**: No impact (backups are created locally)

## Disaster Recovery

### Full System Recovery

1. **Restore Database**:
   ```powershell
   .\scripts\backup-restore.ps1 -Action restore -BackupFile "latest-backup.sql"
   ```

2. **Restart Services**:
   ```powershell
   docker compose up -d
   ```

### Point-in-Time Recovery

1. Choose appropriate backup based on timestamp
2. Restore database from that backup
3. Manually replay any critical changes if needed

## Automation with Task Scheduler

### Windows Task Scheduler Setup

1. Open Task Scheduler (`taskschd.msc`)
2. Create new task with these settings:
   - **Trigger**: Daily at 3:00 AM (after backup creation)
   - **Action**: `PowerShell.exe -File "C:\Path\To\Jettyplanner\scripts\backup-monitor.ps1"`
   - **Run with highest privileges**: Enabled

### Example Task XML

```xml
<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2">
  <RegistrationInfo>
    <Description>Jetty Planner Backup Monitoring</Description>
  </RegistrationInfo>
  <Triggers>
    <CalendarTrigger>
      <StartBoundary>2024-01-01T03:00:00</StartBoundary>
      <ScheduleByDay>
        <DaysInterval>1</DaysInterval>
      </ScheduleByDay>
    </CalendarTrigger>
  </Triggers>
  <Actions>
    <Exec>
      <Command>PowerShell.exe</Command>
      <Arguments>-File "C:\Users\<USER>\Jettyplanner\scripts\backup-monitor.ps1"</Arguments>
    </Exec>
  </Actions>
</Task>
```

## Best Practices

1. **Test Restores Regularly**: Verify backups work by testing restores
2. **Monitor Disk Space**: Ensure adequate space for backup retention
3. **Off-site Storage**: Consider copying backups to cloud storage
4. **Document Recovery Procedures**: Keep this guide accessible
5. **Review Retention Policies**: Adjust based on business requirements

## Support and Maintenance

- **Update Backup System**: Pull latest Docker images periodically
- **Review Logs**: Check backup logs monthly for issues
- **Capacity Planning**: Monitor backup growth and adjust retention
- **Security Updates**: Keep Docker and PostgreSQL images updated