# Disable Maintenance Mode for Jetty Planner
# Removes the maintenance flag file to restore normal operation

$ErrorActionPreference = "Stop"

$ProjectRoot = Split-Path -Parent $PSScriptRoot
$FlagFile = Join-Path $ProjectRoot "maintenance.flag"
$LogFile = Join-Path $ProjectRoot "logs/maintenance.log"

function Write-MaintenanceLog {
    param([string]$Message)
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "$Timestamp - $Message"
    Write-Host $LogEntry
    Add-Content -Path $LogFile -Value $LogEntry -ErrorAction SilentlyContinue
}

try {
    Write-Host "🚀 Disabling Maintenance Mode for Evos Jetty Planner..." -ForegroundColor Green
    Write-Host ""
    
    # Check if maintenance mode is enabled
    if (-not (Test-Path $FlagFile)) {
        Write-Host "ℹ️  Maintenance mode is not currently enabled." -ForegroundColor Blue
        Write-Host "No flag file found at: $FlagFile"
        exit 0
    }
    
    # Show current maintenance info before removing
    Write-Host "📋 Current maintenance info:" -ForegroundColor Gray
    $Content = Get-Content $FlagFile -Raw -ErrorAction SilentlyContinue
    if ($Content) {
        Write-Host $Content -ForegroundColor Gray
    }
    Write-Host ""
    
    # Confirm removal
    $Confirm = Read-Host "Are you sure you want to disable maintenance mode? (Y/n)"
    if ($Confirm -match "^[Nn]") {
        Write-Host "Maintenance mode unchanged." -ForegroundColor Yellow
        exit 0
    }
    
    # Remove the flag file
    Remove-Item $FlagFile -Force
    Write-MaintenanceLog "MAINTENANCE DISABLED by $env:USERNAME"
    
    Write-Host "✅ Maintenance mode disabled successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 The application is now accessible again at:" -ForegroundColor Cyan
    Write-Host "   https://planner.evosgpt.eu"
    Write-Host ""
    Write-Host "📁 Flag file removed from:" -ForegroundColor Gray
    Write-Host "   $FlagFile"
    Write-Host ""
    Write-Host "🔍 You may want to verify the application is working correctly:" -ForegroundColor Yellow
    Write-Host "   • Check the website loads properly"
    Write-Host "   • Verify API endpoints are responding"
    Write-Host "   • Monitor logs for any startup issues"
    
} catch {
    Write-Host "❌ Error disabling maintenance mode: $($_.Exception.Message)" -ForegroundColor Red
    Write-MaintenanceLog "ERROR disabling maintenance mode: $($_.Exception.Message)"
    exit 1
}
