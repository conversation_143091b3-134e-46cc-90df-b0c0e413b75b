"""
Unit tests for VesselService - Database-First Vessel Operations
"""

import pytest
from unittest.mock import Mock, MagicMock, patch
from datetime import datetime

from src.services.vessel_service import VesselService
from src.models.vessel import VesselBase, VesselType, Cargo


class TestVesselService:
    """Test cases for VesselService"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.mock_db = Mock()
        self.vessel_service = VesselService(self.mock_db)
        self.terminal_id = "TNZN"
        
        # Mock database methods
        self.mock_db.get_active_terminal_id.return_value = self.terminal_id
        
    def test_get_available_vessels_from_nominations(self):
        """Test getting vessels from nominations"""
        # Mock nominations data
        mock_nominations = [
            {
                'id': 1,
                'runtime_vessel_id': 'NV001',
                'name': 'Test Vessel 1',
                'vessel_type': 'tanker',
                'length': 200.0,
                'beam': 30.0,
                'draft': 12.0,
                'deadweight': 80000.0,
                'priority': 1,
                'cargoes': [
                    {'product': 'Crude Oil', 'volume': 50000.0, 'is_loading': True}
                ],
                'eta': datetime.now(),
                'etd': None,
                'metadata': {}
            }
        ]
        
        self.mock_db.get_nominations.return_value = mock_nominations
        self.mock_db.get_assignments.return_value = []
        
        # Test
        vessels = self.vessel_service.get_available_vessels(self.terminal_id)
        
        # Assertions
        assert len(vessels) == 1
        assert vessels[0].id == 'NV001'
        assert vessels[0].name == 'Test Vessel 1'
        assert vessels[0].vessel_type == VesselType.tanker
        assert len(vessels[0].cargoes) == 1
        assert vessels[0].cargoes[0].product == 'Crude Oil'
        
        # Verify database calls
        self.mock_db.get_nominations.assert_called_once_with(self.terminal_id, status="ACTIVE")
        self.mock_db.get_assignments.assert_called_once_with(self.terminal_id)
    
    def test_get_available_vessels_from_cancelled_assignments(self):
        """Test getting vessels from cancelled assignments"""
        # Mock cancelled assignments
        mock_assignments = [
            {
                'id': 1,
                'vessel_id': 'V001',
                'vessel_name': 'Cancelled Vessel',
                'vessel_type': 'barge',
                'status': 'CANCELLED',
                'cargo_product': 'Chemicals',
                'cargo_volume': 25000.0,
                'jetty_name': 'Jetty 1'
            }
        ]
        
        self.mock_db.get_nominations.return_value = []
        self.mock_db.get_assignments.return_value = mock_assignments
        
        # Test
        vessels = self.vessel_service.get_available_vessels(self.terminal_id)
        
        # Assertions
        assert len(vessels) == 1
        assert vessels[0].id == 'V001'
        assert vessels[0].name == 'Cancelled Vessel'
        assert vessels[0].vessel_type == VesselType.barge
        assert vessels[0].status == "APPROACHING"
        assert len(vessels[0].cargoes) == 1
        assert vessels[0].cargoes[0].product == 'Chemicals'
        assert vessels[0].metadata['source'] == 'unscheduled_assignment'
    
    def test_get_available_vessels_combined_sources(self):
        """Test getting vessels from both nominations and cancelled assignments"""
        # Mock both data sources
        mock_nominations = [
            {
                'id': 1,
                'runtime_vessel_id': 'NV001',
                'name': 'Nomination Vessel',
                'vessel_type': 'tanker',
                'length': 200.0,
                'beam': 30.0,
                'draft': 12.0,
                'deadweight': 80000.0,
                'priority': 1,
                'cargoes': [],
                'metadata': {}
            }
        ]
        
        mock_assignments = [
            {
                'id': 1,
                'vessel_id': 'V001',
                'vessel_name': 'Cancelled Vessel',
                'vessel_type': 'barge',
                'status': 'CANCELLED',
                'cargo_product': 'Chemicals',
                'cargo_volume': 25000.0
            }
        ]
        
        self.mock_db.get_nominations.return_value = mock_nominations
        self.mock_db.get_assignments.return_value = mock_assignments
        
        # Test
        vessels = self.vessel_service.get_available_vessels(self.terminal_id)
        
        # Assertions
        assert len(vessels) == 2
        vessel_ids = {v.id for v in vessels}
        assert 'NV001' in vessel_ids
        assert 'V001' in vessel_ids
    
    def test_get_available_vessels_no_duplicates(self):
        """Test that duplicate vessel IDs are handled correctly"""
        # Mock data with potential duplicate
        mock_nominations = [
            {
                'id': 1,
                'runtime_vessel_id': 'V001',
                'name': 'Nomination Vessel',
                'vessel_type': 'tanker',
                'length': 200.0,
                'beam': 30.0,
                'draft': 12.0,
                'deadweight': 80000.0,
                'priority': 1,
                'cargoes': [],
                'metadata': {}
            }
        ]
        
        mock_assignments = [
            {
                'id': 1,
                'vessel_id': 'V001',  # Same ID as nomination
                'vessel_name': 'Cancelled Vessel',
                'vessel_type': 'barge',
                'status': 'CANCELLED',
                'cargo_product': 'Chemicals',
                'cargo_volume': 25000.0
            }
        ]
        
        self.mock_db.get_nominations.return_value = mock_nominations
        self.mock_db.get_assignments.return_value = mock_assignments
        
        # Test
        vessels = self.vessel_service.get_available_vessels(self.terminal_id)
        
        # Assertions - should only have one vessel (nomination takes precedence)
        assert len(vessels) == 1
        assert vessels[0].id == 'V001'
        assert vessels[0].name == 'Nomination Vessel'  # From nomination, not assignment
    
    def test_get_vessel_by_id_from_nomination(self):
        """Test getting specific vessel from nominations"""
        mock_nominations = [
            {
                'id': 1,
                'runtime_vessel_id': 'NV001',
                'name': 'Test Vessel',
                'vessel_type': 'tanker',
                'length': 200.0,
                'beam': 30.0,
                'draft': 12.0,
                'deadweight': 80000.0,
                'priority': 1,
                'cargoes': [],
                'metadata': {}
            }
        ]
        
        self.mock_db.get_nominations.return_value = mock_nominations
        self.mock_db.get_assignments.return_value = []
        
        # Test
        vessel = self.vessel_service.get_vessel_by_id('NV001', self.terminal_id)
        
        # Assertions
        assert vessel is not None
        assert vessel.id == 'NV001'
        assert vessel.name == 'Test Vessel'
    
    def test_get_vessel_by_id_not_found(self):
        """Test getting vessel that doesn't exist"""
        self.mock_db.get_nominations.return_value = []
        self.mock_db.get_assignments.return_value = []
        self.mock_db.get_vessel.return_value = None
        
        # Test
        vessel = self.vessel_service.get_vessel_by_id('NONEXISTENT', self.terminal_id)
        
        # Assertions
        assert vessel is None
    
    def test_add_vessel_nomination(self):
        """Test adding vessel nomination"""
        vessel_data = {
            'runtime_vessel_id': 'NV002',
            'name': 'New Vessel',
            'vessel_type': 'tanker',
            'length': 180.0,
            'beam': 28.0,
            'draft': 10.0,
            'deadweight': 60000.0,
            'priority': 1,
            'cargoes': []
        }
        
        self.mock_db.add_nomination.return_value = 1
        
        # Test
        vessel_id = self.vessel_service.add_vessel_nomination(vessel_data, self.terminal_id)
        
        # Assertions
        assert vessel_id == 'NV002'
        self.mock_db.add_nomination.assert_called_once()
        call_args = self.mock_db.add_nomination.call_args[0][0]
        assert call_args['terminal_id'] == self.terminal_id
        assert call_args['status'] == 'ACTIVE'
        assert call_args['runtime_vessel_id'] == 'NV002'
    
    def test_delete_vessel_success(self):
        """Test successful vessel deletion"""
        # Mock vessel exists
        mock_vessel = Mock()
        mock_vessel.id = 'V001'
        mock_vessel.name = 'Test Vessel'
        
        with patch.object(self.vessel_service, 'get_vessel_by_id', return_value=mock_vessel):
            self.mock_db.delete_vessel.return_value = True
            
            # Test
            result = self.vessel_service.delete_vessel('V001', self.terminal_id)
            
            # Assertions
            assert result is True
            self.mock_db.delete_vessel.assert_called_once_with('V001')
    
    def test_delete_vessel_not_found(self):
        """Test deleting vessel that doesn't exist"""
        with patch.object(self.vessel_service, 'get_vessel_by_id', return_value=None):
            self.mock_db.delete_vessel.return_value = False
            
            # Test
            result = self.vessel_service.delete_vessel('NONEXISTENT', self.terminal_id)
            
            # Assertions
            assert result is False
    
    def test_schedule_vessel(self):
        """Test scheduling a vessel"""
        assignment_data = {
            'vessel_id': 'V001',
            'vessel_name': 'Test Vessel',
            'jetty_name': 'Jetty 1',
            'start_time': '2024-01-01T10:00:00',
            'end_time': '2024-01-01T14:00:00',
            'status': 'SCHEDULED'
        }
        
        self.mock_db.add_assignment.return_value = 1
        self.mock_db.get_nominations.return_value = []
        
        # Test
        assignment_id = self.vessel_service.schedule_vessel('V001', assignment_data, self.terminal_id)
        
        # Assertions
        assert assignment_id == 1
        self.mock_db.add_assignment.assert_called_once()
        call_args = self.mock_db.add_assignment.call_args[0][0]
        assert call_args['terminal_id'] == self.terminal_id
    
    def test_unschedule_vessel(self):
        """Test unscheduling a vessel"""
        self.mock_db.update_assignment.return_value = True
        
        # Test
        result = self.vessel_service.unschedule_vessel(1, self.terminal_id)
        
        # Assertions
        assert result is True
        self.mock_db.update_assignment.assert_called_once_with(1, {'status': 'CANCELLED'})
    
    def test_error_handling_no_terminal(self):
        """Test error handling when no terminal is found"""
        self.mock_db.get_active_terminal_id.return_value = None
        
        # Test
        vessels = self.vessel_service.get_available_vessels()
        
        # Assertions
        assert vessels == []
    
    def test_error_handling_database_error(self):
        """Test error handling when database operations fail"""
        self.mock_db.get_nominations.side_effect = Exception("Database error")
        self.mock_db.get_assignments.return_value = []
        
        # Test
        vessels = self.vessel_service.get_available_vessels(self.terminal_id)
        
        # Assertions - should return empty list on error
        assert vessels == []
    
    def test_invalid_vessel_id_filtering(self):
        """Test filtering of invalid vessel IDs"""
        mock_assignments = [
            {
                'id': 1,
                'vessel_id': 'vessel_id',  # Invalid literal string
                'vessel_name': 'Invalid Vessel',
                'vessel_type': 'tanker',
                'status': 'CANCELLED'
            },
            {
                'id': 2,
                'vessel_id': '',  # Empty string
                'vessel_name': 'Empty Vessel',
                'vessel_type': 'tanker',
                'status': 'CANCELLED'
            },
            {
                'id': 3,
                'vessel_id': 'V001',  # Valid vessel ID
                'vessel_name': 'Valid Vessel',
                'vessel_type': 'tanker',
                'status': 'CANCELLED',
                'cargo_product': 'Oil',
                'cargo_volume': 50000.0
            }
        ]
        
        self.mock_db.get_nominations.return_value = []
        self.mock_db.get_assignments.return_value = mock_assignments
        
        # Test
        vessels = self.vessel_service.get_available_vessels(self.terminal_id)
        
        # Assertions - should only have the valid vessel
        assert len(vessels) == 1
        assert vessels[0].id == 'V001'
        assert vessels[0].name == 'Valid Vessel'
