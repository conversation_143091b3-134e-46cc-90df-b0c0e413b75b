"""
Open-Meteo Weather API Client

This module provides integration with the Open-Meteo weather API to get forecasts
that can affect jetty operations.
"""

import logging
import requests
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class WeatherCondition:
    """Constants for weather conditions"""
    CLEAR = "clear"
    PARTLY_CLOUDY = "partly_cloudy"
    CLOUDY = "cloudy"
    RAIN = "rain"
    HEAVY_RAIN = "heavy_rain"
    THUNDERSTORM = "thunderstorm"
    SNOW = "snow"
    FOG = "fog"
    WINDY = "windy"
    STORM = "storm"


class OpenMeteoApiClient:
    """Client for interacting with Open-Meteo Weather API"""

    def __init__(self, api_key: str = None):
        """
        Initialize Open-Meteo API client.

        Args:
            api_key: Not required for Open-Meteo API, but kept for compatibility.
        """
        self.base_url = "https://api.open-meteo.com/v1"

        # Open-Meteo doesn't require an API key, but we'll keep this for compatibility
        self.api_key = api_key

        # Always have mock data available as fallback
        self.use_mock = api_key == "TEST"
        self.fallback_to_mock = True  # Always fallback to mock data if API fails

        if self.use_mock:
            logger.warning("Using mock data for Open-Meteo Weather API")
        else:
            logger.info("Initialized Open-Meteo Weather API client")

    def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Make a request to the Open-Meteo API.

        Args:
            endpoint: API endpoint to call.
            params: Additional parameters to pass to the API.

        Returns:
            JSON response as dictionary.
        """
        # If we're configured to use mock data, return it immediately
        if self.use_mock:
            return self._get_mock_data(endpoint, params)

        url = f"{self.base_url}{endpoint}"
        params = params or {}

        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Error making request to Open-Meteo API: {e}")

            # If we're configured to fallback to mock data, do so
            if self.fallback_to_mock:
                logger.warning(f"Falling back to mock data for endpoint: {endpoint}")
                return self._get_mock_data(endpoint, params)

            # Otherwise return an error response
            return {
                "error": True,
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def get_current_weather(self, latitude: float, longitude: float) -> Dict[str, Any]:
        """
        Get current weather for a specific location.

        Args:
            latitude: Location latitude.
            longitude: Location longitude.

        Returns:
            Current weather data.
        """
        endpoint = "/forecast"
        params = {
            "latitude": latitude,
            "longitude": longitude,
            "current": "temperature_2m,relative_humidity_2m,apparent_temperature,is_day,precipitation,rain,weather_code,cloud_cover,pressure_msl,surface_pressure,wind_speed_10m,wind_direction_10m,wind_gusts_10m",
            "timezone": "Europe/Brussels"
        }

        response = self._make_request(endpoint, params)

        # Check for error in response
        if isinstance(response, dict) and "error" in response and response["error"]:
            logger.error(f"Error getting current weather: {response.get('message', 'Unknown error')}")
            if self.fallback_to_mock:
                return self._get_mock_current_weather(latitude, longitude)
            return {}

        # Format the response to match our expected format
        if "current" in response:
            current = response["current"]

            # Map weather code to description
            weather_code = current.get("weather_code", 0)
            description = self._get_weather_description(weather_code)

            return {
                "temperature": current.get("temperature_2m", 0),
                "wind_speed": current.get("wind_speed_10m", 0),
                "wind_direction": current.get("wind_direction_10m", 0),
                "humidity": current.get("relative_humidity_2m", 0),
                "pressure": current.get("pressure_msl", 1013),
                "visibility": 10000,  # Open-Meteo doesn't provide visibility
                "weather_code": weather_code,
                "description": description,
                "is_day": current.get("is_day", 1) == 1
            }

        # If we couldn't extract current weather, return mock data
        return self._get_mock_current_weather(latitude, longitude)

    def _get_weather_description(self, code: int) -> str:
        """
        Convert WMO weather code to description.

        Args:
            code: WMO weather code.

        Returns:
            Weather description.
        """
        descriptions = {
            0: "Clear sky",
            1: "Mainly clear",
            2: "Partly cloudy",
            3: "Overcast",
            45: "Fog",
            48: "Depositing rime fog",
            51: "Light drizzle",
            53: "Moderate drizzle",
            55: "Dense drizzle",
            56: "Light freezing drizzle",
            57: "Dense freezing drizzle",
            61: "Slight rain",
            63: "Moderate rain",
            65: "Heavy rain",
            66: "Light freezing rain",
            67: "Heavy freezing rain",
            71: "Slight snow fall",
            73: "Moderate snow fall",
            75: "Heavy snow fall",
            77: "Snow grains",
            80: "Slight rain showers",
            81: "Moderate rain showers",
            82: "Violent rain showers",
            85: "Slight snow showers",
            86: "Heavy snow showers",
            95: "Thunderstorm",
            96: "Thunderstorm with slight hail",
            99: "Thunderstorm with heavy hail"
        }
        return descriptions.get(code, "Unknown")

    def get_forecast(self, latitude: float, longitude: float, days: int = 5) -> List[Dict[str, Any]]:
        """
        Get weather forecast for a specific location.

        Args:
            latitude: Location latitude.
            longitude: Location longitude.
            days: Number of days to forecast.

        Returns:
            List of forecast data for each day.
        """
        endpoint = "/forecast"
        params = {
            "latitude": latitude,
            "longitude": longitude,
            "daily": "weather_code,temperature_2m_max,temperature_2m_min,apparent_temperature_max,apparent_temperature_min,sunrise,sunset,precipitation_sum,rain_sum,precipitation_hours,wind_speed_10m_max,wind_gusts_10m_max,wind_direction_10m_dominant",
            "timezone": "Europe/Brussels",
            "forecast_days": days
        }

        response = self._make_request(endpoint, params)

        # Check for error in response
        if isinstance(response, dict) and "error" in response and response["error"]:
            logger.error(f"Error getting forecast: {response.get('message', 'Unknown error')}")
            if self.fallback_to_mock:
                return self._get_mock_forecast(latitude, longitude, days)
            return []

        # Format the response to match our expected format
        if "daily" in response:
            daily = response["daily"]
            forecast = []

            # Process each day
            for i in range(len(daily.get("time", []))):
                date_str = daily["time"][i]
                weather_code = daily["weather_code"][i]

                forecast.append({
                    "date": date_str,
                    "max_temp": daily["temperature_2m_max"][i],
                    "min_temp": daily["temperature_2m_min"][i],
                    "max_wind": daily["wind_speed_10m_max"][i],
                    "wind_direction": daily["wind_direction_10m_dominant"][i],
                    "precipitation": daily["precipitation_sum"][i],
                    "weather_code": weather_code,
                    "description": self._get_weather_description(weather_code),
                    "has_thunderstorm": weather_code in [95, 96, 97, 98, 99]
                })

            return forecast

        # If we couldn't extract forecast data, return mock data
        return self._get_mock_forecast(latitude, longitude, days)

    def get_hourly_forecast(self, latitude: float, longitude: float, hours: int = 24) -> List[Dict[str, Any]]:
        """
        Get hourly weather forecast for a specific location.

        Args:
            latitude: Location latitude.
            longitude: Location longitude.
            hours: Number of hours to forecast (max 168).

        Returns:
            List of forecast data for each hour.
        """
        endpoint = "/forecast"
        params = {
            "latitude": latitude,
            "longitude": longitude,
            "hourly": "temperature_2m,relative_humidity_2m,apparent_temperature,precipitation,rain,weather_code,cloud_cover,pressure_msl,surface_pressure,wind_speed_10m,wind_direction_10m,wind_gusts_10m,is_day",
            "timezone": "Europe/Brussels",
            "forecast_hours": min(hours, 168)  # Open-Meteo supports up to 7 days (168 hours)
        }

        response = self._make_request(endpoint, params)

        # Check for error in response
        if isinstance(response, dict) and "error" in response and response["error"]:
            logger.error(f"Error getting hourly forecast: {response.get('message', 'Unknown error')}")
            if self.fallback_to_mock:
                return self._get_mock_hourly_forecast(latitude, longitude, hours)
            return []

        # Format the response to match our expected format
        if "hourly" in response:
            hourly = response["hourly"]
            forecast = []

            # Process each hour
            for i in range(min(hours, len(hourly.get("time", [])))):
                time_str = hourly["time"][i]
                weather_code = hourly["weather_code"][i]

                # Extract hour from time string
                hour = int(time_str.split("T")[1].split(":")[0]) if "T" in time_str else 0

                forecast.append({
                    "time": time_str,
                    "hour": hour,
                    "temperature": hourly["temperature_2m"][i],
                    "wind_speed": hourly["wind_speed_10m"][i],
                    "wind_direction": hourly["wind_direction_10m"][i],
                    "humidity": hourly["relative_humidity_2m"][i],
                    "precipitation": hourly["precipitation"][i],
                    "weather_code": weather_code,
                    "description": self._get_weather_description(weather_code),
                    "is_day": hourly["is_day"][i] == 1
                })

            return forecast

        # If we couldn't extract hourly forecast data, return mock data
        return self._get_mock_hourly_forecast(latitude, longitude, hours)

    def _get_mock_current_weather(self, latitude: float, longitude: float) -> Dict[str, Any]:
        """
        Generate mock current weather data.

        Args:
            latitude: Location latitude.
            longitude: Location longitude.

        Returns:
            Mock current weather data.
        """
        now = datetime.now()
        hour = now.hour

        # Generate different weather based on time of day
        if 6 <= hour < 12:  # Morning
            weather_code = 2  # Partly cloudy
            temp = 18.5
            wind_speed = 14.2
        elif 12 <= hour < 18:  # Afternoon
            weather_code = 1  # Mainly clear
            temp = 21.0
            wind_speed = 13.2
        elif 18 <= hour < 22:  # Evening
            weather_code = 3  # Overcast
            temp = 17.0
            wind_speed = 11.1
        else:  # Night
            weather_code = 0  # Clear sky
            temp = 15.0
            wind_speed = 7.2

        return {
            "temperature": temp,
            "wind_speed": wind_speed,
            "wind_direction": 225,
            "humidity": 65,
            "pressure": 1015,
            "visibility": 10000,
            "weather_code": weather_code,
            "description": self._get_weather_description(weather_code),
            "is_day": 6 <= hour < 22
        }

    def _get_mock_forecast(self, latitude: float, longitude: float, days: int) -> List[Dict[str, Any]]:
        """
        Generate mock forecast data.

        Args:
            latitude: Location latitude.
            longitude: Location longitude.
            days: Number of days to forecast.

        Returns:
            List of mock forecast data for each day.
        """
        forecast = []
        now = datetime.now()

        for i in range(days):
            date = now + timedelta(days=i)
            date_str = date.strftime("%Y-%m-%d")

            # Vary the weather for each day
            weather_code = (i % 5) * 2  # 0, 2, 4, 6, 8 -> clear, partly cloudy, etc.
            max_temp = 20.0 + i % 5  # 20-24°C
            min_temp = 15.0 - i % 3  # 15-13°C
            max_wind = 14.2 - i % 10  # 14.2-5.2 m/s

            forecast.append({
                "date": date_str,
                "max_temp": max_temp,
                "min_temp": min_temp,
                "max_wind": max_wind,
                "wind_direction": 225 + i * 10,
                "precipitation": i % 5,
                "weather_code": weather_code,
                "description": self._get_weather_description(weather_code),
                "has_thunderstorm": i == 3  # Thunderstorm on the 4th day
            })

        return forecast

    def _get_mock_hourly_forecast(self, latitude: float, longitude: float, hours: int) -> List[Dict[str, Any]]:
        """
        Generate mock hourly forecast data.

        Args:
            latitude: Location latitude.
            longitude: Location longitude.
            hours: Number of hours to forecast.

        Returns:
            List of mock hourly forecast data.
        """
        forecast = []
        now = datetime.now()

        for i in range(hours):
            time = now + timedelta(hours=i)
            time_str = time.strftime("%Y-%m-%dT%H:00")
            hour = time.hour

            # Vary the weather throughout the day
            if 6 <= hour < 12:  # Morning
                weather_code = 2  # Partly cloudy
                temp = 18.0 + i % 3
                wind_speed = 14.8 - (i % 5) * 0.2
            elif 12 <= hour < 18:  # Afternoon
                weather_code = 1  # Mainly clear
                temp = 21.0 - i % 3
                wind_speed = 13.2 - (i % 5) * 0.2
            elif 18 <= hour < 22:  # Evening
                weather_code = 3  # Overcast
                temp = 17.0 - i % 3
                wind_speed = 11.1 - (i % 5) * 0.2
            else:  # Night
                weather_code = 0  # Clear sky
                temp = 15.0 - i % 3
                wind_speed = 7.2 - (i % 5) * 0.2

            forecast.append({
                "time": time_str,
                "hour": hour,
                "temperature": temp,
                "wind_speed": wind_speed,
                "wind_direction": 225 + i * 5,
                "humidity": 65 - i % 20,
                "precipitation": i % 2,
                "weather_code": weather_code,
                "description": self._get_weather_description(weather_code),
                "is_day": 6 <= hour < 22
            })

        return forecast

    def get_weather_warnings(self, latitude: float, longitude: float, days: int = 2) -> List[Dict[str, Any]]:
        """
        Get weather warnings for the next few days based on forecast data.

        Args:
            latitude: Location latitude.
            longitude: Location longitude.
            days: Number of days to check.

        Returns:
            List of weather warnings with timestamp and reason.
        """
        # Analyze forecast data to generate warnings
        forecast = self.get_forecast(latitude, longitude, days)
        warnings = []

        for day in forecast:
            date_str = day.get("date", "")
            max_wind = day.get("max_wind", 0)
            has_thunderstorm = day.get("has_thunderstorm", False)

            # Check for high winds
            if max_wind > 10.8:  # > 21 knots / Beaufort 5+
                warnings.append({
                    "timestamp": date_str,
                    "reason": f"High wind speed ({max_wind} m/s)",
                    "severity": "moderate" if max_wind < 13.8 else "high",
                    "description": f"Wind speeds of {max_wind} m/s expected on {date_str}",
                    "source": "Forecast Analysis"
                })

            # Check for thunderstorms
            if has_thunderstorm:
                warnings.append({
                    "timestamp": date_str,
                    "reason": "Thunderstorm activity",
                    "severity": "high",
                    "description": f"Thunderstorms expected on {date_str}",
                    "source": "Forecast Analysis"
                })

        return warnings

    def is_weather_suitable_for_operations(self, latitude: float, longitude: float) -> Dict[str, Any]:
        """
        Check if current weather conditions are suitable for jetty operations.

        Args:
            latitude: Location latitude.
            longitude: Location longitude.

        Returns:
            Dictionary with suitability assessment and reason.
        """
        weather = self.get_current_weather(latitude, longitude)

        # Check for thunderstorms (WMO codes 95-99)
        weather_code = weather.get("weather_code", 0)
        if weather_code >= 95 and weather_code <= 99:
            return {
                "suitable": False,
                "reason": "Thunderstorm activity",
                "weather": weather
            }

        # Check for high winds
        wind_speed = weather.get("wind_speed", 0)
        if wind_speed >= 17.0:  # Above 33 knots
            return {
                "suitable": False,
                "reason": "High wind speed",
                "weather": weather
            }

        # Check for heavy precipitation
        weather_condition = weather.get("description", "").lower()
        if "heavy rain" in weather_condition or "heavy snow" in weather_condition:
            return {
                "suitable": False,
                "reason": "Heavy precipitation",
                "weather": weather
            }

        # Weather is suitable
        return {"suitable": True, "weather": weather}

    def _get_mock_data(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate mock data for testing.

        Args:
            endpoint: API endpoint.
            params: Request parameters.

        Returns:
            Mock data.
        """
        params = params or {}
        lat = params.get("latitude", 51.0543)
        lon = params.get("longitude", 3.7174)

        if "/forecast" in endpoint:
            if "hourly" in params:
                hours = params.get("forecast_hours", 24)
                return {"hourly": self._get_mock_hourly_forecast(lat, lon, hours)}
            elif "daily" in params:
                days = params.get("forecast_days", 5)
                return {"daily": self._get_mock_forecast(lat, lon, days)}
            else:
                return {"current": self._get_mock_current_weather(lat, lon)}

        # Default mock data
        return {"error": False, "message": "Mock data generated"}