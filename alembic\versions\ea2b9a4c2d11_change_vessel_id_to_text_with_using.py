"""Change vessel_id columns to TEXT with USING casts

Revision ID: ea2b9a4c2d11
Revises: dbb965293f71
Create Date: 2025-09-02 13:10:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ea2b9a4c2d11'
down_revision: Union[str, Sequence[str], None] = 'dbb965293f71'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Alter vessel_id columns to TEXT explicitly with USING casts."""
    # Check if we're using SQLite
    bind = op.get_bind()
    if bind.dialect.name == 'sqlite':
        # SQLite doesn't support ALTER COLUMN TYPE directly
        # For SQLite, we'll use batch operations which handle recreation
        with op.batch_alter_table('assignments', schema=None) as batch_op:
            batch_op.alter_column(
                'vessel_id',
                existing_type=sa.Integer(),
                type_=sa.String(),
                existing_nullable=False
            )
        
        # assignment_changes.vessel_id: INTEGER -> TEXT (nullable)
        try:
            with op.batch_alter_table('assignment_changes', schema=None) as batch_op:
                batch_op.alter_column(
                    'vessel_id',
                    existing_type=sa.Integer(),
                    type_=sa.String(),
                    existing_nullable=True
                )
        except Exception:
            # Table or column may not exist in some environments; skip best-effort
            pass
    else:
        # PostgreSQL version
        op.alter_column(
            'assignments',
            'vessel_id',
            existing_type=sa.Integer(),
            type_=sa.String(),
            existing_nullable=False,
            postgresql_using='vessel_id::text'
        )

        # assignment_changes.vessel_id: INTEGER -> TEXT (nullable)
        try:
            op.alter_column(
                'assignment_changes',
                'vessel_id',
                existing_type=sa.Integer(),
                type_=sa.String(),
                existing_nullable=True,
                postgresql_using='vessel_id::text'
            )
        except Exception:
            # Table or column may not exist in some environments; skip best-effort
            pass


def downgrade() -> None:
    """Revert vessel_id columns back to INTEGER with safe casts where possible."""
    # Check if we're using SQLite
    bind = op.get_bind()
    if bind.dialect.name == 'sqlite':
        # SQLite version - use batch operations
        try:
            with op.batch_alter_table('assignment_changes', schema=None) as batch_op:
                batch_op.alter_column(
                    'vessel_id',
                    existing_type=sa.String(),
                    type_=sa.Integer(),
                    existing_nullable=True
                )
        except Exception:
            pass

        # assignments.vessel_id: TEXT -> INTEGER
        with op.batch_alter_table('assignments', schema=None) as batch_op:
            batch_op.alter_column(
                'vessel_id',
                existing_type=sa.String(),
                type_=sa.Integer(),
                existing_nullable=False
            )
    else:
        # PostgreSQL version
        try:
            op.alter_column(
                'assignment_changes',
                'vessel_id',
                existing_type=sa.String(),
                type_=sa.Integer(),
                existing_nullable=True,
                postgresql_using="NULLIF(regexp_replace(vessel_id, '[^0-9]', '', 'g'), '')::integer"
            )
        except Exception:
            pass

        # assignments.vessel_id: TEXT -> INTEGER
        op.alter_column(
            'assignments',
            'vessel_id',
            existing_type=sa.String(),
            type_=sa.Integer(),
            existing_nullable=False,
            postgresql_using="NULLIF(regexp_replace(vessel_id, '[^0-9]', '', 'g'), '')::integer"
        )


