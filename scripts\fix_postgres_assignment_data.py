#!/usr/bin/env python3
"""
Data cleanup script to fix PostgreSQL migration issues in assignments table.
Identifies and fixes assignments with corrupted vessel_id values.
"""

import sys
import os
sys.path.append('.')

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

from src.database import Database
from src.db.models import Assignment
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_assignment_data():
    """Analyze assignments table for data corruption issues."""
    logger.info("Analyzing assignments table for data corruption...")
    
    db = Database()
    
    with db.get_session() as session:
        # Get all assignments
        assignments = session.query(Assignment).all()
        
        total_assignments = len(assignments)
        corrupted_assignments = []
        
        logger.info(f"Found {total_assignments} assignments to analyze")
        
        for assignment in assignments:
            issues = []
            
            # Check vessel_id for corruption
            if assignment.vessel_id == 'vessel_id':
                issues.append("vessel_id contains literal string 'vessel_id'")
            elif not assignment.vessel_id:
                issues.append("vessel_id is null or empty")
            
            # Check for other common corruption patterns
            if assignment.vessel_name == 'vessel_name':
                issues.append("vessel_name contains literal string 'vessel_name'")
            elif not assignment.vessel_name:
                issues.append("vessel_name is null or empty")
                
            if assignment.vessel_type == 'vessel_type':
                issues.append("vessel_type contains literal string 'vessel_type'")
            elif not assignment.vessel_type:
                issues.append("vessel_type is null or empty")
                
            if assignment.jetty_name == 'jetty_name':
                issues.append("jetty_name contains literal string 'jetty_name'")
            elif not assignment.jetty_name:
                issues.append("jetty_name is null or empty")
                
            # Check for missing cargo data that affects dashboard display
            if not assignment.cargo_product and not assignment.cargo_volume:
                issues.append("both cargo_product and cargo_volume are missing")
            
            if issues:
                corrupted_assignments.append({
                    'id': assignment.id,
                    'issues': issues,
                    'assignment': assignment
                })
                
        logger.info(f"Found {len(corrupted_assignments)} corrupted assignments")
        
        if corrupted_assignments:
            logger.info("Corrupted assignments:")
            for item in corrupted_assignments:
                logger.info(f"  Assignment {item['id']}: {', '.join(item['issues'])}")
                
        return corrupted_assignments

def fix_assignment_data(dry_run=True):
    """Fix corrupted assignment data."""
    logger.info(f"{'DRY RUN: ' if dry_run else ''}Fixing corrupted assignment data...")
    
    corrupted = analyze_assignment_data()
    
    if not corrupted:
        logger.info("No corrupted assignments found.")
        return
    
    db = Database()
    
    with db.get_session() as session:
        for item in corrupted:
            assignment = item['assignment']
            assignment_id = item['id']
            
            logger.info(f"{'Would fix' if dry_run else 'Fixing'} assignment {assignment_id}...")
            
            # For now, we'll delete corrupted assignments as they're likely unusable
            # In a real scenario, you might want to try to reconstruct the data
            # from other sources or mark them as requiring manual review
            
            if not dry_run:
                logger.warning(f"Deleting corrupted assignment {assignment_id}")
                session.delete(assignment)
                
        if not dry_run:
            session.commit()
            logger.info(f"Deleted {len(corrupted)} corrupted assignments")
        else:
            logger.info(f"Would delete {len(corrupted)} corrupted assignments")

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Fix PostgreSQL assignment data corruption')
    parser.add_argument('--fix', action='store_true', help='Actually fix the data (default is dry run)')
    parser.add_argument('--analyze-only', action='store_true', help='Only analyze, do not fix')
    
    args = parser.parse_args()
    
    if args.analyze_only:
        analyze_assignment_data()
    else:
        fix_assignment_data(dry_run=not args.fix)

if __name__ == "__main__":
    main()
