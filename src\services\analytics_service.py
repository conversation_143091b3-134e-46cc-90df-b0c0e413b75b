"""
Analytics service for data collection, classification, and processing.
Handles ML prediction logging, change analysis, and performance metrics.
"""

import logging
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session

from ..db.models import (
    MLPredictionLog, PlanningMetrics, ChangeAnalysis, PerformanceAlert,
    Assignment, AssignmentChange
)

logger = logging.getLogger(__name__)


class AnalyticsService:
    """Service for analytics data collection and processing"""
    
    def __init__(self, db_session: Session):
        self.session = db_session
    
    def categorize_change(self, reason_text: str, context: Dict[str, Any] = None) -> str:
        """
        Automatically categorize changes based on reason text and context.
        
        Args:
            reason_text: The reason provided for the change
            context: Additional context about the change
            
        Returns:
            Category string: 'external_factor', 'internal_optimization', 'ml_correction', 'unknown'
        """
        if not reason_text:
            return 'unknown'
            
        reason_lower = reason_text.lower()
        
        # External factor keywords
        external_keywords = [
            'weather', 'vessel breakdown', 'customer request', 'port congestion',
            'pilot availability', 'tide', 'crew change', 'documentation',
            'customs', 'environmental', 'safety inspection', 'delay',
            'vessel delay', 'traffic', 'lock', 'bridge', 'pilot',
            'berth availability', 'cargo delay', 'loading delay',
            'unloading delay', 'technical issue', 'mechanical'
        ]
        
        # Internal optimization keywords  
        optimization_keywords = [
            'resource optimization', 'efficiency improvement', 'schedule conflict',
            'equipment maintenance', 'tank availability', 'pipeline scheduling',
            'optimize', 'optimization', 'efficiency', 'resource',
            'maintenance', 'schedule', 'planning', 'coordination'
        ]
        
        # ML correction indicators
        ml_keywords = [
            'prediction', 'estimate', 'forecast', 'model', 'ml',
            'machine learning', 'algorithm', 'prediction error'
        ]
        
        # Check for ML correction first
        if context and context.get('prediction_error_detected', False):
            return 'ml_correction'
        if any(keyword in reason_lower for keyword in ml_keywords):
            return 'ml_correction'
            
        # Check for external factors
        if any(keyword in reason_lower for keyword in external_keywords):
            return 'external_factor'
            
        # Check for internal optimization
        if any(keyword in reason_lower for keyword in optimization_keywords):
            return 'internal_optimization'
            
        return 'unknown'
    
    def map_reason_category(self, reason_text: str) -> str:
        """
        Map reason text to standardized reason categories.
        
        Returns:
            Category: 'operational', 'vessel', 'commercial', 'terminal', 'regulatory', 'other'
        """
        if not reason_text:
            return 'other'
            
        reason_lower = reason_text.lower()
        
        # Operational reasons
        if any(word in reason_lower for word in ['schedule', 'planning', 'coordination', 'optimization', 'efficiency']):
            return 'operational'
            
        # Vessel-related reasons
        if any(word in reason_lower for word in ['vessel', 'ship', 'boat', 'crew', 'captain', 'technical', 'mechanical', 'breakdown']):
            return 'vessel'
            
        # Commercial reasons
        if any(word in reason_lower for word in ['customer', 'commercial', 'contract', 'cargo', 'loading', 'unloading']):
            return 'commercial'
            
        # Terminal reasons
        if any(word in reason_lower for word in ['terminal', 'berth', 'jetty', 'tank', 'pipeline', 'equipment', 'maintenance']):
            return 'terminal'
            
        # Regulatory reasons
        if any(word in reason_lower for word in ['customs', 'regulatory', 'inspection', 'safety', 'environmental', 'documentation']):
            return 'regulatory'
            
        return 'other'
    
    def log_assignment_change(self, assignment_id: int, change_type: str, 
                            original_value: str, new_value: str, 
                            reason_text: str = None, changed_by: str = None,
                            vessel_id: str = None, vessel_name: str = None,
                            terminal_id: str = None) -> None:
        """
        Log an assignment change with automatic categorization.
        
        Args:
            assignment_id: ID of the assignment being changed
            change_type: Type of change ('start_time', 'end_time', 'jetty', 'vessel')
            original_value: Original value before change
            new_value: New value after change
            reason_text: Reason for the change
            changed_by: User who made the change
            vessel_id: ID of the vessel
            vessel_name: Name of the vessel
            terminal_id: ID of the terminal
        """
        try:
            # Categorize the change
            change_category = self.categorize_change(reason_text or '')
            reason_category = self.map_reason_category(reason_text or '')
            
            # Calculate impact (simplified - could be enhanced)
            change_impact_minutes = 0
            if change_type in ['start_time', 'end_time'] and original_value and new_value:
                try:
                    # Try to parse datetime and calculate difference
                    from datetime import datetime
                    orig_dt = datetime.fromisoformat(original_value.replace('Z', '+00:00'))
                    new_dt = datetime.fromisoformat(new_value.replace('Z', '+00:00'))
                    change_impact_minutes = abs((new_dt - orig_dt).total_seconds() / 60)
                except:
                    pass
            
            # Create change analysis record
            change_analysis = ChangeAnalysis(
                assignment_id=assignment_id,
                change_type=change_type,
                change_category=change_category,
                reason_category=reason_category,
                reason_text=reason_text,
                original_value=str(original_value),
                new_value=str(new_value),
                change_impact_minutes=int(change_impact_minutes) if change_impact_minutes else None,
                vessel_id=vessel_id,
                vessel_name=vessel_name,
                terminal_id=terminal_id,
                changed_by=changed_by,
                changed_at=datetime.utcnow()
            )
            
            self.session.add(change_analysis)
            self.session.commit()
            
            logger.info(f"Logged change analysis for assignment {assignment_id}: {change_type} - {change_category}")
            
        except Exception as e:
            logger.error(f"Error logging assignment change: {e}")
            self.session.rollback()
    
    def log_ml_prediction(self, assignment_id: int, vessel_id: str, vessel_name: str,
                         prediction_type: str, predicted_minutes: int, 
                         confidence_score: float = None, terminal_id: str = None) -> int:
        """
        Log an ML prediction for later accuracy tracking.
        
        Args:
            assignment_id: ID of the assignment
            vessel_id: ID of the vessel
            vessel_name: Name of the vessel
            prediction_type: Type of prediction ('prepump', 'pump', 'postpump', 'terminal')
            predicted_minutes: Predicted duration in minutes
            confidence_score: Confidence score of the prediction (0.0-1.0)
            terminal_id: ID of the terminal
            
        Returns:
            ID of the created prediction log entry
        """
        try:
            prediction_log = MLPredictionLog(
                assignment_id=assignment_id,
                vessel_id=vessel_id,
                vessel_name=vessel_name,
                prediction_type=prediction_type,
                predicted_minutes=predicted_minutes,
                confidence_score=confidence_score,
                terminal_id=terminal_id,
                prediction_timestamp=datetime.utcnow()
            )
            
            self.session.add(prediction_log)
            self.session.commit()
            
            logger.info(f"Logged ML prediction for assignment {assignment_id}: {prediction_type} = {predicted_minutes}min")
            return prediction_log.id
            
        except Exception as e:
            logger.error(f"Error logging ML prediction: {e}")
            self.session.rollback()
            return None
    
    def update_ml_prediction_actual(self, prediction_id: int, actual_minutes: int) -> None:
        """
        Update an ML prediction with actual results and calculate accuracy.
        
        Args:
            prediction_id: ID of the prediction log entry
            actual_minutes: Actual duration in minutes
        """
        try:
            prediction = self.session.query(MLPredictionLog).filter_by(id=prediction_id).first()
            if not prediction:
                logger.warning(f"ML prediction {prediction_id} not found")
                return
                
            prediction.actual_minutes = actual_minutes
            prediction.actual_timestamp = datetime.utcnow()
            
            # Calculate accuracy
            if prediction.predicted_minutes and actual_minutes:
                error = abs(prediction.predicted_minutes - actual_minutes)
                prediction.absolute_error_minutes = error
                
                # Calculate accuracy percentage (closer to 100% is better)
                max_value = max(prediction.predicted_minutes, actual_minutes)
                if max_value > 0:
                    accuracy = max(0, (1 - error / max_value) * 100)
                    prediction.accuracy_percentage = round(accuracy, 2)
            
            self.session.commit()
            logger.info(f"Updated ML prediction {prediction_id} with actual result: {actual_minutes}min")
            
        except Exception as e:
            logger.error(f"Error updating ML prediction actual: {e}")
            self.session.rollback()
    
    def calculate_daily_metrics(self, target_date: date, terminal_id: str) -> None:
        """
        Calculate and store daily planning metrics for a specific date.
        
        Args:
            target_date: Date to calculate metrics for
            terminal_id: Terminal to calculate metrics for
        """
        try:
            # Get existing metrics or create new
            existing = (self.session.query(PlanningMetrics)
                       .filter_by(date=target_date, terminal_id=terminal_id)
                       .first())
            
            if existing:
                metrics = existing
            else:
                metrics = PlanningMetrics(
                    date=target_date,
                    terminal_id=terminal_id
                )
                self.session.add(metrics)
            
            # Calculate metrics from assignments for that date
            start_datetime = datetime.combine(target_date, datetime.min.time())
            end_datetime = datetime.combine(target_date, datetime.max.time())
            
            assignments = (self.session.query(Assignment)
                          .filter(Assignment.terminal_id == terminal_id)
                          .filter(Assignment.start_time >= start_datetime)
                          .filter(Assignment.start_time <= end_datetime)
                          .all())
            
            metrics.total_assignments = len(assignments)
            
            # Count changes for that day
            changes = (self.session.query(ChangeAnalysis)
                      .filter(ChangeAnalysis.terminal_id == terminal_id)
                      .filter(ChangeAnalysis.changed_at >= start_datetime)
                      .filter(ChangeAnalysis.changed_at <= end_datetime)
                      .all())
            
            metrics.manual_changes = len(changes)
            
            # Calculate basic efficiency metrics
            if assignments:
                total_duration = sum((a.end_time - a.start_time).total_seconds() / 3600 
                                   for a in assignments if a.end_time and a.start_time)
                metrics.average_turnaround_hours = total_duration / len(assignments) if assignments else 0
                
                # Simple schedule utilization (could be enhanced)
                metrics.schedule_utilization_percent = min(100, len(assignments) * 10)  # Simplified
                metrics.total_vessels_processed = len(set(a.vessel_id for a in assignments if a.vessel_id))
            
            self.session.commit()
            logger.info(f"Calculated daily metrics for {target_date} at {terminal_id}")
            
        except Exception as e:
            logger.error(f"Error calculating daily metrics: {e}")
            self.session.rollback()
    
    def create_performance_alert(self, alert_type: str, metric_name: str,
                               current_value: float, threshold_value: float,
                               severity: str = 'warning', description: str = None,
                               terminal_id: str = None) -> None:
        """
        Create a performance alert when metrics exceed thresholds.
        
        Args:
            alert_type: Type of alert (e.g., 'ml_accuracy_low', 'change_frequency_high')
            metric_name: Name of the metric that triggered the alert
            current_value: Current value of the metric
            threshold_value: Threshold value that was exceeded
            severity: Severity level ('info', 'warning', 'critical')
            description: Human-readable description of the alert
            terminal_id: Terminal ID where the alert occurred
        """
        try:
            alert = PerformanceAlert(
                alert_type=alert_type,
                metric_name=metric_name,
                current_value=current_value,
                threshold_value=threshold_value,
                severity=severity,
                description=description,
                terminal_id=terminal_id,
                is_resolved=False,
                created_at=datetime.utcnow()
            )
            
            self.session.add(alert)
            self.session.commit()
            
            logger.warning(f"Created performance alert: {alert_type} - {description}")
            
        except Exception as e:
            logger.error(f"Error creating performance alert: {e}")
            self.session.rollback()
