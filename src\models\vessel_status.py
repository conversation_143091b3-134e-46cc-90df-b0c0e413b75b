"""
Enhanced Vessel Status Management

This module provides comprehensive state management for vessel nominations
following maritime industry best practices.
"""

from enum import Enum
from typing import Dict, List, Optional, Set
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)


class VesselStatus(Enum):
    """Enhanced vessel status enumeration following industry standards."""
    
    # Pre-scheduling states
    SUBMITTED = "SUBMITTED"          # Just submitted, awaiting review
    UNDER_REVIEW = "UNDER_REVIEW"    # Being validated by business rules
    APPROVED = "APPROVED"            # Ready for optimization scheduling
    NEEDS_INFO = "NEEDS_INFO"        # Additional information required
    
    # Scheduling states
    SCHEDULED = "SCHEDULED"          # Time slot and jetty assigned
    CONFIRMED = "CONFIRMED"          # Vessel confirmed ETA and schedule
    RESCHEDULED = "RESCHEDULED"      # Schedule being modified
    
    # Operational states
    APPROACHING = "APPROACHING"      # En route to terminal
    DELAYED = "DELAYED"             # Behind original schedule
    ARRIVED = "ARRIVED"             # At terminal, awaiting berth
    WAITING = "WAITING"             # Waiting for jetty availability
    BERTHED = "BERTHED"             # Moored at assigned jetty
    
    # Execution states
    IN_PROGRESS = "IN_PROGRESS"     # Cargo operations active
    SUSPENDED = "SUSPENDED"         # Operations temporarily paused
    COMPLETED = "COMPLETED"         # All operations finished
    DEPARTED = "DEPARTED"           # Left terminal
    
    # Terminal states
    CANCELLED = "CANCELLED"         # Nomination cancelled
    REJECTED = "REJECTED"           # Nomination rejected
    ARCHIVED = "ARCHIVED"           # Historical record only


class VesselStatusStage(Enum):
    """High-level stages for grouping statuses."""
    PRE_SCHEDULING = "PRE_SCHEDULING"
    SCHEDULING = "SCHEDULING" 
    OPERATIONAL = "OPERATIONAL"
    EXECUTION = "EXECUTION"
    TERMINAL = "TERMINAL"


class StateTransitionValidator:
    """Validates and manages vessel status transitions."""
    
    # Define allowed state transitions
    ALLOWED_TRANSITIONS: Dict[VesselStatus, Set[VesselStatus]] = {
        VesselStatus.SUBMITTED: {
            VesselStatus.UNDER_REVIEW, 
            VesselStatus.REJECTED
        },
        VesselStatus.UNDER_REVIEW: {
            VesselStatus.APPROVED, 
            VesselStatus.REJECTED, 
            VesselStatus.NEEDS_INFO
        },
        VesselStatus.NEEDS_INFO: {
            VesselStatus.UNDER_REVIEW, 
            VesselStatus.REJECTED
        },
        VesselStatus.APPROVED: {
            VesselStatus.SCHEDULED, 
            VesselStatus.CANCELLED
        },
        VesselStatus.SCHEDULED: {
            VesselStatus.CONFIRMED, 
            VesselStatus.RESCHEDULED, 
            VesselStatus.CANCELLED,
            VesselStatus.APPROACHING  # Direct transition possible
        },
        VesselStatus.RESCHEDULED: {
            VesselStatus.SCHEDULED, 
            VesselStatus.CANCELLED
        },
        VesselStatus.CONFIRMED: {
            VesselStatus.APPROACHING, 
            VesselStatus.RESCHEDULED,
            VesselStatus.CANCELLED
        },
        VesselStatus.APPROACHING: {
            VesselStatus.ARRIVED, 
            VesselStatus.DELAYED,
            VesselStatus.CANCELLED
        },
        VesselStatus.DELAYED: {
            VesselStatus.APPROACHING, 
            VesselStatus.RESCHEDULED,
            VesselStatus.CANCELLED
        },
        VesselStatus.ARRIVED: {
            VesselStatus.BERTHED, 
            VesselStatus.WAITING,
            VesselStatus.CANCELLED
        },
        VesselStatus.WAITING: {
            VesselStatus.BERTHED, 
            VesselStatus.RESCHEDULED,
            VesselStatus.CANCELLED
        },
        VesselStatus.BERTHED: {
            VesselStatus.IN_PROGRESS,
            VesselStatus.CANCELLED  # Emergency cancellation
        },
        VesselStatus.IN_PROGRESS: {
            VesselStatus.COMPLETED, 
            VesselStatus.SUSPENDED
        },
        VesselStatus.SUSPENDED: {
            VesselStatus.IN_PROGRESS, 
            VesselStatus.COMPLETED,
            VesselStatus.CANCELLED
        },
        VesselStatus.COMPLETED: {
            VesselStatus.DEPARTED
        },
        VesselStatus.DEPARTED: {
            VesselStatus.ARCHIVED
        },
        # Terminal states
        VesselStatus.CANCELLED: {
            VesselStatus.ARCHIVED
        },
        VesselStatus.REJECTED: {
            VesselStatus.ARCHIVED
        },
        VesselStatus.ARCHIVED: set()  # No transitions from archived
    }
    
    # Status stages mapping
    STATUS_STAGES: Dict[VesselStatus, VesselStatusStage] = {
        VesselStatus.SUBMITTED: VesselStatusStage.PRE_SCHEDULING,
        VesselStatus.UNDER_REVIEW: VesselStatusStage.PRE_SCHEDULING,
        VesselStatus.NEEDS_INFO: VesselStatusStage.PRE_SCHEDULING,
        VesselStatus.APPROVED: VesselStatusStage.PRE_SCHEDULING,
        
        VesselStatus.SCHEDULED: VesselStatusStage.SCHEDULING,
        VesselStatus.CONFIRMED: VesselStatusStage.SCHEDULING,
        VesselStatus.RESCHEDULED: VesselStatusStage.SCHEDULING,
        
        VesselStatus.APPROACHING: VesselStatusStage.OPERATIONAL,
        VesselStatus.DELAYED: VesselStatusStage.OPERATIONAL,
        VesselStatus.ARRIVED: VesselStatusStage.OPERATIONAL,
        VesselStatus.WAITING: VesselStatusStage.OPERATIONAL,
        VesselStatus.BERTHED: VesselStatusStage.OPERATIONAL,
        
        VesselStatus.IN_PROGRESS: VesselStatusStage.EXECUTION,
        VesselStatus.SUSPENDED: VesselStatusStage.EXECUTION,
        VesselStatus.COMPLETED: VesselStatusStage.EXECUTION,
        VesselStatus.DEPARTED: VesselStatusStage.EXECUTION,
        
        VesselStatus.CANCELLED: VesselStatusStage.TERMINAL,
        VesselStatus.REJECTED: VesselStatusStage.TERMINAL,
        VesselStatus.ARCHIVED: VesselStatusStage.TERMINAL,
    }
    
    @classmethod
    def is_valid_transition(cls, from_status: VesselStatus, to_status: VesselStatus) -> bool:
        """Check if a status transition is valid."""
        return to_status in cls.ALLOWED_TRANSITIONS.get(from_status, set())
    
    @classmethod
    def get_allowed_next_statuses(cls, current_status: VesselStatus) -> Set[VesselStatus]:
        """Get all valid next statuses from current status."""
        return cls.ALLOWED_TRANSITIONS.get(current_status, set())
    
    @classmethod
    def get_status_stage(cls, status: VesselStatus) -> VesselStatusStage:
        """Get the stage for a given status."""
        return cls.STATUS_STAGES.get(status, VesselStatusStage.TERMINAL)
    
    @classmethod
    def get_statuses_for_stage(cls, stage: VesselStatusStage) -> Set[VesselStatus]:
        """Get all statuses belonging to a stage."""
        return {status for status, status_stage in cls.STATUS_STAGES.items() 
                if status_stage == stage}
    
    @classmethod
    def validate_transition(cls, from_status: VesselStatus, to_status: VesselStatus, 
                          reason: Optional[str] = None) -> tuple[bool, str]:
        """
        Validate a status transition and return result with message.
        
        Returns:
            Tuple of (is_valid, message)
        """
        if cls.is_valid_transition(from_status, to_status):
            return True, f"Valid transition from {from_status.value} to {to_status.value}"
        
        allowed = [status.value for status in cls.get_allowed_next_statuses(from_status)]
        return False, f"Invalid transition from {from_status.value} to {to_status.value}. Allowed: {allowed}"


class VesselStatusFilter:
    """Utility class for filtering vessels by status."""
    
    @classmethod
    def get_optimizable_statuses(cls) -> Set[VesselStatus]:
        """Get statuses that should be included in optimization."""
        return {
            VesselStatus.APPROVED,
            VesselStatus.CONFIRMED,
            # Could include SCHEDULED for re-optimization
        }
    
    @classmethod
    def get_active_operational_statuses(cls) -> Set[VesselStatus]:
        """Get statuses for vessels currently in terminal operations."""
        return {
            VesselStatus.APPROACHING,
            VesselStatus.ARRIVED,
            VesselStatus.BERTHED,
            VesselStatus.IN_PROGRESS
        }
    
    @classmethod
    def get_attention_required_statuses(cls) -> Set[VesselStatus]:
        """Get statuses that require operator attention."""
        return {
            VesselStatus.NEEDS_INFO,
            VesselStatus.DELAYED,
            VesselStatus.WAITING,
            VesselStatus.SUSPENDED
        }
    
    @classmethod
    def get_completed_statuses(cls) -> Set[VesselStatus]:
        """Get statuses for vessels that have completed their visit."""
        return {
            VesselStatus.COMPLETED,
            VesselStatus.DEPARTED,
            VesselStatus.ARCHIVED
        }


# Legacy status mapping for backward compatibility
LEGACY_STATUS_MAPPING = {
    # Old status -> New status
    'ACTIVE': VesselStatus.APPROVED,
    'SCHEDULED': VesselStatus.SCHEDULED,
    'CANCELLED': VesselStatus.CANCELLED,
    'APPROACHING': VesselStatus.APPROACHING,
    'ARRIVED': VesselStatus.ARRIVED,
    'IN_PROGRESS': VesselStatus.IN_PROGRESS,
    'COMPLETED': VesselStatus.COMPLETED,
    'WAITING': VesselStatus.WAITING,
    'EN_ROUTE': VesselStatus.APPROACHING,
}

def migrate_legacy_status(legacy_status: str) -> VesselStatus:
    """Convert legacy status to new enhanced status."""
    if legacy_status in LEGACY_STATUS_MAPPING:
        return LEGACY_STATUS_MAPPING[legacy_status]
    
    # Try to match by enum value
    try:
        return VesselStatus(legacy_status.upper())
    except ValueError:
        logger.warning(f"Unknown legacy status: {legacy_status}, defaulting to SUBMITTED")
        return VesselStatus.SUBMITTED


def get_status_display_info(status: VesselStatus) -> Dict[str, str]:
    """Get display information for a status."""
    
    display_info = {
        VesselStatus.SUBMITTED: {
            "label": "Submitted",
            "description": "Nomination submitted, awaiting review",
            "color": "blue",
            "icon": "📝"
        },
        VesselStatus.UNDER_REVIEW: {
            "label": "Under Review", 
            "description": "Being validated by business rules",
            "color": "yellow",
            "icon": "🔍"
        },
        VesselStatus.APPROVED: {
            "label": "Approved",
            "description": "Ready for scheduling optimization", 
            "color": "green",
            "icon": "✅"
        },
        VesselStatus.SCHEDULED: {
            "label": "Scheduled",
            "description": "Time slot and jetty assigned",
            "color": "purple",
            "icon": "📅"
        },
        VesselStatus.CONFIRMED: {
            "label": "Confirmed",
            "description": "Vessel confirmed ETA and schedule",
            "color": "teal",
            "icon": "✔️"
        },
        VesselStatus.APPROACHING: {
            "label": "Approaching",
            "description": "En route to terminal",
            "color": "orange",
            "icon": "🚢"
        },
        VesselStatus.ARRIVED: {
            "label": "Arrived",
            "description": "At terminal, awaiting berth",
            "color": "blue",
            "icon": "🏁"
        },
        VesselStatus.BERTHED: {
            "label": "Berthed",
            "description": "Moored at assigned jetty",
            "color": "indigo",
            "icon": "⚓"
        },
        VesselStatus.IN_PROGRESS: {
            "label": "In Progress",
            "description": "Cargo operations active",
            "color": "green",
            "icon": "🔄"
        },
        VesselStatus.COMPLETED: {
            "label": "Completed",
            "description": "All operations finished",
            "color": "green",
            "icon": "✅"
        },
        VesselStatus.DEPARTED: {
            "label": "Departed",
            "description": "Left terminal",
            "color": "gray",
            "icon": "👋"
        },
        VesselStatus.CANCELLED: {
            "label": "Cancelled",
            "description": "Nomination cancelled",
            "color": "red",
            "icon": "❌"
        },
        VesselStatus.REJECTED: {
            "label": "Rejected",
            "description": "Nomination rejected",
            "color": "red", 
            "icon": "🚫"
        }
    }
    
    return display_info.get(status, {
        "label": status.value,
        "description": "Status description not available",
        "color": "gray",
        "icon": "❓"
    })
