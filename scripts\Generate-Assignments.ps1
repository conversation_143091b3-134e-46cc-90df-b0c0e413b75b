# Seed a mixed set of tankers and barges
$u = 'http://localhost:7000/api/nominations'
function New-Nom($name,$type,$len,$beam,$draft,$dwt,$etaIso,$prio,$product,$vol,$isLoading) {
  $body = [pscustomobject]@{
    name=$name; vessel_type=$type; length=$len; beam=$beam; draft=$draft; deadweight=$dwt; eta=$etaIso; priority=$prio;
    cargoes=@(@{ product=$product; volume=$vol; is_loading=$isLoading; tanks=@(); surveyor_required=$true; completed_volume=0 })
  }
  Invoke-RestMethod -Method Post -Uri $u -ContentType 'application/json' -Body ($body | ConvertTo-Json -Depth 6) | Out-Null
}
function IsoIn($hours) { (Get-Date).AddHours($hours).ToUniversalTime().ToString('o') }

# Tankers
New-Nom 'MT Hydro Alpha'   'tanker' 210 32 12.5 52000 (IsoIn 6)  2 'hydrocarbons'    36000 $true
New-Nom 'MT Minerals Echo' 'tanker' 180 28 10.5 30000 (IsoIn 18) 2 'minerals'        22000 $true
New-Nom 'MV Benzene Golf'  'tanker' 125 20  9.0 12000 (IsoIn 22) 2 'benzene'         18000 $true
New-Nom 'MT POX India'     'tanker' 175 27  9.8 28000 (IsoIn 20) 2 'propylene_oxide' 12000 $false
New-Nom 'MT Butane J'      'tanker' 140 23  9.5 18000 (IsoIn 16) 2 'butane'          12000 $false

# Barges
New-Nom 'Barge Minerals A' 'barge'  120 18 4.4 7000 (IsoIn 7)  1 'minerals'        8000 $true
New-Nom 'Barge Hydro C'    'barge'  130 19 4.5 7000 (IsoIn 15) 2 'hydrocarbons'     6000 $false
New-Nom 'Barge Benzene D'  'barge'  115 17 4.4 6500 (IsoIn 11) 2 'benzene'          7000 $false
New-Nom 'Barge POX E'      'barge'  110 16 4.3 6000 (IsoIn 13) 3 'propylene_oxide'  6000 $true

# Run presets (uses http://localhost:7000); ensure server is running
powershell -ExecutionPolicy Bypass -File .\scripts\Optimize-Presets.ps1 -FillUnassigned