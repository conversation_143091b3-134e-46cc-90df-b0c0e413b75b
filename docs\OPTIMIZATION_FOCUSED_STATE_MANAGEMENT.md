# Optimization-Focused State Management

## Core Purpose
**Generate optimal schedules with multiple runs and re-runs, while maintaining logs of all optimization decisions.**

## Simplified State Model for Optimization

### Essential States Only

```python
class OptimizationVesselStatus(Enum):
    """Optimization-focused vessel states."""
    
    # Input states
    NOMINATED = "NOMINATED"      # Just created, needs to be activated
    AVAILABLE = "AVAILABLE"      # Ready for optimization (unscheduled)
    
    # Optimization results
    SCHEDULED = "SCHEDULED"      # Assigned by optimizer, can be changed
    LOCKED = "LOCKED"           # Cannot be moved (docked/in-progress)
    
    # Terminal states
    COMPLETED = "COMPLETED"      # Operations finished
    CANCELLED = "CANCELLED"      # Removed from system
```

### State Purposes

| State | Purpose | Can Optimize? | Can Unschedule? |
|-------|---------|---------------|-----------------|
| `NOMINATED` | Just created | ❌ | ❌ |
| `AVAILABLE` | Ready for scheduling | ✅ | ❌ (already unscheduled) |
| `SCHEDULED` | Assigned by optimizer | ❌ (already scheduled) | ✅ |
| `LOCKED` | Docked/in-progress | ❌ | ❌ |
| `COMPLETED` | Finished | ❌ | ❌ |
| `CANCELLED` | Removed | ❌ | ❌ |

## Optimization Workflow

### 1. **Nomination → Available**
```python
# When nomination is created
vessel.status = "NOMINATED"  # Initial state
vessel.status = "AVAILABLE"  # Auto-activated for optimization
```

### 2. **Optimization Run**
```python
# Get vessels available for optimization
available_vessels = get_vessels_by_status(['AVAILABLE'])
locked_vessels = get_vessels_by_status(['LOCKED'])

# Run optimization
schedule = optimizer.optimize(available_vessels, preserve_locked=locked_vessels)

# Update statuses
for assignment in schedule.assignments:
    vessel.status = "SCHEDULED"
```

### 3. **Re-optimization**
```python
# Unschedule selected vessels
unschedule_vessels(['vessel_1', 'vessel_2'])  # SCHEDULED → AVAILABLE

# Run optimization again with new vessel mix
new_schedule = optimizer.optimize(get_available_vessels())
```

### 4. **Lock Vessels**
```python
# When vessel starts operations (external system integration)
vessel.status = "LOCKED"  # Cannot be moved in future optimizations
```

## Database Schema (Minimal Changes)

### Current Schema Works!
```sql
-- nominations table already has status column
-- assignments table tracks scheduled vessels
-- assignment_changes table logs all changes
```

### Simple Status Updates
```sql
-- Just update the status values we use
UPDATE nominations SET status = 'AVAILABLE' WHERE status = 'ACTIVE';
UPDATE nominations SET status = 'SCHEDULED' WHERE runtime_vessel_id IN (
    SELECT vessel_id FROM assignments WHERE status != 'CANCELLED'
);
```

## Key Functions for Your Use Case

### 1. **Get Optimizable Vessels**
```python
def get_vessels_for_optimization(terminal_id: str) -> List[VesselBase]:
    """Get all vessels that can be included in optimization."""
    
    # Get AVAILABLE nominations (unscheduled vessels)
    available_nominations = db.get_nominations(terminal_id, status='AVAILABLE')
    vessels = [nomination_to_vessel(nom) for nom in available_nominations]
    
    # Get CANCELLED assignments (unscheduled vessels)
    cancelled_assignments = db.get_assignments(terminal_id, status='CANCELLED')
    vessels.extend([assignment_to_vessel(assign) for assign in cancelled_assignments])
    
    return vessels
```

### 2. **Get Locked Vessels (Preserve in Optimization)**
```python
def get_locked_vessels(terminal_id: str) -> List[Assignment]:
    """Get vessels that cannot be moved (already docked/in-progress)."""
    
    # These are assignments that must be preserved
    return db.get_assignments(terminal_id, status=['IN_PROGRESS', 'LOCKED'])
```

### 3. **Schedule Vessels (After Optimization)**
```python
def apply_optimization_results(schedule: Schedule, optimization_run_id: str):
    """Apply optimization results and update vessel statuses."""
    
    for assignment in schedule.assignments:
        # Create assignment in database
        assignment_id = db.add_assignment(assignment.to_dict())
        
        # Update nomination status to SCHEDULED
        db.update_nomination_by_runtime_id(
            assignment.vessel_id, 
            {'status': 'SCHEDULED'}
        )
        
        # Log the optimization decision
        db.log_assignment_change(
            assignment_id=assignment_id,
            new_start_time=assignment.start_time,
            new_end_time=assignment.end_time,
            reason=f"Optimization run {optimization_run_id}",
            vessel_id=assignment.vessel_id,
            vessel_name=assignment.vessel_name,
            jetty_name=assignment.jetty_name,
            changed_by="optimizer",
            terminal_id=assignment.terminal_id
        )
```

### 4. **Unschedule for Re-optimization**
```python
def unschedule_vessels_for_reoptimization(vessel_ids: List[str], reason: str):
    """Unschedule vessels to make them available for re-optimization."""
    
    for vessel_id in vessel_ids:
        # Cancel existing assignments
        assignments = db.get_assignments_by_vessel(vessel_id, status='ACTIVE')
        for assignment in assignments:
            db.update_assignment(assignment['id'], {'status': 'CANCELLED'})
            
            # Log the unscheduling
            db.log_assignment_change(
                assignment_id=assignment['id'],
                old_start_time=assignment['start_time'],
                old_end_time=assignment['end_time'],
                reason=f"Unscheduled for re-optimization: {reason}",
                vessel_id=vessel_id,
                changed_by="user",
                terminal_id=assignment['terminal_id']
            )
        
        # Update nomination status back to AVAILABLE
        db.update_nomination_by_runtime_id(vessel_id, {'status': 'AVAILABLE'})
```

## Logging for Optimization Runs

### Enhanced Optimization Logging
```python
class OptimizationRunLogger:
    """Log detailed information about optimization runs."""
    
    def log_optimization_start(self, run_id: str, parameters: dict, vessels: List[VesselBase]):
        """Log the start of an optimization run."""
        db.log_assignment_change(
            assignment_id=0,  # System event
            reason=f"Optimization run {run_id} started with {len(vessels)} vessels",
            vessel_id="SYSTEM",
            vessel_name="Optimization Engine",
            changed_by="optimizer",
            terminal_id=db.get_active_terminal_id()
        )
        
        # Log detailed parameters
        db.log_change_analysis(
            assignment_id=0,
            change_type='optimization_started',
            reason_text=f"Run {run_id}",
            original_value=json.dumps({"vessel_count": len(vessels)}),
            new_value=json.dumps(parameters),
            changed_by="optimizer"
        )
    
    def log_optimization_result(self, run_id: str, schedule: Schedule, 
                              vessels_scheduled: int, objective_value: float):
        """Log optimization results."""
        db.log_assignment_change(
            assignment_id=0,
            reason=f"Optimization run {run_id} completed: {vessels_scheduled} vessels scheduled, objective: {objective_value}",
            vessel_id="SYSTEM",
            vessel_name="Optimization Engine", 
            changed_by="optimizer",
            terminal_id=db.get_active_terminal_id()
        )
```

## API Enhancements for Your Workflow

### 1. **Bulk Unschedule for Re-optimization**
```python
@app.post("/api/optimization/unschedule")
async def unschedule_for_reoptimization(request: UnscheduleRequest):
    """Unschedule selected vessels for re-optimization."""
    
    unschedule_vessels_for_reoptimization(
        vessel_ids=request.vessel_ids,
        reason=request.reason or "Preparing for re-optimization"
    )
    
    return {"success": True, "unscheduled_count": len(request.vessel_ids)}
```

### 2. **Optimization Run with Tracking**
```python
@app.post("/api/optimization/run")
async def run_tracked_optimization(request: OptimizationRequest):
    """Run optimization with comprehensive logging."""
    
    run_id = f"OPT_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Get vessels for optimization
    available_vessels = get_vessels_for_optimization(terminal_id)
    locked_vessels = get_locked_vessels(terminal_id)
    
    # Log optimization start
    logger.log_optimization_start(run_id, request.parameters, available_vessels)
    
    # Run optimization
    schedule = optimizer.optimize(available_vessels, preserve_locked=locked_vessels)
    
    # Apply results
    apply_optimization_results(schedule, run_id)
    
    # Log results
    logger.log_optimization_result(run_id, schedule, len(schedule.assignments), schedule.objective_value)
    
    return {
        "run_id": run_id,
        "assignments": schedule.assignments,
        "objective_value": schedule.objective_value,
        "vessels_scheduled": len(schedule.assignments)
    }
```

## What This Gives You

### ✅ **Multiple Optimization Runs**
- Vessels can be easily unscheduled and re-optimized
- Locked vessels are preserved across runs
- Clear status tracking for each vessel

### ✅ **Comprehensive Logging**
- Every optimization run is logged with parameters
- All schedule changes are tracked with reasons
- Complete audit trail for analysis

### ✅ **Flexibility**
- Easy to add new vessels (nominations)
- Simple to remove vessels (cancellation)
- Supports partial re-optimization (unschedule subset)

### ✅ **Integration Ready**
- Works with your existing terminal management system
- External systems can update vessel statuses (lock when docked)
- APIs support bulk operations for efficiency

## Migration from Current System

### Zero Disruption Migration
```python
# Current status mapping (no database changes needed)
STATUS_MAPPING = {
    'pending': 'NOMINATED',    # Just created
    'ACTIVE': 'AVAILABLE',     # Ready for optimization  
    'SCHEDULED': 'SCHEDULED',  # Assigned by optimizer
    'CANCELLED': 'AVAILABLE'   # Back to available pool
}
```

This focused approach gives you exactly what you need for optimization-focused scheduling while maintaining all the logging and flexibility you require!
