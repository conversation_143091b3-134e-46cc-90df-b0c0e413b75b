"""
Terminal Manager

This module provides functionality to manage multiple terminals,
handle terminal switching, and coordinate operations across terminals.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from .terminal_config import TerminalConfig, terminal_registry
from .terminal import Terminal
from .vessel import Vessel
from .schedule import Schedule

logger = logging.getLogger(__name__)


class TerminalManager:
    """Manages multiple terminals and their operations"""
    
    def __init__(self):
        self._active_terminal_id: Optional[str] = None
        self._terminal_instances: Dict[str, Terminal] = {}
        self._terminal_data: Dict[str, Dict[str, Any]] = {}
        
        # Initialize with registered terminals
        for terminal_config in terminal_registry.list_terminals():
            self._initialize_terminal(terminal_config)
            
        # Set first terminal as active if none is set
        if not self._active_terminal_id and self._terminal_instances:
            first_terminal_id = list(self._terminal_instances.keys())[0]
            self.set_active_terminal(first_terminal_id)
    
    def _initialize_terminal(self, config: TerminalConfig) -> None:
        """Initialize a terminal instance from configuration"""
        try:
            # Convert config to Terminal model (you'll need to implement this conversion)
            terminal = self._config_to_terminal(config)
            self._terminal_instances[config.terminal_id] = terminal
            
            # Initialize empty data structures for this terminal
            self._terminal_data[config.terminal_id] = {
                "vessels": [],
                "schedule": None,
                "last_updated": datetime.now(),
                "optimization_history": [],
                "settings": {}
            }
            
            logger.info(f"Initialized terminal: {config.name} ({config.terminal_id})")
            
        except Exception as e:
            logger.error(f"Failed to initialize terminal {config.terminal_id}: {str(e)}")
            raise
    
    def _config_to_terminal(self, config: TerminalConfig) -> Terminal:
        """Convert TerminalConfig to Terminal model"""
        # This is a simplified conversion - you may need to enhance this
        # based on your existing Terminal model structure
        
        from .terminal import Jetty, JettyType, LoadingArm, Tank, Pump, Surveyor
        
        # Convert berths to jetties
        jetties = []
        
        # Add vessel berths
        for berth in config.vessel_berths:
            jetty = Jetty(
                id=berth.id,
                name=berth.name,
                jetty_type=JettyType.VESSEL_BERTH,
                max_length=berth.max_length,
                max_draft=berth.max_draft,
                max_deadweight=berth.max_dwt or 100000,
                loading_arms=[
                    LoadingArm(
                        id=f"{berth.id}_LA{i+1}",
                        name=f"{berth.name} Loading Arm {i+1}",
                        flow_rate=1000.0,
                        compatible_products=config.products
                    ) for i in range(berth.loading_arms)
                ],
                connected_tanks=berth.connected_tanks,
                connected_pumps=berth.connected_pumps
            )
            jetties.append(jetty)
        
        # Add barge berths
        for berth in config.barge_berths:
            jetty = Jetty(
                id=berth.id,
                name=berth.name,
                jetty_type=JettyType.BARGE_BERTH,
                max_length=berth.max_length,
                max_draft=berth.max_draft,
                max_deadweight=berth.max_dwt or 5000,
                loading_arms=[
                    LoadingArm(
                        id=f"{berth.id}_LA1",
                        name=f"{berth.name} Loading Arm 1",
                        flow_rate=500.0,
                        compatible_products=config.products
                    )
                ],
                connected_tanks=berth.connected_tanks,
                connected_pumps=berth.connected_pumps
            )
            jetties.append(jetty)
        
        # Convert tank configs to Tank models
        tanks = []
        for tank_config in config.tanks:
            tank = Tank(
                id=tank_config.id,
                name=tank_config.name,
                capacity=tank_config.capacity_cbm,
                current_volume=tank_config.current_level,
                product_type=tank_config.products[0] if tank_config.products else "unknown",
                is_floating_roof="inner_floating_roof" in [f.value for f in tank_config.features]
            )
            tanks.append(tank)
        
        # Convert pump configs to Pump models  
        pumps = []
        for pump_config in config.pumps:
            pump = Pump(
                id=pump_config.id,
                name=pump_config.name,
                flow_rate=pump_config.flow_rate_cbm_per_hour,
                compatible_products=["gasoline", "gasoil", "jetfuel", "biofuel"]  # Default compatible products
            )
            pumps.append(pump)
        
        # Create basic surveyors (you may want to make this configurable)
        surveyors = [
            Surveyor(
                id="S001",
                name="Terminal Surveyor 1",
                availability={},
                specialization=set(config.products)
            ),
            Surveyor(
                id="S002", 
                name="Terminal Surveyor 2",
                availability={},
                specialization=set(config.products)
            )
        ]
        
        return Terminal(
            name=config.name,
            jetties=jetties,
            tanks=tanks,
            pumps=pumps,
            surveyors=surveyors,
            location=config.location
        )
    
    def get_active_terminal(self) -> Optional[Terminal]:
        """Get the currently active terminal"""
        if self._active_terminal_id:
            return self._terminal_instances.get(self._active_terminal_id)
        return None
    
    def get_active_terminal_id(self) -> Optional[str]:
        """Get the ID of the currently active terminal"""
        return self._active_terminal_id
    
    def get_active_terminal_config(self) -> Optional[TerminalConfig]:
        """Get the configuration of the currently active terminal"""
        if self._active_terminal_id:
            return terminal_registry.get_terminal(self._active_terminal_id)
        return None
    
    def set_active_terminal(self, terminal_id: str) -> bool:
        """Set the active terminal"""
        if terminal_id in self._terminal_instances:
            old_terminal = self._active_terminal_id
            self._active_terminal_id = terminal_id
            terminal_registry.set_active_terminal(terminal_id)
            
            logger.info(f"Switched active terminal from {old_terminal} to {terminal_id}")
            return True
        else:
            logger.error(f"Terminal {terminal_id} not found")
            return False
    
    def get_terminal(self, terminal_id: str) -> Optional[Terminal]:
        """Get a specific terminal by ID"""
        return self._terminal_instances.get(terminal_id)
    
    def get_terminal_config(self, terminal_id: str) -> Optional[TerminalConfig]:
        """Get terminal configuration by ID"""
        return terminal_registry.get_terminal(terminal_id)
    
    def list_terminals(self) -> List[Dict[str, Any]]:
        """List all available terminals with their basic info"""
        terminals = []
        for terminal_id, terminal in self._terminal_instances.items():
            config = terminal_registry.get_terminal(terminal_id)
            terminals.append({
                "id": terminal_id,
                "name": terminal.name,
                "location": terminal.location,
                "vessel_berths": len([j for j in terminal.jetties if j.jetty_type.value == "vessel_berth"]),
                "barge_berths": len([j for j in terminal.jetties if j.jetty_type.value == "barge_berth"]),
                "total_capacity": config.total_capacity_cbm if config else 0,
                "operational_since": config.operational_since if config else None,
                "is_active": terminal_id == self._active_terminal_id
            })
        return terminals
    
    def get_terminal_vessels(self, terminal_id: Optional[str] = None) -> List[Vessel]:
        """Get vessels for a specific terminal (or active terminal if not specified)"""
        if terminal_id is None:
            terminal_id = self._active_terminal_id
            
        if terminal_id and terminal_id in self._terminal_data:
            return self._terminal_data[terminal_id]["vessels"]
        return []
    
    def set_terminal_vessels(self, vessels: List[Vessel], terminal_id: Optional[str] = None) -> None:
        """Set vessels for a specific terminal"""
        if terminal_id is None:
            terminal_id = self._active_terminal_id
            
        if terminal_id and terminal_id in self._terminal_data:
            self._terminal_data[terminal_id]["vessels"] = vessels
            self._terminal_data[terminal_id]["last_updated"] = datetime.now()
    
    def get_terminal_schedule(self, terminal_id: Optional[str] = None) -> Optional[Schedule]:
        """Get schedule for a specific terminal"""
        if terminal_id is None:
            terminal_id = self._active_terminal_id
            
        if terminal_id and terminal_id in self._terminal_data:
            return self._terminal_data[terminal_id]["schedule"]
        return None
    
    def set_terminal_schedule(self, schedule: Schedule, terminal_id: Optional[str] = None) -> None:
        """Set schedule for a specific terminal"""
        if terminal_id is None:
            terminal_id = self._active_terminal_id
            
        if terminal_id and terminal_id in self._terminal_data:
            self._terminal_data[terminal_id]["schedule"] = schedule
            self._terminal_data[terminal_id]["last_updated"] = datetime.now()
    
    def add_optimization_result(self, result: Dict[str, Any], terminal_id: Optional[str] = None) -> None:
        """Add optimization result to terminal history"""
        if terminal_id is None:
            terminal_id = self._active_terminal_id
            
        if terminal_id and terminal_id in self._terminal_data:
            self._terminal_data[terminal_id]["optimization_history"].append({
                "timestamp": datetime.now(),
                "result": result
            })
    
    def get_optimization_history(self, terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get optimization history for a terminal"""
        if terminal_id is None:
            terminal_id = self._active_terminal_id
            
        if terminal_id and terminal_id in self._terminal_data:
            return self._terminal_data[terminal_id]["optimization_history"]
        return []
    
    def get_terminal_statistics(self, terminal_id: Optional[str] = None) -> Dict[str, Any]:
        """Get statistics for a terminal"""
        if terminal_id is None:
            terminal_id = self._active_terminal_id
            
        if not terminal_id or terminal_id not in self._terminal_instances:
            return {}
        
        terminal = self._terminal_instances[terminal_id]
        config = terminal_registry.get_terminal(terminal_id)
        vessels = self.get_terminal_vessels(terminal_id)
        schedule = self.get_terminal_schedule(terminal_id)
        
        return {
            "terminal_id": terminal_id,
            "name": terminal.name,
            "jetties": len(terminal.jetties),
            "tanks": len(terminal.tanks),
            "pumps": len(terminal.pumps),
            "total_capacity": config.total_capacity_cbm if config else 0,
            "current_vessels": len(vessels),
            "active_assignments": len(schedule.assignments) if schedule else 0,
            "last_updated": self._terminal_data.get(terminal_id, {}).get("last_updated"),
            "optimization_runs": len(self.get_optimization_history(terminal_id))
        }
    
    def compare_terminals(self, terminal_ids: List[str]) -> Dict[str, Any]:
        """Compare multiple terminals"""
        comparison = {
            "terminals": {},
            "summary": {}
        }
        
        total_capacity = 0
        total_jetties = 0
        total_vessels = 0
        
        for terminal_id in terminal_ids:
            if terminal_id in self._terminal_instances:
                stats = self.get_terminal_statistics(terminal_id)
                comparison["terminals"][terminal_id] = stats
                
                total_capacity += stats.get("total_capacity", 0)
                total_jetties += stats.get("jetties", 0)
                total_vessels += stats.get("current_vessels", 0)
        
        comparison["summary"] = {
            "compared_terminals": len(terminal_ids),
            "total_capacity": total_capacity,
            "total_jetties": total_jetties,
            "total_vessels": total_vessels,
            "comparison_date": datetime.now()
        }
        
        return comparison


# Global terminal manager instance
terminal_manager = TerminalManager() 