"""
Vessel Service - Database-First Vessel Operations

This service encapsulates all vessel operations and provides a database-first
approach to vessel management, replacing the in-memory state pattern.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from src.database import Database
from src.models.vessel import VesselBase, VesselType, Cargo

logger = logging.getLogger(__name__)


class VesselService:
    """
    Service class for vessel operations using database-first approach.
    
    This service provides a single source of truth for vessel data,
    combining nominations and unscheduled assignments into available vessels.
    """
    
    def __init__(self, database: Database):
        self.db = database
    
    def get_available_vessels(self, terminal_id: Optional[str] = None) -> List[VesselBase]:
        """
        Get all vessels available for optimization from database.
        
        This combines:
        1. Active nominations (vessels waiting to be scheduled)
        2. Cancelled assignments (unscheduled vessels)
        
        Args:
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            List of VesselBase objects ready for optimization
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            logger.warning("No active terminal found")
            return []
        
        vessels = []
        
        try:
            # Get active nominations
            nominations = self.db.get_nominations(terminal_id, status="ACTIVE") or []
            logger.info(f"Found {len(nominations)} active nominations")
            
            for nomination in nominations:
                try:
                    logger.debug(f"Processing nomination {nomination.get('id')}: {type(nomination)}")
                    vessel = self._nomination_to_vessel(nomination)
                    vessels.append(vessel)
                    logger.info(f"Successfully converted nomination {nomination.get('id')} to vessel {vessel.id}")
                except Exception as e:
                    logger.error(f"Failed to convert nomination {nomination.get('id')} to vessel: {e}")
                    logger.debug(f"Nomination data: {nomination}")
                    continue
            
            # Get cancelled assignments (unscheduled vessels)
            cancelled_assignments = self.db.get_assignments(terminal_id)
            cancelled_assignments = [a for a in cancelled_assignments if a.get('status', '').upper() == 'CANCELLED']
            logger.info(f"Found {len(cancelled_assignments)} cancelled assignments to convert")
            
            vessel_ids_seen = {vessel.id for vessel in vessels}
            
            for assignment in cancelled_assignments:
                try:
                    vessel_id = str(assignment.get('vessel_id', ''))
                    
                    # Skip if we already have this vessel or if vessel_id is invalid
                    if vessel_id in vessel_ids_seen or not vessel_id or vessel_id == 'vessel_id':
                        continue
                    
                    vessel = self._assignment_to_vessel(assignment)
                    vessels.append(vessel)
                    vessel_ids_seen.add(vessel_id)
                    
                except Exception as e:
                    logger.warning(f"Failed to convert cancelled assignment {assignment.get('id')} to vessel: {e}")
                    continue
            
            logger.info(f"Total available vessels: {len(vessels)}")
            return vessels
            
        except Exception as e:
            logger.error(f"Error getting available vessels: {e}")
            return []
    
    def get_vessel_by_id(self, vessel_id: str, terminal_id: Optional[str] = None) -> Optional[VesselBase]:
        """
        Get a specific vessel by ID from database sources.
        
        Args:
            vessel_id: Vessel ID to search for
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            VesselBase object if found, None otherwise
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            return None
        
        try:
            # First check nominations
            nominations = self.db.get_nominations(terminal_id)
            for nomination in nominations or []:
                if str(nomination.get('runtime_vessel_id')) == str(vessel_id):
                    return self._nomination_to_vessel(nomination)
            
            # Then check cancelled assignments
            assignments = self.db.get_assignments(terminal_id)
            for assignment in assignments or []:
                if (assignment.get('status', '').upper() == 'CANCELLED' and 
                    str(assignment.get('vessel_id')) == str(vessel_id)):
                    return self._assignment_to_vessel(assignment)
            
            # Finally check database vessels
            try:
                vessel_id_int = int(vessel_id)
                vessel_data = self.db.get_vessel(vessel_id_int, terminal_id)
                if vessel_data:
                    return self._db_vessel_to_vessel(vessel_data)
            except (ValueError, TypeError):
                pass
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting vessel {vessel_id}: {e}")
            return None
    
    def add_vessel_nomination(self, vessel_data: Dict[str, Any], terminal_id: Optional[str] = None) -> str:
        """
        Add a new vessel nomination to the database.
        
        Args:
            vessel_data: Dictionary containing vessel information
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            Runtime vessel ID of the created nomination
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            raise ValueError("No active terminal found")
        
        try:
            # Add nomination to database
            nomination_id = self.db.add_nomination({
                **vessel_data,
                'terminal_id': terminal_id,
                'status': 'ACTIVE'
            })
            
            logger.info(f"Created vessel nomination {nomination_id} for vessel {vessel_data.get('runtime_vessel_id')}")
            
            # Log for analytics
            try:
                self.db.log_assignment_change(
                    assignment_id=0,
                    old_start_time=None,
                    old_end_time=None,
                    new_start_time=vessel_data.get('eta'),
                    new_end_time=vessel_data.get('etd'),
                    reason="New vessel nomination created via VesselService",
                    vessel_id=vessel_data.get('runtime_vessel_id'),
                    vessel_name=vessel_data.get('name'),
                    jetty_name=None,
                    changed_by="user",
                    terminal_id=terminal_id
                )
            except Exception as log_e:
                logger.warning(f"Failed to log nomination creation: {log_e}")
            
            return vessel_data.get('runtime_vessel_id')
            
        except Exception as e:
            logger.error(f"Error creating vessel nomination: {e}")
            raise
    
    def delete_vessel(self, vessel_id: str, terminal_id: Optional[str] = None) -> bool:
        """
        Delete a vessel from all database sources.
        
        Args:
            vessel_id: Vessel ID to delete
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            True if vessel was deleted, False if not found
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            return False
        
        try:
            # Get vessel info before deletion for logging
            vessel_info = self.get_vessel_by_id(vessel_id, terminal_id)
            
            # Try to delete from database
            deleted = self.db.delete_vessel(vessel_id)
            
            if deleted and vessel_info:
                # Log deletion for analytics
                try:
                    self.db.log_assignment_change(
                        assignment_id=0,
                        old_start_time=None,
                        old_end_time=None,
                        new_start_time=None,
                        new_end_time=None,
                        reason="Vessel deleted via VesselService",
                        vessel_id=vessel_id,
                        vessel_name=vessel_info.name,
                        jetty_name=None,
                        changed_by="user",
                        terminal_id=terminal_id
                    )
                except Exception as log_e:
                    logger.warning(f"Failed to log vessel deletion: {log_e}")
            
            return deleted
            
        except Exception as e:
            logger.error(f"Error deleting vessel {vessel_id}: {e}")
            return False
    
    def schedule_vessel(self, vessel_id: str, assignment_data: Dict[str, Any], terminal_id: Optional[str] = None) -> int:
        """
        Move vessel from nomination to scheduled assignment atomically.
        
        Args:
            vessel_id: Vessel ID to schedule
            assignment_data: Assignment details
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            Assignment ID of created assignment
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            raise ValueError("No active terminal found")
        
        try:
            # Create assignment
            assignment_id = self.db.add_assignment({
                **assignment_data,
                'terminal_id': terminal_id
            })
            
            # Mark nomination as scheduled if it exists
            try:
                nominations = self.db.get_nominations(terminal_id)
                for nomination in nominations or []:
                    if str(nomination.get('runtime_vessel_id')) == str(vessel_id):
                        # Update nomination status to indicate it's been scheduled
                        self.db.update_nomination(nomination.get('id'), {'status': 'SCHEDULED'})
                        logger.info(f"Updated nomination {nomination.get('id')} status to SCHEDULED")
                        break
            except Exception as e:
                logger.warning(f"Could not update nomination status for vessel {vessel_id}: {e}")
            
            # Log for analytics
            try:
                self.db.log_assignment_change(
                    assignment_id=assignment_id,
                    old_start_time=None,
                    old_end_time=None,
                    new_start_time=assignment_data.get('start_time'),
                    new_end_time=assignment_data.get('end_time'),
                    reason="Vessel scheduled from nomination via VesselService",
                    vessel_id=vessel_id,
                    vessel_name=assignment_data.get('vessel_name', 'Unknown'),
                    jetty_name=assignment_data.get('jetty_name'),
                    changed_by="system",
                    terminal_id=terminal_id
                )
            except Exception as log_e:
                logger.warning(f"Failed to log vessel scheduling: {log_e}")
            
            logger.info(f"Scheduled vessel {vessel_id} to assignment {assignment_id}")
            return assignment_id
            
        except Exception as e:
            logger.error(f"Error scheduling vessel {vessel_id}: {e}")
            raise
    
    def unschedule_vessel(self, assignment_id: int, terminal_id: Optional[str] = None) -> bool:
        """
        Move vessel back to available nominations by cancelling assignment.
        
        Args:
            assignment_id: Assignment ID to unschedule
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            True if successfully unscheduled
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            return False
        
        try:
            # Cancel the assignment
            success = self.db.update_assignment(assignment_id, {'status': 'CANCELLED'})
            
            if success:
                logger.info(f"Unscheduled assignment {assignment_id}")
                
                # The vessel will automatically become available again through
                # get_available_vessels() which includes cancelled assignments
                
            return success
            
        except Exception as e:
            logger.error(f"Error unscheduling assignment {assignment_id}: {e}")
            return False
    
    def _nomination_to_vessel(self, nomination: Dict[str, Any]) -> VesselBase:
        """Convert nomination dictionary to VesselBase object."""
        try:
            logger.debug(f"Converting nomination to vessel: ID={nomination.get('id')}, runtime_vessel_id={nomination.get('runtime_vessel_id')}")
            
            vessel_type_str = nomination.get('vessel_type', 'tanker').lower()
            vessel_type = VesselType(vessel_type_str) if vessel_type_str in ['tanker', 'barge', 'cargo'] else VesselType.tanker
            
            # Create cargoes from nomination data
            cargoes = []
            cargo_list = nomination.get('cargoes', [])
            
            # Handle different cargo data formats
            if isinstance(cargo_list, str):
                # If cargoes is a JSON string, try to parse it
                try:
                    import json
                    cargo_list = json.loads(cargo_list)
                except:
                    cargo_list = []
            
            for cargo_data in cargo_list:
                try:
                    # Handle both dict and object formats
                    if hasattr(cargo_data, 'get'):
                        cargo_dict = {
                            'product': cargo_data.get('product', 'Unknown'),
                            'volume': float(cargo_data.get('volume', 0) or 0),
                            'is_loading': bool(cargo_data.get('is_loading', True))
                        }
                    else:
                        # Handle SQLAlchemy object or other formats
                        cargo_dict = {
                            'product': getattr(cargo_data, 'product', 'Unknown'),
                            'volume': float(getattr(cargo_data, 'volume', 0) or 0),
                            'is_loading': bool(getattr(cargo_data, 'is_loading', True))
                        }
                    
                    cargo = Cargo.from_dict(cargo_dict)
                    cargoes.append(cargo)
                except Exception as e:
                    logger.warning(f"Failed to create cargo from nomination data: {e}")
                    continue
            
            vessel = VesselBase(
                id=nomination.get('runtime_vessel_id'),
                name=nomination.get('name', 'Unknown Vessel'),
                vessel_type=vessel_type,
                length=float(nomination.get('length', 150.0) or 150.0),
                beam=float(nomination.get('beam', 25.0) or 25.0),
                draft=float(nomination.get('draft', 10.0) or 10.0),
                deadweight=float(nomination.get('deadweight', 50000.0) or 50000.0),
                cargoes=cargoes,
                status="APPROACHING",
                priority=int(nomination.get('priority', 1) or 1),
                capacity=nomination.get('capacity'),
                width=nomination.get('width'),
                customer=nomination.get('customer'),
                metadata=self._safe_merge_metadata({
                    'source': 'nomination',
                    'nomination_id': nomination.get('id'),
                    'mmsi': nomination.get('mmsi'),
                    'imo': nomination.get('imo')
                }, nomination.get('metadata'))
            )
            
            # Set timing using standardized ETA fields
            vessel.eta = self._parse_datetime(nomination.get('eta'))
            vessel.etd = self._parse_datetime(nomination.get('etd'))
            vessel.calculated_eta = self._parse_datetime(nomination.get('calculated_eta'))
            vessel.eta_confidence = nomination.get('eta_confidence', 50)
            vessel.eta_source = nomination.get('eta_source', 'user')
            
            return vessel
            
        except Exception as e:
            logger.error(f"Error converting nomination to vessel: {e}")
            raise
    
    def _assignment_to_vessel(self, assignment: Dict[str, Any]) -> VesselBase:
        """Convert cancelled assignment dictionary to VesselBase object."""
        try:
            vessel_type_str = assignment.get('vessel_type', 'tanker').lower()
            vessel_type = VesselType(vessel_type_str) if vessel_type_str in ['tanker', 'barge', 'cargo'] else VesselType.tanker
            
            # Create cargo from assignment data
            cargoes = []
            if assignment.get('cargo_product') and assignment.get('cargo_volume'):
                try:
                    cargo = Cargo.from_dict({
                        'product': assignment.get('cargo_product', 'Unknown'),
                        'volume': float(assignment.get('cargo_volume', 0) or 0),
                        'is_loading': True  # Assume loading for unscheduled vessels
                    })
                    cargoes.append(cargo)
                except Exception as e:
                    logger.warning(f"Failed to create cargo from assignment data: {e}")
            
            vessel = VesselBase(
                id=assignment.get('vessel_id'),
                name=assignment.get('vessel_name', 'Unknown Vessel'),
                vessel_type=vessel_type,
                length=150.0,  # Default dimensions
                beam=25.0,
                draft=10.0,
                deadweight=50000.0,
                cargoes=cargoes,
                status="APPROACHING",  # Make available for optimization
                priority=1,  # Default priority
                capacity=assignment.get('cargo_volume', 50000),
                metadata={
                    'source': 'unscheduled_assignment',
                    'original_assignment_id': assignment.get('id'),
                    'original_jetty': assignment.get('jetty_name')
                }
            )
            
            return vessel
            
        except Exception as e:
            logger.error(f"Error converting assignment to vessel: {e}")
            raise
    
    def _db_vessel_to_vessel(self, vessel_data: Dict[str, Any]) -> VesselBase:
        """Convert database vessel dictionary to VesselBase object."""
        try:
            vessel_type = VesselType(vessel_data.get('type', 'tanker').lower())
            
            # Get cargoes for this vessel
            cargoes = []
            vessel_id = vessel_data.get('id')
            if vessel_id:
                try:
                    cargo_data_list = self.db.get_cargoes_by_vessel(vessel_id)
                    for cargo_data in cargo_data_list:
                        cargo = Cargo.from_dict({
                            'product': cargo_data.get('product', 'Unknown'),
                            'volume': float(cargo_data.get('volume', 0) or 0),
                            'is_loading': bool(cargo_data.get('is_loading', True))
                        })
                        cargoes.append(cargo)
                except Exception as e:
                    logger.warning(f"Failed to load cargoes for vessel {vessel_id}: {e}")
            
            vessel = VesselBase(
                id=str(vessel_data.get('id')),
                name=vessel_data.get('name', 'Unknown Vessel'),
                vessel_type=vessel_type,
                length=150.0,  # Default dimensions
                beam=25.0,
                draft=10.0,
                deadweight=50000.0,
                cargoes=cargoes,
                status=vessel_data.get('status', 'APPROACHING'),
                priority=1,  # Default priority
                capacity=vessel_data.get('total_cargo_volume', 50000),
                metadata={
                    'source': 'database_vessel',
                    'database_id': vessel_data.get('id')
                }
            )
            
            return vessel
            
        except Exception as e:
            logger.error(f"Error converting database vessel to vessel: {e}")
            raise
    
    def _safe_merge_metadata(self, base_metadata: Dict[str, Any], additional_metadata: Any) -> Dict[str, Any]:
        """Safely merge metadata from different sources."""
        result = base_metadata.copy()
        
        if additional_metadata is None:
            return result
        
        try:
            # Handle different metadata formats
            if isinstance(additional_metadata, dict):
                result.update(additional_metadata)
            elif hasattr(additional_metadata, '__dict__'):
                # Handle SQLAlchemy objects or similar
                for key, value in additional_metadata.__dict__.items():
                    if not key.startswith('_'):  # Skip private attributes
                        result[key] = value
            elif isinstance(additional_metadata, str):
                # Handle JSON string metadata
                try:
                    import json
                    parsed_metadata = json.loads(additional_metadata)
                    if isinstance(parsed_metadata, dict):
                        result.update(parsed_metadata)
                except:
                    # If it's not valid JSON, store as string
                    result['raw_metadata'] = additional_metadata
            else:
                # Store unknown format as string representation
                result['raw_metadata'] = str(additional_metadata)
                
        except Exception as e:
            logger.warning(f"Could not merge metadata: {e}")
            result['metadata_error'] = str(e)
        
        return result

    def _parse_datetime(self, value: Any):
        """Parse various datetime representations into a timezone-aware datetime.
        Returns None if parsing fails.
        """
        try:
            if not value:
                return None
            if isinstance(value, datetime):
                return value if value.tzinfo else value.replace(tzinfo=timezone.utc)
            if isinstance(value, str):
                s = value.strip()
                # Handle trailing 'Z' (UTC) common in ISO strings
                if s.endswith('Z'):
                    s = s[:-1] + '+00:00'
                dt = datetime.fromisoformat(s)
                return dt if dt.tzinfo else dt.replace(tzinfo=timezone.utc)
        except Exception:
            pass
        return None


