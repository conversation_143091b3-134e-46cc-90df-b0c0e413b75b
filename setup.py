from setuptools import setup, find_packages

setup(
    name="jetty-planning",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "fastapi>=0.68.0",
        "uvicorn>=0.15.0",
        "pydantic>=1.8.0",
        "ortools>=9.3.0",
        "pandas>=2.0.0",
        "python-dateutil>=2.8.2",
        "requests>=2.26.0",
        "jinja2>=3.0.0",
        "python-dotenv>=0.19.0",
        "pytest>=6.2.5",
        "pytest-cov>=2.12.1",
        "black>=21.7b0",
        "flake8>=3.9.2",
        "mypy>=0.910",
        "isort>=5.9.3",
        "pre-commit>=2.15.0",
    ],
    python_requires=">=3.8",
) 