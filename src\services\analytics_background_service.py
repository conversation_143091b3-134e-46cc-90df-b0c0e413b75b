"""
Background service for analytics data processing and metric calculations.
Handles periodic data aggregation and performance monitoring.
"""

import logging
import asyncio
from datetime import datetime, date, timedelta
from typing import Dict, Any

from ..database import get_database

logger = logging.getLogger(__name__)


class AnalyticsBackgroundService:
    """Background service for analytics processing"""
    
    def __init__(self):
        self.db = get_database()
        self.running = False
        
    async def start(self):
        """Start the background analytics processing"""
        if self.running:
            logger.warning("Analytics background service is already running")
            return
            
        self.running = True
        logger.info("Starting analytics background service")
        
        # Start background tasks
        asyncio.create_task(self._daily_metrics_calculator())
        asyncio.create_task(self._performance_monitor())
        
    async def stop(self):
        """Stop the background analytics processing"""
        self.running = False
        logger.info("Stopping analytics background service")
        
    async def _daily_metrics_calculator(self):
        """Calculate daily planning metrics for the past few days"""
        while self.running:
            try:
                # Calculate metrics for yesterday and today
                today = date.today()
                yesterday = today - timedelta(days=1)
                
                terminal_id = self.db.get_active_terminal_id()
                if terminal_id:
                    # Calculate metrics for yesterday (complete data)
                    success_yesterday = self.db.calculate_daily_planning_metrics(yesterday, terminal_id)
                    if success_yesterday:
                        logger.info(f"Calculated daily metrics for {yesterday}")
                    
                    # Calculate metrics for today (partial data)
                    success_today = self.db.calculate_daily_planning_metrics(today, terminal_id)
                    if success_today:
                        logger.info(f"Updated daily metrics for {today}")
                
                # Wait 1 hour before next calculation
                await asyncio.sleep(3600)
                
            except Exception as e:
                logger.error(f"Error in daily metrics calculator: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
                
    async def _performance_monitor(self):
        """Monitor performance metrics and create alerts"""
        while self.running:
            try:
                await self._check_ml_accuracy_alerts()
                await self._check_change_frequency_alerts()
                
                # Wait 30 minutes before next check
                await asyncio.sleep(1800)
                
            except Exception as e:
                logger.error(f"Error in performance monitor: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
                
    async def _check_ml_accuracy_alerts(self):
        """Check ML accuracy and create alerts if below threshold"""
        try:
            # Get ML accuracy for the last 7 days
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            overview = self.db.get_analytics_overview(start_date, end_date)
            ml_accuracy = overview.get('ml_accuracy', 0)
            
            # Alert if accuracy is below 70%
            if ml_accuracy > 0 and ml_accuracy < 70:
                alert_exists = self._check_existing_alert('ml_accuracy_low')
                if not alert_exists:
                    self._create_performance_alert(
                        alert_type='ml_accuracy_low',
                        metric_name='ml_accuracy',
                        current_value=ml_accuracy,
                        threshold_value=70.0,
                        severity='warning',
                        description=f'ML prediction accuracy has dropped to {ml_accuracy:.1f}% (below 70% threshold)'
                    )
                    
        except Exception as e:
            logger.error(f"Error checking ML accuracy alerts: {e}")
            
    async def _check_change_frequency_alerts(self):
        """Check change frequency and create alerts if too high"""
        try:
            # Get change frequency for the last 7 days
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            overview = self.db.get_analytics_overview(start_date, end_date)
            change_frequency = overview.get('change_frequency', 0)
            
            # Alert if more than 10 changes per day on average
            if change_frequency > 10:
                alert_exists = self._check_existing_alert('change_frequency_high')
                if not alert_exists:
                    self._create_performance_alert(
                        alert_type='change_frequency_high',
                        metric_name='change_frequency',
                        current_value=change_frequency,
                        threshold_value=10.0,
                        severity='info',
                        description=f'Assignment change frequency is high: {change_frequency:.1f} changes/day (above 10/day threshold)'
                    )
                    
        except Exception as e:
            logger.error(f"Error checking change frequency alerts: {e}")
            
    def _check_existing_alert(self, alert_type: str) -> bool:
        """Check if an unresolved alert of this type already exists"""
        try:
            with self.db.get_session() as session:
                from ..db.models import PerformanceAlert
                
                existing = (session.query(PerformanceAlert)
                           .filter_by(alert_type=alert_type, is_resolved=False)
                           .filter(PerformanceAlert.created_at >= datetime.now() - timedelta(days=1))
                           .first())
                return existing is not None
        except Exception:
            return False
            
    def _create_performance_alert(self, alert_type: str, metric_name: str,
                                current_value: float, threshold_value: float,
                                severity: str, description: str):
        """Create a performance alert"""
        try:
            with self.db.get_session() as session:
                from ..db.models import PerformanceAlert
                
                alert = PerformanceAlert(
                    alert_type=alert_type,
                    metric_name=metric_name,
                    current_value=current_value,
                    threshold_value=threshold_value,
                    severity=severity,
                    description=description,
                    terminal_id=self.db.get_active_terminal_id(),
                    is_resolved=False,
                    created_at=datetime.utcnow()
                )
                
                session.add(alert)
                session.commit()
                
                logger.warning(f"Created performance alert: {alert_type} - {description}")
                
        except Exception as e:
            logger.error(f"Error creating performance alert: {e}")


# Global background service instance
_analytics_service = None

def get_analytics_background_service() -> AnalyticsBackgroundService:
    """Get or create the global analytics background service"""
    global _analytics_service
    if _analytics_service is None:
        _analytics_service = AnalyticsBackgroundService()
    return _analytics_service
