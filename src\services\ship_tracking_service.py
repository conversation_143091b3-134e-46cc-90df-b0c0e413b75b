"""
Ship Tracking Service

This module provides enhanced ship tracking capabilities for nominated vessels,
including ETA calculations, geofence monitoring, and tidal/lock information integration.
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import math
import requests
from geopy import distance
from geopy.distance import geodesic

logger = logging.getLogger(__name__)


class GeofenceZone(Enum):
    """Geofence zones around the terminal"""
    ZONE_2H = "2_hour"  # 2-hour ETA zone
    ZONE_4H = "4_hour"  # 4-hour ETA zone
    TERMINAL = "terminal"  # At terminal


@dataclass
class ShipPosition:
    """Represents a ship's current position and movement data"""
    mmsi: str
    latitude: float
    longitude: float
    course: float  # degrees
    speed: float  # knots
    timestamp: datetime
    heading: Optional[float] = None  # true heading
    status: str = "Unknown"


@dataclass
class TidalInfo:
    """Tidal information for navigation planning"""
    station_code: str
    station_name: str
    current_level: float  # meters
    high_tide_time: Optional[datetime] = None
    high_tide_level: Optional[float] = None
    low_tide_time: Optional[datetime] = None
    low_tide_level: Optional[float] = None
    tidal_window: bool = False  # Whether current time is good for navigation


@dataclass
class LockInfo:
    """Lock status and planning information"""
    lock_name: str
    status: str  # "open", "closed", "scheduled"
    next_opening: Optional[datetime] = None
    restrictions: List[str] = field(default_factory=list)
    chamber_available: bool = True


@dataclass
class TrackedShip:
    """A ship being tracked in the nomination system"""
    mmsi: str
    vessel_name: str
    nomination_id: Optional[str] = None
    current_position: Optional[ShipPosition] = None

    # Standardized ETA fields
    eta: Optional[datetime] = None  # User-specified ETA from nomination
    calculated_eta: Optional[datetime] = None  # System-calculated ETA (renamed from estimated_eta)
    eta_confidence: int = 50  # Confidence score for ETA prediction (0-100)
    eta_source: str = "ais_calculated"  # Source of ETA

    current_zone: GeofenceZone = GeofenceZone.ZONE_4H

    # Backward compatibility property (deprecated)
    @property
    def estimated_eta(self) -> Optional[datetime]:
        """Deprecated: Use calculated_eta instead"""
        return self.calculated_eta

    @estimated_eta.setter
    def estimated_eta(self, value: Optional[datetime]):
        """Deprecated: Use calculated_eta instead"""
        self.calculated_eta = value
    last_update: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Navigation constraints
    draft: float = 0.0  # meters
    length: float = 0.0  # meters
    beam: float = 0.0  # meters
    
    # Route information
    distance_to_terminal: Optional[float] = None  # nautical miles
    route_waypoints: List[Tuple[float, float]] = field(default_factory=list)
    
    # Tidal and lock dependencies
    requires_high_tide: bool = False
    requires_lock_passage: bool = False
    tidal_constraints: List[str] = field(default_factory=list)


class ShipTrackingService:
    """Service for tracking nominated ships and calculating ETAs"""
    
    def __init__(self, terminal_location: Tuple[float, float]):
        """
        Initialize the ship tracking service.
        
        Args:
            terminal_location: (latitude, longitude) of the terminal
        """
        self.terminal_location = terminal_location
        self.tracked_ships: Dict[str, TrackedShip] = {}
        
        # Geofence zones (nautical miles from terminal)
        self.geofence_zones = {
            GeofenceZone.ZONE_2H: 16.0,  # ~2 hours at 8 knots
            GeofenceZone.ZONE_4H: 32.0,  # ~4 hours at 8 knots
        }
        
        # Navigation parameters
        self.average_vessel_speed = 8.0  # knots, conservative estimate
        self.tide_sensitive_draft = 10.0  # meters, vessels above this need tide consideration
        self.lock_required_length = 200.0  # meters, vessels above this need lock coordination
        
        # API endpoints for tidal and lock information
        self.rws_base_url = "https://waterwebservices.rijkswaterstaat.nl"
        self.bgv_api_url = "https://api.blauwegolf.nl"  # BGV API for lock status
        
        logger.info(f"Ship tracking service initialized for terminal at {terminal_location}")
    
    def add_tracked_ship(self, mmsi: str, vessel_name: str, draft: float = 0.0, 
                        length: float = 0.0, beam: float = 0.0, 
                        nomination_id: Optional[str] = None) -> TrackedShip:
        """
        Add a ship to the tracking system.
        
        Args:
            mmsi: Maritime Mobile Service Identity
            vessel_name: Name of the vessel
            draft: Vessel draft in meters
            length: Vessel length in meters
            beam: Vessel beam in meters
            nomination_id: Associated nomination ID
            
        Returns:
            TrackedShip object
        """
        tracked_ship = TrackedShip(
            mmsi=mmsi,
            vessel_name=vessel_name,
            nomination_id=nomination_id,
            draft=draft,
            length=length,
            beam=beam,
            requires_high_tide=draft > self.tide_sensitive_draft,
            requires_lock_passage=length > self.lock_required_length
        )
        
        self.tracked_ships[mmsi] = tracked_ship
        logger.info(f"Added ship {vessel_name} (MMSI: {mmsi}) to tracking system")
        
        return tracked_ship
    
    def update_ship_position(self, mmsi: str, position: ShipPosition) -> Optional[TrackedShip]:
        """
        Update a tracked ship's position and recalculate ETA.
        
        Args:
            mmsi: Maritime Mobile Service Identity
            position: Current position data
            
        Returns:
            Updated TrackedShip object or None if not tracked
        """
        if mmsi not in self.tracked_ships:
            return None
        
        ship = self.tracked_ships[mmsi]
        ship.current_position = position
        ship.last_update = datetime.now(timezone.utc)
        
        # Calculate distance to terminal
        if position.latitude and position.longitude:
            ship.distance_to_terminal = self._calculate_distance_nautical_miles(
                (position.latitude, position.longitude),
                self.terminal_location
            )
            
            # Update geofence zone
            ship.current_zone = self._determine_geofence_zone(ship.distance_to_terminal)
            
            # Calculate ETA using standardized field
            ship.calculated_eta = self._calculate_eta(ship)
            ship.eta_source = "ais_calculated"
            ship.eta_confidence = self._calculate_eta_confidence(ship)
        
        logger.debug(f"Updated position for {ship.vessel_name}: {ship.distance_to_terminal:.1f}nm from terminal, ETA: {ship.estimated_eta}")
        
        return ship
    
    def _calculate_distance_nautical_miles(self, pos1: Tuple[float, float], 
                                         pos2: Tuple[float, float]) -> float:
        """Calculate distance between two positions in nautical miles"""
        distance_km = geodesic(pos1, pos2).kilometers
        return distance_km * 0.539957  # Convert km to nautical miles
    
    def _determine_geofence_zone(self, distance_nm: float) -> GeofenceZone:
        """Determine which geofence zone a ship is in based on distance"""
        if distance_nm <= 1.0:  # Within 1 nautical mile
            return GeofenceZone.TERMINAL
        elif distance_nm <= self.geofence_zones[GeofenceZone.ZONE_2H]:
            return GeofenceZone.ZONE_2H
        elif distance_nm <= self.geofence_zones[GeofenceZone.ZONE_4H]:
            return GeofenceZone.ZONE_4H
        else:
            return GeofenceZone.ZONE_4H  # Outside all zones, still track
    
    def _calculate_eta(self, ship: TrackedShip) -> Optional[datetime]:
        """
        Calculate estimated time of arrival considering various factors.
        
        Args:
            ship: TrackedShip object with current position and constraints
            
        Returns:
            Estimated arrival datetime or None if insufficient data
        """
        if not ship.current_position or not ship.distance_to_terminal:
            return None
        
        # Base calculation: distance / speed
        speed_knots = ship.current_position.speed if ship.current_position.speed > 0 else self.average_vessel_speed
        travel_time_hours = ship.distance_to_terminal / speed_knots
        
        # Add delay factors
        delay_hours = 0.0
        
        # Tidal delay for draft-sensitive vessels
        if ship.requires_high_tide:
            tidal_delay = self._calculate_tidal_delay(ship)
            delay_hours += tidal_delay
            ship.tidal_constraints.append(f"High tide required (+{tidal_delay:.1f}h delay)")
        
        # Lock passage delay
        if ship.requires_lock_passage:
            lock_delay = self._calculate_lock_delay(ship)
            delay_hours += lock_delay
        
        # Weather delay (simplified - could be enhanced with weather API)
        if ship.current_position.speed < 5.0:  # Very slow, might be weather-related
            delay_hours += 0.5  # Add 30 minutes for weather
        
        total_travel_time = travel_time_hours + delay_hours
        eta = datetime.now(timezone.utc) + timedelta(hours=total_travel_time)
        
        logger.debug(f"ETA calculation for {ship.vessel_name}: {travel_time_hours:.1f}h travel + {delay_hours:.1f}h delays = {total_travel_time:.1f}h total")

        return eta

    def _calculate_eta_confidence(self, ship: TrackedShip) -> int:
        """
        Calculate confidence score for ETA prediction based on data quality.

        Args:
            ship: TrackedShip object with current position and constraints

        Returns:
            Confidence score from 0-100
        """
        confidence = 50  # Base confidence

        if not ship.current_position:
            return 20  # Very low confidence without position

        # Increase confidence based on data quality
        if ship.current_position.speed > 0:
            confidence += 20  # Have speed data

        if ship.distance_to_terminal and ship.distance_to_terminal < 50:
            confidence += 15  # Close to terminal, more predictable

        if ship.current_position.course is not None:
            confidence += 10  # Have course data

        # Decrease confidence for complex routing
        if ship.requires_lock_passage:
            confidence -= 15  # Lock delays are unpredictable

        if ship.requires_high_tide:
            confidence -= 10  # Tidal constraints add uncertainty

        # Ensure confidence stays within bounds
        return max(10, min(95, confidence))
    
    def _calculate_tidal_delay(self, ship: TrackedShip) -> float:
        """
        Calculate delay due to tidal constraints.
        
        Args:
            ship: TrackedShip object
            
        Returns:
            Delay in hours
        """
        # Simplified tidal calculation
        # In reality, this would query the RWS Waterwebservices API
        
        # For deep draft vessels, assume they need high tide window
        if ship.draft > 12.0:
            # High tide occurs roughly every 12.5 hours
            # Assume ship might need to wait up to 6 hours for suitable tide
            return min(6.0, max(0.0, 6.0 - (ship.distance_to_terminal / self.average_vessel_speed)))
        
        return 0.0
    
    def _calculate_lock_delay(self, ship: TrackedShip) -> float:
        """
        Calculate delay due to lock passage requirements.
        
        Args:
            ship: TrackedShip object
            
        Returns:
            Delay in hours
        """
        # Simplified lock delay calculation
        # In reality, this would query lock scheduling APIs
        
        if ship.requires_lock_passage:
            # Assume average lock delay of 1-2 hours for large vessels
            return 1.5
        
        return 0.0
    
    async def get_tidal_information(self, station_code: str = "VLISSGN") -> Optional[TidalInfo]:
        """
        Get tidal information from RWS Waterwebservices.
        
        Args:
            station_code: RWS station code (default: Vlissingen for Westerschelde)
            
        Returns:
            TidalInfo object or None if data unavailable
        """
        try:
            # Mock implementation - replace with actual RWS API call
            # Endpoint would be: {self.rws_base_url}/ONLINEWAARNEMINGENSERVICES_DBO/OphalenWaarnemingen
            
            current_time = datetime.now(timezone.utc)
            
            # Return mock tidal data for development
            return TidalInfo(
                station_code=station_code,
                station_name="Vlissingen",
                current_level=2.1,  # meters above NAP
                high_tide_time=current_time + timedelta(hours=3),
                high_tide_level=3.8,
                low_tide_time=current_time + timedelta(hours=9),
                low_tide_level=0.4,
                tidal_window=True
            )
            
        except Exception as e:
            logger.error(f"Failed to fetch tidal information: {e}")
            return None
    
    async def get_lock_information(self, lock_name: str = "Terneuzen") -> Optional[LockInfo]:
        """
        Get lock status and planning information.
        
        Args:
            lock_name: Name of the lock
            
        Returns:
            LockInfo object or None if data unavailable
        """
        try:
            # Mock implementation - replace with actual lock API calls
            # Could use BGV API or RWS Vaarweginformatie
            
            current_time = datetime.now(timezone.utc)
            
            return LockInfo(
                lock_name=lock_name,
                status="scheduled",
                next_opening=current_time + timedelta(hours=2),
                restrictions=["Max length: 366m", "Max beam: 45m", "Max draft: 12.5m"],
                chamber_available=True
            )
            
        except Exception as e:
            logger.error(f"Failed to fetch lock information: {e}")
            return None
    
    def get_ships_in_zone(self, zone: GeofenceZone) -> List[TrackedShip]:
        """
        Get all ships currently in a specific geofence zone.
        
        Args:
            zone: GeofenceZone to filter by
            
        Returns:
            List of TrackedShip objects in the zone
        """
        return [ship for ship in self.tracked_ships.values() if ship.current_zone == zone]
    
    def get_tracking_summary(self) -> Dict[str, Any]:
        """
        Get a summary of current ship tracking status.
        
        Returns:
            Dictionary with tracking statistics and ship information
        """
        total_ships = len(self.tracked_ships)
        zones_summary = {}
        
        for zone in GeofenceZone:
            ships_in_zone = self.get_ships_in_zone(zone)
            zones_summary[zone.value] = {
                "count": len(ships_in_zone),
                "ships": [{"mmsi": ship.mmsi, "name": ship.vessel_name, 
                          "eta": ship.estimated_eta.isoformat() if ship.estimated_eta else None,
                          "distance_nm": ship.distance_to_terminal} 
                         for ship in ships_in_zone]
            }
        
        return {
            "total_tracked_ships": total_ships,
            "terminal_location": self.terminal_location,
            "geofence_zones": zones_summary,
            "last_update": datetime.now(timezone.utc).isoformat()
        }
    
    def remove_tracked_ship(self, mmsi: str) -> bool:
        """
        Remove a ship from tracking.
        
        Args:
            mmsi: Maritime Mobile Service Identity
            
        Returns:
            True if ship was removed, False if not found
        """
        if mmsi in self.tracked_ships:
            ship_name = self.tracked_ships[mmsi].vessel_name
            del self.tracked_ships[mmsi]
            logger.info(f"Removed ship {ship_name} (MMSI: {mmsi}) from tracking")
            return True
        return False
