FROM python:3.10-slim

WORKDIR /app

# Install system dependencies including OR-Tools requirements
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Create necessary directories
RUN mkdir -p logs data

# Copy application code
COPY . .

# Expose the port
EXPOSE 7000

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PRODUCTION=true
ENV API_HOST=0.0.0.0
ENV API_PORT=7000

# Command to run the application
CMD ["python", "run.py", "--production"]
