{% extends "base.html" %}

{% block title %}Vessels - Terneuzen Terminal Jetty Planning{% endblock %}

{% block header %}<span id="terminal-vessels-title">Vessels</span>{% endblock %}

{% block user_actions %}
    <button id="add-vessel-btn" class="btn btn-primary">
        <i class="fas fa-plus"></i>
        Add Vessel
    </button>
    <button class="btn btn-secondary" id="refresh-btn">
        <i class="fas fa-sync-alt"></i>
        Refresh
    </button>
{% endblock %}

{% block content %}
            <!-- Add Vessel Modal -->
            <div id="add-vessel-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Add New Vessel</h2>
                        <span class="close">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="add-vessel-form">
                            <!-- Vessel Information Section -->
                            <div class="form-section">
                                <h3 class="section-title">Vessel Information</h3>

                                <div class="form-group">
                                    <label for="vessel-name">Vessel Name*</label>
                                    <input type="text" id="vessel-name" required placeholder="Enter vessel name">
                                    <span class="form-help-text">Enter the official vessel name</span>
                                </div>

                                <div class="form-group">
                                    <label for="vessel-type">Vessel Type*</label>
                                    <select id="vessel-type" required>
                                        <option value="">-- Select type --</option>
                                        <option value="tanker">Vessel (seagoing)</option>
                                        <option value="barge">Barge</option>
                                    </select>
                                    <span class="form-help-text">Select the type of vessel</span>
                                </div>

                                <div class="form-row">
                                    <div class="form-group half">
                                        <label for="vessel-length">Length (m)*</label>
                                        <div class="input-with-help">
                                            <input type="number" id="vessel-length" min="10" max="500" step="0.1" required placeholder="10-500">
                                            <div class="input-tooltip" data-tooltip="Range: 10-500 meters">ⓘ</div>
                                        </div>
                                    </div>
                                    <div class="form-group half">
                                        <label for="vessel-beam">Beam (m)*</label>
                                        <div class="input-with-help">
                                            <input type="number" id="vessel-beam" min="5" max="100" step="0.1" required placeholder="5-100">
                                            <div class="input-tooltip" data-tooltip="Range: 5-100 meters">ⓘ</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group half">
                                        <label for="vessel-draft">Draft (m)*</label>
                                        <div class="input-with-help">
                                            <input type="number" id="vessel-draft" min="1" max="30" step="0.1" required placeholder="1-30">
                                            <div class="input-tooltip" data-tooltip="Range: 1-30 meters">ⓘ</div>
                                        </div>
                                    </div>
                                    <div class="form-group half">
                                        <label for="vessel-deadweight">Deadweight (t)*</label>
                                        <div class="input-with-help">
                                            <input type="number" id="vessel-deadweight" min="100" max="500000" step="10" required placeholder="100-500,000">
                                            <div class="input-tooltip" data-tooltip="Range: 100-500,000 tonnes">ⓘ</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Scheduling Section -->
                            <div class="form-section">
                                <h3 class="section-title">Scheduling & Status</h3>

                                <div class="form-row">
                                    <div class="form-group half">
                                        <label for="vessel-eta">ETA</label>
                                        <input type="datetime-local" id="vessel-eta">
                                        <span class="form-help-text">Expected arrival time (Dutch format)</span>
                                    </div>
                                    <div class="form-group half">
                                        <label for="vessel-status">Status*</label>
                                        <select id="vessel-status" required>
                                            <option value="">-- Select status --</option>
                                            <option value="EN_ROUTE">EN_ROUTE</option>
                                            <option value="APPROACHING">APPROACHING</option>
                                            <option value="ARRIVED">ARRIVED</option>
                                            <option value="WAITING">WAITING</option>
                                            <option value="DOCKED">DOCKED</option>
                                            <option value="DEPARTED">DEPARTED</option>
                                        </select>
                                        <span class="form-help-text">Current vessel status</span>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group half">
                                        <label for="vessel-customer">Customer</label>
                                        <input type="text" id="vessel-customer" placeholder="Optional">
                                        <span class="form-help-text">Optional customer name</span>
                                    </div>
                                    <div class="form-group half">
                                        <label for="vessel-priority">Priority</label>
                                        <div class="input-with-help">
                                            <input type="number" id="vessel-priority" min="1" max="10" value="1" placeholder="1-10">
                                            <div class="input-tooltip" data-tooltip="Range: 1-10 (higher = more priority)">ⓘ</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Cargo Section -->
                            <div class="form-section">
                                <h3 class="section-title">Cargo Information</h3>
                                <div id="cargo-container">
                                    <div class="cargo-item">
                                        <div class="cargo-header">
                                            <span class="cargo-title">Cargo #1</span>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group half">
                                                <label for="cargo-product-0">Product*</label>
                                                <input type="text" id="cargo-product-0" class="cargo-product" required placeholder="e.g., Crude Oil, Diesel">
                                            </div>
                                            <div class="form-group half">
                                                <label for="cargo-volume-0">Volume (m³)*</label>
                                                <div class="input-with-help">
                                                    <input type="number" id="cargo-volume-0" class="cargo-volume" min="1" max="500000" step="1" required placeholder="1-500,000">
                                                    <div class="input-tooltip" data-tooltip="Range: 1-500,000 cubic meters">ⓘ</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-row">
                                            <div class="form-group half">
                                                <label for="cargo-direction-0">Operation*</label>
                                                <select id="cargo-direction-0" class="cargo-direction" required>
                                                    <option value="">-- Select operation --</option>
                                                    <option value="false">Unloading</option>
                                                    <option value="true">Loading</option>
                                                </select>
                                            </div>
                                            <div class="form-group half">
                                                <label for="cargo-surveyor-0">Surveyor Required</label>
                                                <select id="cargo-surveyor-0" class="cargo-surveyor">
                                                    <option value="true">Yes</option>
                                                    <option value="false">No</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group cargo-actions">
                                    <button type="button" id="add-cargo-btn" class="btn btn-secondary btn-with-icon">
                                        <i class="fas fa-plus"></i> Add Another Cargo
                                    </button>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                                <button type="submit" class="btn btn-primary">Save Vessel</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Vessel Filters -->
            <div class="filters">
                <div class="filter-group">
                    <label for="status-filter">Status:</label>
                    <select id="status-filter">
                        <option value="">All</option>
                        <option value="EN_ROUTE">EN_ROUTE</option>
                        <option value="APPROACHING">APPROACHING</option>
                        <option value="ARRIVED">ARRIVED</option>
                        <option value="WAITING">WAITING</option>
                        <option value="DOCKED">DOCKED</option>
                        <option value="DEPARTED">DEPARTED</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="type-filter">Type:</label>
                    <select id="type-filter">
                        <option value="">All</option>
                        <option value="tanker">Vessel (seagoing)</option>
                        <option value="barge">Barge</option>
                    </select>
                </div>
                <button id="apply-filters" class="btn btn-sm btn-primary">Apply Filters</button>
            </div>

            <!-- Vessels List -->
            <div class="card">
                <div class="card-header">
                    <h3>All Vessels</h3>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Length</th>
                                    <th>Draft</th>
                                    <th>Deadweight</th>
                                    <th>ETA</th>
                                    <th>Cargo</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="vessels-tbody">
                                <!-- Vessels will be loaded here via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Selected Vessel Details -->
            <div class="card" id="vessel-details-card" style="display: none;">
                <div class="card-header">
                    <h3>Vessel Details: <span id="detail-vessel-name"></span></h3>
                </div>
                <div class="card-body">
                    <div class="detail-container">
                        <div class="detail-section">
                            <h4>General Information</h4>
                            <div class="detail-row">
                                <div class="detail-label">ID:</div>
                                <div class="detail-value" id="detail-vessel-id"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Type:</div>
                                <div class="detail-value" id="detail-vessel-type"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Status:</div>
                                <div class="detail-value" id="detail-vessel-status"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Customer:</div>
                                <div class="detail-value" id="detail-vessel-customer"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Priority:</div>
                                <div class="detail-value" id="detail-vessel-priority"></div>
                            </div>
                        </div>
                        <div class="detail-section">
                            <h4>Dimensions</h4>
                            <div class="detail-row">
                                <div class="detail-label">Length:</div>
                                <div class="detail-value" id="detail-vessel-length"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Beam:</div>
                                <div class="detail-value" id="detail-vessel-beam"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Draft:</div>
                                <div class="detail-value" id="detail-vessel-draft"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Deadweight:</div>
                                <div class="detail-value" id="detail-vessel-deadweight"></div>
                            </div>
                        </div>
                        <div class="detail-section">
                            <h4>Schedule</h4>
                            <div class="detail-row">
                                <div class="detail-label">ETA:</div>
                                <div class="detail-value" id="detail-vessel-eta"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">ETD:</div>
                                <div class="detail-value" id="detail-vessel-etd"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Arrival:</div>
                                <div class="detail-value" id="detail-vessel-arrival"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Departure:</div>
                                <div class="detail-value" id="detail-vessel-departure"></div>
                            </div>
                            <div class="detail-row">
                                <div class="detail-label">Current Jetty:</div>
                                <div class="detail-value" id="detail-vessel-jetty"></div>
                            </div>
                        </div>
                    </div>

                    <div class="cargo-details">
                        <h4>Cargo Information</h4>
                        <table>
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Volume</th>
                                    <th>Direction</th>
                                    <th>Tanks</th>
                                    <th>Surveyor Required</th>
                                    <th>Completed</th>
                                </tr>
                            </thead>
                            <tbody id="cargo-tbody">
                                <!-- Cargo details will be loaded here via JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <div class="vessel-assignments">
                        <h4>Current & Upcoming Assignments</h4>
                        <div id="vessel-assignments-container" class="table-container">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Jetty</th>
                                        <th>Product</th>
                                        <th>Volume</th>
                                        <th>Operation</th>
                                        <th>Start Time</th>
                                        <th>End Time</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="vessel-assignments-tbody">
                                    <tr>
                                        <td colspan="8" class="text-center">Loading assignments...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="button-group">
                        <button class="btn btn-primary" id="schedule-vessel-btn">
                            <i class="fas fa-calendar-plus"></i> Schedule
                        </button>
                        <button class="btn btn-secondary" id="view-schedule-btn">
                            <i class="fas fa-calendar-alt"></i> View All Assignments
                        </button>
                        <button class="btn btn-danger" id="delete-vessel-btn">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>

            <!-- Status Legend Component -->
            <div class="status-legend-container">
                <div class="status-legend-toggle">
                    <i class="fas fa-info-circle"></i> Status Legend
                </div>
                <div class="status-legend">
                    <div class="legend-section">
                        <h4>Vessel Statuses</h4>
                        <div class="legend-items">
                            <div class="legend-item">
                                <span class="status-badge status-scheduled">EN_ROUTE</span>
                                <span class="legend-description">Vessel is en route to the terminal</span>
                            </div>
                            <div class="legend-item">
                                <span class="status-badge status-approaching">APPROACHING</span>
                                <span class="legend-description">Vessel is approaching the terminal</span>
                            </div>
                            <div class="legend-item">
                                <span class="status-badge status-waiting">ARRIVED</span>
                                <span class="legend-description">Vessel has arrived and is waiting</span>
                            </div>
                            <div class="legend-item">
                                <span class="status-badge status-waiting">WAITING</span>
                                <span class="legend-description">Vessel is waiting for availability</span>
                            </div>
                            <div class="legend-item">
                                <span class="status-badge status-berthed">DOCKED</span>
                                <span class="legend-description">Vessel is docked at a jetty</span>
                            </div>
                            <div class="legend-item">
                                <span class="status-badge status-departed">DEPARTED</span>
                                <span class="legend-description">Vessel has departed</span>
                            </div>
                        </div>
                    </div>
                    <div class="legend-section">
                        <h4>Status Transitions</h4>
                        <div class="legend-items">
                            <div class="legend-item">
                                <i class="fas fa-exchange-alt"></i>
                                <span class="legend-description">EN_ROUTE → APPROACHING</span>
                            </div>
                            <div class="legend-item">
                                <i class="fas fa-exchange-alt"></i>
                                <span class="legend-description">APPROACHING → ARRIVED/WAITING</span>
                            </div>
                            <div class="legend-item">
                                <i class="fas fa-exchange-alt"></i>
                                <span class="legend-description">ARRIVED/WAITING → DOCKED</span>
                            </div>
                            <div class="legend-item">
                                <i class="fas fa-exchange-alt"></i>
                                <span class="legend-description">DOCKED → DEPARTED</span>
                            </div>
                            <div class="legend-item">
                                <i class="fas fa-exchange-alt"></i>
                                <span class="legend-description">DEPARTED → EN_ROUTE (new cycle)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script nonce="{{ nonce }}">
        document.addEventListener('DOMContentLoaded', function() {
            // Add active class to current nav link
            const currentPath = window.location.pathname;
            document.querySelectorAll('.nav-link').forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });

            // Store vessel status transitions
            let vesselStatusTransitions = {};
            
            // Fetch vessel status transitions from API
            fetch('/api/status/vessel-transitions')
                .then(response => response.json())
                .then(data => {
                    vesselStatusTransitions = data;
                    console.log('Loaded vessel status transitions:', vesselStatusTransitions);
                })
                .catch(error => {
                    console.error('Error loading vessel status transitions:', error);
                });
                
            // Function to check if a status transition is valid
            function isValidStatusTransition(currentStatus, newStatus) {
                // If we don't have transitions data yet, allow it
                if (!vesselStatusTransitions || Object.keys(vesselStatusTransitions).length === 0) {
                    return true;
                }
                
                // If no current status (new vessel), all statuses are valid
                if (!currentStatus) {
                    return true;
                }
                
                // If same status, it's valid
                if (currentStatus === newStatus) {
                    return true;
                }
                
                // Check if the transition is allowed
                const allowedTransitions = vesselStatusTransitions[currentStatus] || [];
                return allowedTransitions.includes(newStatus);
            }

            // Modal handling
            const modal = document.getElementById('add-vessel-modal');
            const addVesselBtn = document.getElementById('add-vessel-btn');
            const closeButtons = document.querySelectorAll('.close, .close-modal');

            addVesselBtn.addEventListener('click', function() {
                modal.style.display = 'block';
            });

            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    modal.style.display = 'none';
                });
            });

            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
            
            // Add status dropdown validation
            const statusSelect = document.getElementById('vessel-status');
            statusSelect.addEventListener('change', function() {
                // Clear previous error styling
                this.classList.remove('input-validation-error');
                
                const currentStatus = this.getAttribute('data-current-status');
                if (currentStatus && !isValidStatusTransition(currentStatus, this.value)) {
                    showError(this, `Invalid transition from ${currentStatus} to ${this.value}`);
                    
                    // Optionally show the allowed transitions
                    const allowedTransitions = vesselStatusTransitions[currentStatus] || [];
                    if (allowedTransitions.length > 0) {
                        const helpText = document.createElement('span');
                        helpText.className = 'form-help-text';
                        helpText.textContent = `Allowed transitions: ${allowedTransitions.join(', ')}`;
                        this.parentNode.appendChild(helpText);
                    }
                }
            });

            // Add cargo button
            let cargoCount = 1;
            document.getElementById('add-cargo-btn').addEventListener('click', function() {
                const cargoContainer = document.getElementById('cargo-container');
                const newCargo = document.createElement('div');
                newCargo.className = 'cargo-item';
                newCargo.innerHTML = `
                    <div class="cargo-header">
                        <span class="cargo-title">Cargo #${cargoCount + 1}</span>
                        <button type="button" class="btn btn-sm btn-danger remove-cargo">
                            <i class="fas fa-times"></i> Remove
                        </button>
                    </div>
                    <div class="form-row">
                        <div class="form-group half">
                            <label for="cargo-product-${cargoCount}">Product*</label>
                            <input type="text" id="cargo-product-${cargoCount}" class="cargo-product" required placeholder="e.g., Crude Oil, Diesel">
                        </div>
                        <div class="form-group half">
                            <label for="cargo-volume-${cargoCount}">Volume (m³)*</label>
                            <div class="input-with-help">
                                <input type="number" id="cargo-volume-${cargoCount}" class="cargo-volume" min="1" max="500000" step="1" required placeholder="1-500,000">
                                <div class="input-tooltip" data-tooltip="Range: 1-500,000 cubic meters">ⓘ</div>
                            </div>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group half">
                            <label for="cargo-direction-${cargoCount}">Operation*</label>
                            <select id="cargo-direction-${cargoCount}" class="cargo-direction" required>
                                <option value="">-- Select operation --</option>
                                <option value="false">Unloading</option>
                                <option value="true">Loading</option>
                            </select>
                        </div>
                        <div class="form-group half">
                            <label for="cargo-surveyor-${cargoCount}">Surveyor Required</label>
                            <select id="cargo-surveyor-${cargoCount}" class="cargo-surveyor">
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                            </select>
                        </div>
                    </div>
                `;
                cargoContainer.appendChild(newCargo);
                cargoCount++;

                // Add remove button handlers
                document.querySelectorAll('.remove-cargo').forEach(button => {
                    button.addEventListener('click', function() {
                        this.closest('.cargo-item').remove();

                        // Renumber the cargo items
                        const cargoItems = document.querySelectorAll('.cargo-item');
                        cargoItems.forEach((item, index) => {
                            const titleEl = item.querySelector('.cargo-title');
                            if (titleEl) {
                                titleEl.textContent = `Cargo #${index + 1}`;
                            }
                        });
                    });
                });
            });

            // Form validation and submission
            document.getElementById('add-vessel-form').addEventListener('submit', function(e) {
                e.preventDefault();

                // Clear previous error styling
                document.querySelectorAll('.input-validation-error').forEach(el => el.classList.remove('input-validation-error'));

                // Validate form fields
                let isValid = true;

                // Validate vessel name
                const nameInput = document.getElementById('vessel-name');
                if (!nameInput.value.trim()) {
                    showError(nameInput, 'Vessel name is required');
                    isValid = false;
                }
                
                // Validate vessel status
                const statusSelect = document.getElementById('vessel-status');
                if (!statusSelect.value) {
                    showError(statusSelect, 'Vessel status is required');
                    isValid = false;
                }
                
                // For vessel editing (we'll add this later when we implement vessel editing)
                // If this is an existing vessel, validate status transition
                // const currentStatus = statusSelect.getAttribute('data-current-status');
                // if (currentStatus && !isValidStatusTransition(currentStatus, statusSelect.value)) {
                //     showError(statusSelect, `Invalid status transition from ${currentStatus} to ${statusSelect.value}`);
                //     isValid = false;
                // }

                // Validate numeric fields
                const numericFields = [
                    { id: 'vessel-length', name: 'Length', min: 10, max: 500 },
                    { id: 'vessel-beam', name: 'Beam', min: 5, max: 100 },
                    { id: 'vessel-draft', name: 'Draft', min: 1, max: 30 },
                    { id: 'vessel-deadweight', name: 'Deadweight', min: 100, max: 500000 },
                    { id: 'vessel-priority', name: 'Priority', min: 1, max: 10 }
                ];

                numericFields.forEach(field => {
                    const input = document.getElementById(field.id);
                    const value = parseFloat(input.value);

                    if (isNaN(value)) {
                        showError(input, `${field.name} must be a number`);
                        isValid = false;
                    } else if (value < field.min || value > field.max) {
                        showError(input, `${field.name} must be between ${field.min} and ${field.max}`);
                        isValid = false;
                    }
                });

                // Validate cargo fields
                const cargoProducts = document.querySelectorAll('.cargo-product');
                const cargoVolumes = document.querySelectorAll('.cargo-volume');

                for (let i = 0; i < cargoProducts.length; i++) {
                    if (!cargoProducts[i].value.trim()) {
                        showError(cargoProducts[i], 'Product name is required');
                        isValid = false;
                    }

                    const volume = parseFloat(cargoVolumes[i].value);
                    if (isNaN(volume)) {
                        showError(cargoVolumes[i], 'Volume must be a number');
                        isValid = false;
                    } else if (volume < 1 || volume > 500000) {
                        showError(cargoVolumes[i], 'Volume must be between 1 and 500,000');
                        isValid = false;
                    }
                }

                // If validation fails, stop submission
                if (!isValid) {
                    return;
                }

                // Collect cargo items
                const cargoes = [];
                const cargoDirections = document.querySelectorAll('.cargo-direction');
                const cargoSurveyors = document.querySelectorAll('.cargo-surveyor');

                for (let i = 0; i < cargoProducts.length; i++) {
                    cargoes.push({
                        product: cargoProducts[i].value,
                        volume: parseFloat(cargoVolumes[i].value),
                        is_loading: cargoDirections[i].value === 'true',
                        tanks: [],
                        surveyor_required: cargoSurveyors[i].value === 'true'
                    });
                }

                // Create vessel data object
                const vesselData = {
                    name: nameInput.value,
                    vessel_type: document.getElementById('vessel-type').value,
                    length: parseFloat(document.getElementById('vessel-length').value),
                    beam: parseFloat(document.getElementById('vessel-beam').value),
                    draft: parseFloat(document.getElementById('vessel-draft').value),
                    deadweight: parseFloat(document.getElementById('vessel-deadweight').value),
                    status: document.getElementById('vessel-status').value,
                    customer: document.getElementById('vessel-customer').value,
                    priority: parseInt(document.getElementById('vessel-priority').value),
                    cargoes: cargoes
                };

                // Add ETA if provided - use Dutch date format
                const etaInput = document.getElementById('vessel-eta').value;
                if (etaInput) {
                    // Convert to ISO format for API
                    const etaDate = new Date(etaInput);
                    vesselData.eta = etaDate.toISOString();

                    // Display in Dutch format for user feedback
                    const formattedDate = etaDate.toLocaleDateString('nl-NL', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    console.log(`ETA formatted for display: ${formattedDate}`);
                }

                // Show loading state
                const submitBtn = document.querySelector('#add-vessel-form button[type="submit"]');
                const originalBtnText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                submitBtn.disabled = true;

                // Send the data to the API
                fetch('/api/vessels', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(vesselData),
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(err => {
                            throw new Error(err.detail || 'Failed to create vessel');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    // Show success message using toast
                    showSuccessToast(`Vessel ${data.name} created successfully`);

                    // Close modal after delay
                    setTimeout(() => {
                        modal.style.display = 'none';
                        loadVessels(); // Reload the vessels list
                        // Reset the form
                        document.getElementById('add-vessel-form').reset();
                        // Reset cargo items to just one
                        resetCargoContainer();
                    }, 1500);
                })
                .catch(error => {
                    // Show error message using toast
                    showErrorToast(error.message);
                })
                .finally(() => {
                    // Reset button state
                    submitBtn.innerHTML = originalBtnText;
                    submitBtn.disabled = false;
                });
            });

            // Helper function to show validation errors using toast notifications
            function showError(inputElement, errorMessage) {
                inputElement.classList.add('input-validation-error');
                showErrorToast(errorMessage);
                inputElement.focus();
            }

            // Helper function to reset cargo container
            function resetCargoContainer() {
                const cargoContainer = document.getElementById('cargo-container');
                cargoContainer.innerHTML = `
                    <div class="cargo-item">
                        <div class="cargo-header">
                            <span class="cargo-title">Cargo #1</span>
                        </div>
                        <div class="form-row">
                            <div class="form-group half">
                                <label for="cargo-product-0">Product*</label>
                                <input type="text" id="cargo-product-0" class="cargo-product" required placeholder="e.g., Crude Oil, Diesel">
                            </div>
                            <div class="form-group half">
                                <label for="cargo-volume-0">Volume (m³)*</label>
                                <div class="input-with-help">
                                    <input type="number" id="cargo-volume-0" class="cargo-volume" min="1" max="500000" step="1" required placeholder="1-500,000">
                                    <div class="input-tooltip" data-tooltip="Range: 1-500,000 cubic meters">ⓘ</div>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group half">
                                <label for="cargo-direction-0">Operation*</label>
                                <select id="cargo-direction-0" class="cargo-direction" required>
                                    <option value="">-- Select operation --</option>
                                    <option value="false">Unloading</option>
                                    <option value="true">Loading</option>
                                </select>
                            </div>
                            <div class="form-group half">
                                <label for="cargo-surveyor-0">Surveyor Required</label>
                                <select id="cargo-surveyor-0" class="cargo-surveyor">
                                    <option value="true">Yes</option>
                                    <option value="false">No</option>
                                </select>
                            </div>
                        </div>
                    </div>
                `;
                cargoCount = 1;
            }

            // Load vessels from API
            window.loadVessels = function loadVessels() {
                const statusFilter = document.getElementById('status-filter').value;
                const typeFilter = document.getElementById('type-filter').value;

                let url = '/api/vessels';
                const params = [];
                if (statusFilter) params.push(`status=${statusFilter}`);
                if (typeFilter) params.push(`vessel_type=${typeFilter}`);
                if (params.length > 0) url += '?' + params.join('&');

                fetch(url)
                .then(response => response.json())
                .then(vessels => {
                    const tbody = document.getElementById('vessels-tbody');
                    tbody.innerHTML = '';

                    vessels.forEach(vessel => {
                        const row = document.createElement('tr');

                        // Format ETA
                        let etaDisplay = 'N/A';
                        if (vessel.eta) {
                            const etaDate = new Date(vessel.eta);
                            etaDisplay = etaDate.toLocaleString('nl-NL', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', hour12: false });
                        }

                        // Determine status class
                        let statusClass = '';
                        switch(vessel.status.toLowerCase()) {
                            case 'approaching': statusClass = 'approaching'; break;
                            case 'waiting': statusClass = 'waiting'; break;
                            case 'berthed': statusClass = 'berthed'; break;
                            case 'loading': statusClass = 'loading'; break;
                            case 'unloading': statusClass = 'unloading'; break;
                            case 'departed': statusClass = 'departed'; break;
                        }

                        row.innerHTML = `
                            <td>${vessel.id}</td>
                            <td>${vessel.name}</td>
                            <td>${vessel.type}</td>
                            <td><span class="vessel-status ${statusClass}">${vessel.status}</span></td>
                            <td>${vessel.length.toFixed(1)} m</td>
                            <td>${vessel.draft.toFixed(1)} m</td>
                            <td>${vessel.deadweight.toLocaleString()} t</td>
                            <td>${etaDisplay}</td>
                            <td>${vessel.cargo_count} (${vessel.total_cargo_volume.toLocaleString()} m³)</td>
                            <td>
                                <button class="btn btn-sm btn-primary view-vessel" data-id="${vessel.id}">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-danger delete-vessel" data-id="${vessel.id}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        `;
                        tbody.appendChild(row);
                    });

                    // Add vessel detail view handlers
                    document.querySelectorAll('.view-vessel').forEach(button => {
                        button.addEventListener('click', function() {
                            const vesselId = this.getAttribute('data-id');
                            loadVesselDetails(vesselId);
                        });
                    });

                    // Add delete handlers
                    document.querySelectorAll('.delete-vessel').forEach(button => {
                        button.addEventListener('click', async function() {
                            const vesselId = this.getAttribute('data-id');

                            // Create confirmation using toast system
                            const confirmed = await showConfirmationToast(
                                `Are you sure you want to delete vessel ${vesselId}?`,
                                'This action cannot be undone.',
                                'Delete Vessel'
                            );

                            if (confirmed) {
                                deleteVessel(vesselId);
                            }
                        });
                    });
                })
                .catch(error => {
                    console.error('Error loading vessels:', error);
                    showErrorToast('Failed to load vessels. Please try again.');
                });
            }

            // Delete vessel
            function deleteVessel(vesselId) {
                fetch(`/api/vessels/${vesselId}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(err => { throw new Error(err.detail || 'Failed to delete vessel'); });
                    }
                    return response.json();
                })
                .then(data => {
                    showSuccessToast(data.message);
                    loadVessels();
                    document.getElementById('vessel-details-card').style.display = 'none';
                })
                .catch(error => {
                    showErrorToast(`Error: ${error.message}`);
                });
            }

            // Load vessel details
            function loadVesselDetails(vesselId) {
                fetch(`/api/vessels/${vesselId}`)
                .then(response => response.json())
                .then(vessel => {
                    // Set all detail fields
                    document.getElementById('detail-vessel-name').textContent = vessel.name;
                    document.getElementById('detail-vessel-id').textContent = vessel.id;
                    document.getElementById('detail-vessel-type').textContent = vessel.type;
                    document.getElementById('detail-vessel-status').textContent = vessel.status;
                    document.getElementById('detail-vessel-customer').textContent = vessel.customer || 'N/A';
                    document.getElementById('detail-vessel-priority').textContent = vessel.priority;

                    document.getElementById('detail-vessel-length').textContent = `${vessel.length.toFixed(1)} m`;
                    document.getElementById('detail-vessel-beam').textContent = `${vessel.beam.toFixed(1)} m`;
                    document.getElementById('detail-vessel-draft').textContent = `${vessel.draft.toFixed(1)} m`;
                    document.getElementById('detail-vessel-deadweight').textContent = `${vessel.deadweight.toLocaleString()} t`;

                    document.getElementById('detail-vessel-eta').textContent = vessel.eta ? new Date(vessel.eta).toLocaleString('nl-NL', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', hour12: false }) : 'N/A';
                    document.getElementById('detail-vessel-etd').textContent = vessel.etd ? new Date(vessel.etd).toLocaleString('nl-NL', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', hour12: false }) : 'N/A';
                    document.getElementById('detail-vessel-arrival').textContent = vessel.arrival_time ? new Date(vessel.arrival_time).toLocaleString('nl-NL', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', hour12: false }) : 'N/A';
                    document.getElementById('detail-vessel-departure').textContent = vessel.departure_time ? new Date(vessel.departure_time).toLocaleString('nl-NL', { day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', hour12: false }) : 'N/A';
                    document.getElementById('detail-vessel-jetty').textContent = vessel.current_jetty || 'None';

                    // Load cargo information
                    const cargoTbody = document.getElementById('cargo-tbody');
                    cargoTbody.innerHTML = '';

                    vessel.cargoes.forEach(cargo => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${cargo.product}</td>
                            <td>${cargo.volume.toLocaleString()} m³</td>
                            <td>${cargo.is_loading ? 'Loading' : 'Unloading'}</td>
                            <td>${cargo.tanks.length > 0 ? cargo.tanks.join(', ') : 'None assigned'}</td>
                            <td>${cargo.surveyor_required ? 'Yes' : 'No'}</td>
                            <td>${cargo.completed_volume ? `${cargo.completed_volume.toLocaleString()} m³` : 'N/A'}</td>
                        `;
                        cargoTbody.appendChild(row);
                    });

                    // Show vessel details card
                    document.getElementById('vessel-details-card').style.display = 'block';

                    // Set up delete handler for vessel details
                    document.getElementById('delete-vessel-btn').addEventListener('click', async function() {
                        const confirmed = await showConfirmationToast(
                            `Are you sure you want to delete vessel ${vessel.id}?`,
                            'This action cannot be undone.',
                            'Delete Vessel'
                        );

                        if (confirmed) {
                            deleteVessel(vessel.id);
                        }
                    });

                    // Load vessel assignments
                    loadVesselAssignments(vessel.id);
                    
                    // Set up schedule button
                    document.getElementById('schedule-vessel-btn').addEventListener('click', function() {
                        window.location.href = `/schedule/add?vessel=${vessel.id}`;
                    });
                    
                    // Set up view schedule button
                    document.getElementById('view-schedule-btn').addEventListener('click', function() {
                        window.location.href = `/schedule?filter=vessel&vessel_id=${vessel.id}`;
                    });
                })
                .catch(error => {
                    console.error('Error loading vessel details:', error);
                    showErrorToast('Failed to load vessel details. Please try again.');
                });
            }
            
            // Function to load assignments for a specific vessel
            function loadVesselAssignments(vesselId) {
                const assignmentsTbody = document.getElementById('vessel-assignments-tbody');
                
                // Set loading state
                assignmentsTbody.innerHTML = '<tr><td colspan="8" class="text-center">Loading assignments...</td></tr>';
                
                // Fetch assignments for this vessel
                fetch(`/api/schedule/assignments?vessel_id=${vesselId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to load vessel assignments');
                    }
                    return response.json();
                })
                .then(assignments => {
                    if (!assignments || assignments.length === 0) {
                        assignmentsTbody.innerHTML = '<tr><td colspan="8" class="text-center">No assignments found for this vessel</td></tr>';
                        return;
                    }
                    
                    // Sort assignments by start time (most recent first)
                    assignments.sort((a, b) => new Date(a.start_time) - new Date(b.start_time));
                    
                    // Format date for display
                    const formatDate = (dateStr) => {
                        if (!dateStr) return 'N/A';
                        const date = new Date(dateStr);
                        return date.toLocaleString('nl-NL', { 
                            day: '2-digit', 
                            month: '2-digit', 
                            year: 'numeric', 
                            hour: '2-digit', 
                            minute: '2-digit', 
                            hour12: false 
                        });
                    };
                    
                    // Build table rows for assignments
                    assignmentsTbody.innerHTML = assignments.map(assignment => {
                        // Extract information
                        const product = assignment.product || (assignment.cargo_product || "Unknown");
                        const volume = assignment.volume ? `${assignment.volume.toLocaleString()} m³` : 
                                      (assignment.cargo_volume ? `${assignment.cargo_volume.toLocaleString()} m³` : "Unknown");
                        const operation = assignment.operation || (assignment.is_loading ? "Loading" : "Unloading");
                        
                        // Status classes
                        let statusClass = 'waiting';
                        if (assignment.status) {
                            const status = assignment.status.toLowerCase();
                            if (status === 'completed') statusClass = 'berthed';
                            else if (status === 'active' || status === 'in_progress') statusClass = 'loading';
                            else if (status === 'planned' || status === 'scheduled') statusClass = 'scheduled';
                        }
                        
                        return `
                            <tr>
                                <td>${assignment.jetty_name}</td>
                                <td>${product}</td>
                                <td>${volume}</td>
                                <td>${operation}</td>
                                <td>${formatDate(assignment.start_time)}</td>
                                <td>${formatDate(assignment.end_time)}</td>
                                <td><span class="vessel-status ${statusClass}">${assignment.status || 'Unknown'}</span></td>
                                <td>
                                    <a href="/schedule/edit/${assignment.id}" class="btn btn-sm btn-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                        `;
                    }).join('');
                })
                .catch(error => {
                    console.error('Error loading vessel assignments:', error);
                    assignmentsTbody.innerHTML = '<tr><td colspan="8" class="text-center error-text">Error loading assignments</td></tr>';
                });
            }
            
            // Initialize vessels list
            loadVessels();

            // Refresh button
            document.getElementById('refresh-btn').addEventListener('click', function() {
                loadVessels();
            });

            // Filter button
            document.getElementById('apply-filters').addEventListener('click', function() {
                loadVessels();
            });
            
            // Status Legend Toggle
            const legendToggle = document.querySelector('.status-legend-toggle');
            const legend = document.querySelector('.status-legend');
            
            legendToggle.addEventListener('click', function() {
                legend.classList.toggle('visible');
            });
            
            // Close legend when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.status-legend-container')) {
                    legend.classList.remove('visible');
                }
            });
        });

        // Listen for terminal changes
        document.addEventListener('terminalChanged', function(event) {
            console.log('Terminal changed on vessels page:', event.detail);
            // Update title and reload vessels for new terminal
            updateTerminalInfo();
            if (typeof window.loadVessels === 'function') {
                window.loadVessels();
            }
        });

        // Listen for page refresh requests
        document.addEventListener('refreshPageData', function(event) {
            console.log('Refreshing vessels page data:', event.detail);
            if (typeof window.loadVessels === 'function') {
                window.loadVessels();
            }
        });

        // Function to update terminal-specific information
        async function updateTerminalInfo() {
            try {
                const response = await fetch('/api/terminal');
                if (response.ok) {
                    const terminalData = await response.json();
                    
                    // Update page title
                    const titleElement = document.getElementById('terminal-vessels-title');
                    if (titleElement) {
                        titleElement.textContent = `${terminalData.name} Vessels`;
                    }
                    
                    console.log('Terminal info updated on vessels page:', terminalData);
                }
            } catch (error) {
                console.error('Error loading terminal info:', error);
            }
        }

        // Initial terminal info load
        updateTerminalInfo();

        // Confirmation toast helper function
        function showConfirmationToast(message, subtitle, confirmText = 'Confirm') {
            return new Promise((resolve) => {
                // Create confirmation toast with custom buttons
                const toast = document.createElement('div');
                toast.className = 'toast toast-warning';
                toast.style.cssText = 'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 10000; max-width: 500px; width: 90%;';
                toast.setAttribute('role', 'dialog');
                toast.setAttribute('aria-live', 'assertive');

                toast.innerHTML = `
                    <div class="toast-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="toast-message" style="flex: 1;">
                        <strong>${message}</strong><br>
                        ${subtitle}
                    </div>
                    <div style="display: flex; gap: 8px; margin-left: 10px;">
                        <button class="btn btn-sm btn-secondary" data-action="cancel">Cancel</button>
                        <button class="btn btn-sm btn-danger" data-action="confirm">${confirmText}</button>
                    </div>
                `;

                // Add backdrop
                const backdrop = document.createElement('div');
                backdrop.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 9999;';
                backdrop.onclick = () => {
                    backdrop.remove();
                    toast.remove();
                    resolve(false);
                };

                document.body.appendChild(backdrop);
                document.body.appendChild(toast);

                // Add event listeners to buttons
                toast.querySelector('[data-action="cancel"]').onclick = () => {
                    backdrop.remove();
                    toast.remove();
                    resolve(false);
                };
                toast.querySelector('[data-action="confirm"]').onclick = () => {
                    backdrop.remove();
                    toast.remove();
                    resolve(true);
                };
            });
        }
    </script>
{% endblock %}