"""
Claude 3.5 Interface

This module provides an interface for interacting with Claude 3.5 API to enable
natural language interactions with the optimization system.
"""

import logging
import json
import re
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timedelta

import anthropic

from src.models.terminal import Terminal
from src.models.vessel import Vessel, Barge, VesselType, Cargo
from src.models.schedule import Schedule, Assignment
from src.optimization.scheduler import JettyScheduler

logger = logging.getLogger(__name__)


class ClaudeInterface:
    """Interface for interacting with Claude 3.5 API"""

    def __init__(self, api_key: str,
                 model: str = "claude-3-5-sonnet-20240620"):
        """
        Initialize Claude interface.

        Args:
            api_key: API key for Claude API.
            model: Claude model to use.
        """
        self.api_key = api_key
        self.model = model
        self.use_mock = api_key == "TEST" or not api_key

        if self.use_mock:
            logger.warning("Using mock responses for <PERSON> (TEST API key or no key provided)")
        else:
            logger.info("Using real Claude API with key")
            self.client = anthropic.Anthropic(api_key=api_key)

        # System prompt that defines the capabilities and behavior of the assistant
        self.system_prompt = """
        You are an AI assistant specialized in jetty planning for a petrochemical terminal.
        You understand the operations of a terminal with 14 jetties (9 barge berths and 7 vessel berths),
        handling products like gasoline, gasoil, jetfuel, and biofuels.

        Scope of help:
        - Jetty allocation and scheduling
        - Vessel and barge operations
        - Terminal capacity and throughput
        - Weather impacts on operations
        - Upcoming vessel arrivals and departures
        - Schedule optimization and trade‑offs

        Response formatting rules (important):
        - Use compact, skimmable Markdown formatting suitable for a dashboard UI.
        - Start with a one‑sentence answer or outcome, then details.
        - Prefer headings (###), short paragraphs, and bullet lists with bold lead words.
        - Use Markdown tables for structured data (schedules, comparisons, KPIs).
        - Use SI units and include units on numbers. Keep times in local terminal time when possible.
        - If unsure, ask exactly one concise clarifying question at the end under "### Clarification".
        - When proposing schedule changes, include two sections: "### Proposed changes" (table) and "### Impact" (bullets).
        - When showing commands or expressions, use fenced code blocks.

        Domain glossary:
        - "Jetty": Dock where vessels/barges berth for loading/unloading
        - "Berth": Act of mooring a vessel at a jetty
        - "Floating roof tank": Tank with a roof that floats on the liquid surface
        - "Loading arm": Equipment used to transfer product between vessel and terminal
        - "Surveyor": Professional who inspects/verifies cargo quality and quantity
        - "Demurrage": Penalty when vessels wait beyond agreed laytime

        Be concise but thorough. Prioritize correctness and operational safety.
        """

        # Conversation history
        self.conversation_history = []

    def query(self, user_message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Send a user query to Claude and get the response.

        Args:
            user_message: The user's query or request.
            context: Additional context to include, such as terminal state, schedule, etc.

        Returns:
            Claude's response.
        """
        if self.use_mock:
            return self._mock_response(user_message, context)

        # Build the messages with conversation history
        messages = []

        # Add conversation history
        for entry in self.conversation_history:
            messages.append(entry)

        # Format context information as a string if provided
        context_str = ""
        if context:
            context_str = "\n\nCurrent System Context:\n"
            for key, value in context.items():
                try:
                    if key == 'weather' and isinstance(value, dict):
                        context_str += f"\n{key}:\n"
                        for wk, wv in value.items():
                            context_str += f"  {wk}: {wv}\n"
                    else:
                        # Try to extract basic information from complex objects
                        if hasattr(value, '__dict__'):
                            # For terminal object
                            if key == 'terminal' and hasattr(value, 'name') and hasattr(value, 'jetties'):
                                context_str += f"\n{key}: Terminal '{value.name}' with {len(value.jetties)} jetties\n"
                            # For vessels list
                            elif key == 'vessels':
                                vessel_info = []
                                for i, v in enumerate(value):
                                    if hasattr(v, 'name') and hasattr(v, 'status'):
                                        status = v.status.name if hasattr(v.status, 'name') else str(v.status)
                                        vessel_info.append(f"Vessel {i+1}: {v.name} ({status})")
                                context_str += f"\n{key}: {len(value)} vessels\n" + "\n".join(vessel_info) + "\n"
                            # For schedule object
                            elif key == 'schedule' and hasattr(value, 'assignments'):
                                context_str += f"\n{key}: Schedule with {len(value.assignments)} assignments\n"
                            else:
                                context_str += f"\n{key}: {str(value)}\n"
                        else:
                            context_str += f"\n{key}: {str(value)}\n"
                except Exception as e:
                    logger.error(f"Error formatting context for {key}: {e}")
                    context_str += f"\n{key}: [Error formatting data]\n"

        # Add user message with context
        full_message = f"{user_message}{context_str}"
        messages.append({"role": "user", "content": full_message})

        # Send request to Claude
        try:
            response = self.client.messages.create(
                model=self.model,
                max_tokens=4000,
                system=self.system_prompt,
                messages=messages
            )

            assistant_response = response.content[0].text

            # Update conversation history
            self.conversation_history.append({"role": "user", "content": full_message})
            self.conversation_history.append({"role": "assistant", "content": assistant_response})

            # Limit conversation history to last 10 exchanges
            if len(self.conversation_history) > 20:
                self.conversation_history = self.conversation_history[-20:]

            return assistant_response
        except Exception as e:
            logger.error(f"Error querying Claude API: {e}")
            return f"I encountered an error while processing your request: {str(e)}"

    def _mock_response(self, user_message: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Generate a mock response for testing without Claude API.

        Args:
            user_message: The user's query or request.
            context: Additional context provided.

        Returns:
            Mock response.
        """
        # Context is now handled in each specific response type

        # Look for keywords in the user message to determine the type of response
        user_message_lower = user_message.lower()

        # Schedule optimization request
        if "optimize" in user_message_lower or "reschedule" in user_message_lower:
            # Include context information in the response
            context_info = ""
            if context:
                if "terminal" in context and context["terminal"]:
                    terminal = context["terminal"]
                    context_info += f"\n\nBased on your terminal with {len(terminal.jetties)} jetties "

                if "vessels" in context and context["vessels"]:
                    vessels = context["vessels"]
                    context_info += f"and {len(vessels)} vessels in the system, "

                if "weather" in context and context["weather"] and isinstance(context["weather"], dict) and "wind_speed" in context["weather"]:
                    wind_speed = context["weather"]["wind_speed"]
                    if wind_speed > 12.0:
                        context_info += f"taking into account the current high wind conditions ({wind_speed} m/s), "

            return f"""
            I've optimized the jetty schedule based on your request. Here's the new schedule:{context_info}

            ## Optimized Schedule

            | Jetty | Vessel/Barge | Cargo | Start Time | End Time | Status |
            |-------|--------------|-------|------------|----------|--------|
            | J-01  | TESTTANKER1  | Gasoline (25,000 m³) | 2025-04-08 14:00 | 2025-04-09 02:00 | Planned |
            | J-03  | TESTTANKER2  | Gasoil (18,000 m³) | 2025-04-08 16:00 | 2025-04-09 00:00 | Planned |
            | J-05  | TESTBARGE1   | Jetfuel (1,800 m³) | 2025-04-08 12:00 | 2025-04-08 18:00 | Active |

            The optimization prioritized:
            1. Maximizing throughput (total cargo volume)
            2. Minimizing vessel waiting time to reduce demurrage costs
            3. Respecting all compatibility constraints between vessels and jetties

            Jetty utilization has improved from 68% to 74%, and expected demurrage has been reduced by approximately €15,000.

            Would you like me to explain any specific part of this optimization in more detail?
            """

        # Query about current schedule
        elif "schedule" in user_message_lower or "current plan" in user_message_lower:
            # Include context information in the response
            context_info = ""
            assignment_count = 0
            waiting_vessels = []

            if context:
                if "schedule" in context and context["schedule"] and hasattr(context["schedule"], 'assignments'):
                    schedule = context["schedule"]
                    assignment_count = len(schedule.assignments)
                    context_info += f"\n\nBased on the current schedule with {assignment_count} assignments, "

                if "vessels" in context and context["vessels"]:
                    vessels = context["vessels"]
                    waiting_vessels = [v for v in vessels if hasattr(v, 'status') and v.status and v.status.name == 'WAITING']
                    if waiting_vessels:
                        context_info += f"with {len(waiting_vessels)} vessels currently waiting, "

                if "weather" in context and context["weather"] and isinstance(context["weather"], dict) and "wind_speed" in context["weather"]:
                    wind_speed = context["weather"]["wind_speed"]
                    if wind_speed > 12.0:
                        context_info += f"and current wind conditions ({wind_speed} m/s) that may affect operations, "

            return f"""
            Here's the current jetty schedule:{context_info}

            ## Current Schedule (2025-04-08 11:45)

            | Jetty | Vessel/Barge | Cargo | Start Time | End Time | Status |
            |-------|--------------|-------|------------|----------|--------|
            | J-01  | Currently unassigned | | | | |
            | J-02  | Currently unassigned | | | | |
            | J-03  | TESTTANKER2  | Gasoil (18,000 m³) | 2025-04-08 18:00 | 2025-04-09 02:00 | Planned |
            | J-04  | Currently unassigned | | | | |
            | J-05  | TESTBARGE1   | Jetfuel (1,800 m³) | 2025-04-08 12:00 | 2025-04-08 18:00 | Active |
            | J-06  | Currently unassigned | | | | |
            | J-07  | Currently unassigned | | | | |
            | J-08  | Currently unassigned | | | | |
            | J-09  | Currently unassigned | | | | |
            | J-10  | Currently unassigned | | | | |
            | J-11  | Currently unassigned | | | | |
            | J-12  | Currently unassigned | | | | |
            | J-13  | Currently unassigned | | | | |
            | J-14  | Currently unassigned | | | | |

            Current jetty utilization: 14.3%
            Vessels waiting: 1 (TESTTANKER1, arrived 2025-04-08 10:30)
            Vessels expected within 24h: 2

            Would you like to see more details about any specific jetty or vessel?
            """

        # Query about vessels
        elif "vessel" in user_message_lower or "barge" in user_message_lower or "ship" in user_message_lower:
            # Include context information in the response
            context_info = ""
            vessel_count = 0
            active_vessels = []
            waiting_vessels = []

            if context:
                if "vessels" in context and context["vessels"]:
                    vessels = context["vessels"]
                    vessel_count = len(vessels)

                    if hasattr(vessels[0], 'status'):
                        active_vessels = [v for v in vessels if hasattr(v, 'status') and v.status and v.status.name in ['LOADING', 'UNLOADING']]
                        waiting_vessels = [v for v in vessels if hasattr(v, 'status') and v.status and v.status.name == 'WAITING']

                    context_info += f"\n\nBased on the current data for {vessel_count} vessels in the system, "

                    if active_vessels:
                        context_info += f"with {len(active_vessels)} vessels currently at berth, "

                    if waiting_vessels:
                        context_info += f"and {len(waiting_vessels)} vessels waiting, "

                if "weather" in context and context["weather"] and isinstance(context["weather"], dict) and "wind_speed" in context["weather"]:
                    wind_speed = context["weather"]["wind_speed"]
                    if wind_speed > 12.0:
                        context_info += f"noting that current wind conditions ({wind_speed} m/s) may affect vessel operations, "

            return f"""
            Here are the vessels currently at or approaching the terminal:{context_info}

            ## Vessels at Terminal

            | Vessel Name | Type | Status | Cargo | Jetty | ETA/ETD |
            |-------------|------|--------|-------|-------|---------|
            | TESTBARGE1  | Barge | LOADING | Jetfuel (1,800 m³) | J-05 | ETD: 2025-04-08 18:00 |

            ## Vessels Waiting

            | Vessel Name | Type | Status | Cargo | Waiting Since | Priority |
            |-------------|------|--------|-------|---------------|----------|
            | TESTTANKER1 | Tanker | WAITING | Gasoline (25,000 m³) | 2025-04-08 10:30 | High |

            ## Vessels Approaching

            | Vessel Name | Type | Status | Cargo | ETA | Distance |
            |-------------|------|--------|-------|-----|----------|
            | TESTTANKER2 | Tanker | APPROACHING | Gasoil (18,000 m³) | 2025-04-08 16:00 | 35 nm |

            Would you like more details about any specific vessel?
            """

        # Query about weather
        elif "weather" in user_message_lower:
            # Include context information in the response
            context_info = ""
            vessel_count = 0
            active_vessels = []

            if context:
                if "vessels" in context and context["vessels"]:
                    vessels = context["vessels"]
                    vessel_count = len(vessels)

                    if hasattr(vessels[0], 'status'):
                        active_vessels = [v for v in vessels if hasattr(v, 'status') and v.status and v.status.name in ['LOADING', 'UNLOADING']]

                    if active_vessels:
                        context_info += f"\n\nWith {len(active_vessels)} vessels currently at berth, "

                if "terminal" in context and context["terminal"]:
                    terminal = context["terminal"]
                    context_info += f"and considering your terminal's location in Ghent, Belgium, "

                if "weather" in context and context["weather"] and isinstance(context["weather"], dict) and "wind_speed" in context["weather"]:
                    wind_speed = context["weather"]["wind_speed"]
                    if wind_speed > 12.0:
                        context_info += f"the current wind conditions are significant for operations. "

            # Get actual weather data from context if available
            temp = 18.0
            wind_speed = 14.2
            wind_dir = 225
            conditions = "Partly Cloudy"

            if context and "weather" in context and context["weather"] and isinstance(context["weather"], dict):
                weather = context["weather"]
                if "temperature" in weather:
                    temp = weather["temperature"]
                if "wind_speed" in weather:
                    wind_speed = weather["wind_speed"]
                if "wind_direction" in weather:
                    wind_dir = weather["wind_direction"]
                if "description" in weather:
                    conditions = weather["description"]

            return f"""
            ## Current Weather Conditions{context_info}

            Temperature: {temp}°C
            Wind: {wind_speed} m/s ({round(wind_speed * 1.94384, 1)} knots) from {self._get_wind_direction_text(wind_dir)} ({wind_dir}°)
            Conditions: {conditions}
            Visibility: Good (10km)

            Current weather conditions require caution for vessel operations due to wind speeds above 12 m/s.

            ## Weather Forecast (Next 24 Hours)

            | Time | Conditions | Temperature | Wind Speed | Wind Direction |
            |------|------------|-------------|------------|----------------|
            | 12:00 | Partly Cloudy | 19°C | 14.8 m/s | SW (225°) |
            | 15:00 | Sunny | 21°C | 13.2 m/s | SW (230°) |
            | 18:00 | Cloudy | 20°C | 11.1 m/s | W (260°) |
            | 21:00 | Cloudy | 17°C | 9.9 m/s | W (270°) |
            | 00:00 | Light Rain | 16°C | 7.2 m/s | NW (300°) |
            | 03:00 | Clear | 15°C | 4.3 m/s | NW (310°) |
            | 06:00 | Clear | 14°C | 3.1 m/s | N (350°) |
            | 09:00 | Sunny | 16°C | 2.8 m/s | NE (45°) |

            ## Weather Warnings

            Wind speeds are currently in the CAUTION range (12-17 m/s). Vessel operations should proceed with caution. Wind speeds are expected to decrease throughout the day, with normal operations possible after 18:00.
            """

        # Help request
        elif "help" in user_message_lower or "what can you do" in user_message_lower:
            return """
            I can help you with the following jetty planning tasks:

            1. **Schedule Management**
               - View the current jetty schedule
               - Check jetty availability
               - View scheduled operations

            2. **Vessel Information**
               - List vessels at terminal
               - Check vessels waiting or approaching
               - View vessel details and cargo information

            3. **Schedule Optimization**
               - Optimize jetty assignments
               - Reschedule vessels to maximize throughput
               - Minimize demurrage costs
               - Account for constraints (weather, surveyor availability, etc.)

            4. **Weather Information**
               - Check current weather conditions
               - View weather forecast
               - Assess weather impact on operations

            5. **Resource Management**
               - Check surveyor availability
               - Monitor tank capacity and utilization
               - Track product flow rates and volumes

            6. **Analysis**
               - Calculate terminal throughput
               - Analyze jetty utilization
               - Identify bottlenecks in operations

            How can I assist you with your jetty planning needs today?
            """

        # Default response
        else:
            return """
            I understand you're asking about the jetty planning system. To best assist you, could you provide more specific details about what you'd like to know?

            I can help with:
            - Current jetty schedule and availability
            - Vessel information and status
            - Weather conditions and forecasts
            - Schedule optimization
            - Resource management

            Please let me know what specific information you're looking for.
            """

    def parse_natural_language_command(self, command: str) -> Dict[str, Any]:
        """
        Parse a natural language command into structured parameters.

        Args:
            command: Natural language command from the user.

        Returns:
            Dictionary with parsed command parameters.
        """
        result = {
            "command_type": None,
            "parameters": {}
        }

        # Convert to lowercase for easier matching
        command_lower = command.lower()

        # Check for command types
        if "schedule" in command_lower and ("vessel" in command_lower or "barge" in command_lower):
            result["command_type"] = "schedule_vessel"

            # Try to extract vessel name
            vessel_match = re.search(r"vessel\s+([A-Za-z0-9\s]+)", command, re.IGNORECASE)
            if vessel_match:
                result["parameters"]["vessel_name"] = vessel_match.group(1).strip()

            # Try to extract jetty
            jetty_match = re.search(r"jetty\s+([A-Za-z0-9\-]+)", command, re.IGNORECASE)
            if jetty_match:
                result["parameters"]["jetty_id"] = jetty_match.group(1).strip()

            # Try to extract time
            time_match = re.search(r"(?:at|from)\s+(\d{1,2}(?::\d{2})?(?:\s*[AP]M)?)", command, re.IGNORECASE)
            if time_match:
                result["parameters"]["start_time"] = time_match.group(1).strip()

        elif "optimize" in command_lower or "optimise" in command_lower:
            result["command_type"] = "optimize_schedule"

            # Check for specific optimization parameters
            if "throughput" in command_lower:
                result["parameters"]["maximize_throughput"] = True

            if "demurrage" in command_lower or "waiting time" in command_lower:
                result["parameters"]["minimize_demurrage"] = True

            if "utilization" in command_lower or "utilisation" in command_lower:
                result["parameters"]["maximize_utilization"] = True

        elif "weather" in command_lower:
            result["command_type"] = "get_weather"

            # Check if forecast is requested
            if "forecast" in command_lower:
                result["parameters"]["forecast"] = True

                # Check for specific time period
                days_match = re.search(r"(\d+)\s+day", command_lower)
                if days_match:
                    result["parameters"]["days"] = int(days_match.group(1))

        elif ("vessel" in command_lower or "barge" in command_lower or "ship" in command_lower) and not "schedule" in command_lower:
            result["command_type"] = "get_vessel_info"

            # Try to extract vessel name
            vessel_match = re.search(r"(?:vessel|barge|ship)\s+([A-Za-z0-9\s]+)", command, re.IGNORECASE)
            if vessel_match:
                result["parameters"]["vessel_name"] = vessel_match.group(1).strip()

        elif "schedule" in command_lower and not ("vessel" in command_lower or "barge" in command_lower):
            result["command_type"] = "get_schedule"

            # Check for specific jetty
            jetty_match = re.search(r"jetty\s+([A-Za-z0-9\-]+)", command, re.IGNORECASE)
            if jetty_match:
                result["parameters"]["jetty_id"] = jetty_match.group(1).strip()

            # Check for specific date
            date_match = re.search(r"(?:on|for)\s+(\d{4}-\d{2}-\d{2}|\d{1,2}/\d{1,2}(?:/\d{2,4})?)", command, re.IGNORECASE)
            if date_match:
                result["parameters"]["date"] = date_match.group(1).strip()

        else:
            # Default to general query
            result["command_type"] = "general_query"
            result["parameters"]["query"] = command

        return result

    def start_interactive_session(self):
        """Start an interactive session with the user."""
        print("\nWelcome to the Jetty Planning Assistant. Type 'exit' to end the session.\n")

        while True:
            user_input = input("\nYou: ").strip()

            if user_input.lower() in ["exit", "quit", "bye"]:
                print("\nJetty Planning Assistant: Goodbye! The optimization session has ended.")
                break

            response = self.query(user_input)
            print(f"\nJetty Planning Assistant: {response}")

    def _get_wind_direction_text(self, degrees: float) -> str:
        """
        Convert wind direction in degrees to cardinal direction.

        Args:
            degrees: Wind direction in degrees.

        Returns:
            Cardinal direction (N, NE, E, etc.)
        """
        directions = ["N", "NNE", "NE", "ENE", "E", "ESE", "SE", "SSE",
                      "S", "SSW", "SW", "WSW", "W", "WNW", "NW", "NNW"]
        index = round(degrees / 22.5) % 16
        return directions[index]
