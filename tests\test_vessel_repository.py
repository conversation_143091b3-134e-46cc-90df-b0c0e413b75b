"""
Unit tests for VesselRepository - Database Access Layer
"""

import pytest
from unittest.mock import Mock, MagicMock

from src.repositories.vessel_repository import DatabaseVesselRepository
from src.models.vessel import VesselBase, VesselType


class TestDatabaseVesselRepository:
    """Test cases for DatabaseVesselRepository"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.mock_db = Mock()
        self.repository = DatabaseVesselRepository(self.mock_db)
        self.terminal_id = "TNZN"
    
    def test_get_available_vessels_success(self):
        """Test successful retrieval of available vessels"""
        # Mock nominations and assignments
        mock_nominations = [
            {
                'id': 1,
                'runtime_vessel_id': 'NV001',
                'name': 'Test Vessel',
                'vessel_type': 'tanker',
                'length': 200.0,
                'beam': 30.0,
                'draft': 12.0,
                'deadweight': 80000.0,
                'priority': 1,
                'cargoes': [],
                'metadata': {}
            }
        ]
        
        mock_assignments = [
            {
                'id': 1,
                'vessel_id': 'V001',
                'vessel_name': 'Cancelled Vessel',
                'vessel_type': 'barge',
                'status': 'CANCELLED',
                'cargo_product': 'Chemicals',
                'cargo_volume': 25000.0
            }
        ]
        
        self.mock_db.get_nominations.return_value = mock_nominations
        self.mock_db.get_assignments.return_value = mock_assignments
        
        # Test
        vessels = self.repository.get_available_vessels(self.terminal_id)
        
        # Assertions
        assert len(vessels) == 2
        vessel_ids = {v.id for v in vessels}
        assert 'NV001' in vessel_ids
        assert 'V001' in vessel_ids
        
        # Verify database calls
        self.mock_db.get_nominations.assert_called_once_with(self.terminal_id, status="ACTIVE")
        self.mock_db.get_assignments.assert_called_once_with(self.terminal_id)
    
    def test_get_available_vessels_error_handling(self):
        """Test error handling in get_available_vessels"""
        self.mock_db.get_nominations.side_effect = Exception("Database error")
        
        # Test
        vessels = self.repository.get_available_vessels(self.terminal_id)
        
        # Assertions - should return empty list on error
        assert vessels == []
    
    def test_get_vessel_by_id_from_nomination(self):
        """Test getting vessel by ID from nominations"""
        mock_nominations = [
            {
                'id': 1,
                'runtime_vessel_id': 'NV001',
                'name': 'Test Vessel',
                'vessel_type': 'tanker',
                'length': 200.0,
                'beam': 30.0,
                'draft': 12.0,
                'deadweight': 80000.0,
                'priority': 1,
                'cargoes': [],
                'metadata': {}
            }
        ]
        
        self.mock_db.get_nominations.return_value = mock_nominations
        self.mock_db.get_assignments.return_value = []
        
        # Test
        vessel = self.repository.get_vessel_by_id('NV001', self.terminal_id)
        
        # Assertions
        assert vessel is not None
        assert vessel.id == 'NV001'
        assert vessel.name == 'Test Vessel'
        assert vessel.vessel_type == VesselType.tanker
    
    def test_get_vessel_by_id_from_assignment(self):
        """Test getting vessel by ID from cancelled assignments"""
        mock_assignments = [
            {
                'id': 1,
                'vessel_id': 'V001',
                'vessel_name': 'Cancelled Vessel',
                'vessel_type': 'barge',
                'status': 'CANCELLED',
                'cargo_product': 'Oil',
                'cargo_volume': 50000.0
            }
        ]
        
        self.mock_db.get_nominations.return_value = []
        self.mock_db.get_assignments.return_value = mock_assignments
        
        # Test
        vessel = self.repository.get_vessel_by_id('V001', self.terminal_id)
        
        # Assertions
        assert vessel is not None
        assert vessel.id == 'V001'
        assert vessel.name == 'Cancelled Vessel'
        assert vessel.vessel_type == VesselType.barge
        assert vessel.metadata['source'] == 'unscheduled_assignment'
    
    def test_get_vessel_by_id_from_database_vessel(self):
        """Test getting vessel by ID from database vessels"""
        mock_vessel_data = {
            'id': 123,
            'name': 'DB Vessel',
            'type': 'tanker',
            'status': 'APPROACHING',
            'total_cargo_volume': 75000
        }
        
        self.mock_db.get_nominations.return_value = []
        self.mock_db.get_assignments.return_value = []
        self.mock_db.get_vessel.return_value = mock_vessel_data
        self.mock_db.get_cargoes_by_vessel.return_value = []
        
        # Test
        vessel = self.repository.get_vessel_by_id('123', self.terminal_id)
        
        # Assertions
        assert vessel is not None
        assert vessel.id == '123'
        assert vessel.name == 'DB Vessel'
        assert vessel.vessel_type == VesselType.tanker
        assert vessel.metadata['source'] == 'database_vessel'
        
        # Verify database calls
        self.mock_db.get_vessel.assert_called_once_with(123, self.terminal_id)
        self.mock_db.get_cargoes_by_vessel.assert_called_once_with(123)
    
    def test_get_vessel_by_id_not_found(self):
        """Test getting vessel by ID when not found"""
        self.mock_db.get_nominations.return_value = []
        self.mock_db.get_assignments.return_value = []
        self.mock_db.get_vessel.return_value = None
        
        # Test
        vessel = self.repository.get_vessel_by_id('NONEXISTENT', self.terminal_id)
        
        # Assertions
        assert vessel is None
    
    def test_save_vessel_nomination_success(self):
        """Test successful vessel nomination saving"""
        vessel_data = {
            'runtime_vessel_id': 'NV002',
            'name': 'New Vessel',
            'vessel_type': 'tanker'
        }
        
        self.mock_db.add_nomination.return_value = 1
        
        # Test
        vessel_id = self.repository.save_vessel_nomination(vessel_data, self.terminal_id)
        
        # Assertions
        assert vessel_id == 'NV002'
        self.mock_db.add_nomination.assert_called_once()
        call_args = self.mock_db.add_nomination.call_args[0][0]
        assert call_args['terminal_id'] == self.terminal_id
        assert call_args['status'] == 'ACTIVE'
    
    def test_save_vessel_nomination_error(self):
        """Test error handling in save_vessel_nomination"""
        vessel_data = {'runtime_vessel_id': 'NV002'}
        self.mock_db.add_nomination.side_effect = Exception("Database error")
        
        # Test
        with pytest.raises(Exception):
            self.repository.save_vessel_nomination(vessel_data, self.terminal_id)
    
    def test_delete_vessel_success(self):
        """Test successful vessel deletion"""
        self.mock_db.delete_vessel.return_value = True
        
        # Test
        result = self.repository.delete_vessel('V001', self.terminal_id)
        
        # Assertions
        assert result is True
        self.mock_db.delete_vessel.assert_called_once_with('V001')
    
    def test_delete_vessel_failure(self):
        """Test vessel deletion failure"""
        self.mock_db.delete_vessel.return_value = False
        
        # Test
        result = self.repository.delete_vessel('V001', self.terminal_id)
        
        # Assertions
        assert result is False
    
    def test_delete_vessel_error(self):
        """Test error handling in delete_vessel"""
        self.mock_db.delete_vessel.side_effect = Exception("Database error")
        
        # Test
        result = self.repository.delete_vessel('V001', self.terminal_id)
        
        # Assertions
        assert result is False
    
    def test_get_vessel_count(self):
        """Test getting vessel count"""
        # Mock vessels
        mock_vessels = [Mock(), Mock(), Mock()]  # 3 vessels
        
        with pytest.mock.patch.object(self.repository, 'get_available_vessels', return_value=mock_vessels):
            # Test
            count = self.repository.get_vessel_count(self.terminal_id)
            
            # Assertions
            assert count == 3
    
    def test_get_vessel_count_error(self):
        """Test error handling in get_vessel_count"""
        with pytest.mock.patch.object(self.repository, 'get_available_vessels', side_effect=Exception("Error")):
            # Test
            count = self.repository.get_vessel_count(self.terminal_id)
            
            # Assertions
            assert count == 0
    
    def test_nomination_to_vessel_conversion_with_cargoes(self):
        """Test nomination to vessel conversion with cargoes"""
        nomination = {
            'id': 1,
            'runtime_vessel_id': 'NV001',
            'name': 'Test Vessel',
            'vessel_type': 'tanker',
            'length': 200.0,
            'beam': 30.0,
            'draft': 12.0,
            'deadweight': 80000.0,
            'priority': 2,
            'capacity': 100000,
            'width': 32.0,
            'customer': 'Test Customer',
            'cargoes': [
                {
                    'product': 'Crude Oil',
                    'volume': 50000.0,
                    'is_loading': True
                },
                {
                    'product': 'Gasoline',
                    'volume': 30000.0,
                    'is_loading': False
                }
            ],
            'eta': '2024-01-01T10:00:00',
            'etd': '2024-01-01T16:00:00',
            'mmsi': '123456789',
            'imo': 'IMO1234567',
            'metadata': {'test_key': 'test_value'}
        }
        
        # Test
        vessel = self.repository._nomination_to_vessel(nomination)
        
        # Assertions
        assert vessel.id == 'NV001'
        assert vessel.name == 'Test Vessel'
        assert vessel.vessel_type == VesselType.tanker
        assert vessel.length == 200.0
        assert vessel.beam == 30.0
        assert vessel.draft == 12.0
        assert vessel.deadweight == 80000.0
        assert vessel.priority == 2
        assert vessel.capacity == 100000
        assert vessel.width == 32.0
        assert vessel.customer == 'Test Customer'
        assert vessel.status == "APPROACHING"
        
        # Check cargoes
        assert len(vessel.cargoes) == 2
        assert vessel.cargoes[0].product == 'Crude Oil'
        assert vessel.cargoes[0].volume == 50000.0
        assert vessel.cargoes[0].is_loading is True
        assert vessel.cargoes[1].product == 'Gasoline'
        assert vessel.cargoes[1].volume == 30000.0
        assert vessel.cargoes[1].is_loading is False
        
        # Check metadata
        assert vessel.metadata['source'] == 'nomination'
        assert vessel.metadata['nomination_id'] == 1
        assert vessel.metadata['mmsi'] == '123456789'
        assert vessel.metadata['imo'] == 'IMO1234567'
        assert vessel.metadata['test_key'] == 'test_value'
        
        # Check timing
        assert vessel.eta is not None
        assert vessel.etd is not None
    
    def test_assignment_to_vessel_conversion(self):
        """Test assignment to vessel conversion"""
        assignment = {
            'id': 1,
            'vessel_id': 'V001',
            'vessel_name': 'Cancelled Vessel',
            'vessel_type': 'barge',
            'cargo_product': 'Chemicals',
            'cargo_volume': 25000.0,
            'jetty_name': 'Jetty 1'
        }
        
        # Test
        vessel = self.repository._assignment_to_vessel(assignment)
        
        # Assertions
        assert vessel.id == 'V001'
        assert vessel.name == 'Cancelled Vessel'
        assert vessel.vessel_type == VesselType.barge
        assert vessel.status == "APPROACHING"
        assert vessel.capacity == 25000.0
        
        # Check cargo
        assert len(vessel.cargoes) == 1
        assert vessel.cargoes[0].product == 'Chemicals'
        assert vessel.cargoes[0].volume == 25000.0
        assert vessel.cargoes[0].is_loading is True
        
        # Check metadata
        assert vessel.metadata['source'] == 'unscheduled_assignment'
        assert vessel.metadata['original_assignment_id'] == 1
        assert vessel.metadata['original_jetty'] == 'Jetty 1'
