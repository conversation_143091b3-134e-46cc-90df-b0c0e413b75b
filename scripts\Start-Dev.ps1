Param(
    [int]$Port = 7070,
    [switch]$Rebuild
)

# Ensure we are at repo root
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location (Join-Path $scriptDir "..")

Write-Host "Starting Jetty Planner dev container on host port $Port (container 7000)..." -ForegroundColor Cyan

# Confirm Docker is available
if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
    Write-Error "Docker is not installed or not in PATH. Install Docker Desktop first."
    exit 1
}

# Optional rebuild
if ($Rebuild) {
    Write-Host "Rebuilding image..." -ForegroundColor Yellow
    docker compose -f docker-compose.dev.yml build | cat
}

# Adjust port mapping dynamically if user overrides Port
if ($Port -ne 7070) {
    $overrideFile = ".docker-dev.override.yml"
    @"
services:
  jetty-planning-app-dev:
    ports:
      - "${Port}:7000"
"@ | Set-Content -Encoding UTF8 $overrideFile

    try {
        docker compose -f docker-compose.dev.yml -f $overrideFile up -d | cat
    } finally {
        Remove-Item $overrideFile -ErrorAction SilentlyContinue
    }
} else {
    docker compose -f docker-compose.dev.yml up -d | cat
}

Write-Host "Dev instance should be available at http://localhost:$Port" -ForegroundColor Green

