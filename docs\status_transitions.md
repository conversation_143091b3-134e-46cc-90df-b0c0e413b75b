# Status Standardization Documentation

## Vessel Statuses

| Status | Description | Color Code | Next Possible Statuses |
|--------|-------------|------------|------------------------|
| SCHEDULED | Vessel is scheduled but not yet approaching | Blue (#3498db) | APPROACHING |
| APPROACHING | Vessel is en route to the terminal | Purple (#9b59b6) | ARRIVED |
| ARRIVED | Vessel has arrived but not yet docked | Teal (#1abc9c) | DOCKED |
| DOCKED | Vessel is docked at a jetty | Green (#2ecc71) | LOADING, UNLOADING, DEPARTING |
| LOADING | Vessel is currently loading cargo | Orange (#e67e22) | DOCKED, DEPARTING |
| UNLOADING | Vessel is currently unloading cargo | Yellow (#f1c40f) | DOCKED, DEPARTING |
| DEPARTING | Vessel is in the process of departing | Pink (#e91e63) | DEPARTED |
| DEPARTED | Vessel has left the terminal | Gray (#95a5a6) | (Terminal State) |
| DELAYED | Vessel is delayed from its scheduled time | Red (#e74c3c) | Any status except DEPARTED |
| CANCELLED | Vessel's visit has been cancelled | Dark Gray (#7f8c8d) | (Terminal State) |

## Assignment Statuses

| Status | Description | Color Code | Related Vessel Status |
|--------|-------------|------------|------------------------|
| SCHEDULED | Assignment is scheduled but not active | Blue (#3498db) | SCHEDULED, APPROACHING |
| ACTIVE | Assignment is currently in progress | Green (#2ecc71) | DOCKED, LOADING, UNLOADING |
| COMPLETED | Assignment has been completed | Gray (#95a5a6) | DEPARTING, DEPARTED |
| CANCELLED | Assignment has been cancelled | Dark Gray (#7f8c8d) | CANCELLED |
| DELAYED | Assignment is delayed from scheduled time | Red (#e74c3c) | DELAYED |
| PENDING_APPROVAL | Assignment requires approval | Orange (#e67e22) | Any |

## Status Transition Rules

### Vessel Status Transitions
1. **SCHEDULED** → **APPROACHING** → **ARRIVED** → **DOCKED** is the normal progression.
2. From **DOCKED**, a vessel can transition to **LOADING**, **UNLOADING**, or directly to **DEPARTING**.
3. From **LOADING** or **UNLOADING**, a vessel can return to **DOCKED** or proceed to **DEPARTING**.
4. **DEPARTING** only transitions to **DEPARTED**.
5. **DELAYED** status can be applied at any point before **DEPARTED**.
6. **CANCELLED** is a terminal state that can be applied at any point but typically before **DOCKED**.

### Assignment Status Transitions
1. **PENDING_APPROVAL** → **SCHEDULED** → **ACTIVE** → **COMPLETED** is the normal progression.
2. **DELAYED** status can be applied to **SCHEDULED** or **ACTIVE** assignments.
3. **CANCELLED** can be applied at any point before **COMPLETED**.

## Status Relationship Rules

1. When a vessel status changes to **DOCKED**, any related assignments should become **ACTIVE**.
2. When a vessel status changes to **DEPARTING** or **DEPARTED**, any related assignments should become **COMPLETED**.
3. When a vessel status changes to **CANCELLED**, any related assignments should become **CANCELLED**.
4. When a vessel status changes to **DELAYED**, any impacted assignments should become **DELAYED**.

## Implementation Guidelines

1. Status values should be stored in UPPERCASE in the database for consistency.
2. Status comparisons should be case-insensitive throughout the application.
3. UI components should display status with consistent styling using the color codes provided.
4. Status changes should trigger appropriate notifications to relevant users.
5. The system should validate status transitions to ensure only valid transitions occur.
6. A status history should be maintained for audit purposes.

## Status Display Components

1. Use the `status-badge` CSS class for consistent display across the application.
2. Include a status legend component on pages where multiple statuses are displayed.
3. Ensure status colors are accessible and meet contrast requirements. 