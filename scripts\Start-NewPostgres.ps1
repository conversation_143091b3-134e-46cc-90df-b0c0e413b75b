# Start Full Application Stack with PostgreSQL on Port 7432
# Usage: .\scripts\Start-NewPostgres.ps1

Write-Host "Starting full application stack with PostgreSQL on port 7432..." -ForegroundColor Green

# Start all services (app + database)
docker-compose up -d

# Wait for the container to be ready
Write-Host "Waiting for PostgreSQL to be ready..." -ForegroundColor Yellow
$maxAttempts = 30
$attempt = 0

do {
    Start-Sleep -Seconds 2
    $attempt++
    $healthStatus = docker inspect jetty-postgres --format='{{.State.Health.Status}}' 2>$null
    Write-Host "Attempt $attempt/$maxAttempts - Health Status: $healthStatus" -ForegroundColor Cyan
} while ($healthStatus -ne "healthy" -and $attempt -lt $maxAttempts)

if ($healthStatus -eq "healthy") {
    Write-Host "✅ New PostgreSQL instance is ready on port 7432!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Update your .env file to use DB_PORT=7432" -ForegroundColor White
    Write-Host "2. Run database migrations: python -m alembic upgrade head" -ForegroundColor White
    Write-Host "3. Optionally migrate data from old instance (port 4432)" -ForegroundColor White
    Write-Host ""
    Write-Host "Container details:" -ForegroundColor Yellow
    docker ps --filter "name=jetty-postgres" --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
    Write-Host ""
    Write-Host "Application details:" -ForegroundColor Yellow
    docker ps --filter "name=jetty-planning-app" --format "table {{.Names}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
} else {
    Write-Host "❌ Failed to start PostgreSQL instance. Check logs:" -ForegroundColor Red
    docker-compose logs postgres
}
