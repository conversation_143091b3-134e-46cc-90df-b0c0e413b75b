"""ETA Standardization Migration

Revision ID: eta_standardization
Revises: 33d8ae7a1d9a
Create Date: 2024-01-15 10:00:00.000000

This migration standardizes ETA field names across all tables to ensure consistency.

STANDARDIZED ETA FIELDS:
- eta: User-specified/customer-provided estimated time of arrival
- calculated_eta: System-calculated ETA based on AIS position/conditions  
- actual_arrival: Actual recorded arrival time
- eta_confidence: Confidence score for ETA prediction (0-100)
- eta_source: Source of ETA ("user", "ais_calculated", "ml_predicted")

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'eta_standardization'
down_revision = '33d8ae7a1d9a'
branch_labels = None
depends_on = None


def upgrade():
    """Add standardized ETA columns to all relevant tables"""
    
    # 1. NOMINATIONS TABLE - Add new standardized columns
    print("Adding standardized ETA columns to nominations table...")
    op.add_column('nominations', sa.Column('calculated_eta', sa.DateTime(timezone=True), nullable=True))
    op.add_column('nominations', sa.Column('eta_confidence', sa.Integer(), nullable=False, server_default='50'))
    op.add_column('nominations', sa.Column('eta_source', sa.String(length=20), nullable=False, server_default='user'))
    
    # 2. VESSEL_VISITS TABLE - Rename and add columns
    print("Standardizing vessel_visits table ETA columns...")
    # Rename estimated_arrival to eta for consistency
    op.alter_column('vessel_visits', 'estimated_arrival', new_column_name='eta')
    # Add new standardized columns
    op.add_column('vessel_visits', sa.Column('calculated_eta', sa.DateTime(timezone=True), nullable=True))
    op.add_column('vessel_visits', sa.Column('eta_confidence', sa.Integer(), nullable=False, server_default='50'))
    op.add_column('vessel_visits', sa.Column('eta_source', sa.String(length=20), nullable=False, server_default='user'))
    
    # 3. ASSIGNMENTS TABLE - Add ETA tracking
    print("Adding ETA tracking to assignments table...")
    op.add_column('assignments', sa.Column('original_eta', sa.DateTime(timezone=True), nullable=True))
    op.add_column('assignments', sa.Column('calculated_eta', sa.DateTime(timezone=True), nullable=True))
    op.add_column('assignments', sa.Column('eta_confidence', sa.Integer(), nullable=False, server_default='50'))
    op.add_column('assignments', sa.Column('eta_source', sa.String(length=20), nullable=False, server_default='user'))
    
    # 4. Create indexes for performance
    print("Creating indexes for ETA queries...")
    op.create_index('ix_nominations_eta', 'nominations', ['eta'])
    op.create_index('ix_nominations_calculated_eta', 'nominations', ['calculated_eta'])
    op.create_index('ix_vessel_visits_eta', 'vessel_visits', ['eta'])
    op.create_index('ix_vessel_visits_calculated_eta', 'vessel_visits', ['calculated_eta'])
    op.create_index('ix_assignments_original_eta', 'assignments', ['original_eta'])
    
    # 5. Data migration - copy existing data to new standardized format
    print("Migrating existing ETA data...")
    
    # For nominations: eta column already exists and is correct
    # Set eta_source to 'user' for existing nominations with ETA
    op.execute("""
        UPDATE nominations 
        SET eta_source = 'user' 
        WHERE eta IS NOT NULL
    """)
    
    # For vessel_visits: actual_arrival column already exists and is correct
    # Set eta_source to 'user' for existing visits with ETA
    op.execute("""
        UPDATE vessel_visits 
        SET eta_source = 'user' 
        WHERE eta IS NOT NULL
    """)
    
    print("ETA standardization migration completed successfully!")


def downgrade():
    """Remove standardized ETA columns and revert changes"""
    
    print("Reverting ETA standardization...")
    
    # Remove indexes
    op.drop_index('ix_assignments_original_eta', table_name='assignments')
    op.drop_index('ix_vessel_visits_calculated_eta', table_name='vessel_visits')
    op.drop_index('ix_vessel_visits_eta', table_name='vessel_visits')
    op.drop_index('ix_nominations_calculated_eta', table_name='nominations')
    op.drop_index('ix_nominations_eta', table_name='nominations')
    
    # Remove columns from assignments
    op.drop_column('assignments', 'eta_source')
    op.drop_column('assignments', 'eta_confidence')
    op.drop_column('assignments', 'calculated_eta')
    op.drop_column('assignments', 'original_eta')
    
    # Revert vessel_visits table
    op.drop_column('vessel_visits', 'eta_source')
    op.drop_column('vessel_visits', 'eta_confidence')
    op.drop_column('vessel_visits', 'calculated_eta')
    op.alter_column('vessel_visits', 'eta', new_column_name='estimated_arrival')
    
    # Remove columns from nominations
    op.drop_column('nominations', 'eta_source')
    op.drop_column('nominations', 'eta_confidence')
    op.drop_column('nominations', 'calculated_eta')
    
    print("ETA standardization reverted successfully!")
