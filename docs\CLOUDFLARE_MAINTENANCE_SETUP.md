# Cloudflare Maintenance Mode Setup Guide

This guide shows how to implement maintenance pages using Cloudflare's built-in features for your **Docker → nginx → Cloudflare → Cloudflare Zero Trust** setup.

## 🎯 **Cloudflare vs nginx Approach**

| Feature | Cloudflare Workers | Cloudflare Access | nginx (Current) |
|---------|-------------------|-------------------|-----------------|
| **Global Edge** | ✅ Served from 300+ locations | ✅ Global Zero Trust | ❌ Single server |
| **Works when server is down** | ✅ Completely independent | ✅ Independent | ❌ Requires nginx running |
| **IP Whitelisting** | ✅ Built-in | ✅ Built-in | ⚠️ Manual config |
| **Zero Trust Integration** | ⚠️ Separate system | ✅ Native | ❌ Bypasses Access |
| **Setup Complexity** | ⭐⭐ Medium | ⭐ Easy | ⭐⭐⭐ Complex |
| **Cost** | 💰 $5/month for 10M requests | 💰 Included with Zero Trust | 💰 Free |

## 🚀 **Option 1: Cloudflare Workers (Recommended)**

### Setup Steps

1. **Go to Cloudflare Dashboard**
   - Navigate to **Workers & Pages** → **Overview**
   - Click **Create application** → **Create Worker**

2. **Deploy the Worker**
   - Name it `evos-maintenance-worker`
   - Replace the default code with the content from `cloudflare/maintenance-worker.js`
   - **Important**: Update the `allowedIPs` array with your actual IP addresses:
     ```javascript
     const allowedIPs = [
       '***********',     // Replace with your office IP
       '************',    // Replace with your home IP
     ]
     ```

3. **Set Up Routes** (when you need maintenance)
   - Go to **Websites** → **planner.evosgpt.eu** → **Workers Routes**
   - Add route: `planner.evosgpt.eu/*` → `evos-maintenance-worker`
   - **Save**

### Usage

```powershell
# Enable maintenance mode
# 1. Go to Cloudflare Dashboard
# 2. Add the worker route: planner.evosgpt.eu/* -> evos-maintenance-worker
# 3. Done! Site shows maintenance page globally

# Disable maintenance mode  
# 1. Go to Cloudflare Dashboard
# 2. Remove/disable the worker route
# 3. Done! Site returns to normal
```

### Benefits
- **⚡ Lightning fast**: Served from Cloudflare's global edge network
- **🌍 Geographic redundancy**: Works even if your entire server is down
- **🔒 IP whitelisting**: Your team can still access the site during maintenance
- **📱 Mobile-optimized**: Same beautiful design, optimized for all devices

---

## 🛡️ **Option 2: Cloudflare Access Maintenance Mode**

Perfect since you're already using Zero Trust!

### Setup Steps

1. **Create Maintenance Policy**
   - Go to **Zero Trust** → **Access** → **Applications**
   - Find your `planner.evosgpt.eu` application
   - Click **Edit**

2. **Add Maintenance Rule**
   - In **Policies** section, add a new policy:
     - **Name**: `Maintenance Mode - Block All`
     - **Action**: `Block`
     - **Include**: `Everyone`
   - **Important**: Place this policy ABOVE your normal allow policies

3. **Configure Block Page**
   - Go to **Settings** → **Custom pages**
   - Edit **Blocked** page
   - Upload your custom maintenance HTML

### Usage

```powershell
# Enable maintenance mode
# 1. Go to Zero Trust Dashboard
# 2. Edit planner.evosgpt.eu application  
# 3. Enable the "Maintenance Mode - Block All" policy
# 4. Move it to the top of the policy list

# Disable maintenance mode
# 1. Go to Zero Trust Dashboard
# 2. Disable or delete the maintenance mode policy
```

### Benefits
- **🔐 Native Zero Trust integration**: Uses your existing security setup
- **👥 Team access**: Easily allow specific team members during maintenance
- **📊 Audit logs**: Full visibility of who accessed what during maintenance
- **🎛️ Granular control**: Can allow specific paths/API endpoints if needed

---

## 🎯 **Option 3: Page Rules (Simplest)**

### Setup Steps

1. **Create Maintenance Page**
   - Host your maintenance.html somewhere static (GitHub Pages, Netlify, etc.)
   - Example: `https://your-username.github.io/maintenance.html`

2. **Create Page Rule**
   - Go to **Rules** → **Page Rules**
   - **URL pattern**: `planner.evosgpt.eu/*`
   - **Setting**: `Forwarding URL` → `302 Temporary Redirect`
   - **Destination**: `https://your-maintenance-page.html`

### Usage

```powershell
# Enable maintenance mode
# 1. Go to Cloudflare Dashboard → Page Rules
# 2. Enable the maintenance redirect rule

# Disable maintenance mode  
# 1. Disable the page rule
```

---

## 📊 **Comparison: Which Should You Choose?**

### **For Your Setup, I Recommend:**

1. **🥇 Cloudflare Workers** - Best overall solution
   - Perfect performance and reliability
   - Easy IP whitelisting for your team
   - Works independently of your infrastructure

2. **🥈 Keep nginx solution + Workers as backup**
   - Use nginx for quick maintenance
   - Use Workers for major outages/infrastructure work

3. **🥉 Cloudflare Access** - If you want Zero Trust integration
   - Great if you need team collaboration during maintenance
   - More complex to set up but powerful

### **Recommended Hybrid Approach:**

```powershell
# Quick maintenance (server restarts, updates)
.\scripts\Enable-MaintenanceMode.ps1

# Major maintenance (infrastructure work, long outages)  
# Use Cloudflare Workers route activation
```

## 🔧 **Implementation Steps**

### Phase 1: Set Up Cloudflare Workers (15 minutes)
1. Deploy the worker using `cloudflare/maintenance-worker.js`
2. Update IP addresses in the script
3. Test by temporarily adding a route

### Phase 2: Update Current nginx (5 minutes)
1. Keep your existing nginx maintenance setup
2. Update documentation to mention Cloudflare option

### Phase 3: Create Management Scripts (10 minutes)
1. Create PowerShell scripts to enable/disable Cloudflare routes
2. Add Cloudflare API integration for automation

## 🚨 **Emergency Procedures**

### When Everything is Down
1. **Cloudflare Workers**: Add route `planner.evosgpt.eu/*` → `evos-maintenance-worker`
2. **Result**: Maintenance page served globally in under 60 seconds

### When nginx is Down but Docker Works
1. **nginx failover**: Your current setup handles this automatically
2. **Cloudflare backup**: Workers route as secondary option

### When You Need Team Access During Maintenance
1. **Workers**: Add your IPs to the whitelist
2. **Access**: Create temporary allow policy for your team

## 💡 **Pro Tips**

1. **Test both systems**: Keep nginx setup AND set up Workers as backup
2. **IP whitelist**: Always add your current IP to Workers before enabling maintenance
3. **Monitor**: Use Cloudflare Analytics to see maintenance page traffic
4. **Automation**: Consider Cloudflare API scripts for automated maintenance windows

Would you like me to help you set up any of these Cloudflare options?
