from __future__ import annotations

from datetime import datetime, timezone, date, timedelta
from typing import Optional, Union


def normalize_iso_to_utc_microseconds(value: Union[str, datetime, None]) -> Optional[datetime]:
    """Parse an incoming ISO-like datetime and normalize to UTC with microsecond precision (<=6).

    - Accepts strings with 'Z' or timezone offsets.
    - Trims/pads fractional seconds to max 6 digits.
    - Returns timezone-aware datetime in UTC.
    - Returns None if value is falsy or cannot be parsed.
    """
    if value is None:
        return None
    if isinstance(value, datetime):
        dt = value
        if dt.tzinfo is None:
            # Assume naive means UTC
            return dt.replace(tzinfo=timezone.utc)
        return dt.astimezone(timezone.utc)
    if not isinstance(value, str):
        return None

    s = value.strip()
    if not s:
        return None

    # Normalize 'Z' to '+00:00'
    if s.endswith('Z'):
        s = s[:-1] + '+00:00'

    # Clamp fractional seconds to 6 digits while preserving timezone
    if '.' in s:
        head, rest = s.split('.', 1)
        frac = rest
        tz = ''
        plus_idx = rest.find('+')
        minus_idx = rest.find('-')
        sep_idx = -1
        if plus_idx != -1 and minus_idx != -1:
            sep_idx = min(plus_idx, minus_idx)
        elif plus_idx != -1:
            sep_idx = plus_idx
        elif minus_idx != -1:
            sep_idx = minus_idx

        if sep_idx != -1:
            frac, tz = rest[:sep_idx], rest[sep_idx:]
        else:
            frac, tz = rest, ''

        frac_digits = ''.join(ch for ch in frac if ch.isdigit())
        if len(frac_digits) > 6:
            frac_digits = frac_digits[:6]
        elif len(frac_digits) > 0:
            frac_digits = frac_digits.ljust(6, '0')

        s = f"{head}.{frac_digits}{tz}" if frac_digits else f"{head}{tz}"

    # Try parsing
    try:
        dt = datetime.fromisoformat(s)
    except Exception:
        try:
            dt = datetime.fromisoformat(s.replace(' ', 'T'))
        except Exception:
            return None

    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(timezone.utc)


def to_utc_end_of_day(d: Union[date, datetime]) -> datetime:
    """Convert a date/datetime to the last microsecond of that day in UTC.

    If a datetime with tz is provided, it is converted to UTC and clamped to end-of-day in that date in UTC.
    """
    if isinstance(d, datetime):
        # Convert to UTC then clamp
        dt_utc = d.astimezone(timezone.utc) if d.tzinfo else d.replace(tzinfo=timezone.utc)
        eod = dt_utc.replace(hour=23, minute=59, second=59, microsecond=999999)
        return eod
    # date instance
    return datetime(d.year, d.month, d.day, 23, 59, 59, 999999, tzinfo=timezone.utc)


def ensure_utc(dt: Optional[datetime]) -> Optional[datetime]:
    """Ensure a datetime is timezone-aware in UTC."""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt.astimezone(timezone.utc)


