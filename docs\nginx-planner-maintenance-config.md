# Nginx Maintenance Mode Configuration for <PERSON><PERSON> Planner

This document shows how to modify your nginx configuration to support maintenance mode with automatic fallback.

## Updated planner.evosgpt.eu Server Block

Replace the planner server block (lines 288-399) in your nginx.conf with this enhanced version:

```nginx
# ───── planner.evosgpt.eu (<PERSON><PERSON> Planner via Cloudflare Tunnel) ───────────
server {
    # Local-only: cloudflared connects here
    listen 127.0.0.1:8080;
    server_name planner.evosgpt.eu;

    # Optional ACME path (harmless if unused)
    location /.well-known/acme-challenge/ {
        root C:/Users/<USER>/nginx-1.26.3/html;
    }

    client_max_body_size 100M;

    # ---- MAINTENANCE MODE CHECK ----
    # If maintenance.flag exists, serve maintenance page for all requests
    location @maintenance {
        root C:/Users/<USER>/Jettyplanner/src/static;
        try_files /maintenance.html =503;
        
        # Security headers for maintenance page
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header Expires "0" always;
    }

    # ---- ERROR PAGES (when upstream is down) ----
    error_page 502 503 504 @maintenance;

    # ---- API ----
    location /api/ {
        # Check for maintenance flag file
        if (-f C:/Users/<USER>/Jettyplanner/maintenance.flag) {
            return 503;
        }
        
        if ($has_user = 0) { return 403; }  # require Access header
        limit_req zone=api burst=20 nodelay;

        proxy_pass http://planner/api/;
        proxy_set_header Host              $host;
        proxy_set_header X-Real-IP         $remote_addr;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-CSP-Nonce       $csp_nonce;

        proxy_http_version 1.1;
        proxy_set_header Upgrade           $http_upgrade;
        proxy_set_header Connection        "upgrade";

        proxy_buffering off;
        proxy_connect_timeout 10s;
        proxy_send_timeout    300s;
        proxy_read_timeout    300s;
    }

    # ---- Backup Files Browser ----
    location /backups/ {
        # Check for maintenance flag file
        if (-f C:/Users/<USER>/Jettyplanner/maintenance.flag) {
            return 503;
        }
        
        if ($has_user = 0) { return 403; }  # require Cloudflare Access
        
        alias "C:/Users/<USER>/Jettyplanner/backups/";
        autoindex on;
        autoindex_exact_size off;
        autoindex_localtime on;
        autoindex_format html;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        
        # Force download for backup files
        location ~* \.(sql|gz|tar|dump)$ {
            if ($has_user = 0) { return 403; }
            add_header Content-Disposition 'attachment';
            add_header Cache-Control "private, no-cache, no-store, must-revalidate";
        }
        
        # Deny access to hidden files
        location ~ /\. {
            deny all;
        }
    }

    # ---- Static assets: enable long caching for fonts ----
    location ~* ^/static/fonts/.*\.(woff2|woff|ttf)$ {
        # Check for maintenance flag file
        if (-f C:/Users/<USER>/Jettyplanner/maintenance.flag) {
            return 503;
        }
        
        if ($has_user = 0) { return 403; }
        proxy_pass http://planner;
        proxy_set_header Host $host;
        add_header Cache-Control $font_cache_control always;
    }

    # ---- UI / websockets / everything else ----
    location / {
        # Check for maintenance flag file
        if (-f C:/Users/<USER>/Jettyplanner/maintenance.flag) {
            return 503;
        }
        
        if ($has_user = 0) { return 403; }  # require Access header

        proxy_pass http://planner;
        proxy_set_header Host              $host;
        proxy_set_header X-Real-IP         $remote_addr;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-CSP-Nonce       $csp_nonce;

        proxy_http_version 1.1;
        proxy_set_header Upgrade           $http_upgrade;
        proxy_set_header Connection        "upgrade";

        proxy_buffering off;
        proxy_connect_timeout 10s;
        proxy_send_timeout    300s;
        proxy_read_timeout    300s;

        # Security headers
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer" always;
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

        # Content Security Policy
        # - Keep nonce for scripts we generate server-side
        # - Allow unsafe-inline styles only if needed (Bootstrap inline)
        # - Allow Google Fonts for external font loading
        add_header Content-Security-Policy "
          default-src 'self';
          script-src 'self' 'nonce-$csp_nonce';
          style-src  'self' 'unsafe-inline' https://fonts.googleapis.com;
          font-src   'self' data: https://fonts.gstatic.com https://r2cdn.perplexity.ai;
          img-src    'self' data: blob: https://*.tile.openstreetmap.org;
          connect-src 'self' https: wss:;
          frame-ancestors 'none';
          base-uri 'self';
        " always;
    }
}
```

## How It Works

### 1. Manual Maintenance Mode
- Create a file `C:/Users/<USER>/Jettyplanner/maintenance.flag`
- All requests will serve the maintenance page
- Remove the file to exit maintenance mode

### 2. Automatic Maintenance Mode
- When your Docker container is down/unreachable
- Nginx automatically serves the maintenance page for 502/503/504 errors
- No manual intervention needed

### 3. Flag File Location
The flag file should be created in your project root:
`C:/Users/<USER>/Jettyplanner/maintenance.flag`

## Deployment Steps

1. **Update nginx.conf**: Replace the planner server block with the version above
2. **Test configuration**: Run `nginx -t`
3. **Reload nginx**: Run `nginx -s reload`
4. **Test maintenance mode**: Create the flag file and verify the maintenance page displays

## Security Features

- Maintenance page bypasses Cloudflare Access for better UX during maintenance
- Proper cache headers prevent caching of maintenance page
- Same CSP and security headers as normal operation
- Flag file approach prevents accidental maintenance mode activation
