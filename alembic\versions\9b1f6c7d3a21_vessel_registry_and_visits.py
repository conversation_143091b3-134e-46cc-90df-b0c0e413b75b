"""vessel registry and visits

Revision ID: 9b1f6c7d3a21
Revises: ea2b9a4c2d11
Create Date: 2025-09-03 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9b1f6c7d3a21'
down_revision: Union[str, None] = 'ea2b9a4c2d11'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create vessel_registry table
    op.create_table(
        'vessel_registry',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('imo', sa.String(length=10), unique=True, nullable=True),
        sa.Column('mmsi', sa.String(length=9), unique=True, nullable=True),
        sa.Column('call_sign', sa.String(length=10), nullable=True),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('previous_names', sa.JSON(), nullable=True),
        sa.Column('vessel_type', sa.String(length=50), nullable=False),
        sa.Column('vessel_subtype', sa.String(length=50), nullable=True),
        sa.Column('deadweight', sa.Float(), nullable=True),
        sa.Column('gross_tonnage', sa.Float(), nullable=True),
        sa.Column('length_overall', sa.Float(), nullable=True),
        sa.Column('beam', sa.Float(), nullable=True),
        sa.Column('maximum_draft', sa.Float(), nullable=True),
        sa.Column('flag_state', sa.String(length=3), nullable=True),
        sa.Column('port_of_registry', sa.String(length=100), nullable=True),
        sa.Column('owner', sa.String(length=255), nullable=True),
        sa.Column('operator', sa.String(length=255), nullable=True),
        sa.Column('manager', sa.String(length=255), nullable=True),
        sa.Column('build_year', sa.Integer(), nullable=True),
        sa.Column('shipyard', sa.String(length=255), nullable=True),
        sa.Column('hull_number', sa.String(length=50), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True, server_default='ACTIVE'),
        sa.Column('is_blacklisted', sa.Boolean(), nullable=True, server_default=sa.text('false')),
        sa.Column('data_source', sa.String(length=50), nullable=True),
        sa.Column('confidence_score', sa.Integer(), nullable=True, server_default='100'),
        sa.Column('last_ais_update', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('created_by', sa.String(length=100), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
    )
    op.create_index('idx_vessel_registry_imo', 'vessel_registry', ['imo'])
    op.create_index('idx_vessel_registry_mmsi', 'vessel_registry', ['mmsi'])
    op.create_index('idx_vessel_registry_name', 'vessel_registry', ['name'])

    # Create vessel_visits table
    op.create_table(
        'vessel_visits',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('vessel_id', sa.Integer(), sa.ForeignKey('vessel_registry.id', ondelete='CASCADE')),
        sa.Column('terminal_id', sa.String(), sa.ForeignKey('terminals.id')),
        sa.Column('visit_number', sa.Integer(), nullable=True),
        sa.Column('external_reference', sa.String(length=100), nullable=True),
        sa.Column('estimated_arrival', sa.DateTime(), nullable=True),
        sa.Column('actual_arrival', sa.DateTime(), nullable=True),
        sa.Column('estimated_departure', sa.DateTime(), nullable=True),
        sa.Column('actual_departure', sa.DateTime(), nullable=True),
        sa.Column('operation_type', sa.String(length=20), nullable=True),
        sa.Column('cargo_types', sa.JSON(), nullable=True),
        sa.Column('total_cargo_volume', sa.Float(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True, server_default='PLANNED'),
        sa.Column('berth_assignments', sa.JSON(), nullable=True),
        sa.Column('customer', sa.String(length=255), nullable=True),
        sa.Column('agent', sa.String(length=255), nullable=True),
        sa.Column('priority', sa.Integer(), nullable=True, server_default='1'),
        sa.Column('actual_berth_time', sa.Integer(), nullable=True),
        sa.Column('actual_operation_time', sa.Integer(), nullable=True),
        sa.Column('total_port_time', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
    )
    op.create_index('idx_vessel_visits_vessel_id', 'vessel_visits', ['vessel_id'])
    op.create_index('idx_vessel_visits_terminal_id', 'vessel_visits', ['terminal_id'])

    # Create vessel_ais_data table
    op.create_table(
        'vessel_ais_data',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('vessel_id', sa.Integer(), sa.ForeignKey('vessel_registry.id', ondelete='CASCADE')),
        sa.Column('mmsi', sa.String(length=9), nullable=False),
        sa.Column('latitude', sa.Float(), nullable=True),
        sa.Column('longitude', sa.Float(), nullable=True),
        sa.Column('course', sa.Float(), nullable=True),
        sa.Column('speed', sa.Float(), nullable=True),
        sa.Column('heading', sa.Integer(), nullable=True),
        sa.Column('navigation_status', sa.Integer(), nullable=True),
        sa.Column('rate_of_turn', sa.Integer(), nullable=True),
        sa.Column('position_accuracy', sa.Integer(), nullable=True),
        sa.Column('draught', sa.Float(), nullable=True),
        sa.Column('destination', sa.String(length=100), nullable=True),
        sa.Column('eta_raw', sa.Integer(), nullable=True),
        sa.Column('eta_parsed', sa.DateTime(), nullable=True),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('age_seconds', sa.Integer(), nullable=True),
        sa.Column('signal_quality', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('source', sa.String(length=50), nullable=True),
    )
    op.create_index('idx_ais_vessel_id', 'vessel_ais_data', ['vessel_id'])
    op.create_index('idx_ais_timestamp', 'vessel_ais_data', ['timestamp'])
    op.create_index('idx_ais_mmsi', 'vessel_ais_data', ['mmsi'])
    op.create_index('idx_ais_position', 'vessel_ais_data', ['latitude', 'longitude'])

    # Alter assignments table
    op.add_column('assignments', sa.Column('vessel_db_id', sa.Integer(), nullable=True))
    op.add_column('assignments', sa.Column('visit_id', sa.Integer(), nullable=True))
    op.add_column('assignments', sa.Column('nomination_reference', sa.String(), nullable=True))
    op.add_column('assignments', sa.Column('assignment_type', sa.String(), nullable=False, server_default='SCHEDULED'))
    op.create_foreign_key('fk_assignments_vessel_db_id', 'assignments', 'vessel_registry', ['vessel_db_id'], ['id'])
    op.create_foreign_key('fk_assignments_visit_id', 'assignments', 'vessel_visits', ['visit_id'], ['id'])
    op.create_index('idx_assignments_vessel_db_id', 'assignments', ['vessel_db_id'])
    op.create_index('idx_assignments_visit_id', 'assignments', ['visit_id'])


def downgrade() -> None:
    # Revert assignments alterations
    op.drop_index('idx_assignments_visit_id', table_name='assignments')
    op.drop_index('idx_assignments_vessel_db_id', table_name='assignments')
    op.drop_constraint('fk_assignments_visit_id', 'assignments', type_='foreignkey')
    op.drop_constraint('fk_assignments_vessel_db_id', 'assignments', type_='foreignkey')
    op.drop_column('assignments', 'assignment_type')
    op.drop_column('assignments', 'nomination_reference')
    op.drop_column('assignments', 'visit_id')
    op.drop_column('assignments', 'vessel_db_id')

    # Drop AIS table
    op.drop_index('idx_ais_position', table_name='vessel_ais_data')
    op.drop_index('idx_ais_mmsi', table_name='vessel_ais_data')
    op.drop_index('idx_ais_timestamp', table_name='vessel_ais_data')
    op.drop_index('idx_ais_vessel_id', table_name='vessel_ais_data')
    op.drop_table('vessel_ais_data')

    # Drop visits table
    op.drop_index('idx_vessel_visits_terminal_id', table_name='vessel_visits')
    op.drop_index('idx_vessel_visits_vessel_id', table_name='vessel_visits')
    op.drop_table('vessel_visits')

    # Drop registry
    op.drop_index('idx_vessel_registry_name', table_name='vessel_registry')
    op.drop_index('idx_vessel_registry_mmsi', table_name='vessel_registry')
    op.drop_index('idx_vessel_registry_imo', table_name='vessel_registry')
    op.drop_table('vessel_registry')


