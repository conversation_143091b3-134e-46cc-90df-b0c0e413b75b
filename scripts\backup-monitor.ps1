# PostgreSQL Backup Monitoring Script
# Checks backup health and sends alerts if needed

param(
    [Parameter()]
    [string]$BackupDir = ".\backups",
    
    [Parameter()]
    [int]$MaxAgeHours = 26,  # Alert if no backup in last 26 hours
    
    [Parameter()]
    [string]$AlertEmail = "",
    
    [Parameter()]
    [string]$SmtpServer = "",
    
    [Parameter()]
    [int]$SmtpPort = 587,
    
    [Parameter()]
    [string]$SmtpUser = "",
    
    [Parameter()]
    [string]$SmtpPassword = "",
    
    [Parameter()]
    [switch]$Verbose = $false
)

# Health check results
$HealthStatus = @{
    Overall = "HEALTHY"
    LastBackup = $null
    LastBackupAge = 0
    BackupCount = 0
    TotalSize = 0
    Alerts = @()
    Warnings = @()
}

Write-Host "PostgreSQL Backup Monitor - Jetty Planner" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Check if backup directory exists
if (!(Test-Path $BackupDir)) {
    $HealthStatus.Overall = "CRITICAL"
    $HealthStatus.Alerts += "Backup directory not found: $BackupDir"
    Write-Host "✗ CRITICAL: Backup directory not found" -ForegroundColor Red
} else {
    # Get backup files
    $BackupFiles = Get-ChildItem -Path $BackupDir -Filter "*.sql" | Sort-Object LastWriteTime -Descending
    $HealthStatus.BackupCount = $BackupFiles.Count
    
    if ($BackupFiles.Count -eq 0) {
        $HealthStatus.Overall = "CRITICAL"
        $HealthStatus.Alerts += "No backup files found"
        Write-Host "✗ CRITICAL: No backup files found" -ForegroundColor Red
    } else {
        # Check most recent backup
        $LatestBackup = $BackupFiles[0]
        $HealthStatus.LastBackup = $LatestBackup.LastWriteTime
        $HealthStatus.LastBackupAge = (Get-Date) - $LatestBackup.LastWriteTime
        
        # Calculate total backup size
        $HealthStatus.TotalSize = ($BackupFiles | Measure-Object -Property Length -Sum).Sum
        
        # Check backup age
        if ($HealthStatus.LastBackupAge.TotalHours -gt $MaxAgeHours) {
            $HealthStatus.Overall = "WARNING"
            $HealthStatus.Warnings += "Last backup is $([math]::Round($HealthStatus.LastBackupAge.TotalHours, 1)) hours old (threshold: $MaxAgeHours hours)"
            Write-Host "⚠ WARNING: Last backup is too old" -ForegroundColor Yellow
        } else {
            Write-Host "✓ Recent backup found: $($LatestBackup.Name)" -ForegroundColor Green
        }
        
        # Check backup file sizes (detect corruption or incomplete backups)
        $SuspiciouslySmallBackups = $BackupFiles | Where-Object { $_.Length -lt 1MB }
        if ($SuspiciouslySmallBackups.Count -gt 0) {
            $HealthStatus.Warnings += "$($SuspiciouslySmallBackups.Count) backup(s) are suspiciously small (< 1MB)"
            Write-Host "⚠ WARNING: Some backups are very small" -ForegroundColor Yellow
        }
        
        # Check for backup consistency (file sizes shouldn't vary too much)
        if ($BackupFiles.Count -ge 3) {
            $RecentSizes = ($BackupFiles | Select-Object -First 3).Length
            $AvgSize = ($RecentSizes | Measure-Object -Average).Average
            $MaxDeviation = ($RecentSizes | ForEach-Object { [math]::Abs($_ - $AvgSize) / $AvgSize * 100 } | Measure-Object -Maximum).Maximum
            
            if ($MaxDeviation -gt 50) {  # 50% deviation threshold
                $HealthStatus.Warnings += "Recent backup sizes vary significantly (max deviation: $([math]::Round($MaxDeviation, 1))%)"
                Write-Host "⚠ WARNING: Backup sizes vary significantly" -ForegroundColor Yellow
            }
        }
    }
}

# Check Docker backup service health
try {
    $BackupContainer = docker ps --filter "name=jetty-postgres-backup" --format "table {{.Names}}\t{{.Status}}" 2>$null
    if ($BackupContainer -like "*jetty-postgres-backup*") {
        if ($BackupContainer -like "*Up*") {
            Write-Host "✓ Backup service container is running" -ForegroundColor Green
        } else {
            $HealthStatus.Overall = "WARNING"
            $HealthStatus.Warnings += "Backup service container is not running properly"
            Write-Host "⚠ WARNING: Backup service container status: $BackupContainer" -ForegroundColor Yellow
        }
    } else {
        $HealthStatus.Overall = "WARNING"
        $HealthStatus.Warnings += "Backup service container not found"
        Write-Host "⚠ WARNING: Backup service container not found" -ForegroundColor Yellow
    }
} catch {
    $HealthStatus.Warnings += "Could not check Docker container status: $($_.Exception.Message)"
    Write-Host "⚠ WARNING: Could not check Docker status" -ForegroundColor Yellow
}

# Display summary
Write-Host ""
Write-Host "Backup Health Summary:" -ForegroundColor Cyan
Write-Host "  Overall Status: $($HealthStatus.Overall)" -ForegroundColor $(
    switch ($HealthStatus.Overall) {
        "HEALTHY" { "Green" }
        "WARNING" { "Yellow" }
        "CRITICAL" { "Red" }
        default { "Gray" }
    }
)
Write-Host "  Backup Count: $($HealthStatus.BackupCount)"
if ($HealthStatus.LastBackup) {
    Write-Host "  Latest Backup: $($HealthStatus.LastBackup.ToString('yyyy-MM-dd HH:mm:ss'))"
    Write-Host "  Backup Age: $([math]::Round($HealthStatus.LastBackupAge.TotalHours, 1)) hours"
}
Write-Host "  Total Size: $([math]::Round($HealthStatus.TotalSize / 1MB, 1)) MB"

if ($HealthStatus.Warnings.Count -gt 0) {
    Write-Host ""
    Write-Host "Warnings:" -ForegroundColor Yellow
    foreach ($Warning in $HealthStatus.Warnings) {
        Write-Host "  • $Warning" -ForegroundColor Yellow
    }
}

if ($HealthStatus.Alerts.Count -gt 0) {
    Write-Host ""
    Write-Host "Critical Alerts:" -ForegroundColor Red
    foreach ($Alert in $HealthStatus.Alerts) {
        Write-Host "  • $Alert" -ForegroundColor Red
    }
}

# Send email alerts if configured
if ($AlertEmail -and $SmtpServer -and ($HealthStatus.Overall -ne "HEALTHY")) {
    try {
        $Subject = "Jetty Planner Backup Alert - Status: $($HealthStatus.Overall)"
        
        $Body = @"
Jetty Planner Backup Status Report

Overall Status: $($HealthStatus.Overall)
Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

Summary:
- Backup Count: $($HealthStatus.BackupCount)
- Latest Backup: $(if ($HealthStatus.LastBackup) { $HealthStatus.LastBackup.ToString('yyyy-MM-dd HH:mm:ss') } else { 'None' })
- Backup Age: $(if ($HealthStatus.LastBackup) { "$([math]::Round($HealthStatus.LastBackupAge.TotalHours, 1)) hours" } else { 'N/A' })
- Total Size: $([math]::Round($HealthStatus.TotalSize / 1MB, 1)) MB

$(if ($HealthStatus.Warnings.Count -gt 0) {
"Warnings:
" + ($HealthStatus.Warnings | ForEach-Object { "• $_" } | Out-String)
})

$(if ($HealthStatus.Alerts.Count -gt 0) {
"Critical Alerts:
" + ($HealthStatus.Alerts | ForEach-Object { "• $_" } | Out-String)
})

This is an automated message from the Jetty Planner backup monitoring system.
"@
        
        $SecurePassword = ConvertTo-SecureString -String $SmtpPassword -AsPlainText -Force
        $Credentials = New-Object System.Management.Automation.PSCredential($SmtpUser, $SecurePassword)
        
        Send-MailMessage -To $AlertEmail -From $SmtpUser -Subject $Subject -Body $Body `
                        -SmtpServer $SmtpServer -Port $SmtpPort -UseSsl -Credential $Credentials
        
        Write-Host ""
        Write-Host "✓ Alert email sent to $AlertEmail" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to send alert email: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Exit with appropriate code for monitoring systems
switch ($HealthStatus.Overall) {
    "HEALTHY" { exit 0 }
    "WARNING" { exit 1 }
    "CRITICAL" { exit 2 }
    default { exit 3 }
}