#!/usr/bin/env python3
"""
Test script to verify nomination timestamp logging works correctly.
"""

import sys
import os
sys.path.append('.')

from dotenv import load_dotenv
load_dotenv()

from src.database import Database
from src.db.models import Nomination, AssignmentChange
from datetime import datetime, timezone, timedelta
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_nomination_timestamp_logging():
    """Test that nomination timestamps are properly captured in assignment change logs."""
    
    db = Database()
    
    with db.get_session() as session:
        # Get recent nomination to planned transitions
        recent_changes = session.query(AssignmentChange).filter(
            AssignmentChange.reason.like('%nomination to planned%')
        ).order_by(AssignmentChange.changed_at.desc()).limit(10).all()
        
        logger.info(f"Found {len(recent_changes)} recent nomination to planned transitions")
        
        epoch_count = 0
        valid_count = 0
        
        for change in recent_changes:
            # Check if timestamps are epoch (1970-01-01)
            if change.old_start_time:
                if change.old_start_time.year == 1970:
                    epoch_count += 1
                    logger.warning(f"Assignment {change.assignment_id} has epoch timestamp: {change.old_start_time}")
                else:
                    valid_count += 1
                    logger.info(f"Assignment {change.assignment_id} has valid timestamp: {change.old_start_time}")
            
            # Show the log entry format
            old_range = "None → None"
            if change.old_start_time and change.old_end_time:
                old_range = f"{change.old_start_time.strftime('%d/%m/%Y, %H:%M:%S')} → {change.old_end_time.strftime('%d/%m/%Y, %H:%M:%S')}"
            
            new_range = "None → None"
            if change.new_start_time and change.new_end_time:
                new_range = f"{change.new_start_time.strftime('%d/%m/%Y, %H:%M:%S')} → {change.new_end_time.strftime('%d/%m/%Y, %H:%M:%S')}"
            
            logger.info(f"  #{change.assignment_id} {change.vessel_name} {change.jetty_name} {old_range} → {new_range}")
        
        logger.info(f"Summary: {epoch_count} epoch timestamps, {valid_count} valid timestamps")
        
        if epoch_count > 0:
            logger.warning(f"Found {epoch_count} assignments with epoch timestamps - this indicates the fix hasn't been applied yet or nominations are missing")

def check_nomination_data():
    """Check if we have nomination data that should be used for logging."""
    
    db = Database()
    
    with db.get_session() as session:
        # Get recent nominations
        nominations = session.query(Nomination).order_by(Nomination.created_at.desc()).limit(10).all()
        
        logger.info(f"Found {len(nominations)} recent nominations")
        
        for nom in nominations:
            eta_str = nom.eta.strftime('%d/%m/%Y, %H:%M:%S') if nom.eta else 'None'
            etd_str = nom.etd.strftime('%d/%m/%Y, %H:%M:%S') if nom.etd else 'None'
            created_str = nom.created_at.strftime('%d/%m/%Y, %H:%M:%S') if nom.created_at else 'None'
            
            logger.info(f"  Nomination {nom.runtime_vessel_id} ({nom.name}): ETA={eta_str}, ETD={etd_str}, Created={created_str}")

def main():
    """Main test function."""
    logger.info("Testing nomination timestamp logging...")
    
    logger.info("\n=== Checking existing assignment change logs ===")
    test_nomination_timestamp_logging()
    
    logger.info("\n=== Checking nomination data availability ===")
    check_nomination_data()
    
    logger.info("\nTest complete. If you see epoch timestamps (1970), run an optimization to test the fix.")

if __name__ == "__main__":
    main()
