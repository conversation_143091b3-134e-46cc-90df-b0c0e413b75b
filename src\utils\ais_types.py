"""
Utilities for mapping AIS Ship Type codes to human-readable text.

Reference based on ITU-R M.1371 categories.
"""

from typing import Optional


AIS_SHIP_TYPE_MAP = {
    0: "Not available",

    # 20–29 Wing in ground
    20: "Wing in ground",
    21: "Wing in ground (hazardous category A)",
    22: "Wing in ground (hazardous category B)",
    23: "Wing in ground (hazardous category C)",
    24: "Wing in ground (hazardous category D)",
    25: "Wing in ground (reserved)",
    26: "Wing in ground (reserved)",
    27: "Wing in ground (reserved)",
    28: "Wing in ground (reserved)",
    29: "Wing in ground (no additional information)",

    # 30–39 Fishing, towing, pleasure, sailing, etc.
    30: "Fishing",
    31: "Towing",
    32: "Towing >200m or breadth >25m",
    33: "Dredging or underwater ops",
    34: "Diving ops",
    35: "Military ops",
    36: "Sailing",
    37: "Pleasure craft",

    # 40–49 High speed craft
    40: "High speed craft",
    41: "HSC (hazardous category A)",
    42: "HSC (hazardous category B)",
    43: "HSC (hazardous category C)",
    44: "HSC (hazardous category D)",
    45: "HSC (reserved)",
    46: "HSC (reserved)",
    47: "HSC (reserved)",
    48: "HSC (reserved)",
    49: "HSC (no additional information)",

    # 50–59 Special craft
    50: "Pilot vessel",
    51: "Search and rescue vessel",
    52: "Tug",
    53: "Port tender",
    54: "Anti-pollution equipment",
    55: "Law enforcement",
    56: "Spare (local) craft",
    57: "Spare (local) craft",
    58: "Medical transport",
    59: "Noncombatant ship",

    # 60–69 Passenger
    60: "Passenger",
    61: "Passenger (hazardous category A)",
    62: "Passenger (hazardous category B)",
    63: "Passenger (hazardous category C)",
    64: "Passenger (hazardous category D)",
    65: "Passenger (reserved)",
    66: "Passenger (reserved)",
    67: "Passenger (reserved)",
    68: "Passenger (reserved)",
    69: "Passenger (no additional information)",

    # 70–79 Cargo
    70: "Cargo",
    71: "Cargo (hazardous category A)",
    72: "Cargo (hazardous category B)",
    73: "Cargo (hazardous category C)",
    74: "Cargo (hazardous category D)",
    75: "Cargo (reserved)",
    76: "Cargo (reserved)",
    77: "Cargo (reserved)",
    78: "Cargo (reserved)",
    79: "Cargo (no additional information)",

    # 80–89 Tanker (liquid bulk relevant)
    80: "Tanker",
    81: "Tanker (hazardous category A)",
    82: "Tanker (hazardous category B)",
    83: "Tanker (hazardous category C)",
    84: "Tanker (hazardous category D)",
    85: "Tanker (reserved)",
    86: "Tanker (reserved)",
    87: "Tanker (reserved)",
    88: "Chemical tanker",
    89: "Gas tanker",

    # 90–99 Other
    90: "Other",
    91: "Other (hazardous category A)",
    92: "Other (hazardous category B)",
    93: "Other (hazardous category C)",
    94: "Other (hazardous category D)",
    95: "Other (reserved)",
    96: "Other (reserved)",
    97: "Other (reserved)",
    98: "Other (reserved)",
    99: "Other (no additional information)",
}


def get_ship_type_text(ais_type: Optional[int]) -> Optional[str]:
    """Convert AIS numeric ship type to a readable label.

    If ais_type is already a string, return it normalized; if None, return None.
    """
    if ais_type is None:
        return None
    try:
        if isinstance(ais_type, str):
            # Already textual; clean it up
            value = ais_type.strip()
            return value or None
        code = int(ais_type)
    except Exception:
        return None
    return AIS_SHIP_TYPE_MAP.get(code, f"Type {code}")


