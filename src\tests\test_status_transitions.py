"""
Test Status Transitions

This module tests the status utilities and status transitions in models.
"""

import unittest
from datetime import datetime, timedelta
import uuid

from src.models.vessel import Vessel, VesselType
from src.models.terminal import Jetty
from src.models.schedule import Assignment
from src.utils.status_utils import (
    normalize_status, 
    is_valid_vessel_status, 
    is_valid_vessel_transition,
    is_valid_assignment_status,
    is_valid_assignment_transition
)

class TestStatusUtilities(unittest.TestCase):
    """Test status utility functions"""
    
    def test_normalize_status(self):
        """Test status normalization"""
        self.assertEqual(normalize_status("ACTIVE"), "ACTIVE")
        self.assertEqual(normalize_status("active"), "ACTIVE")
        self.assertEqual(normalize_status("Active"), "ACTIVE")
        self.assertEqual(normalize_status(" active "), "ACTIVE")
        self.assertEqual(normalize_status("en_route"), "EN_ROUTE")
        self.assertEqual(normalize_status("EN ROUTE"), "EN_ROUTE")
        
    def test_valid_vessel_status(self):
        """Test vessel status validation"""
        self.assertTrue(is_valid_vessel_status("EN_ROUTE"))
        self.assertTrue(is_valid_vessel_status("APPROACHING"))
        self.assertTrue(is_valid_vessel_status("ARRIVED"))
        self.assertTrue(is_valid_vessel_status("DOCKED"))
        self.assertTrue(is_valid_vessel_status("DEPARTED"))
        self.assertFalse(is_valid_vessel_status("INVALID_STATUS"))
        
    def test_valid_assignment_status(self):
        """Test assignment status validation"""
        self.assertTrue(is_valid_assignment_status("PENDING_APPROVAL"))
        self.assertTrue(is_valid_assignment_status("APPROVED"))
        self.assertTrue(is_valid_assignment_status("ACTIVE"))
        self.assertTrue(is_valid_assignment_status("COMPLETED"))
        self.assertTrue(is_valid_assignment_status("CANCELLED"))
        self.assertFalse(is_valid_assignment_status("INVALID_STATUS"))
        
    def test_vessel_status_transition(self):
        """Test vessel status transitions"""
        # Valid transitions
        self.assertTrue(is_valid_vessel_transition("EN_ROUTE", "APPROACHING")[0])
        self.assertTrue(is_valid_vessel_transition("APPROACHING", "ARRIVED")[0])
        self.assertTrue(is_valid_vessel_transition("ARRIVED", "DOCKED")[0])
        self.assertTrue(is_valid_vessel_transition("DOCKED", "DEPARTED")[0])
        
        # Invalid transitions
        self.assertFalse(is_valid_vessel_transition("EN_ROUTE", "DOCKED")[0])
        self.assertFalse(is_valid_vessel_transition("DEPARTED", "ARRIVED")[0])
        
    def test_assignment_status_transition(self):
        """Test assignment status transitions"""
        # Valid transitions
        self.assertTrue(is_valid_assignment_transition("PENDING_APPROVAL", "APPROVED")[0])
        self.assertTrue(is_valid_assignment_transition("APPROVED", "ACTIVE")[0])
        self.assertTrue(is_valid_assignment_transition("ACTIVE", "COMPLETED")[0])
        self.assertTrue(is_valid_assignment_transition("PENDING_APPROVAL", "CANCELLED")[0])
        
        # Invalid transitions
        self.assertFalse(is_valid_assignment_transition("COMPLETED", "ACTIVE")[0])
        self.assertFalse(is_valid_assignment_transition("CANCELLED", "ACTIVE")[0])


class TestVesselModel(unittest.TestCase):
    """Test vessel model status handling"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.vessel = Vessel(
            id=str(uuid.uuid4()),
            name="Test Vessel",
            vessel_type=VesselType.TANKER,
            status="EN_ROUTE",
            eta=datetime.now() + timedelta(days=1),
            capacity=100000.0,
            length=200.0,
            width=30.0
        )
        
    def test_vessel_initialization(self):
        """Test vessel initialization with status"""
        self.assertEqual(self.vessel.status, "EN_ROUTE")
        
        # Test with lowercase status
        vessel = Vessel(
            id=str(uuid.uuid4()),
            name="Test Vessel 2",
            vessel_type=VesselType.TANKER,
            status="approaching",
            capacity=100000.0,
            length=200.0
        )
        self.assertEqual(vessel.status, "APPROACHING")
        
        # Test with invalid status should raise ValueError
        with self.assertRaises(ValueError):
            Vessel(
                id=str(uuid.uuid4()),
                name="Test Vessel 3",
                vessel_type=VesselType.TANKER,
                status="INVALID_STATUS",
                capacity=100000.0,
                length=200.0
            )
            
    def test_vessel_status_update(self):
        """Test vessel status updates"""
        # Valid transition
        success, _ = self.vessel.update_status("APPROACHING")
        self.assertTrue(success)
        self.assertEqual(self.vessel.status, "APPROACHING")
        
        # Another valid transition
        success, _ = self.vessel.update_status("ARRIVED")
        self.assertTrue(success)
        self.assertEqual(self.vessel.status, "ARRIVED")
        
        # Invalid transition
        success, message = self.vessel.update_status("EN_ROUTE")
        self.assertFalse(success)
        self.assertNotEqual(self.vessel.status, "EN_ROUTE")
        self.assertEqual(self.vessel.status, "ARRIVED")
        

class TestAssignmentModel(unittest.TestCase):
    """Test assignment model status handling"""
    
    def setUp(self):
        """Set up test fixtures"""
        jetty = Jetty(
            id=str(uuid.uuid4()),
            name="Test Jetty",
            length=250.0,
            max_draft=15.0,
            min_draft=5.0,
            max_width=45.0,
            min_width=10.0
        )
        
        vessel = Vessel(
            id=str(uuid.uuid4()),
            name="Test Vessel",
            vessel_type=VesselType.TANKER,
            status="ARRIVED",
            capacity=100000.0,
            length=200.0,
            width=30.0
        )
        
        self.assignment = Assignment(
            id=str(uuid.uuid4()),
            jetty=jetty,
            vessel=vessel,
            start_time=datetime.now() + timedelta(hours=1),
            end_time=datetime.now() + timedelta(hours=5),
            status="PENDING_APPROVAL"
        )
        
    def test_assignment_initialization(self):
        """Test assignment initialization with status"""
        self.assertEqual(self.assignment.status, "PENDING_APPROVAL")
        
        # Test with lowercase status
        assignment = Assignment(
            id=str(uuid.uuid4()),
            jetty=self.assignment.jetty,
            vessel=self.assignment.vessel,
            start_time=datetime.now() + timedelta(hours=1),
            end_time=datetime.now() + timedelta(hours=5),
            status="approved"
        )
        self.assertEqual(assignment.status, "APPROVED")
        
        # Test with invalid status should raise ValueError
        with self.assertRaises(ValueError):
            Assignment(
                id=str(uuid.uuid4()),
                jetty=self.assignment.jetty,
                vessel=self.assignment.vessel,
                start_time=datetime.now() + timedelta(hours=1),
                end_time=datetime.now() + timedelta(hours=5),
                status="INVALID_STATUS"
            )
            
    def test_assignment_status_update(self):
        """Test assignment status updates"""
        # First update to APPROVED
        success, _ = self.assignment.update_status("APPROVED")
        self.assertTrue(success)
        self.assertEqual(self.assignment.status, "APPROVED")
        
        # Then to ACTIVE
        success, _ = self.assignment.update_status("ACTIVE")
        self.assertTrue(success)
        self.assertEqual(self.assignment.status, "ACTIVE")
        
        # Then to COMPLETED
        success, _ = self.assignment.update_status("COMPLETED")
        self.assertTrue(success)
        self.assertEqual(self.assignment.status, "COMPLETED")
        
        # Invalid transition from COMPLETED to ACTIVE
        success, message = self.assignment.update_status("ACTIVE")
        self.assertFalse(success)
        self.assertNotEqual(self.assignment.status, "ACTIVE")
        self.assertEqual(self.assignment.status, "COMPLETED")
        
    def test_assignment_activate_method(self):
        """Test assignment activate method"""
        # First set to APPROVED
        self.assignment.update_status("APPROVED")
        
        # Activate the assignment
        self.assignment.activate()
        self.assertEqual(self.assignment.status, "ACTIVE")
        self.assertIsNotNone(self.assignment.actual_start_time)
        
        # Verify vessel status is updated
        self.assertEqual(self.assignment.vessel.status, "DOCKED")
        self.assertEqual(self.assignment.vessel.current_jetty, self.assignment.jetty.id)
        
    def test_assignment_complete_method(self):
        """Test assignment complete method"""
        # First activate
        self.assignment.update_status("APPROVED")
        self.assignment.activate()
        
        # Then complete
        self.assignment.complete()
        self.assertEqual(self.assignment.status, "COMPLETED")
        self.assertIsNotNone(self.assignment.actual_end_time)
        
        # Verify vessel status is updated
        self.assertEqual(self.assignment.vessel.status, "DEPARTED")
        self.assertIsNone(self.assignment.vessel.current_jetty)
        
    def test_assignment_cancel_method(self):
        """Test assignment cancel method"""
        # Cancel the assignment
        reason = "Weather conditions"
        self.assignment.cancel(reason)
        self.assertEqual(self.assignment.status, "CANCELLED")
        self.assertIn(reason, self.assignment.notes)


if __name__ == "__main__":
    unittest.main() 