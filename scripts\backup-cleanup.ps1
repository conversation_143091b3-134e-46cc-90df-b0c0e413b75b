# Automated Backup Cleanup Script
# This script implements intelligent backup retention policies

param(
    [Parameter()]
    [string]$BackupDir = ".\backups",
    
    [Parameter()]
    [int]$DailyKeepDays = 30,
    
    [Parameter()]
    [int]$WeeklyKeepWeeks = 8,
    
    [Parameter()]
    [int]$MonthlyKeepMonths = 6,
    
    [Parameter()]
    [switch]$DryRun = $false
)

Write-Host "PostgreSQL Backup Cleanup - Jetty Planner" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Cyan

if (!(Test-Path $BackupDir)) {
    Write-Host "Backup directory not found: $BackupDir" -ForegroundColor Red
    exit 1
}

# Get all backup files
$AllBackups = Get-ChildItem -Path $BackupDir -Filter "*.sql" | Sort-Object LastWriteTime -Descending

if ($AllBackups.Count -eq 0) {
    Write-Host "No backup files found in $BackupDir" -ForegroundColor Yellow
    exit 0
}

Write-Host "Found $($AllBackups.Count) backup files" -ForegroundColor Green

# Calculate retention dates
$Now = Get-Date
$DailyCutoff = $Now.AddDays(-$DailyKeepDays)
$WeeklyCutoff = $Now.AddWeeks(-$WeeklyKeepWeeks)
$MonthlyCutoff = $Now.AddMonths(-$MonthlyKeepMonths)

Write-Host ""
Write-Host "Retention Policy:" -ForegroundColor Cyan
Write-Host "  Daily backups:   Keep $DailyKeepDays days   (since $($DailyCutoff.ToString('yyyy-MM-dd')))"
Write-Host "  Weekly backups:  Keep $WeeklyKeepWeeks weeks  (since $($WeeklyCutoff.ToString('yyyy-MM-dd')))"
Write-Host "  Monthly backups: Keep $MonthlyKeepMonths months (since $($MonthlyCutoff.ToString('yyyy-MM-dd')))"
Write-Host ""

# Categorize backups
$KeepFiles = @()
$DeleteFiles = @()

# Group backups by week and month for selection
$WeeklyBackups = @{}
$MonthlyBackups = @{}

foreach ($Backup in $AllBackups) {
    $BackupDate = $Backup.LastWriteTime
    $Age = ($Now - $BackupDate).TotalDays
    
    # Always keep recent daily backups
    if ($BackupDate -ge $DailyCutoff) {
        $KeepFiles += $Backup
        Write-Verbose "Keep (daily): $($Backup.Name) - $($Age.ToString('F0')) days old"
        continue
    }
    
    # For older backups, keep one per week
    if ($BackupDate -ge $WeeklyCutoff) {
        $WeekStart = $BackupDate.AddDays(-[int]$BackupDate.DayOfWeek).Date
        
        if (-not $WeeklyBackups.ContainsKey($WeekStart) -or 
            $WeeklyBackups[$WeekStart].LastWriteTime -lt $BackupDate) {
            $WeeklyBackups[$WeekStart] = $Backup
        }
        continue
    }
    
    # For very old backups, keep one per month
    if ($BackupDate -ge $MonthlyCutoff) {
        $MonthStart = Get-Date -Year $BackupDate.Year -Month $BackupDate.Month -Day 1
        
        if (-not $MonthlyBackups.ContainsKey($MonthStart) -or 
            $MonthlyBackups[$MonthStart].LastWriteTime -lt $BackupDate) {
            $MonthlyBackups[$MonthStart] = $Backup
        }
        continue
    }
    
    # Everything else gets deleted
    $DeleteFiles += $Backup
}

# Add selected weekly and monthly backups to keep list
$KeepFiles += $WeeklyBackups.Values
$KeepFiles += $MonthlyBackups.Values

# Determine which files to delete
$AllKeptNames = $KeepFiles | ForEach-Object { $_.Name }
$DeleteFiles += $AllBackups | Where-Object { $_.Name -notin $AllKeptNames }

# Remove duplicates from delete list
$DeleteFiles = $DeleteFiles | Sort-Object Name -Unique

Write-Host "Backup Analysis Results:" -ForegroundColor Green
Write-Host "  Files to keep:   $($KeepFiles.Count)" -ForegroundColor Green
Write-Host "  Files to delete: $($DeleteFiles.Count)" -ForegroundColor Yellow

if ($DeleteFiles.Count -eq 0) {
    Write-Host "  No cleanup needed!" -ForegroundColor Green
    exit 0
}

# Show files that will be deleted
Write-Host ""
Write-Host "Files scheduled for deletion:" -ForegroundColor Yellow
$TotalSizeToDelete = 0
foreach ($File in $DeleteFiles) {
    $SizeMB = [math]::Round($File.Length / 1MB, 1)
    $TotalSizeToDelete += $File.Length
    $Age = ($Now - $File.LastWriteTime).TotalDays
    Write-Host "  - $($File.Name) ($($SizeMB) MB, $($Age.ToString('F0')) days old)" -ForegroundColor Gray
}

$TotalSizeMB = [math]::Round($TotalSizeToDelete / 1MB, 1)
Write-Host ""
Write-Host "Total space to reclaim: $TotalSizeMB MB" -ForegroundColor Cyan

if ($DryRun) {
    Write-Host ""
    Write-Host "DRY RUN MODE - No files will be deleted" -ForegroundColor Yellow
    Write-Host "Run without -DryRun to perform actual cleanup" -ForegroundColor Yellow
    exit 0
}

# Confirm deletion
Write-Host ""
$Confirm = Read-Host "Proceed with cleanup? (yes/no)"

if ($Confirm -eq "yes") {
    $DeletedCount = 0
    $DeletedSize = 0
    
    foreach ($File in $DeleteFiles) {
        try {
            $DeletedSize += $File.Length
            Remove-Item $File.FullName -Force
            $DeletedCount++
            Write-Host "  ✓ Deleted: $($File.Name)" -ForegroundColor Gray
        } catch {
            Write-Host "  ✗ Failed to delete: $($File.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    $DeletedSizeMB = [math]::Round($DeletedSize / 1MB, 1)
    Write-Host ""
    Write-Host "✓ Cleanup completed!" -ForegroundColor Green
    Write-Host "  Deleted: $DeletedCount files" -ForegroundColor Green
    Write-Host "  Reclaimed: $DeletedSizeMB MB" -ForegroundColor Green
} else {
    Write-Host "Cleanup cancelled." -ForegroundColor Yellow
}