Param(
    [string]$BaseUrl = 'http://localhost:7000',
    [int]$HorizonDays = 14,
    [int]$GranularityHours = 1,
    [switch]$FillUnassigned
)

Set-StrictMode -Version Latest
$ErrorActionPreference = 'Stop'

function Invoke-OptimizePreset {
    Param(
        [string]$Name,
        [double]$WeightThroughput,
        [double]$WeightDemurrage,
        [double]$WeightPriority,
        [double]$WeightJettyBalance = 0,
        [bool]$ForceAssignAll = $false,
        [bool]$Fill = $false,
        [int]$ApproachHours = 2,
        [int]$FreeWaitBufferHours = 1,
        [int]$PollSeconds = 180
    )

    Write-Host "Running preset: $Name" -ForegroundColor Cyan

    $body = [pscustomobject]@{
        horizon_days            = $HorizonDays
        time_granularity_hours  = $GranularityHours
        weight_throughput       = $WeightThroughput
        weight_demurrage        = $WeightDemurrage
        weight_priority         = $WeightPriority
        weight_weather          = 2.0
        weight_jetty_balance    = $WeightJettyBalance
        force_assign_all        = $ForceAssignAll
        include_mock_assignments= $false
        approach_time_hours     = $ApproachHours
        free_wait_buffer_hours  = $FreeWaitBufferHours
        fill_unassigned         = $Fill
    }

    $json = $body | ConvertTo-Json -Depth 6
    $optUrl = "$BaseUrl/api/optimize"
    Invoke-RestMethod -Method Post -Uri $optUrl -ContentType 'application/json' -Body $json | Out-Null

    # Poll for completion
    $statusUrl = "$BaseUrl/api/optimize/status"
    $deadline = (Get-Date).AddSeconds($PollSeconds)
    do {
        Start-Sleep -Seconds 1
        $st = Invoke-RestMethod -Method Get -Uri $statusUrl
    } while ($st.in_progress -and (Get-Date) -lt $deadline)

    if ($st.in_progress) { throw "Optimization did not finish within $PollSeconds seconds for preset $Name" }

    # Fetch persisted assignments
    $assignmentsUrl = "$BaseUrl/api/schedule/assignments"
    $assignments = Invoke-RestMethod -Method Get -Uri $assignmentsUrl

    # Ensure reports directory
    $reportsDir = Join-Path -Path (Resolve-Path .) -ChildPath 'reports'
    if (-not (Test-Path $reportsDir)) { New-Item -ItemType Directory -Path $reportsDir | Out-Null }

    # Save raw JSON
    $jsonPath = Join-Path $reportsDir ("assignments_" + $Name + ".json")
    $assignments | ConvertTo-Json -Depth 6 | Out-File -FilePath $jsonPath -Encoding UTF8

    # Save CSV summary
    $csvPath = Join-Path $reportsDir ("assignments_" + $Name + ".csv")
    $assignments |
        Select-Object id, vessel_id, vessel_name, vessel_type, jetty_name, start_time, end_time, status |
        Export-Csv -NoTypeInformation -Path $csvPath -Encoding UTF8

    # Print quick summary
    $count = @($assignments).Count
    $byJetty = $assignments | Group-Object jetty_name | Select-Object Name,Count | Sort-Object Name
    Write-Host ("Assignments: {0}" -f $count) -ForegroundColor Green
    $byJetty | ForEach-Object { Write-Host ("  {0}: {1}" -f $_.Name, $_.Count) }

    # Save status with objective breakdown if available
    $statusPath = Join-Path $reportsDir ("opt_status_" + $Name + ".json")
    $st | ConvertTo-Json -Depth 8 | Out-File -FilePath $statusPath -Encoding UTF8
}

# Preset catalog (tune as needed)
$presets = @(
    @{ Name='ThroughputMax';   wt=12.0; wd=2.0;  wp=1.0;  wb=0.0; force=$false; fill=$FillUnassigned.IsPresent },
    @{ Name='DemurrageMin';    wt=8.0;  wd=10.0; wp=2.0;  wb=0.0; force=$false; fill=$FillUnassigned.IsPresent },
    @{ Name='PriorityFirst';   wt=8.0;  wd=5.0;  wp=8.0;  wb=0.0; force=$false; fill=$FillUnassigned.IsPresent },
    @{ Name='BalancedJets';    wt=10.0; wd=5.0;  wp=3.0;  wb=2.0; force=$false; fill=$FillUnassigned.IsPresent },
    @{ Name='ForceAssignAll';  wt=8.0;  wd=5.0;  wp=2.0;  wb=1.0; force=$true;  fill=$FillUnassigned.IsPresent },
    @{ Name='SpreadAndFill';   wt=9.0;  wd=5.0;  wp=3.0;  wb=5.0; force=$false; fill=$true }
)

foreach ($p in $presets) {
    Invoke-OptimizePreset -Name $p.Name `
        -WeightThroughput $p.wt -WeightDemurrage $p.wd -WeightPriority $p.wp -WeightJettyBalance $p.wb `
        -ForceAssignAll $p.force -Fill $p.fill
}

Write-Host "Done. See the 'reports' folder for JSON/CSV outputs per preset." -ForegroundColor Yellow


