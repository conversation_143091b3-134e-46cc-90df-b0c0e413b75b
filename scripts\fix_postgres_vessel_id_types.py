"""
One-off script to align Postgres schema with app expectations:
- Change assignments.vessel_id to TEXT
- Change assignment_changes.vessel_id to TEXT (nullable)
Also prints the detected engine URL and resulting column types for verification.
Run inside the app container:
  python scripts/fix_postgres_vessel_id_types.py
"""
from __future__ import annotations

import sys
from sqlalchemy import text
import os
import sys
from pathlib import Path

# Ensure project root (containing 'src') is on sys.path when run from /app/scripts
try:
    project_root = Path(__file__).resolve().parents[1]
    if str(project_root) not in sys.path:
        sys.path.insert(0, str(project_root))
except Exception:
    pass

try:
    from src.db.session import engine
except Exception as e:
    print(f"Failed to import engine: {e}")
    sys.exit(1)


def main() -> int:
    print("engine.url=", str(engine.url))
    with engine.connect() as conn:
        # Detect backend by attempting Postgres version; fallback prints otherwise
        try:
            version = conn.exec_driver_sql("select version()").scalar()
            print("postgres version:", version)
        except Exception as e:
            print("Warning: postgres version probe failed (are you on SQLite?)", e)

        # Apply ALTER TABLE statements with USING casts (idempotent if already TEXT)
        try:
            conn.execute(text("ALTER TABLE assignments ALTER COLUMN vessel_id TYPE TEXT USING vessel_id::text"))
            print("Altered assignments.vessel_id to TEXT")
        except Exception as e:
            print("Note: assignments.vessel_id alter may have failed (possibly already TEXT):", e)

        try:
            conn.execute(text("ALTER TABLE assignment_changes ALTER COLUMN vessel_id TYPE TEXT USING vessel_id::text"))
            print("Altered assignment_changes.vessel_id to TEXT")
        except Exception as e:
            print("Note: assignment_changes.vessel_id alter may have failed (possibly already TEXT):", e)

        # Verify
        try:
            rows = conn.execute(text(
                "SELECT table_schema, table_name, column_name, data_type "
                "FROM information_schema.columns "
                "WHERE table_name='assignments' AND column_name='vessel_id'"
            )).fetchall()
            print("assignments.vessel_id:", rows)
        except Exception as e:
            print("Verification failed for assignments:", e)

        try:
            rows2 = conn.execute(text(
                "SELECT table_schema, table_name, column_name, data_type "
                "FROM information_schema.columns "
                "WHERE table_name='assignment_changes' AND column_name='vessel_id'"
            )).fetchall()
            print("assignment_changes.vessel_id:", rows2)
        except Exception as e:
            print("Verification failed for assignment_changes:", e)

        conn.commit()

    print("Done.")
    return 0


if __name__ == "__main__":
    raise SystemExit(main())


