# Maintenance Mode Guide for Evos Jetty Planner

This guide explains how to use the maintenance mode system for the Jetty Planner application running through Docker → nginx → Cloudflare → Cloudflare Zero Trust.

## 🏗️ How It Works

### Architecture Overview
```
User → Cloudflare → Cloudflare Zero Trust → nginx (port 8080) → Docker (port 7000)
```

### Maintenance Mode Triggers

1. **Manual Mode**: Create a flag file to intentionally enable maintenance
2. **Automatic Mode**: nginx automatically shows maintenance page when upstream is down (502/503/504 errors)

## 🚀 Quick Start

### Enable Maintenance Mode
```powershell
# Basic maintenance mode
.\scripts\Enable-MaintenanceMode.ps1

# With custom message and duration
.\scripts\Enable-MaintenanceMode.ps1 -Reason "Database upgrade" -EstimatedDuration "1 hour"
```

### Disable Maintenance Mode
```powershell
.\scripts\Disable-MaintenanceMode.ps1
```

### Check Status
```powershell
.\scripts\Check-MaintenanceStatus.ps1
```

## 📋 Setup Instructions

### 1. Update nginx Configuration

1. **Backup your current config**:
   ```powershell
   Copy-Item "C:\nginx\conf\nginx.conf" "C:\nginx\conf\nginx.conf.backup"
   ```

2. **Apply the new configuration**:
   - Open `docs/nginx-planner-maintenance-config.md`
   - Replace the planner server block in your nginx.conf
   - Test: `nginx -t`
   - Reload: `nginx -s reload`

### 2. Test the Setup

1. **Test manual maintenance mode**:
   ```powershell
   .\scripts\Enable-MaintenanceMode.ps1 -Reason "Testing setup"
   ```
   - Visit https://planner.evosgpt.eu
   - Should see the maintenance page

2. **Test automatic mode**:
   ```powershell
   # Stop your Docker container
   docker stop jetty-planner-container
   ```
   - Visit https://planner.evosgpt.eu
   - Should see the maintenance page

3. **Restore normal operation**:
   ```powershell
   .\scripts\Disable-MaintenanceMode.ps1
   docker start jetty-planner-container
   ```

## 🛠️ Usage Scenarios

### Planned Maintenance
```powershell
# Before starting maintenance work
.\scripts\Enable-MaintenanceMode.ps1 -Reason "Server updates and optimizations" -EstimatedDuration "30 minutes"

# Perform your maintenance tasks...
# Update Docker images, database migrations, etc.

# After maintenance is complete
.\scripts\Disable-MaintenanceMode.ps1
```

### Emergency Maintenance
```powershell
# Quick enable for urgent issues
.\scripts\Enable-MaintenanceMode.ps1 -Reason "Investigating critical issue"

# Fix the problem...

# Re-enable when ready
.\scripts\Disable-MaintenanceMode.ps1
```

### Deployment Process
```powershell
# 1. Enable maintenance mode
.\scripts\Enable-MaintenanceMode.ps1 -Reason "Deploying application updates" -EstimatedDuration "15 minutes"

# 2. Stop current container
docker stop jetty-planner-container

# 3. Deploy new version
docker pull your-new-image
docker run --name jetty-planner-container -p 7000:7000 your-new-image

# 4. Verify deployment works
# Test API endpoints, database connectivity, etc.

# 5. Disable maintenance mode
.\scripts\Disable-MaintenanceMode.ps1
```

## 📁 File Locations

### Key Files
- **Flag file**: `C:\Users\<USER>\Jettyplanner\maintenance.flag`
- **Maintenance page**: `src/static/maintenance.html`
- **Log file**: `logs/maintenance.log`

### Scripts
- `scripts/Enable-MaintenanceMode.ps1` - Enable maintenance mode
- `scripts/Disable-MaintenanceMode.ps1` - Disable maintenance mode  
- `scripts/Check-MaintenanceStatus.ps1` - Check current status

### Documentation
- `docs/nginx-planner-maintenance-config.md` - nginx configuration
- `docs/MAINTENANCE_MODE_GUIDE.md` - This guide

## 🎨 Maintenance Page Features

The maintenance page (`src/static/maintenance.html`) includes:

- **Professional design** with Evos branding
- **Google Fonts** (Roboto, Blinker) matching your app theme
- **Responsive design** for all devices
- **Auto-refresh** every 2 minutes
- **Clear messaging** about what's happening
- **Estimated completion time**
- **Contact information** for urgent issues

## 🔧 Advanced Configuration

### Custom Maintenance Messages

Edit `src/static/maintenance.html` to customize:
- Messages and descriptions
- Estimated completion times
- Contact information
- Styling and branding

### nginx Customization

The nginx configuration supports:
- **Multiple environments** (modify paths as needed)
- **Custom error codes** (add more error pages if needed)
- **Security headers** (already included for maintenance page)

### Monitoring Integration

Add monitoring alerts for:
```powershell
# Check if maintenance mode is unexpectedly enabled
if (Test-Path "C:\Users\<USER>\Jettyplanner\maintenance.flag") {
    # Send alert to monitoring system
}

# Check if application is returning errors (automatic maintenance)
# Monitor nginx error logs for 502/503/504 responses
```

## 🚨 Troubleshooting

### Maintenance Page Not Showing

1. **Check nginx configuration**:
   ```bash
   nginx -t
   ```

2. **Verify flag file location**:
   ```powershell
   Test-Path "C:\Users\<USER>\Jettyplanner\maintenance.flag"
   ```

3. **Check nginx error logs**:
   ```powershell
   Get-Content "C:\nginx\logs\error.log" -Tail 20
   ```

### Unable to Disable Maintenance Mode

1. **Check file permissions**:
   ```powershell
   Get-Acl "C:\Users\<USER>\Jettyplanner\maintenance.flag"
   ```

2. **Manually remove flag file**:
   ```powershell
   Remove-Item "C:\Users\<USER>\Jettyplanner\maintenance.flag" -Force
   ```

### Cloudflare Cache Issues

If users still see the old page:
1. **Purge Cloudflare cache** via dashboard
2. **Clear browser cache** (Ctrl+F5)
3. **Check cache headers** in maintenance page (already set to no-cache)

## 🔒 Security Considerations

- **Maintenance page bypasses Cloudflare Zero Trust** for better user experience
- **No sensitive information** exposed on maintenance page
- **Proper cache headers** prevent caching of maintenance page
- **Flag file location** is not web-accessible
- **Maintenance logs** are stored locally and include audit trail

## 📞 Support

For issues with the maintenance system:
1. Check this guide first
2. Review nginx error logs
3. Test with manual flag file creation/removal
4. Verify Docker container status
5. Check Cloudflare configuration if needed
