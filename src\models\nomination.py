"""
Nomination Model

This module defines the Nomination class for representing pending vessel nominations
in the waiting list before they are processed by the OR-tools optimizer.
"""

import logging
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta, timezone
from enum import Enum

from .vessel import VesselType, Cargo, _parse_datetime_safe


class NominationStatus(Enum):
    """Status of a nomination in the waiting list"""
    PENDING = "pending"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    PROCESSED = "processed"  # When converted to vessel and scheduled


@dataclass
class Nomination:
    """
    Represents a vessel nomination in the waiting list before optimization
    """
    id: str
    name: str
    vessel_type: VesselType
    length: float  # in meters
    beam: float  # in meters
    draft: float  # in meters
    deadweight: float  # in tonnes
    cargoes: List[Cargo] = field(default_factory=list)
    status: NominationStatus = NominationStatus.PENDING

    # Standardized ETA fields
    eta: Optional[datetime] = None  # User-specified/customer-provided ETA
    etd: Optional[datetime] = None  # User-specified/customer-provided ETD
    calculated_eta: Optional[datetime] = None  # System-calculated ETA based on AIS/conditions
    eta_confidence: int = 50  # Confidence score for ETA prediction (0-100)
    eta_source: str = "user"  # Source of ETA ("user", "ais_calculated", "ml_predicted")
    customer: Optional[str] = None  # Customer name
    priority: int = 0  # Priority level (higher means more priority)
    capacity: float = 0.0  # Capacity in cubic meters
    width: float = 0.0  # Width/beam in meters
    
    # Nomination-specific fields
    nomination_date: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    submitted_by: Optional[str] = None  # User who submitted the nomination
    review_notes: Optional[str] = None  # Notes from review process
    processing_notes: Optional[str] = None  # Notes from optimization processing
    
    # Vessel-specific fields (for creating vessels later)
    cargo_type: Optional[str] = None
    flag: Optional[str] = None
    imo: Optional[str] = None
    mmsi: Optional[str] = None
    customs_cleared: bool = False
    last_port: Optional[str] = None
    next_port: Optional[str] = None
    
    # Barge-specific fields
    owner: Optional[str] = None
    registration_number: Optional[str] = None
    tug_boat: Optional[str] = None
    operation_type: Optional[str] = None
    has_crane: bool = False
    
    # Default setup/cleanup times for estimation
    setup_duration: timedelta = field(default=timedelta(hours=12))
    cleanup_duration: timedelta = field(default=timedelta(hours=3.6))
    
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def total_cargo_volume(self) -> float:
        """Calculate total cargo volume"""
        return sum(cargo.volume for cargo in self.cargoes)
    
    def get_cargo_products(self) -> set:
        """Get unique products in cargoes"""
        return {cargo.product for cargo in self.cargoes}
    
    def get_loading_cargoes(self) -> List[Cargo]:
        """Get cargoes to be loaded"""
        return [cargo for cargo in self.cargoes if cargo.is_loading]
    
    def get_unloading_cargoes(self) -> List[Cargo]:
        """Get cargoes to be unloaded"""
        return [cargo for cargo in self.cargoes if not cargo.is_loading]
    
    def is_compatible_with_jetty(self, jetty_max_length: float, jetty_max_draft: float, 
                                jetty_max_deadweight: float) -> bool:
        """Check if nomination is compatible with jetty dimensions"""
        return (self.length <= jetty_max_length and 
                self.draft <= jetty_max_draft and 
                self.deadweight <= jetty_max_deadweight)
    
    def estimated_operation_time(self, max_flow_rate: float) -> timedelta:
        """Estimate operation time based on cargo volume and flow rate"""
        if max_flow_rate <= 0:
            return timedelta(hours=24)  # Default to 24 hours if flow rate is unknown
        
        total_volume = self.total_cargo_volume()
        
        # Ensure division by zero is handled
        if total_volume <= 0:
            operation_hours = 0.0
        elif max_flow_rate <= 0:
            logging.warning(f"Max flow rate is zero or negative for nomination {self.id}. Returning base setup/cleanup time.")
            return self.setup_duration + self.cleanup_duration 
        else:
            operation_hours = total_volume / max_flow_rate
            
        total_duration = timedelta(hours=operation_hours) + self.setup_duration + self.cleanup_duration
        return total_duration
    
    def to_vessel_data(self) -> dict:
        """Convert nomination to vessel data dictionary for vessel creation"""
        from .vessel import Vessel, Barge
        
        base_data = {
            "id": f"V{self.id}",  # Prefix with V to distinguish from nomination ID
            "name": self.name,
            "vessel_type": self.vessel_type,
            "length": self.length,
            "beam": self.beam,
            "draft": self.draft,
            "deadweight": self.deadweight,
            "cargoes": [cargo.to_dict() for cargo in self.cargoes],  # Convert Cargo objects to dictionaries
            "status": "EN_ROUTE",  # Default status for new vessels
            "eta": self.eta.isoformat() if self.eta else None,
            "etd": self.etd.isoformat() if self.etd else None,
            "arrival_time": self.eta.isoformat() if self.eta else None,  # Use ETA as arrival time
            "departure_time": None,  # Will be calculated after ML prediction
            "current_jetty": None,  # Not yet assigned for new nominations
            "customer": self.customer,
            "priority": self.priority,
            "capacity": self.capacity,
            "width": self.width,
            "metadata": self.metadata.copy()
        }
        
        # Add vessel-specific or barge-specific fields
        if self.vessel_type == VesselType.TANKER:
            base_data.update({
                "cargo_type": self.cargo_type,
                "flag": self.flag,
                "imo": self.imo,
                "mmsi": self.mmsi,
                "customs_cleared": self.customs_cleared,
                "last_port": self.last_port,
                "next_port": self.next_port
            })
        elif self.vessel_type == VesselType.BARGE:
            base_data.update({
                "owner": self.owner,
                "registration_number": self.registration_number,
                "tug_boat": self.tug_boat,
                "operation_type": self.operation_type,
                "has_crane": self.has_crane
            })
        
        return base_data
    
    def to_dict(self) -> dict:
        """Convert nomination to dictionary for serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "vessel_type": self.vessel_type.value,
            "length": self.length,
            "beam": self.beam,
            "draft": self.draft,
            "deadweight": self.deadweight,
            "cargoes": [cargo.to_dict() for cargo in self.cargoes],
            "status": self.status.value,
            "eta": self.eta.isoformat() if self.eta else None,
            "etd": self.etd.isoformat() if self.etd else None,
            "customer": self.customer,
            "priority": self.priority,
            "capacity": self.capacity,
            "width": self.width,
            "nomination_date": self.nomination_date.isoformat(),
            "submitted_by": self.submitted_by,
            "review_notes": self.review_notes,
            "processing_notes": self.processing_notes,
            "cargo_type": self.cargo_type,
            "flag": self.flag,
            "imo": self.imo,
            "mmsi": self.mmsi,
            "customs_cleared": self.customs_cleared,
            "last_port": self.last_port,
            "next_port": self.next_port,
            "owner": self.owner,
            "registration_number": self.registration_number,
            "tug_boat": self.tug_boat,
            "operation_type": self.operation_type,
            "has_crane": self.has_crane,
            "setup_duration": str(self.setup_duration),
            "cleanup_duration": str(self.cleanup_duration),
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'Nomination':
        """Create nomination from dictionary"""
        return cls(
            id=data["id"],
            name=data["name"],
            vessel_type=VesselType(data["vessel_type"]),
            length=data["length"],
            beam=data["beam"],
            draft=data["draft"],
            deadweight=data["deadweight"],
            cargoes=[Cargo.from_dict(c) for c in data["cargoes"]],
            status=NominationStatus(data["status"]),

            # Standardized ETA fields
            eta=_parse_datetime_safe(data.get("eta")),
            etd=_parse_datetime_safe(data.get("etd")),
            calculated_eta=_parse_datetime_safe(data.get("calculated_eta")),
            eta_confidence=data.get("eta_confidence", 50),
            eta_source=data.get("eta_source", "user"),
            customer=data["customer"],
            priority=data["priority"],
            capacity=data.get("capacity", 0.0),
            width=data.get("width", 0.0),
            nomination_date=_parse_datetime_safe(data.get("nomination_date")) or datetime.now(timezone.utc),
            submitted_by=data.get("submitted_by"),
            review_notes=data.get("review_notes"),
            processing_notes=data.get("processing_notes"),
            cargo_type=data.get("cargo_type"),
            flag=data.get("flag"),
            imo=data.get("imo"),
            mmsi=data.get("mmsi"),
            customs_cleared=data.get("customs_cleared", False),
            last_port=data.get("last_port"),
            next_port=data.get("next_port"),
            owner=data.get("owner"),
            registration_number=data.get("registration_number"),
            tug_boat=data.get("tug_boat"),
            operation_type=data.get("operation_type"),
            has_crane=data.get("has_crane", False),
            metadata=data.get("metadata", {})
        )


@dataclass
class NominationList:
    """Container for managing a list of nominations"""
    nominations: List[Nomination] = field(default_factory=list)
    
    def add_nomination(self, nomination: Nomination) -> bool:
        """Add a nomination to the list"""
        # Check if nomination ID already exists
        if any(n.id == nomination.id for n in self.nominations):
            return False
        
        self.nominations.append(nomination)
        return True
    
    def remove_nomination(self, nomination_id: str) -> bool:
        """Remove a nomination from the list"""
        for i, nomination in enumerate(self.nominations):
            if nomination.id == nomination_id:
                self.nominations.pop(i)
                return True
        return False
    
    def get_nomination_by_id(self, nomination_id: str) -> Optional[Nomination]:
        """Get a nomination by ID"""
        for nomination in self.nominations:
            if nomination.id == nomination_id:
                return nomination
        return None
    
    def get_pending_nominations(self) -> List[Nomination]:
        """Get all pending nominations"""
        return [n for n in self.nominations if n.status == NominationStatus.PENDING]
    
    def get_approved_nominations(self) -> List[Nomination]:
        """Get all approved nominations ready for processing"""
        return [n for n in self.nominations if n.status == NominationStatus.APPROVED]
    
    def mark_as_processed(self, nomination_ids: List[str]) -> int:
        """Mark nominations as processed and return count of updated nominations"""
        count = 0
        for nomination in self.nominations:
            if nomination.id in nomination_ids:
                nomination.status = NominationStatus.PROCESSED
                count += 1
        return count
    
    def clear_processed(self) -> int:
        """Remove all processed nominations and return count of removed nominations"""
        initial_count = len(self.nominations)
        self.nominations = [n for n in self.nominations if n.status != NominationStatus.PROCESSED]
        return initial_count - len(self.nominations)
    
    def to_dict(self) -> dict:
        """Convert nomination list to dictionary for serialization"""
        return {
            "nominations": [n.to_dict() for n in self.nominations]
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'NominationList':
        """Create nomination list from dictionary"""
        return cls(
            nominations=[Nomination.from_dict(n) for n in data["nominations"]]
        )
