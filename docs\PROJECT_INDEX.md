# Jetty Planner — Project Index

## Overview

- Purpose: FastAPI-based “Jetty Planning Optimizer” for petrochemical terminals with scheduling, optimization, AIS/weather integrations, analytics, and background jobs.
- Core runtime: `FastAPI` app with routers, SQLAlchemy + Alembic for Postgres, OR-Tools optimizer, APScheduler background jobs.

## How To Run

- Local API (via wrapper): `python run.py --api --host 127.0.0.1 --port 7000`
- Start Postgres (Compose): `python run.py --start-postgres` then run API
- Production mode (defaults to API): `python run.py --production`
- Direct Uvicorn: `uvicorn src.api.fastapi_app:app --host 0.0.0.0 --port 7000`

## Entry Points

- `run.py` – CLI wrapper: loads `.env`, can start API, run optimization, interactive mode; optional Docker Compose start for Postgres.
- `src/main.py` – Runtime coordinator; configures logging/env, proxies `app` to FastAPI app and adds some page routes.
- `src/api/fastapi_app.py` – Primary FastAPI app: routers, templates/static, CORS, startup/shutdown jobs, global state.

## Application Modules

- API (`src/api`)
  - `fastapi_app.py` – App factory, mounts static/templates, registers routers, startup/shutdown, AIS preload, `APScheduler` job.
  - `assignment_api.py` – CRUD for assignments, status validation/transition checks.
  - `terminal_api.py` – Terminal-related endpoints (+ backward-compat router).
  - `status_api.py` – Health/version/status endpoints.
  - `ml_api.py` – ML-related endpoints (ETA/analytics hooks).
- Models (`src/models`) – Domain classes: `terminal.py`, `vessel.py`, `schedule.py`, `nomination.py`, etc.
- Optimization (`src/optimization/scheduler.py`) – OR-Tools scheduler (JettyScheduler).
- Services (`src/services`) – Business services: AIS/ship tracking, tidal, ETA (ML/dynamic), analytics, locks, nomination workflow, realtime schedule, background `assignment_status_job.py`.
- Repositories (`src/repositories`) – Data access helpers.
- Utils (`src/utils`) – `datetime_utils.py`, `status_utils.py`, `ais_types.py`.

## Database Layer

- ORM: SQLAlchemy models in `src/db/models.py` and session/engine in `src/db/session.py`.
- Facade: `src/database.py` – Initializes DB, seeds defaults (terminal TNZN, jetties, settings, tanks), and exposes CRUD/query helpers.
- Migrations: Alembic in `alembic/` with multiple versions (initial baseline, nominations, analytics, locks, ETA standardization). Configure per `alembic.ini`.

## Web Layer (Templates/Static)

- Templates: `src/templates/`
  - Layout: `base.html` (nav to Dashboard, Schedule, Nomination, Smart ETA, Weather, Optimize, Assistant, Analytics, Logs, Settings).
  - Views: `index.html`, `schedule.html`, `optimize.html`, `analytics.html`, `logs.html`, `nominated_vessels.html`, `nomination.html`, `settings.html`, `terminals.html`, `weather.html`.
  - Components: `components/status_badge.html`, `components/status_legend.html`.
- Static: `src/static/`
  - JS: `analytics-dashboard.js`, `nomination.js`, `optimization.js`, `ship-map.js`, `settings.js`, `terminal-manager.js`, `toast.js`, `components/status_badge.js`.
  - CSS: `style.css`, component styles, vendor assets (Bootstrap, Chart.js, Leaflet, Font Awesome).
  - Assets: icons, fonts (Roboto/Blinker), images, `Terminal-map.pdf`.

## Scripts & Ops

- `docker-compose*.yml`, `Dockerfile` – Containerization and local stack.
- `scripts/` – PowerShell and shell scripts for start, migrations, backups, maintenance mode, and scheduled tasks.
- Cloudflare worker: `cloudflare/maintenance-worker.js`.

## Tests

- `tests/` and `src/tests/` – Pytest suites for repositories, services, transactions, and assignment status transitions.
- Run tests: `pytest`.

## Configuration

- `requirements.txt` – Core libraries: FastAPI, Uvicorn, SQLAlchemy, Alembic, OR-Tools, APScheduler, numpy/pandas/sklearn/xgboost/joblib, requests/aiohttp/websockets, etc.
- `.env` (sample present): server settings, terminal location, API keys (Anthropic/AIS), weather provider, Postgres settings, pooling and logging. Do not commit real secrets.

## Notable Paths

- App: `src/api/fastapi_app.py`, `src/main.py`, `run.py`
- DB: `src/db/models.py`, `src/db/session.py`, `src/database.py`, `alembic/`
- Optimization: `src/optimization/scheduler.py`
- Services: `src/services/*`
- Templates/Static: `src/templates/*`, `src/static/*`

