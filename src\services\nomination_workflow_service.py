from typing import Dict, Any, Optional

from ..database import Database
from .vessel_registry_service import VesselRegistryService
from .visit_tracking_service import VisitTrackingService
from .ais_integration_service import AISIntegrationService


class NominationWorkflowService:
    def __init__(self, db: Optional[Database] = None):
        self.db = db or Database()
        self.registry = VesselRegistryService(self.db)
        self.visits = VisitTrackingService(self.db)
        self.ais = AISIntegrationService(self.db)

    def create_nomination_links(self, nomination: Dict[str, Any], terminal_id: Optional[str] = None) -> Dict[str, Any]:
        """Create/attach registry vessel and visit, return linkage IDs.

        Expects nomination keys: name, vessel_type, mmsi?, imo?, eta?, etd?
        """
        vessel_id = self.registry.find_or_create_vessel(
            imo=nomination.get('imo'),
            mmsi=nomination.get('mmsi'),
            name=nomination.get('name'),
            vessel_type=nomination.get('vessel_type'),
            length_overall=nomination.get('length'),
            beam=nomination.get('beam'),
            maximum_draft=nomination.get('draft'),
            deadweight=nomination.get('deadweight'),
        )

        terminal_id = terminal_id or self.db.get_active_terminal_id()
        visit_id = self.visits.create_visit(
            vessel_id=vessel_id,
            terminal_id=terminal_id,
            eta=nomination.get('eta'),
            estimated_departure=nomination.get('etd'),
            operation_type='LOADING' if nomination.get('cargoes') else None,
            cargo_types=[c.get('product') for c in (nomination.get('cargoes') or [])],
            total_cargo_volume=sum((c.get('volume') or 0) for c in (nomination.get('cargoes') or [])),
            customer=nomination.get('customer'),
            priority=nomination.get('priority', 1),
            eta_source='user',
            eta_confidence=nomination.get('eta_confidence', 50),
            calculated_eta=nomination.get('calculated_eta')
        )

        # Log ETA creation for analysis
        try:
            self.db.log_eta_update(
                source=nomination.get('eta_source', 'user') or 'user',
                eta=nomination.get('eta'),
                confidence=nomination.get('eta_confidence', 50),
                vessel_registry_id=vessel_id,
                visit_id=visit_id,
                runtime_vessel_id=nomination.get('runtime_vessel_id')
            )
            if nomination.get('calculated_eta'):
                self.db.log_eta_update(
                    source='ais_calculated',
                    eta=nomination.get('calculated_eta'),
                    confidence=nomination.get('eta_confidence', 50),
                    vessel_registry_id=vessel_id,
                    visit_id=visit_id,
                    runtime_vessel_id=nomination.get('runtime_vessel_id'),
                    context='initial calculated_eta at nomination'
                )
        except Exception:
            pass

        if nomination.get('mmsi'):
            self.ais.link_ais_to_vessel(mmsi=str(nomination.get('mmsi')), vessel_id=vessel_id)

        return {
            'vessel_db_id': vessel_id,
            'visit_id': visit_id
        }


