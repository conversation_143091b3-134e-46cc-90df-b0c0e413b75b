"""
ML-Enhanced ETA Prediction Service
Uses machine learning models to improve ETA accuracy based on historical patterns
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import pickle
import os
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score

from ..database import Database

logger = logging.getLogger(__name__)

@dataclass
class ETAPrediction:
    """ML-based ETA prediction result"""
    vessel_id: str
    predicted_eta: datetime
    confidence: int
    model_used: str
    features_used: List[str]
    prediction_accuracy: Optional[float] = None

class MLETAService:
    """Machine Learning service for ETA predictions"""
    
    def __init__(self, database: Database, model_cache_dir: str = "models/eta"):
        """
        Initialize ML ETA Service
        
        Args:
            database: Database connection
            model_cache_dir: Directory to cache trained models
        """
        self.database = database
        self.model_cache_dir = model_cache_dir
        
        # Ensure model directory exists
        os.makedirs(model_cache_dir, exist_ok=True)
        
        # Models
        self.eta_model = None
        self.delay_model = None
        self.scaler = None
        
        # Model metadata
        self.model_last_trained = None
        self.model_accuracy = None
        self.feature_importance = {}
        
        # Configuration
        self.retrain_interval_days = 7  # Retrain weekly
        self.min_training_samples = 50  # Minimum samples needed for training
        self.confidence_threshold = 0.7  # R² threshold for high confidence
        
    async def initialize_models(self):
        """Initialize or load existing ML models"""
        logger.info("Initializing ML ETA models")
        
        try:
            # Try to load existing models
            if await self._load_cached_models():
                logger.info("Loaded cached ML models")
                return True
            
            # Train new models if no cache exists
            logger.info("No cached models found, training new models")
            return await self.train_models()
            
        except Exception as e:
            logger.error(f"Failed to initialize ML models: {e}")
            return False
    
    async def predict_eta(self, vessel_data: Dict) -> Optional[ETAPrediction]:
        """
        Predict ETA using ML models
        
        Args:
            vessel_data: Vessel information dictionary
            
        Returns:
            ETAPrediction or None if prediction fails
        """
        if not self.eta_model or not self.scaler:
            logger.warning("ML models not initialized")
            return None
        
        try:
            # Extract features
            features = await self._extract_features(vessel_data)
            if not features:
                return None
            
            # Prepare feature vector
            feature_vector = np.array([features]).reshape(1, -1)
            feature_vector_scaled = self.scaler.transform(feature_vector)
            
            # Make prediction (hours from now)
            predicted_hours = self.eta_model.predict(feature_vector_scaled)[0]
            
            # Convert to datetime
            predicted_eta = datetime.now(timezone.utc) + timedelta(hours=predicted_hours)
            
            # Calculate confidence based on model accuracy and feature quality
            confidence = await self._calculate_prediction_confidence(features, predicted_hours)
            
            # Determine which features were most important
            feature_names = self._get_feature_names()
            important_features = [
                feature_names[i] for i, importance in enumerate(self.feature_importance.values())
                if importance > 0.1
            ]
            
            return ETAPrediction(
                vessel_id=vessel_data.get('runtime_vessel_id', 'unknown'),
                predicted_eta=predicted_eta,
                confidence=confidence,
                model_used="RandomForest" if isinstance(self.eta_model, RandomForestRegressor) else "GradientBoosting",
                features_used=important_features,
                prediction_accuracy=self.model_accuracy
            )
            
        except Exception as e:
            logger.error(f"ML ETA prediction failed: {e}")
            return None
    
    async def train_models(self) -> bool:
        """Train ML models using historical data"""
        logger.info("Training ML ETA models")
        
        try:
            # Get training data
            training_data = await self._get_training_data()
            
            if len(training_data) < self.min_training_samples:
                logger.warning(f"Insufficient training data: {len(training_data)} samples (need {self.min_training_samples})")
                return False
            
            logger.info(f"Training with {len(training_data)} historical samples")
            
            # Prepare features and targets
            X, y = await self._prepare_training_data(training_data)
            
            if X is None or y is None:
                logger.error("Failed to prepare training data")
                return False
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train primary ETA model
            self.eta_model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            
            self.eta_model.fit(X_train_scaled, y_train)
            
            # Evaluate model
            y_pred = self.eta_model.predict(X_test_scaled)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            self.model_accuracy = r2
            self.model_last_trained = datetime.now(timezone.utc)
            
            # Store feature importance
            feature_names = self._get_feature_names()
            self.feature_importance = dict(zip(feature_names, self.eta_model.feature_importances_))
            
            logger.info(f"Model training complete - MAE: {mae:.2f} hours, R²: {r2:.3f}")
            
            # Cache models
            await self._cache_models()
            
            return r2 > 0.3  # Require reasonable accuracy
            
        except Exception as e:
            logger.error(f"Model training failed: {e}")
            return False
    
    async def _get_training_data(self) -> List[Dict]:
        """Get historical data for training"""
        try:
            with self.database.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Get historical vessel visits with actual arrival times
                    cursor.execute("""
                        SELECT 
                            n.runtime_vessel_id,
                            n.name,
                            n.vessel_type,
                            n.length,
                            n.beam, 
                            n.draft,
                            n.deadweight,
                            n.eta as original_eta,
                            n.calculated_eta,
                            n.eta_confidence,
                            n.eta_source,
                            vv.estimated_arrival,
                            vv.actual_arrival,
                            vv.created_at,
                            EXTRACT(EPOCH FROM (vv.actual_arrival - n.eta))/3600 as eta_error_hours,
                            EXTRACT(DOW FROM n.eta) as day_of_week,
                            EXTRACT(HOUR FROM n.eta) as hour_of_day,
                            EXTRACT(EPOCH FROM (vv.actual_arrival - vv.created_at))/3600 as lead_time_hours
                        FROM nominations n
                        JOIN vessel_visits vv ON n.runtime_vessel_id = vv.vessel_id
                        WHERE vv.actual_arrival IS NOT NULL
                        AND n.eta IS NOT NULL
                        AND vv.actual_arrival > n.created_at
                        AND vv.actual_arrival < NOW()
                        ORDER BY vv.actual_arrival DESC
                        LIMIT 1000;
                    """)
                    
                    columns = [desc[0] for desc in cursor.description]
                    rows = cursor.fetchall()
                    
                    return [dict(zip(columns, row)) for row in rows]
                    
        except Exception as e:
            logger.error(f"Failed to get training data: {e}")
            return []
    
    async def _prepare_training_data(self, training_data: List[Dict]) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """Prepare features and targets for training"""
        try:
            features = []
            targets = []
            
            for record in training_data:
                # Extract features
                feature_dict = await self._extract_features_from_record(record)
                if not feature_dict:
                    continue
                
                # Target: hours from record creation to actual arrival
                target = record['lead_time_hours']
                if target is None or target < 0:
                    continue
                
                features.append(list(feature_dict.values()))
                targets.append(target)
            
            if not features:
                return None, None
            
            return np.array(features), np.array(targets)
            
        except Exception as e:
            logger.error(f"Failed to prepare training data: {e}")
            return None, None
    
    async def _extract_features(self, vessel_data: Dict) -> Optional[Dict]:
        """Extract features for ML prediction"""
        try:
            current_time = datetime.now(timezone.utc)
            eta = vessel_data.get('eta')
            
            if not eta:
                return None
            
            # Convert string to datetime if needed
            if isinstance(eta, str):
                eta = datetime.fromisoformat(eta.replace('Z', '+00:00'))
            
            features = {
                'vessel_length': float(vessel_data.get('length', 0)),
                'vessel_beam': float(vessel_data.get('beam', 0)),
                'vessel_draft': float(vessel_data.get('draft', 0)),
                'vessel_deadweight': float(vessel_data.get('deadweight', 0)),
                'eta_confidence': float(vessel_data.get('eta_confidence', 50)),
                'hours_to_eta': (eta - current_time).total_seconds() / 3600,
                'day_of_week': eta.weekday(),
                'hour_of_day': eta.hour,
                'is_weekend': 1 if eta.weekday() >= 5 else 0,
                'is_rush_hour': 1 if 6 <= eta.hour <= 9 or 16 <= eta.hour <= 19 else 0,
                'vessel_type_tanker': 1 if vessel_data.get('vessel_type') == 'TANKER' else 0,
                'eta_source_user': 1 if vessel_data.get('eta_source') == 'user' else 0,
                'eta_source_ais': 1 if vessel_data.get('eta_source') == 'ais_calculated' else 0,
            }
            
            return features
            
        except Exception as e:
            logger.error(f"Feature extraction failed: {e}")
            return None
    
    async def _extract_features_from_record(self, record: Dict) -> Optional[Dict]:
        """Extract features from historical record"""
        try:
            features = {
                'vessel_length': float(record.get('length', 0)),
                'vessel_beam': float(record.get('beam', 0)),
                'vessel_draft': float(record.get('draft', 0)),
                'vessel_deadweight': float(record.get('deadweight', 0)),
                'eta_confidence': float(record.get('eta_confidence', 50)),
                'hours_to_eta': float(record.get('lead_time_hours', 0)),
                'day_of_week': int(record.get('day_of_week', 0)),
                'hour_of_day': int(record.get('hour_of_day', 0)),
                'is_weekend': 1 if int(record.get('day_of_week', 0)) >= 5 else 0,
                'is_rush_hour': 1 if 6 <= int(record.get('hour_of_day', 0)) <= 9 or 16 <= int(record.get('hour_of_day', 0)) <= 19 else 0,
                'vessel_type_tanker': 1 if record.get('vessel_type') == 'TANKER' else 0,
                'eta_source_user': 1 if record.get('eta_source') == 'user' else 0,
                'eta_source_ais': 1 if record.get('eta_source') == 'ais_calculated' else 0,
            }
            
            return features
            
        except Exception as e:
            logger.error(f"Feature extraction from record failed: {e}")
            return None
    
    def _get_feature_names(self) -> List[str]:
        """Get list of feature names in order"""
        return [
            'vessel_length', 'vessel_beam', 'vessel_draft', 'vessel_deadweight',
            'eta_confidence', 'hours_to_eta', 'day_of_week', 'hour_of_day',
            'is_weekend', 'is_rush_hour', 'vessel_type_tanker', 
            'eta_source_user', 'eta_source_ais'
        ]
    
    async def _calculate_prediction_confidence(self, features: Dict, predicted_hours: float) -> int:
        """Calculate confidence score for prediction"""
        base_confidence = 60
        
        # Adjust based on model accuracy
        if self.model_accuracy:
            accuracy_bonus = int(self.model_accuracy * 30)  # 0-30 points
            base_confidence += accuracy_bonus
        
        # Adjust based on feature quality
        if features.get('eta_confidence', 0) > 70:
            base_confidence += 10
        
        # Adjust based on prediction horizon
        hours_to_eta = features.get('hours_to_eta', 0)
        if hours_to_eta < 12:  # Very near term
            base_confidence += 10
        elif hours_to_eta > 48:  # Far future
            base_confidence -= 10
        
        return max(10, min(95, base_confidence))
    
    async def _cache_models(self):
        """Cache trained models to disk"""
        try:
            model_path = os.path.join(self.model_cache_dir, 'eta_model.pkl')
            scaler_path = os.path.join(self.model_cache_dir, 'scaler.pkl')
            metadata_path = os.path.join(self.model_cache_dir, 'metadata.pkl')
            
            with open(model_path, 'wb') as f:
                pickle.dump(self.eta_model, f)
            
            with open(scaler_path, 'wb') as f:
                pickle.dump(self.scaler, f)
            
            metadata = {
                'model_last_trained': self.model_last_trained,
                'model_accuracy': self.model_accuracy,
                'feature_importance': self.feature_importance
            }
            
            with open(metadata_path, 'wb') as f:
                pickle.dump(metadata, f)
            
            logger.info("ML models cached successfully")
            
        except Exception as e:
            logger.error(f"Failed to cache models: {e}")
    
    async def _load_cached_models(self) -> bool:
        """Load cached models from disk"""
        try:
            model_path = os.path.join(self.model_cache_dir, 'eta_model.pkl')
            scaler_path = os.path.join(self.model_cache_dir, 'scaler.pkl')
            metadata_path = os.path.join(self.model_cache_dir, 'metadata.pkl')
            
            if not all(os.path.exists(p) for p in [model_path, scaler_path, metadata_path]):
                return False
            
            with open(model_path, 'rb') as f:
                self.eta_model = pickle.load(f)
            
            with open(scaler_path, 'rb') as f:
                self.scaler = pickle.load(f)
            
            with open(metadata_path, 'rb') as f:
                metadata = pickle.load(f)
                self.model_last_trained = metadata.get('model_last_trained')
                self.model_accuracy = metadata.get('model_accuracy')
                self.feature_importance = metadata.get('feature_importance', {})
            
            # Check if models need retraining
            if self.model_last_trained:
                days_since_training = (datetime.now(timezone.utc) - self.model_last_trained).days
                if days_since_training > self.retrain_interval_days:
                    logger.info(f"Models are {days_since_training} days old, will retrain")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load cached models: {e}")
            return False
    
    def get_model_info(self) -> Dict:
        """Get information about the current models"""
        return {
            'model_loaded': self.eta_model is not None,
            'model_last_trained': self.model_last_trained,
            'model_accuracy': self.model_accuracy,
            'feature_importance': self.feature_importance,
            'retrain_interval_days': self.retrain_interval_days,
            'min_training_samples': self.min_training_samples
        }
