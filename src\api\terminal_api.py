"""
Terminal API Module

This module provides API endpoints for Terneuzen terminal operations.
Simplified from multi-terminal version to focus only on EVOS Terneuzen.
"""

from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel
import logging

from ..database import Database
from ..models.terminal_config import terminal_registry, TerminalConfig
# Terminal manager removed - only using Terneuzen terminal

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/terminals", tags=["terminals"])

# Initialize database
db = Database()

# Pydantic models for request/response validation
# Terminal switching removed - only Terneuzen terminal is used

class TerminalResponse(BaseModel):
    """Response model for terminal information"""
    id: str
    name: str
    location: Dict[str, float]
    capacity_cbm: float
    number_of_tanks: int
    draft_meters: float
    vessel_berths: int
    barge_berths: int
    operational_since: str
    is_active: bool
    products: List[str] = []
    certifications: List[str] = []

class TerminalStatistics(BaseModel):
    """Response model for terminal statistics"""
    terminal_id: str
    name: str
    jetties: int
    tanks: int
    pumps: int
    total_capacity: float
    current_vessels: int
    active_assignments: int
    last_updated: Optional[str] = None
    optimization_runs: int

class TerminalComparison(BaseModel):
    """Response model for terminal comparison"""
    terminals: Dict[str, TerminalStatistics]
    summary: Dict[str, Any]

@router.get("/", response_model=List[TerminalResponse])
async def get_all_terminals():
    """Get all available terminals"""
    try:
        logger.info("Attempting to retrieve all terminals from database")
        terminals = db.get_terminals()
        logger.info(f"Database returned {len(terminals)} terminals")
        
        if not terminals:
            logger.warning("No terminals found in database")
            return []
        
        response = []
        for terminal in terminals:
            try:
                # Get terminal config for additional details (may not exist)
                config = None
                try:
                    config = terminal_registry.get_terminal(terminal['id'])
                except Exception as config_error:
                    logger.warning(f"Failed to get config for terminal {terminal['id']}: {config_error}")
                
                terminal_data = TerminalResponse(
                    id=terminal['id'],
                    name=terminal['name'],
                    location={
                        "lat": terminal['location_lat'],
                        "lon": terminal['location_lon']
                    },
                    capacity_cbm=terminal['total_capacity_cbm'],
                    number_of_tanks=terminal['number_of_tanks'],
                    draft_meters=terminal['draft_meters'],
                    vessel_berths=terminal['vessel_berths'],
                    barge_berths=terminal['barge_berths'],
                    operational_since=str(terminal['operational_since']),
                    is_active=bool(terminal['is_active']),
                    products=config.products if config else [],
                    certifications=config.certifications if config and hasattr(config, 'certifications') else []
                )
                response.append(terminal_data)
            except Exception as terminal_error:
                logger.error(f"Error processing terminal {terminal.get('id', 'unknown')}: {terminal_error}")
                continue
        
        logger.info(f"Successfully processed {len(response)} terminals")
        return response
        
    except Exception as e:
        logger.error(f"Error retrieving terminals: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve terminals: {str(e)}")

@router.get("/{terminal_id}", response_model=TerminalResponse)
async def get_terminal(terminal_id: str):
    """Get a specific terminal by ID"""
    try:
        terminal = db.get_terminal(terminal_id)
        if not terminal:
            raise HTTPException(status_code=404, detail=f"Terminal {terminal_id} not found")
        
        # Get terminal config for additional details
        config = terminal_registry.get_terminal(terminal_id)
        
        return TerminalResponse(
            id=terminal['id'],
            name=terminal['name'],
            location={
                "lat": terminal['location_lat'],
                "lon": terminal['location_lon']
            },
            capacity_cbm=terminal['total_capacity_cbm'],
            number_of_tanks=terminal['number_of_tanks'],
            draft_meters=terminal['draft_meters'],
            vessel_berths=terminal['vessel_berths'],
            barge_berths=terminal['barge_berths'],
            operational_since=str(terminal['operational_since']),
            is_active=bool(terminal['is_active']),
            products=config.products if config else [],
            certifications=[cert.value for cert in config.certifications] if config else []
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving terminal {terminal_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve terminal")

@router.get("/active/current")
async def get_active_terminal():
    """Get the currently active terminal"""
    try:
        logger.info("Attempting to retrieve active terminal")
        active_terminal_id = db.get_active_terminal_id()
        logger.info(f"Active terminal ID: {active_terminal_id}")
        
        active_terminal = db.get_terminal(active_terminal_id)
        logger.info(f"Active terminal data: {active_terminal}")
        
        if not active_terminal:
            logger.error(f"No terminal found with ID {active_terminal_id}")
            raise HTTPException(status_code=404, detail="No active terminal found")
        
        # Get terminal config for additional details (may not exist)
        config = None
        try:
            config = terminal_registry.get_terminal(active_terminal_id)
        except Exception as config_error:
            logger.warning(f"Failed to get config for active terminal {active_terminal_id}: {config_error}")
        
        result = {
            "id": active_terminal['id'],
            "name": active_terminal['name'],
            "location": {
                "lat": active_terminal['location_lat'],
                "lon": active_terminal['location_lon']
            },
            "capacity_cbm": active_terminal['total_capacity_cbm'],
            "is_active": True,
            "products": config.products if config else [],
            "certifications": config.certifications if config and hasattr(config, 'certifications') else []
        }
        
        logger.info(f"Returning active terminal: {result}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving active terminal: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve active terminal: {str(e)}")

# Terminal switching endpoint removed - only Terneuzen terminal is used

@router.get("/{terminal_id}/statistics", response_model=TerminalStatistics)
async def get_terminal_statistics(terminal_id: str):
    """Get statistics for a specific terminal"""
    try:
        # Validate terminal exists
        terminal = db.get_terminal(terminal_id)
        if not terminal:
            raise HTTPException(status_code=404, detail=f"Terminal {terminal_id} not found")
        
        # Get terminal data
        jetties = db.get_jetties(terminal_id)
        tanks = db.get_tanks(terminal_id)
        pumps = db.get_pumps(terminal_id)
        vessels = db.get_vessels(terminal_id)
        assignments = db.get_assignments(terminal_id)
        
        # Optimization history simplified for Terneuzen only
        optimization_history = []  # TODO: Implement if needed
        
        return TerminalStatistics(
            terminal_id=terminal_id,
            name=terminal['name'],
            jetties=len(jetties),
            tanks=len(tanks),
            pumps=len(pumps),
            total_capacity=terminal['total_capacity_cbm'],
            current_vessels=len(vessels),
            active_assignments=len(assignments),
            optimization_runs=len(optimization_history)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving statistics for terminal {terminal_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve terminal statistics")

@router.post("/compare", response_model=TerminalComparison)
async def compare_terminals(terminal_ids: List[str]):
    """Compare multiple terminals"""
    try:
        if len(terminal_ids) < 2:
            raise HTTPException(status_code=400, detail="At least 2 terminals required for comparison")
        
        if len(terminal_ids) > 5:
            raise HTTPException(status_code=400, detail="Maximum 5 terminals can be compared at once")
        
        # Validate all terminals exist
        terminals = {}
        for terminal_id in terminal_ids:
            terminal = db.get_terminal(terminal_id)
            if not terminal:
                raise HTTPException(status_code=404, detail=f"Terminal {terminal_id} not found")
            terminals[terminal_id] = terminal
        
        # Get statistics for each terminal
        comparison_data = {}
        total_capacity = 0
        total_jetties = 0
        total_vessels = 0
        
        for terminal_id in terminal_ids:
            jetties = db.get_jetties(terminal_id)
            tanks = db.get_tanks(terminal_id)
            pumps = db.get_pumps(terminal_id)
            vessels = db.get_vessels(terminal_id)
            assignments = db.get_assignments(terminal_id)
            optimization_history = []  # Simplified for Terneuzen only
            
            terminal = terminals[terminal_id]
            
            stats = TerminalStatistics(
                terminal_id=terminal_id,
                name=terminal['name'],
                jetties=len(jetties),
                tanks=len(tanks),
                pumps=len(pumps),
                total_capacity=terminal['total_capacity_cbm'],
                current_vessels=len(vessels),
                active_assignments=len(assignments),
                optimization_runs=len(optimization_history)
            )
            
            comparison_data[terminal_id] = stats
            
            # Accumulate totals for summary
            total_capacity += stats.total_capacity
            total_jetties += stats.jetties
            total_vessels += stats.current_vessels
        
        # Create summary
        summary = {
            "compared_terminals": len(terminal_ids),
            "total_capacity": total_capacity,
            "total_jetties": total_jetties,
            "total_vessels": total_vessels,
            "largest_capacity": max(comparison_data.values(), key=lambda x: x.total_capacity).terminal_id,
            "most_jetties": max(comparison_data.values(), key=lambda x: x.jetties).terminal_id,
            "most_active": max(comparison_data.values(), key=lambda x: x.current_vessels).terminal_id
        }
        
        return TerminalComparison(
            terminals=comparison_data,
            summary=summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error comparing terminals: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to compare terminals")

@router.get("/{terminal_id}/jetties")
async def get_terminal_jetties(terminal_id: str):
    """Get jetties for a specific terminal"""
    try:
        # Validate terminal exists
        terminal = db.get_terminal(terminal_id)
        if not terminal:
            raise HTTPException(status_code=404, detail=f"Terminal {terminal_id} not found")
        
        jetties = db.get_jetties(terminal_id)
        return {
            "terminal_id": terminal_id,
            "terminal_name": terminal['name'],
            "jetties": jetties
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving jetties for terminal {terminal_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve terminal jetties")

@router.get("/{terminal_id}/vessels")
async def get_terminal_vessels(terminal_id: str):
    """Get vessels for a specific terminal"""
    try:
        # Validate terminal exists
        terminal = db.get_terminal(terminal_id)
        if not terminal:
            raise HTTPException(status_code=404, detail=f"Terminal {terminal_id} not found")
        
        vessels = db.get_vessels(terminal_id)
        return {
            "terminal_id": terminal_id,
            "terminal_name": terminal['name'],
            "vessels": vessels
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving vessels for terminal {terminal_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve terminal vessels")

@router.get("/{terminal_id}/assignments")
async def get_terminal_assignments(terminal_id: str):
    """Get assignments for a specific terminal"""
    try:
        # Validate terminal exists
        terminal = db.get_terminal(terminal_id)
        if not terminal:
            raise HTTPException(status_code=404, detail=f"Terminal {terminal_id} not found")
        
        assignments = db.get_assignments(terminal_id)
        return {
            "terminal_id": terminal_id,
            "terminal_name": terminal['name'],
            "assignments": assignments
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving assignments for terminal {terminal_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve terminal assignments")

# Create a separate router for backward compatibility endpoints without prefix
backward_compat_router = APIRouter(tags=["terminal-compat"])

@backward_compat_router.get("/api/terminal")
async def get_current_terminal():
    """Get current active terminal with resource counts (for backward compatibility)"""
    try:
        logger.info("Backward compatibility endpoint: retrieving current terminal")
        active_terminal_id = db.get_active_terminal_id()
        logger.info(f"Active terminal ID: {active_terminal_id}")
        
        terminal_data = db.get_terminal(active_terminal_id)
        logger.info(f"Terminal data: {terminal_data}")
        
        if not terminal_data:
            logger.error(f"No terminal found with ID {active_terminal_id}")
            raise HTTPException(status_code=404, detail="Active terminal not found")
        
        # Get terminal resources with error handling
        jetties = []
        tanks = []
        pumps = []
        surveyors = []
        
        try:
            jetties = db.get_jetties(active_terminal_id)
            logger.info(f"Retrieved {len(jetties)} jetties")
        except Exception as e:
            logger.warning(f"Failed to get jetties: {e}")
            
        try:
            tanks = db.get_tanks(active_terminal_id)
            logger.info(f"Retrieved {len(tanks)} tanks")
        except Exception as e:
            logger.warning(f"Failed to get tanks: {e}")
            
        try:
            pumps = db.get_pumps(active_terminal_id)
            logger.info(f"Retrieved {len(pumps)} pumps")
        except Exception as e:
            logger.warning(f"Failed to get pumps: {e}")
            
        try:
            surveyors = db.get_surveyors(active_terminal_id)
            logger.info(f"Retrieved {len(surveyors)} surveyors")
        except Exception as e:
            logger.warning(f"Failed to get surveyors: {e}")
        
        # Get terminal config for additional details (may not exist)
        config = None
        try:
            config = terminal_registry.get_terminal(active_terminal_id)
        except Exception as config_error:
            logger.warning(f"Failed to get config for terminal {active_terminal_id}: {config_error}")
        
        result = {
            "id": terminal_data["id"],
            "name": terminal_data["name"],
            "location": {
                "lat": terminal_data["location_lat"],
                "lon": terminal_data["location_lon"]
            },
            "capacity_cbm": terminal_data["total_capacity_cbm"],
            "draft_meters": terminal_data["draft_meters"],
            "jetty_count": len(jetties),
            "vessel_berth_count": terminal_data["vessel_berths"],
            "barge_berth_count": terminal_data["barge_berths"],
            "tank_count": len(tanks),
            "pump_count": len(pumps),
            "surveyor_count": len(surveyors),
            "products": config.products if config else [],
            "certifications": config.certifications if config and hasattr(config, 'certifications') else []
        }
        
        logger.info(f"Returning terminal info: {result}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting terminal info: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve terminal information: {str(e)}")

@router.get("/active/throughput")
async def get_terminal_throughput(date: str = Query(..., description="Date in YYYY-MM-DD format")):
    """Get terminal throughput data for a specific date"""
    try:
        logger.info(f"Getting throughput data for date: {date}")
        
        # Get active terminal ID
        active_terminal_id = "TNZN"  # Fixed for Terneuzen
        
        # Get assignments for the specified date
        assignments = db.get_assignments()
        
        # Filter assignments for the specified date and calculate throughput
        total_volume = 0
        completed_operations = 0
        
        for assignment in assignments:
            assignment_date = assignment.get('start_time', '').split('T')[0] if assignment.get('start_time') else ''
            
            if assignment_date == date:
                # Check if assignment is completed or in progress
                status = assignment.get('status', '').upper()
                if status in ['COMPLETED', 'IN_PROGRESS', 'ACTIVE']:
                    volume = assignment.get('cargo_volume', 0)
                    if volume > 0:
                        total_volume += volume
                        completed_operations += 1
        
        # Calculate average (simple approach - could be enhanced with historical data)
        # For now, assume average daily throughput is around 50,000 m³
        average_daily_throughput = 50000
        percent_change_from_average = ((total_volume - average_daily_throughput) / average_daily_throughput * 100) if average_daily_throughput > 0 else 0
        
        result = {
            "date": date,
            "terminal_id": active_terminal_id,
            "total_volume": total_volume,
            "completed_operations": completed_operations,
            "average_daily_throughput": average_daily_throughput,
            "percent_change_from_average": percent_change_from_average
        }
        
        logger.info(f"Throughput data: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Error getting throughput data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve throughput data: {str(e)}")

# Export both routers
__all__ = ["router", "backward_compat_router"] 