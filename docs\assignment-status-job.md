## Backend-owned periodic job: automatic assignment status transitions

### Objectives
- Automatically transition assignment `status` based on time:
  - To IN_PROGRESS when `start_time <= now < end_time` and status is one of SCHEDULED | APPROVED | PENDING_APPROVAL
  - To COMPLETED when `end_time <= now` and status is one of IN_PROGRESS | APPROVED | SCHEDULED
- Persist changes in the primary database and write an audit row into `assignment_changes` with reason "Auto transition" and `changed_by = 'scheduler'`.
- Idempotent, safe to run concurrently, observable.

### Scope and assumptions
- Single source of truth is the DB used by the API (`DB_BACKEND=postgres` preferred). Times are stored in UTC (timestamptz in Postgres). UI formatting handles local time.
- The job does not infer business exceptions; it only does time-driven transitions. Manual edits (PATCH) still go through the status state machine.

### Architecture options (pick one)
1) In-app scheduler (recommended for simplicity)
   - Use APScheduler (async) inside the FastAPI app. Schedule a job every 60 seconds.
   - Protect with a distributed lock when running with multiple instances (Postgres advisory lock). Fallback to single-instance mode when SQLite is used (no advisory locks available).
2) External scheduler (production hardening)
   - Use Celery Beat (or systemd/cron/Kubernetes CronJob) to call a dedicated endpoint or run a small worker that executes the same logic. Good for HA and retries.
3) Database-native scheduler
   - Use `pg_cron` in Postgres to run two UPDATE statements and optional INSERTs for the audit log. Requires DB extension and more SQL to build readable reasons.

### Proposed implementation (APScheduler in FastAPI)
1) Dependencies
   - Add APScheduler (e.g., `apscheduler>=3.10`) to `requirements.txt`.

2) Job wiring in FastAPI
   - In `src/api/fastapi_app.py` startup event, create a single APScheduler (BackgroundScheduler) and schedule `transition_assignments_job()` every 60s (configurable via env `ASSIGNMENT_STATUS_CRON_INTERVAL_SEC`).
   - On shutdown, gracefully stop the scheduler.

3) Concurrency control
   - If `DB_BACKEND=postgres`, use `pg_try_advisory_lock(bigint_key)` at the beginning of each run; skip the run if not acquired.
   - If SQLite, document that only a single API instance should run the job (or gate with `ENABLE_STATUS_CRON=true` in one pod/VM only).

4) Logic (idempotent, batched)
   - Always use UTC `now = datetime.utcnow()`.
   - Read candidate assignments in two batches:
     - Batch A (start → IN_PROGRESS): `WHERE status IN ('SCHEDULED','APPROVED','PENDING_APPROVAL') AND start_time <= now AND end_time > now` (limit N)
     - Batch B (end → COMPLETED): `WHERE status IN ('IN_PROGRESS','APPROVED','SCHEDULED') AND end_time <= now` (limit N)
   - For each assignment, validate transition with `status_utils.is_valid_assignment_transition` to enforce the state machine.
   - Perform update through existing DB adapter (`db.update_assignment`) to change status, then call `db.log_assignment_change` with:
     - `assignment_id`, `old_start_time`, `old_end_time`, `new_start_time`, `new_end_time` (unchanged), `reason='Auto transition: {from}->{to}'`, `changed_by='scheduler'`, `jetty_name` and vessel fields from current row.
   - Commit in small batches (e.g., 100 rows per run) to avoid long locks.

5) Observability
   - Log counts per run (attempted, transitioned to IN_PROGRESS, transitioned to COMPLETED, skipped invalid transitions).
   - Emit metrics hooks if available (e.g., Prometheus counters).
   - Include last-run timestamp and last-run duration in a lightweight in-memory variable or an app-level status endpoint.

6) Configuration knobs
   - `ASSIGNMENT_STATUS_CRON_INTERVAL_SEC` (default: 60)
   - `ASSIGNMENT_STATUS_CRON_BATCH_SIZE` (default: 100)
   - `ENABLE_STATUS_CRON` (default: true)
   - `STATUS_CRON_LOCK_KEY` (default: a fixed bigint for advisory lock when on Postgres)

7) Error handling
   - Wrap the job body in try/except; log the exception with context and continue next cycle.
   - Make updates idempotent: re-running should not break correctness.

8) Testing plan
   - Unit tests: feed synthetic assignments with past/ongoing windows; assert transitions and audit rows.
   - Integration test: spin up API against Postgres, insert sample rows, run one job tick, assert DB state.
   - Clock edge cases: exact boundary at `start_time == now` and `end_time == now`.

### Pseudocode (job body)
```python
async def transition_assignments_job():
    if not ENABLE_STATUS_CRON: return
    if using_postgres and not acquire_advisory_lock(STATUS_CRON_LOCK_KEY):
        return
    try:
        now = datetime.utcnow()
        # To IN_PROGRESS
        to_active = db.find_assignments(
            statuses=['SCHEDULED','APPROVED','PENDING_APPROVAL'],
            where_start_le=now, where_end_gt=now, limit=BATCH_SIZE)
        for a in to_active:
            if is_valid_transition(a.status, 'IN_PROGRESS'):
                db.update_assignment(a.id, { ... 'status': 'IN_PROGRESS' ... })
                db.log_assignment_change(
                    assignment_id=a.id,
                    old_start_time=a.start_time, old_end_time=a.end_time,
                    new_start_time=a.start_time, new_end_time=a.end_time,
                    vessel_id=a.vessel_id, vessel_name=a.vessel_name,
                    jetty_name=a.jetty_name,
                    reason=f"Auto transition: {a.status} -> IN_PROGRESS",
                    changed_by='scheduler')

        # To COMPLETED
        to_completed = db.find_assignments(
            statuses=['IN_PROGRESS','APPROVED','SCHEDULED'],
            where_end_le=now, limit=BATCH_SIZE)
        for a in to_completed:
            if is_valid_transition(a.status, 'COMPLETED'):
                db.update_assignment(a.id, { ... 'status': 'COMPLETED' ... })
                db.log_assignment_change(... reason=f"Auto transition: {a.status} -> COMPLETED", changed_by='scheduler')
    finally:
        if using_postgres:
            release_advisory_lock(STATUS_CRON_LOCK_KEY)
```

### Optional: pg_cron alternative (pure SQL)
- Install `pg_cron`. Schedule every minute:
  - `UPDATE assignments SET status='IN_PROGRESS', updated_at=now() WHERE status IN (...) AND start_time <= now() AND end_time > now();`
  - `UPDATE assignments SET status='COMPLETED', updated_at=now() WHERE status IN (...) AND end_time <= now();`
- For audit rows, use an `INSERT ... SELECT` into `assignment_changes` joining affected rows and synthesizing the reason text and `changed_by='scheduler'`.
- Pros: no app process needed. Cons: harder to reuse Python status validation, more SQL to keep reason text consistent.

### Rollout steps
1) Add dependency and feature flags; deploy with job disabled.
2) Ship code; enable in staging (`ENABLE_STATUS_CRON=true`) with short interval (e.g., 20s) and verify logs + UI + audit.
3) Enable in production with 60–120s interval; monitor for a few days.

### Notes / pitfalls
- Ensure all comparisons are UTC.
- Keep batch sizes small to avoid long-running transactions.
- If optimization replaces the entire assignment set, the next job cycle should still behave correctly (idempotent updates only change rows meeting time predicates).

