#!/usr/bin/env python3
"""
Run script for Jetty Planning Optimizer

This script provides a command-line interface for the Jetty Planning Optimizer.
"""

import argparse
import logging
import os
import sys
import signal
from pathlib import Path
from dotenv import load_dotenv

from src.main import main as optimizer_main

# Global variable to track if we're shutting down
shutting_down = False


def signal_handler(sig, frame):
    """Handle termination signals gracefully"""
    global shutting_down
    if shutting_down:
        print("\nForced exit...")
        sys.exit(1)
    
    print("\nReceived termination signal. Shutting down gracefully...")
    shutting_down = True
    sys.exit(0)


def setup_production_mode():
    """Configure the environment for production mode"""
    # Set production flag
    os.environ['PRODUCTION'] = 'true'
    
    # Set default host for production
    if 'API_HOST' not in os.environ:
        os.environ['API_HOST'] = '0.0.0.0'  # Listen on all interfaces
    
    # Create required directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)


def load_environment():
    """Load environment variables from .env file"""
    # Always load from .env when present (do not override already-set env)
    env_path = Path('.') / '.env'
    if env_path.exists():
        load_dotenv(dotenv_path=env_path, override=False)
        print(f"Loaded environment variables from {env_path}")
        
        # Show database configuration
        db_host = os.getenv('DB_HOST', 'localhost')
        db_port = os.getenv('DB_PORT', '5432')
        db_name = os.getenv('DB_NAME', 'planner')
        db_user = os.getenv('DB_USER', 'postgres')
        
        print(f"PostgreSQL connection: {db_user}@{db_host}:{db_port}/{db_name}")
        
        # Guidance: host vs container port mapping
        if db_host in ['localhost', '127.0.0.1']:
            if db_port == '7432':
                print("Note: Using host-mapped port 7432 -> container 5432 (Docker Compose)")
            elif db_port == '5432':
                print("Note: Port 5432 on localhost expects a locally running PostgreSQL (not the Docker-mapped port)")
            print("If using Docker Compose Postgres: use DB_PORT=7432 on the host, or connect to service 'postgres' on 5432 from containers.")
    else:
        print(f"Warning: .env file not found. Using default or system environment variables.")
        print(f"To configure database and API keys, create a .env file with your settings.")
        print(f"For PostgreSQL with Docker Compose, you'll need to configure DB_HOST, DB_PORT, etc.")


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description='Jetty Planning Optimizer for Petrochemical Terminals',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    parser.add_argument('--test', action='store_true',
                        help='Use test data instead of real API data')
    
    parser.add_argument('--api', action='store_true',
                        help='Start the API server')
    
    parser.add_argument('--host', type=str, default=os.getenv('API_HOST', '127.0.0.1'),
                        help='Host to run the API server on')
    
    parser.add_argument('--port', type=int, default=int(os.getenv('API_PORT', '7000')),
                        help='Port to run the API server on')
    
    parser.add_argument('--optimize', action='store_true',
                        help='Run optimization with current data')
    
    parser.add_argument('--interactive', action='store_true',
                        help='Start interactive LLM interface')
    
    parser.add_argument('-v', '--verbose', action='store_true',
                        help='Enable verbose logging')
    
    parser.add_argument('--production', action='store_true',
                        help='Run in production mode')
    
    parser.add_argument('--start-postgres', action='store_true',
                        help='Start PostgreSQL using docker-compose first')
    
    return parser.parse_args()


def start_postgres_if_needed():
    """Start PostgreSQL using Docker Compose if requested"""
    import subprocess
    
    print("Starting PostgreSQL with Docker Compose...")
    try:
        # Check if docker-compose.postgres.yml exists
        if not os.path.exists('docker-compose.postgres.yml'):
            print("Error: docker-compose.postgres.yml not found")
            print("This file is needed to start PostgreSQL for local development")
            return False
            
        # Start PostgreSQL
        result = subprocess.run([
            'docker-compose', '-f', 'docker-compose.postgres.yml', 'up', '-d'
        ], capture_output=True, text=True, shell=True)
        
        if result.returncode == 0:
            print("PostgreSQL started successfully")
            print("Waiting a few seconds for database to be ready...")
            import time
            time.sleep(5)
            return True
        else:
            print(f"Failed to start PostgreSQL: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("Error: docker-compose command not found")
        print("Please install Docker and Docker Compose")
        return False
    except Exception as e:
        print(f"Error starting PostgreSQL: {e}")
        return False


def main():
    """Main entry point of the script"""
    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Parse command line arguments
    args = parse_arguments()
    
    # Start PostgreSQL if requested
    if args.start_postgres:
        if not start_postgres_if_needed():
            print("Failed to start PostgreSQL. Exiting...")
            sys.exit(1)
    
    # Configure production mode if requested
    if args.production or os.getenv("PRODUCTION", "false").lower() == "true":
        setup_production_mode()
    
    # Set debug mode if verbose is enabled
    if args.verbose:
        os.environ['DEBUG'] = 'true'
    
    # Load environment variables
    load_environment()
    
    # Override some environment variables for testing if needed
    if args.test:
        os.environ['TEST_MODE'] = 'true'
        os.environ['ANTHROPIC_API_KEY'] = 'TEST'
    
    # Set host and port environment variables
    os.environ['API_HOST'] = args.host
    os.environ['API_PORT'] = str(args.port)
    
    # Create an array of arguments to pass to the main function
    sys_args = []
    
    if args.test or os.getenv('TEST_MODE', 'false').lower() == 'true':
        sys_args.append('--test')
    
    if args.api:
        sys_args.append('--api')
        sys_args.append('--host')
        sys_args.append(args.host)
        sys_args.append('--port')
        sys_args.append(str(args.port))
    
    if args.optimize:
        sys_args.append('--optimize')
    
    if args.interactive:
        sys_args.append('--interactive')
    
    if args.verbose:
        sys_args.append('--debug')
    
    # In production mode, default to API server if no action specified
    is_production = os.getenv("PRODUCTION", "false").lower() == "true"
    if is_production and not (args.api or args.optimize or args.interactive):
        sys_args.append('--api')
        sys_args.append('--host')
        sys_args.append(args.host)
        sys_args.append('--port')
        sys_args.append(str(args.port))
    
    # If no specific action is selected and not in production, show a menu
    if not is_production and not (args.api or args.optimize or args.interactive):
        show_menu()
        return
    
    # Run the main function
    try:
        print(f"Starting Jetty Planning Optimizer with arguments: {' '.join(sys_args)}")
        sys.argv = ['src/main.py'] + sys_args
        optimizer_main()
    except KeyboardInterrupt:
        print("\nInterrupted by user. Exiting...")
        sys.exit(0)
    except Exception as e:
        error_msg = str(e)
        
        # Check for database connection errors
        if "connection to server" in error_msg.lower() or "connection refused" in error_msg.lower():
            print("\n" + "="*60)
            print("DATABASE CONNECTION ERROR")
            print("="*60)
            print("Failed to connect to PostgreSQL database.")
            print("\nPossible solutions:")
            print("1. Start PostgreSQL with Docker Compose:")
            print("   python run.py --start-postgres")
            print("\n2. Or manually start PostgreSQL:")
            print("   docker-compose -f docker-compose.postgres.yml up -d")
            print("\n3. Check your .env file database configuration:")
            print("   DB_HOST, DB_PORT, DB_USER, DB_PASSWORD")
            print("\n4. If using existing PostgreSQL, ensure it's reachable on the configured port (commonly 5432, or 7432 if host-mapped via Docker Compose)")
            print("="*60)
        elif "no password supplied" in error_msg.lower():
            print("\n" + "="*60)
            print("DATABASE AUTHENTICATION ERROR")
            print("="*60)
            print("PostgreSQL connection failed: no password supplied")
            print("\nPlease check your .env file and ensure DB_PASSWORD is set:")
            print("   DB_PASSWORD=your_postgres_password")
            print("="*60)
        else:
            logging.error(f"Error running optimizer: {e}", exc_info=True)
        
        sys.exit(1)


def show_menu():
    """Show an interactive menu for the user to choose options"""
    print("\n====== Jetty Planning Optimizer ======\n")
    print("Select an option:")
    print("1. Start PostgreSQL and run API server (with test data)")
    print("2. Run API server (with test data)")
    print("3. Run API server (with real API data)")
    print("4. Run optimization (with test data)")
    print("5. Run optimization (with real API data)")
    print("6. Start interactive Claude interface (with test data)")
    print("7. Start interactive Claude interface (with real API data)")
    print("8. Run in production mode")
    print("9. Start PostgreSQL only")
    print("0. Exit")
    
    try:
        choice = input("\nEnter your choice (0-9): ")
        
        if choice == '0':
            print("Exiting...")
            sys.exit(0)
        elif choice == '1':
            # Start PostgreSQL and run API server with test data
            if start_postgres_if_needed():
                os.environ['TEST_MODE'] = 'true'
                sys.argv = ['src/main.py', '--test', '--api']
                optimizer_main()
            else:
                print("Failed to start PostgreSQL")
        elif choice == '2':
            os.environ['TEST_MODE'] = 'true'
            sys.argv = ['src/main.py', '--test', '--api']
            optimizer_main()
        elif choice == '3':
            sys.argv = ['src/main.py', '--api']
            optimizer_main()
        elif choice == '4':
            os.environ['TEST_MODE'] = 'true'
            sys.argv = ['src/main.py', '--test', '--optimize']
            optimizer_main()
        elif choice == '5':
            sys.argv = ['src/main.py', '--optimize']
            optimizer_main()
        elif choice == '6':
            os.environ['TEST_MODE'] = 'true'
            sys.argv = ['src/main.py', '--test', '--interactive']
            optimizer_main()
        elif choice == '7':
            sys.argv = ['src/main.py', '--interactive']
            optimizer_main()
        elif choice == '8':
            os.environ['PRODUCTION'] = 'true'
            setup_production_mode()
            sys.argv = ['src/main.py', '--api']
            optimizer_main()
        elif choice == '9':
            # Start PostgreSQL only
            if start_postgres_if_needed():
                print("PostgreSQL is now running. You can use other options to run the application.")
                show_menu()
            else:
                print("Failed to start PostgreSQL")
        else:
            print("Invalid choice. Please try again.")
            show_menu()
    except KeyboardInterrupt:
        print("\nExiting...")
        sys.exit(0)


if __name__ == "__main__":
    main()
