from typing import List, Dict, Any, Optional

from ..database import Database
from ..db.models import VesselAISData, VesselVisit


class AISIntegrationService:
    def __init__(self, db: Optional[Database] = None):
        self.db = db or Database()

    def process_ais_updates(self, ais_data_batch: List[Dict[str, Any]]) -> int:
        """Persist AIS updates and reflect ETA into the latest visit when linked.

        If an AIS row has `vessel_id` and `eta_parsed`, we update the most recent
        VesselVisit for that vessel with `calculated_eta` and set `eta_source='ais_calculated'`.
        """
        count = 0
        with self.db.get_session() as db:
            for row in ais_data_batch:
                rec = VesselAISData(
                    vessel_id=row.get('vessel_id'),
                    mmsi=row['mmsi'],
                    latitude=row.get('latitude'),
                    longitude=row.get('longitude'),
                    course=row.get('course'),
                    speed=row.get('speed'),
                    heading=row.get('heading'),
                    navigation_status=row.get('navigation_status'),
                    rate_of_turn=row.get('rate_of_turn'),
                    position_accuracy=row.get('position_accuracy'),
                    draught=row.get('draught'),
                    destination=row.get('destination'),
                    eta_raw=row.get('eta_raw'),
                    eta_parsed=row.get('eta_parsed'),
                    timestamp=row['timestamp'],
                    age_seconds=row.get('age_seconds'),
                    signal_quality=row.get('signal_quality'),
                    source=row.get('source')
                )
                db.add(rec)
                count += 1

                # Reflect ETA into latest visit if we can
                if row.get('vessel_id') and row.get('eta_parsed'):
                    latest_visit = (
                        db.query(VesselVisit)
                        .filter_by(vessel_id=row['vessel_id'])
                        .order_by(VesselVisit.created_at.desc())
                        .first()
                    )
                    if latest_visit:
                        prev_eta = latest_visit.calculated_eta
                        latest_visit.calculated_eta = row['eta_parsed']
                        latest_visit.eta_source = 'ais_calculated'
                        # Log ETA change
                        try:
                            self.db.log_eta_update(
                                source='ais_calculated',
                                eta=row['eta_parsed'],
                                confidence=None,
                                vessel_registry_id=row['vessel_id'],
                                visit_id=latest_visit.id,
                                previous_eta=prev_eta,
                                context='AIS update'
                            )
                        except Exception:
                            pass
            db.commit()
        return count

    def link_ais_to_vessel(self, mmsi: str, vessel_id: int) -> int:
        with self.db.get_session() as db:
            q = db.query(VesselAISData).filter_by(mmsi=mmsi, vessel_id=None)
            updated = q.update({VesselAISData.vessel_id: vessel_id})
            db.commit()
            return updated

    def update_vessel_position(self, vessel_id: int, position_data: Dict[str, Any]) -> bool:
        # Simplified: just insert a new record with the latest timestamp
        position_data = dict(position_data)
        position_data['vessel_id'] = vessel_id
        return self.process_ais_updates([position_data]) == 1

    def detect_arrival_departure(self, vessel_id: int, terminal_geofence) -> Optional[str]:
        # Placeholder: return status string
        return None


