#!/usr/bin/env python3
"""
Test script to verify the complete nomination workflow.

This script tests the end-to-end flow:
1. Create nomination via API
2. Verify it appears in unscheduled vessels
3. Run optimization
4. Verify vessel gets scheduled
5. Unschedule vessel
6. Verify it becomes available again

Usage: python test_nomination_workflow.py
"""

import sys
import asyncio
import aiohttp
import json
from datetime import datetime, timedelta, timezone

BASE_URL = "http://localhost:8000"

async def test_nomination_workflow():
    """Test the complete nomination workflow."""
    
    print("🚢 Testing Nomination Workflow")
    print("=" * 50)
    
    async with aiohttp.ClientSession() as session:
        
        # Step 1: Create a test nomination
        print("\n1️⃣ Creating test nomination...")
        
        nomination_data = {
            "name": "TEST VESSEL WORKFLOW",
            "vessel_type": "tanker",
            "length": 180.0,
            "beam": 25.0,
            "draft": 8.5,
            "deadweight": 35000.0,
            "priority": 1,
            "capacity": 35000.0,
            "customer": "Test Customer",
            "cargoes": [
                {
                    "product": "NAPHTA",
                    "volume": 15000.0,
                    "is_loading": False,
                    "connection_size": "12\"",
                    "requires_vapor_return": False,
                    "requires_nitrogen_purge": False
                }
            ],
            "metadata": {
                "test_vessel": True,
                "created_by": "test_script"
            }
        }
        
        try:
            async with session.post(f"{BASE_URL}/api/nominations", 
                                  json=nomination_data,
                                  headers={"Content-Type": "application/json"}) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    vessel_id = result.get("id")
                    print(f"   ✅ Created vessel: {result.get('name')} (ID: {vessel_id})")
                else:
                    error_text = await resp.text()
                    print(f"   ❌ Failed to create nomination: {resp.status} - {error_text}")
                    return False
        except Exception as e:
            print(f"   ❌ Error creating nomination: {e}")
            return False
        
        # Step 2: Verify vessel appears in available vessels
        print("\n2️⃣ Checking if vessel appears in available vessels...")
        
        try:
            async with session.get(f"{BASE_URL}/api/vessels") as resp:
                if resp.status == 200:
                    vessels = await resp.json()
                    test_vessel = None
                    for vessel in vessels:
                        if vessel.get("id") == vessel_id:
                            test_vessel = vessel
                            break
                    
                    if test_vessel:
                        print(f"   ✅ Found vessel in available list: {test_vessel.get('name')}")
                        print(f"      Status: {test_vessel.get('status')}")
                    else:
                        print(f"   ⚠️  Vessel {vessel_id} not found in available vessels list")
                        print(f"      Available vessels: {[v.get('id') for v in vessels]}")
                else:
                    print(f"   ❌ Failed to get vessels: {resp.status}")
        except Exception as e:
            print(f"   ❌ Error getting vessels: {e}")
        
        # Step 3: Run optimization
        print("\n3️⃣ Running optimization...")
        
        optimization_params = {
            "horizon_days": 7,
            "time_granularity_hours": 1,
            "force_assign_all": True,
            "preserve_locked": True,
            "approach_time_hours": 2,
            "free_wait_buffer_hours": 1
        }
        
        try:
            async with session.post(f"{BASE_URL}/api/optimize", 
                                  json=optimization_params,
                                  headers={"Content-Type": "application/json"}) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    assignments_created = len(result.get("assignments", []))
                    print(f"   ✅ Optimization completed: {assignments_created} assignments created")
                    
                    # Look for our test vessel in assignments
                    test_assignment = None
                    for assignment in result.get("assignments", []):
                        if assignment.get("vessel_id") == vessel_id:
                            test_assignment = assignment
                            break
                    
                    if test_assignment:
                        print(f"   ✅ Test vessel was scheduled!")
                        print(f"      Jetty: {test_assignment.get('jetty_name')}")
                        print(f"      Start: {test_assignment.get('start_time')}")
                        print(f"      End: {test_assignment.get('end_time')}")
                        assignment_id = test_assignment.get("id")
                    else:
                        print(f"   ⚠️  Test vessel was not scheduled in this optimization")
                        assignment_id = None
                else:
                    error_text = await resp.text()
                    print(f"   ❌ Optimization failed: {resp.status} - {error_text}")
                    assignment_id = None
        except Exception as e:
            print(f"   ❌ Error running optimization: {e}")
            assignment_id = None
        
        # Step 4: If scheduled, test unscheduling
        if assignment_id:
            print(f"\n4️⃣ Testing unscheduling assignment {assignment_id}...")
            
            unschedule_data = {
                "reason": "Testing unschedule workflow"
            }
            
            try:
                async with session.post(f"{BASE_URL}/api/schedule/assignments/{assignment_id}/unschedule",
                                      json=unschedule_data,
                                      headers={"Content-Type": "application/json"}) as resp:
                    if resp.status == 200:
                        result = await resp.json()
                        print(f"   ✅ Successfully unscheduled assignment")
                    else:
                        error_text = await resp.text()
                        print(f"   ❌ Failed to unschedule: {resp.status} - {error_text}")
            except Exception as e:
                print(f"   ❌ Error unscheduling: {e}")
            
            # Step 5: Verify vessel is available again
            print("\n5️⃣ Checking if vessel is available again after unscheduling...")
            
            try:
                async with session.get(f"{BASE_URL}/api/vessels") as resp:
                    if resp.status == 200:
                        vessels = await resp.json()
                        test_vessel = None
                        for vessel in vessels:
                            if vessel.get("id") == vessel_id:
                                test_vessel = vessel
                                break
                        
                        if test_vessel:
                            print(f"   ✅ Vessel is available again: {test_vessel.get('name')}")
                            print(f"      Status: {test_vessel.get('status')}")
                        else:
                            print(f"   ⚠️  Vessel {vessel_id} not found in available vessels after unscheduling")
                    else:
                        print(f"   ❌ Failed to get vessels: {resp.status}")
            except Exception as e:
                print(f"   ❌ Error getting vessels: {e}")
        
        # Step 6: Clean up - delete test vessel
        print(f"\n6️⃣ Cleaning up test vessel {vessel_id}...")
        
        try:
            async with session.delete(f"{BASE_URL}/api/vessels/{vessel_id}") as resp:
                if resp.status == 200:
                    print(f"   ✅ Test vessel deleted successfully")
                else:
                    print(f"   ⚠️  Could not delete test vessel: {resp.status}")
        except Exception as e:
            print(f"   ❌ Error deleting test vessel: {e}")
    
    print("\n🎉 Nomination workflow test completed!")
    print("=" * 50)
    return True

async def main():
    """Main test function."""
    print("Starting nomination workflow test...")
    print("Make sure the application is running on localhost:8000")
    
    try:
        success = await test_nomination_workflow()
        if success:
            print("\n✅ All tests completed!")
            return 0
        else:
            print("\n❌ Some tests failed!")
            return 1
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
