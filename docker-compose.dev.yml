services:
  jetty-planning-app-dev:
    build: .
    container_name: jetty-planning-app-dev
    env_file:
      - .env
    environment:
      - PRODUCTION=false
      - DEBUG=true
      - API_HOST=0.0.0.0
      - API_PORT=7000
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-TEST}
      - DB_BACKEND=${DB_BACKEND:-sqlite}
    ports:
      - "7070:7000"
    volumes:
      - jetty-data-dev:/app/data
    restart: unless-stopped
    command: ["python", "run.py", "--api", "--host", "0.0.0.0", "--port", "7000", "--test"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7000/api/terminal"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

volumes:
  jetty-data-dev:


