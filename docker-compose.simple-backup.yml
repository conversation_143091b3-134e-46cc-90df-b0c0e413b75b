version: '3.8'

services:
  # Simple PostgreSQL backup service
  postgres-backup:
    image: postgres:16-alpine
    container_name: jetty-postgres-backup
    restart: unless-stopped
    environment:
      - PGPASSWORD=${DB_PASSWORD:-XM/8KBtBmMJcP2tmjvjNJx0KDtFPLMqU}
    volumes:
      - ./backups:/backups
      - ./scripts/backup-entrypoint.sh:/backup-entrypoint.sh
    working_dir: /backups
    command: ["/bin/sh", "/backup-entrypoint.sh"]
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Simple backup web interface
  backup-web:
    image: nginx:alpine
    container_name: jetty-backup-web
    restart: unless-stopped
    ports:
      - "8081:80"
    volumes:
      - ./backups:/usr/share/nginx/html/backups:ro
      - ./scripts/backup-web.conf:/etc/nginx/nginx.conf:ro