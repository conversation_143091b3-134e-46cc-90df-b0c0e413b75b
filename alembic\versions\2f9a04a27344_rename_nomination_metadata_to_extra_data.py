"""Rename nomination metadata to extra_data

Revision ID: 2f9a04a27344
Revises: 33d8ae7a1d9a
Create Date: 2025-09-02 15:59:50.788055

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2f9a04a27344'
down_revision: Union[str, Sequence[str], None] = '33d8ae7a1d9a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Rename metadata column to extra_data in nominations table
    # For SQLite, we need to use batch operations
    bind = op.get_bind()
    if bind.dialect.name == 'sqlite':
        with op.batch_alter_table('nominations', schema=None) as batch_op:
            batch_op.add_column(sa.Column('extra_data', sa.JSON(), nullable=True))
        
        # Copy data from metadata to extra_data
        op.execute("""
            UPDATE nominations 
            SET extra_data = metadata
            WHERE metadata IS NOT NULL
        """)
        
        # Drop the old metadata column
        with op.batch_alter_table('nominations', schema=None) as batch_op:
            batch_op.drop_column('metadata')
    else:
        # PostgreSQL version
        op.add_column('nominations', sa.Column('extra_data', sa.JSON(), nullable=True))
        op.execute("UPDATE nominations SET extra_data = metadata WHERE metadata IS NOT NULL")
        op.drop_column('nominations', 'metadata')


def downgrade() -> None:
    """Downgrade schema."""
    # Rename extra_data column back to metadata
    bind = op.get_bind()
    if bind.dialect.name == 'sqlite':
        with op.batch_alter_table('nominations', schema=None) as batch_op:
            batch_op.add_column(sa.Column('metadata', sa.JSON(), nullable=True))
        
        # Copy data from extra_data to metadata
        op.execute("""
            UPDATE nominations 
            SET metadata = extra_data
            WHERE extra_data IS NOT NULL
        """)
        
        # Drop the extra_data column
        with op.batch_alter_table('nominations', schema=None) as batch_op:
            batch_op.drop_column('extra_data')
    else:
        # PostgreSQL version
        op.add_column('nominations', sa.Column('metadata', sa.JSON(), nullable=True))
        op.execute("UPDATE nominations SET metadata = extra_data WHERE extra_data IS NOT NULL")
        op.drop_column('nominations', 'extra_data')
