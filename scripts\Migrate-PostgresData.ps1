# Migrate data from old PostgreSQL instance (4432) to new instance (7432)
# Usage: .\scripts\Migrate-PostgresData.ps1

param(
    [string]$SourcePort = "4432",
    [string]$TargetPort = "7432",
    [string]$DbName = "planner",
    [string]$DbUser = "postgres"
)

Write-Host "🔄 Migrating PostgreSQL data from port $SourcePort to port $TargetPort" -ForegroundColor Green

# Read password from environment or prompt
$DbPassword = $env:DB_PASSWORD
if (-not $DbPassword) {
    $SecurePassword = Read-Host "Enter PostgreSQL password" -AsSecureString
    $DbPassword = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($SecurePassword))
}

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupFile = ".\backups\migration_backup_${timestamp}.sql"

try {
    # Ensure backups directory exists
    New-Item -ItemType Directory -Force -Path ".\backups" | Out-Null

    Write-Host "📦 Creating backup from source database (port $SourcePort)..." -ForegroundColor Yellow
    
    # Create dump from source database
    $env:PGPASSWORD = $DbPassword
    & docker exec jetty-postgres-dev pg_dump -h localhost -U $DbUser -d $DbName --clean --if-exists > $backupFile
    
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to create backup from source database"
    }

    Write-Host "✅ Backup created: $backupFile" -ForegroundColor Green
    
    Write-Host "📥 Restoring to target database (port $TargetPort)..." -ForegroundColor Yellow
    
    # Restore to target database
    Get-Content $backupFile | docker exec -i jetty-postgres psql -h localhost -U $DbUser -d $DbName
    
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to restore to target database"
    }

    Write-Host "✅ Migration completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Update your .env file: DB_PORT=7432" -ForegroundColor White
    Write-Host "2. Test your application with the new database" -ForegroundColor White
    Write-Host "3. Once confirmed working, you can stop the old container:" -ForegroundColor White
    Write-Host "   docker-compose -f docker-compose.postgres.yml down" -ForegroundColor Gray

} catch {
    Write-Host "❌ Migration failed: $_" -ForegroundColor Red
    Write-Host "Backup file preserved at: $backupFile" -ForegroundColor Yellow
} finally {
    # Clear password from environment
    Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
}
