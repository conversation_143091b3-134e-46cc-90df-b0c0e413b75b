"""
AIS Stream API Client

This module provides integration with the AIS Stream API for real-time vessel tracking.
AIS Stream uses WebSocket connections for live vessel data streaming.
"""

import asyncio
import json
import logging
import websockets
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import threading
import math
import time

logger = logging.getLogger(__name__)

try:
    # Lazy import guard for utils
    from ..utils.ais_types import get_ship_type_text
except Exception:  # pragma: no cover
    def get_ship_type_text(v):
        return None

class AISStreamClient:
    """Client for interacting with AIS Stream API via WebSocket"""
    
    def __init__(self, api_key: str, websocket_url: str = "wss://stream.aisstream.io/v0/stream"):
        """
        Initialize AIS Stream API client.
        
        Args:
            api_key: API key for AIS Stream API.
            websocket_url: WebSocket URL for the API (default: wss://stream.aisstream.io/v0/stream).
        """
        self.api_key = api_key
        self.websocket_url = websocket_url
        self.use_mock = api_key == "TEST" or not api_key
        
        # Real-time data storage
        self.vessel_data = {}  # MMSI -> vessel data
        self.last_update = {}  # MMSI -> timestamp
        
        # WebSocket connection
        self.websocket = None
        self.connection_active = False
        self.reconnect_interval = 60  # seconds (reduced churn)
        
        # Background thread for WebSocket connection
        self.background_thread = None
        self.stop_event = threading.Event()
        
        # Connection health tracking
        self.consecutive_failures = 0
        self.last_successful_connection = None
        self.total_messages_received = 0
        
        # Subscription region filtering (server-side via bounding boxes)
        self.bounding_boxes: Optional[List[List[float]]] = None  # [[south, west], [north, east]] or multiple
        
        # Optional pre-collection vessel type filter (drop non-liquid/gas tankers)
        self.only_liquid_filter_enabled: bool = False

        # Optional server-side filters for targeted subscriptions
        self._current_mmsi_filters: Optional[List[str]] = None
        # Include Class B position variants to ensure positions for inland/other vessels
        self._filter_message_types: List[str] = [
            "PositionReport",
            "ExtendedClassBPositionReport",
            "StandardClassBPositionReport",
            "ShipStaticData",
        ]
        
        if self.use_mock:
            logger.warning("Using mock data for AIS Stream API")
            self._populate_mock_data()
        else:
            logger.info("Initializing AIS Stream WebSocket client")
            self._validate_api_key()
    
    def _populate_mock_data(self):
        """Populate with mock vessel data for testing"""
        mock_vessels = [
            {
                "mmsi": "218500001",
                "imo": "9800001",
                "name": "DEMO TANKER ALPHA",
                "vessel_type": "Chemical/Oil Products Tanker",
                "length": 183,
                "width": 27,
                "draft": 10.1,
                "deadweight": 37000,
                "position": {
                    "latitude": 51.2993,
                    "longitude": 3.7174,
                    "course": 15,
                    "speed": 8.2,
                    "timestamp": datetime.now().isoformat()
                },
                "destination": "DEMO PORT",
                "eta": (datetime.now() + timedelta(hours=2)).isoformat(),
                "status": "Under way using engine"
            },
            {
                "mmsi": "235100002",
                "imo": "9800002",
                "name": "DEMO VESSEL BETA",
                "vessel_type": "Chemical/Oil Products Tanker",
                "length": 140,
                "width": 18,
                "draft": 7.5,
                "deadweight": 12500,
                "position": {
                    "latitude": 51.2850,
                    "longitude": 3.7100,
                    "course": 320,
                    "speed": 6.5,
                    "timestamp": datetime.now().isoformat()
                },
                "destination": "DEMO PORT",
                "eta": (datetime.now() + timedelta(hours=4)).isoformat(),
                "status": "Under way using engine"
            },
            {
                "mmsi": "244600003",
                "imo": "9800003",
                "name": "DEMO BARGE GAMMA",
                "vessel_type": "Inland Tanker",
                "length": 86,
                "width": 10.5,
                "draft": 4.2,
                "deadweight": 2200,
                "position": {
                    "latitude": 51.2700,
                    "longitude": 3.6900,
                    "course": 275,
                    "speed": 4.5,
                    "timestamp": datetime.now().isoformat()
                },
                "destination": "DEMO PORT",
                "eta": (datetime.now() + timedelta(hours=6)).isoformat(),
                "status": "Under way using engine"
            }
        ]
        
        for vessel in mock_vessels:
            self.vessel_data[vessel["mmsi"]] = vessel
            self.last_update[vessel["mmsi"]] = datetime.now()
    
    def _validate_api_key(self):
        """Validate API key format and provide warnings"""
        if not self.api_key:
            logger.error("AIS Stream API key is empty")
            return False
        
        if self.api_key == "TEST":
            logger.warning("AIS Stream API key is set to TEST (mock mode will be used)")
            return False
        
        # Basic format validation
        if len(self.api_key) < 10:
            logger.warning(f"AIS Stream API key seems unusually short ({len(self.api_key)} characters)")
        
        if ' ' in self.api_key:
            logger.warning("AIS Stream API key contains spaces - this may cause issues")
        
        if self.api_key.startswith(' ') or self.api_key.endswith(' '):
            logger.warning("AIS Stream API key has leading/trailing whitespace - trimming")
            self.api_key = self.api_key.strip()
        
        logger.info(f"AIS Stream API key validated: {len(self.api_key)} characters")
        return True
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get detailed health status of the AIS Stream connection"""
        return {
            "connected": self.connection_active,
            "use_mock": self.use_mock,
            "vessels_tracked": len(self.vessel_data),
            "total_messages_received": self.total_messages_received,
            "consecutive_failures": self.consecutive_failures,
            "last_successful_connection": self.last_successful_connection.isoformat() if self.last_successful_connection else None,
            "last_data_time": max(self.last_update.values()).isoformat() if self.last_update else None,
            "api_key_configured": bool(self.api_key and self.api_key != "TEST"),
            "background_thread_alive": self.background_thread.is_alive() if self.background_thread else False
        }
    
    async def _create_subscription_message(self, bounding_boxes: List[List[float]] = None, 
                                         mmsi_filters: List[str] = None) -> Dict[str, Any]:
        """
        Create subscription message for WebSocket connection.
        
        Args:
            bounding_boxes: List of bounding boxes [[south, west], [north, east]]
            mmsi_filters: List of MMSI numbers to filter for
            
        Returns:
            Subscription message dictionary
        """
        # Prefer an explicitly set subscription region if available
        if bounding_boxes is None:
            if self.bounding_boxes:
                bounding_boxes = self.bounding_boxes
            else:
                # Single broad box (fallback): Southern North Sea/Channel
                bounding_boxes = [
                    [49.50, 2.00],
                    [54.50, 7.50]
                ]
        
        # Allow passing either a single box [[S,W],[N,E]] or multiple boxes [[[S,W],[N,E]], ...]
        if bounding_boxes and len(bounding_boxes) == 2 and all(isinstance(v, (int, float)) for v in bounding_boxes[0]):
            boxes_payload = [bounding_boxes]
        else:
            boxes_payload = bounding_boxes

        # If no explicit MMSI filters provided, fallback to current tracking filters if available
        if not mmsi_filters and hasattr(self, "_current_mmsi_filters"):
            try:
                if self._current_mmsi_filters:
                    mmsi_filters = list(self._current_mmsi_filters)[:50]  # API supports max 50
            except Exception:
                pass

        subscription = {
            "APIKey": self.api_key,
            "BoundingBoxes": boxes_payload,
            "FilterMessageTypes": self._filter_message_types or [
                "PositionReport",
                "ExtendedClassBPositionReport",
                "StandardClassBPositionReport",
                "ShipStaticData",
            ]
        }
        
        # Add MMSI filter if provided
        if mmsi_filters:
            subscription["FiltersShipMMSI"] = mmsi_filters
            
        return subscription

    # --- Filtering helpers -------------------------------------------------
    def set_only_liquid_filter(self, enabled: bool = True) -> None:
        """Enable/disable pre-collection filter to keep only liquid/gas tankers.

        When enabled, the client will:
        - Skip storing PositionReport messages until ShipStaticData confirms a liquid type
        - Drop any non-liquid vessels upon receiving ShipStaticData
        """
        try:
            self.only_liquid_filter_enabled = bool(enabled)
            if self.only_liquid_filter_enabled:
                logger.info("AIS client liquid-only filter enabled (includes oil/chemical/gas, inland)")
            else:
                logger.info("AIS client liquid-only filter disabled")
        except Exception:
            self.only_liquid_filter_enabled = enabled

    def set_mmsi_filters(self, mmsi_list: Optional[List[str]]) -> None:
        """Set server-side MMSI filters (max 50) for upcoming subscriptions."""
        try:
            if not mmsi_list:
                self._current_mmsi_filters = None
            else:
                self._current_mmsi_filters = [str(m) for m in mmsi_list][:50]
            logger.info(f"AIS client MMSI filters set: {len(self._current_mmsi_filters or [])} entries")
        except Exception:
            self._current_mmsi_filters = mmsi_list

    def set_message_types(self, message_types: Optional[List[str]]) -> None:
        """Set FilterMessageTypes for upcoming subscriptions (e.g., [ShipStaticData])."""
        try:
            if not message_types:
                self._filter_message_types = [
                    "PositionReport",
                    "ExtendedClassBPositionReport",
                    "StandardClassBPositionReport",
                    "ShipStaticData",
                ]
            else:
                self._filter_message_types = list(message_types)
            logger.info(f"AIS client message types set: {self._filter_message_types}")
        except Exception:
            self._filter_message_types = message_types or [
                "PositionReport",
                "ExtendedClassBPositionReport",
                "StandardClassBPositionReport",
                "ShipStaticData",
            ]

    @staticmethod
    def _is_liquid_ship_type(type_text: Optional[str] = None, type_code: Optional[int] = None) -> bool:
        """Return True if AIS ship type indicates liquid/gas tanker.

        Accept if:
        - Numeric AIS type code is in 80–89 (tanker classes)
        - Or textual type contains any of: tanker, oil, chemical, inland, liquid, gas, lng, lpg
        """
        try:
            if type_code is not None:
                code_val = int(type_code)
                if 80 <= code_val <= 89:
                    return True
        except Exception:
            pass
        if not type_text:
            return False
        t = str(type_text).lower()
        return (
            "tanker" in t or "oil" in t or "chemical" in t or "inland" in t or
            "liquid" in t or "gas" in t or "lng" in t or "lpg" in t
        )

    def set_subscription_region(self, latitude: float, longitude: float, radius_km: float = 50.0) -> None:
        """
        Limit AIS subscription to a bounding box around a center point.
        Uses an approximate conversion of km to degrees.

        Args:
            latitude: Center latitude in degrees
            longitude: Center longitude in degrees
            radius_km: Radius from center in kilometers
        """
        try:
            delta_lat = radius_km / 111.0
            # Prevent division by zero at poles
            cos_lat = max(0.0001, math.cos(math.radians(latitude)))
            delta_lon = radius_km / (111.320 * cos_lat)
            south = latitude - delta_lat
            north = latitude + delta_lat
            west = longitude - delta_lon
            east = longitude + delta_lon
            self.bounding_boxes = [[south, west], [north, east]]
            logger.info(f"AIS subscription region set to bbox: S{south:.4f}, W{west:.4f} to N{north:.4f}, E{east:.4f} (~{radius_km} km radius)")
        except Exception as e:
            logger.error(f"Failed to set AIS subscription region: {e}")
            self.bounding_boxes = None

    def _is_within_subscribed_region(self, lat: Optional[float], lon: Optional[float]) -> bool:
        # If we are using server-side MMSI filters, allow positions globally to avoid
        # dropping valid updates outside the local bounding box. The server already
        # restricts messages by MMSI, so client-side bbox is just a safety net.
        try:
            if getattr(self, "_current_mmsi_filters", None):
                return True
        except Exception:
            pass
        if lat is None or lon is None:
            return False
        if not self.bounding_boxes:
            return True
        # Support single box [[S,W],[N,E]] or list of boxes [[[S,W],[N,E]], ...]
        boxes = self.bounding_boxes
        if len(boxes) == 2 and all(isinstance(v, (int, float)) for v in boxes[0]):
            boxes = [boxes]
        for box in boxes:
            (south, west), (north, east) = box
            if south <= lat <= north and west <= lon <= east:
                return True
        return False
    
    async def _handle_websocket_message(self, message: str):
        """
        Handle incoming WebSocket message from AIS Stream.
        
        Args:
            message: JSON message from AIS Stream
        """
        try:
            data = json.loads(message)
            
            # Extract vessel information from AIS message
            message_type = data.get("MessageType")
            if "Message" in data and message_type:
                # Handle position reports (Class A and B variants)
                if message_type in ("PositionReport", "ExtendedClassBPositionReport", "StandardClassBPositionReport"):
                    payload_key = message_type
                    ais_message = data["Message"].get(payload_key, {})
                    mmsi = str(ais_message.get("UserID", ""))
                    if mmsi:
                        # Optional region filter (client-side safety net)
                        lat = ais_message.get("Latitude")
                        lon = ais_message.get("Longitude")
                        if not self._is_within_subscribed_region(lat, lon):
                            return
                        # If liquid-only filter is enabled and we already know this vessel is non-liquid,
                        # skip storing position updates
                        if self.only_liquid_filter_enabled and mmsi in self.vessel_data:
                            existing = self.vessel_data.get(mmsi, {})
                            if not self._is_liquid_ship_type(existing.get("vessel_type_text"), existing.get("vessel_type")):
                                return
                        # Map AIS fields: Cog (course over ground), TrueHeading (heading)
                        vessel_data = {
                            "mmsi": mmsi,
                            "position": {
                                "latitude": lat,
                                "longitude": lon,
                                "course": ais_message.get("Cog", ais_message.get("TrueHeading")),
                                "heading": ais_message.get("TrueHeading"),
                                "speed": ais_message.get("Sog"),  # Speed over ground (knots)
                                "timestamp": data.get("Timestamp", datetime.now().isoformat())
                            },
                            "status": ais_message.get("NavigationalStatus", "Unknown")
                        }
                        
                        # Update existing vessel data or create new entry
                        if mmsi in self.vessel_data:
                            self.vessel_data[mmsi].update(vessel_data)
                        else:
                            self.vessel_data[mmsi] = vessel_data
                        
                        self.last_update[mmsi] = datetime.now()
                
                # Handle ship static data
                elif message_type == "ShipStaticData":
                    ais_message = data["Message"].get("ShipStaticData", {})
                    mmsi = str(ais_message.get("UserID", ""))
                    if mmsi:
                        # Extract vessel information from ShipStaticData message
                        dimensions = ais_message.get("Dimension", {})
                        vessel_data = {
                            "mmsi": mmsi,
                            "name": ais_message.get("Name", "").strip(),
                            "imo": ais_message.get("ImoNumber"),
                            "vessel_type": ais_message.get("Type"),
                            "vessel_type_text": get_ship_type_text(ais_message.get("Type")),
                            "length": dimensions.get("A", 0) + dimensions.get("B", 0),
                            "width": dimensions.get("C", 0) + dimensions.get("D", 0),
                            "draft": ais_message.get("MaximumStaticDraught"),
                            "destination": ais_message.get("Destination", "").strip(),
                            "eta": ais_message.get("Eta")
                        }
                        
                        # Apply pre-collection liquid-only filter
                        if self.only_liquid_filter_enabled:
                            type_code = ais_message.get("Type")
                            if not self._is_liquid_ship_type(vessel_data.get("vessel_type_text"), type_code):
                                # Remove existing non-liquid entries for this MMSI, if any
                                if mmsi in self.vessel_data:
                                    try:
                                        del self.vessel_data[mmsi]
                                        del self.last_update[mmsi]
                                    except Exception:
                                        self.vessel_data.pop(mmsi, None)
                                        self.last_update.pop(mmsi, None)
                                return
                        
                        # Update existing vessel data or create new entry
                        if mmsi in self.vessel_data:
                            self.vessel_data[mmsi].update(vessel_data)
                        else:
                            self.vessel_data[mmsi] = vessel_data
                        
                        self.last_update[mmsi] = datetime.now()
                        
        except Exception as e:
            logger.error(f"Error processing AIS Stream message: {e}")
    
    async def _websocket_connection(self):
        """Maintain WebSocket connection with AIS Stream"""
        consecutive_failures = 0
        max_consecutive_failures = 5
        
        while not self.stop_event.is_set():
            try:
                logger.info("Connecting to AIS Stream WebSocket...")
                
                # Add connection headers and timeout
                connect_kwargs = {
                    "ping_interval": 30,  # Increased ping interval
                    "ping_timeout": 15,   # Increased ping timeout
                    "close_timeout": 10,  # Wait 10 seconds to close gracefully
                    "max_size": 2**20,    # 1MB message size limit
                }
                
                async with websockets.connect(self.websocket_url, **connect_kwargs) as websocket:
                    self.websocket = websocket
                    self.connection_active = True
                    consecutive_failures = 0  # Reset failure counter
                    self.consecutive_failures = 0  # Reset instance counter
                    self.last_successful_connection = datetime.now()
                    
                    # Send subscription message
                    subscription = await self._create_subscription_message()
                    subscription_json = json.dumps(subscription)
                    
                    logger.debug(f"Sending subscription: {subscription_json}")
                    await websocket.send(subscription_json)
                    logger.info("Subscribed to AIS Stream data")
                    
                    # Wait for initial response to validate subscription
                    try:
                        initial_response = await asyncio.wait_for(websocket.recv(), timeout=10)
                        logger.info("Received initial response from AIS Stream")
                        
                        # Check if it's an error response
                        try:
                            response_data = json.loads(initial_response)
                            if "error" in response_data or "Error" in response_data:
                                logger.error(f"AIS Stream returned error: {response_data}")
                                raise Exception(f"AIS Stream error: {response_data}")
                        except json.JSONDecodeError:
                            # Not JSON, probably valid AIS data
                            logger.debug("First message is not JSON, assuming valid AIS data")
                        
                        # Process the initial message
                        await self._handle_websocket_message(initial_response)
                    except asyncio.TimeoutError:
                        logger.warning("No initial response from AIS Stream (timeout after 10s)")
                        logger.warning("This might indicate API key issues or service problems")
                    
                    # Listen for messages with heartbeat
                    message_count = 1  # Already processed initial message
                    last_message_time = datetime.now()
                    connection_start_time = datetime.now()
                    
                    async for message in websocket:
                        if self.stop_event.is_set():
                            break
                        
                        message_count += 1
                        self.total_messages_received += 1
                        last_message_time = datetime.now()
                        
                        if message_count % 200 == 0:  # Log less frequently
                            connection_duration = (datetime.now() - connection_start_time).total_seconds()
                            logger.info(f"Received {message_count} AIS messages, connection stable for {connection_duration:.0f}s")
                        
                        await self._handle_websocket_message(message)
                        
            except websockets.exceptions.ConnectionClosed as e:
                consecutive_failures += 1
                self.consecutive_failures += 1
                self.connection_active = False
                
                # Provide detailed error information
                error_details = f"WebSocket connection closed: {e.code}"
                if e.reason:
                    error_details += f" - {e.reason}"
                
                if e.code == 1006:
                    error_details += " (Abnormal closure - possible authentication failure)"
                elif e.code == 1000:
                    error_details += " (Normal closure)"
                elif e.code == 1001:
                    error_details += " (Going away)"
                elif e.code == 1002:
                    error_details += " (Protocol error)"
                elif e.code == 1003:
                    error_details += " (Unsupported data)"
                
                logger.warning(error_details)
                
                if consecutive_failures >= max_consecutive_failures:
                    logger.error(f"Too many consecutive failures ({consecutive_failures}). Stopping reconnection attempts.")
                    logger.error("This usually indicates:")
                    logger.error("  1. Invalid API key")
                    logger.error("  2. Account issues (suspended, quota exceeded)")
                    logger.error("  3. Service unavailable")
                    logger.error("Enable DEBUG logging and verify API key/network; see DEPLOYMENT.md Troubleshooting")
                    break
                    
            except websockets.exceptions.InvalidStatusCode as e:
                consecutive_failures += 1
                self.consecutive_failures += 1
                self.connection_active = False
                logger.error(f"Invalid status code from server: {e.status_code}")
                if e.status_code == 401:
                    logger.error("Authentication failed - check your API key")
                elif e.status_code == 403:
                    logger.error("Access forbidden - check your account permissions")
                elif e.status_code == 429:
                    logger.error("Rate limited - too many requests")
                    
            except Exception as e:
                consecutive_failures += 1
                self.consecutive_failures += 1
                self.connection_active = False
                logger.error(f"WebSocket connection error: {e}")
                logger.debug(f"Error type: {type(e).__name__}")
                
            if not self.stop_event.is_set() and consecutive_failures < max_consecutive_failures:
                # Exponential backoff for reconnection
                wait_time = min(self.reconnect_interval * (2 ** min(consecutive_failures, 4)), 300)  # Max 5 minutes
                logger.info(f"Reconnecting in {wait_time} seconds... (attempt {consecutive_failures + 1})")
                await asyncio.sleep(wait_time)
    
    def start_streaming(self):
        """Start the WebSocket connection in a background thread"""
        if self.use_mock:
            logger.info("Mock mode - skipping WebSocket connection")
            return
            
        if self.background_thread and self.background_thread.is_alive():
            logger.warning("AIS Stream connection already running")
            return
        
        def run_websocket():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self._websocket_connection())
            loop.close()
        
        self.background_thread = threading.Thread(target=run_websocket, daemon=True)
        self.background_thread.start()
        logger.info("Started AIS Stream background connection")
    
    def start_streaming_on_demand(self, duration_minutes: int = 2):
        """Start streaming for a limited time (used during vessel search)"""
        if self.use_mock:
            return
            
        # Start streaming
        self.start_streaming()
        
        # Schedule automatic stop after duration
        def auto_stop():
            import time
            deadline = time.time() + duration_minutes * 60
            # Gracefully stop only if there are no active consumers after deadline
            while time.time() < deadline:
                time.sleep(5)
            try:
                if self.total_messages_received == 0:
                    self.stop_streaming()
                    logger.info(f"Auto-stopped AIS Stream (idle) after {duration_minutes} minutes")
                else:
                    logger.info("Keeping AIS Stream running due to active data flow")
            except Exception:
                self.stop_streaming()
        
        auto_stop_thread = threading.Thread(target=auto_stop, daemon=True)
        auto_stop_thread.start()
        logger.info(f"Started on-demand AIS Stream for {duration_minutes} minutes")
    
    def _run_continuous_connection(self):
        """Run the continuous WebSocket connection loop"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self._websocket_connection())
        except Exception as e:
            logger.error(f"Error in continuous connection: {e}")
        finally:
            loop.close()

    def start_streaming_continuous(self):
        """
        Start continuous streaming (for application startup preloading)
        This will keep reconnecting automatically with longer sessions
        """
        if self.use_mock:
            logger.info("Using mock AIS data - not starting real continuous connection")
            return

        if self.connection_active:
            logger.info("AIS Stream already active")
            return

        logger.info("Starting continuous AIS Stream for vessel preloading")
        self.should_connect = True
        self.auto_stop_time = None  # No auto-stop for continuous mode

        # Start in background thread
        thread = threading.Thread(target=self._run_continuous_connection, daemon=True)
        thread.start()
        logger.info("Continuous AIS Stream initiated - building vessel database")
    
    def stop_streaming(self):
        """Stop the WebSocket connection"""
        if self.stop_event:
            self.stop_event.set()
        
        if self.background_thread and self.background_thread.is_alive():
            self.background_thread.join(timeout=5)
        
        self.connection_active = False
        logger.info("Stopped AIS Stream connection")
    
    def get_vessels_near_port(self, latitude: float, longitude: float, radius: int = 50) -> List[Dict[str, Any]]:
        """
        Get vessels near a specific port location.
        
        Args:
            latitude: Port latitude.
            longitude: Port longitude.
            radius: Radius in kilometers to search for vessels.
            
        Returns:
            List of vessels near the specified port.
        """
        vessels = []
        current_time = datetime.now()
        
        for mmsi, vessel in self.vessel_data.items():
            # Skip vessels with stale data (older than 1 hour)
            last_update = self.last_update.get(mmsi)
            if last_update and (current_time - last_update).seconds > 3600:
                continue
            
            # Check if vessel has position data
            if "position" not in vessel or not vessel["position"]:
                continue
            
            pos = vessel["position"]
            if pos.get("latitude") is None or pos.get("longitude") is None:
                continue
            
            # Calculate distance (simplified)
            lat_diff = abs(pos["latitude"] - latitude)
            lon_diff = abs(pos["longitude"] - longitude)
            
            # Rough distance calculation (1 degree ≈ 111 km)
            distance_km = ((lat_diff ** 2 + lon_diff ** 2) ** 0.5) * 111
            
            if distance_km <= radius:
                vessels.append(vessel)
        
        return vessels
    
    def get_vessel_by_mmsi(self, mmsi: str) -> Optional[Dict[str, Any]]:
        """
        Get vessel information by MMSI number.
        
        Args:
            mmsi: MMSI number of the vessel.
            
        Returns:
            Vessel information if found, None otherwise.
        """
        return self.vessel_data.get(mmsi)
    
    def get_vessel_by_imo(self, imo: str) -> Optional[Dict[str, Any]]:
        """
        Get vessel information by IMO number.
        
        Args:
            imo: IMO number of the vessel.
            
        Returns:
            Vessel information if found, None otherwise.
        """
        for vessel in self.vessel_data.values():
            if vessel.get("imo") == imo:
                return vessel
        return None
    
    def get_vessels_by_name(self, name: str) -> List[Dict[str, Any]]:
        """
        Get vessels by name (partial match).
        
        Args:
            name: Name of the vessel to search for.
            
        Returns:
            List of vessels matching the provided name.
        """
        matching_vessels = []
        name_lower = name.lower()
        
        for vessel in self.vessel_data.values():
            vessel_name = vessel.get("name", "").lower()
            if name_lower in vessel_name:
                matching_vessels.append(vessel)
        
        return matching_vessels
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get connection status information.
        
        Returns:
            Dictionary with connection status details.
        """
        # Backward compatibility - use the new health status method
        return self.get_health_status()
