from dataclasses import dataclass, field
from datetime import datetime
from typing import Set, Dict

@dataclass
class Surveyor:
    """Surveyor for cargo operations"""
    id: str
    name: str
    specialization: Set[str] = field(default_factory=set)  # Product types they are specialized in
    availability: Dict[datetime, bool] = field(default_factory=dict)  # Availability by time slot
    is_active: bool = True

    def to_dict(self) -> dict:
        """Convert surveyor to dictionary for serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "specialization": list(self.specialization),
            "availability": {k.isoformat(): v for k, v in self.availability.items()},
            "is_active": self.is_active
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'Surveyor':
        """Create surveyor from dictionary"""
        surveyor = cls(
            id=data["id"],
            name=data["name"],
            specialization=set(data.get("specialization", [])),
            is_active=data.get("is_active", True)
        )
        if "availability" in data:
            surveyor.availability = {
                datetime.fromisoformat(k): v 
                for k, v in data["availability"].items()
            }
        return surveyor 