--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13
-- Dumped by pg_dump version 16.9

-- Started on 2025-09-04 09:16:45 UTC

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

DROP DATABASE IF EXISTS planner;
--
-- TOC entry 3606 (class 1262 OID 108322)
-- Name: planner; Type: DATABASE; Schema: -; Owner: postgres
--

CREATE DATABASE planner WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'en_US.utf8';


ALTER DATABASE planner OWNER TO postgres;

\connect planner

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 232 (class 1259 OID 108465)
-- Name: alembic_version; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.alembic_version (
    version_num character varying(32) NOT NULL
);


ALTER TABLE public.alembic_version OWNER TO postgres;

--
-- TOC entry 229 (class 1259 OID 108427)
-- Name: assignment_changes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.assignment_changes (
    id integer NOT NULL,
    terminal_id character varying NOT NULL,
    assignment_id integer NOT NULL,
    vessel_id character varying,
    vessel_name character varying,
    jetty_name character varying,
    old_start_time timestamp without time zone,
    old_end_time timestamp without time zone,
    new_start_time timestamp without time zone,
    new_end_time timestamp without time zone,
    reason text NOT NULL,
    changed_by character varying,
    changed_at timestamp without time zone
);


ALTER TABLE public.assignment_changes OWNER TO postgres;

--
-- TOC entry 228 (class 1259 OID 108426)
-- Name: assignment_changes_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.assignment_changes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.assignment_changes_id_seq OWNER TO postgres;

--
-- TOC entry 3607 (class 0 OID 0)
-- Dependencies: 228
-- Name: assignment_changes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.assignment_changes_id_seq OWNED BY public.assignment_changes.id;


--
-- TOC entry 217 (class 1259 OID 108343)
-- Name: assignments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.assignments (
    id integer NOT NULL,
    terminal_id character varying NOT NULL,
    vessel_id character varying NOT NULL,
    vessel_name character varying NOT NULL,
    vessel_type character varying NOT NULL,
    jetty_name character varying NOT NULL,
    cargo_product character varying,
    cargo_volume double precision,
    start_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone NOT NULL,
    status character varying NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    vessel_db_id integer,
    visit_id integer,
    nomination_reference character varying(50),
    assignment_type character varying(20) DEFAULT 'SCHEDULED'::character varying
);


ALTER TABLE public.assignments OWNER TO postgres;

--
-- TOC entry 216 (class 1259 OID 108342)
-- Name: assignments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.assignments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.assignments_id_seq OWNER TO postgres;

--
-- TOC entry 3608 (class 0 OID 0)
-- Dependencies: 216
-- Name: assignments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.assignments_id_seq OWNED BY public.assignments.id;


--
-- TOC entry 231 (class 1259 OID 108441)
-- Name: cargoes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cargoes (
    id integer NOT NULL,
    terminal_id character varying NOT NULL,
    vessel_id integer NOT NULL,
    product character varying NOT NULL,
    volume double precision NOT NULL,
    is_loading boolean NOT NULL,
    connection_size character varying,
    vapor_return boolean,
    nitrogen_purge boolean,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.cargoes OWNER TO postgres;

--
-- TOC entry 230 (class 1259 OID 108440)
-- Name: cargoes_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cargoes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cargoes_id_seq OWNER TO postgres;

--
-- TOC entry 3609 (class 0 OID 0)
-- Dependencies: 230
-- Name: cargoes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cargoes_id_seq OWNED BY public.cargoes.id;


--
-- TOC entry 240 (class 1259 OID 116570)
-- Name: change_analysis; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.change_analysis (
    id integer NOT NULL,
    assignment_id integer NOT NULL,
    change_type character varying NOT NULL,
    change_category character varying,
    reason_category character varying,
    reason_text text,
    original_value character varying,
    new_value character varying,
    change_impact_minutes integer,
    change_frequency_score double precision,
    vessel_id character varying,
    vessel_name character varying,
    terminal_id character varying NOT NULL,
    changed_by character varying,
    changed_at timestamp without time zone
);


ALTER TABLE public.change_analysis OWNER TO postgres;

--
-- TOC entry 239 (class 1259 OID 116569)
-- Name: change_analysis_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.change_analysis_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.change_analysis_id_seq OWNER TO postgres;

--
-- TOC entry 3610 (class 0 OID 0)
-- Dependencies: 239
-- Name: change_analysis_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.change_analysis_id_seq OWNED BY public.change_analysis.id;


--
-- TOC entry 221 (class 1259 OID 108371)
-- Name: jetties; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.jetties (
    id integer NOT NULL,
    terminal_id character varying NOT NULL,
    name character varying NOT NULL,
    type character varying NOT NULL,
    vessel_type_restriction character varying,
    min_dwt double precision NOT NULL,
    max_dwt double precision NOT NULL,
    min_loa double precision,
    max_loa double precision NOT NULL,
    max_beam double precision NOT NULL,
    max_draft double precision NOT NULL,
    primary_use character varying NOT NULL,
    max_flow_rate double precision,
    is_operational boolean,
    created_at timestamp without time zone
);


ALTER TABLE public.jetties OWNER TO postgres;

--
-- TOC entry 220 (class 1259 OID 108370)
-- Name: jetties_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.jetties_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.jetties_id_seq OWNER TO postgres;

--
-- TOC entry 3611 (class 0 OID 0)
-- Dependencies: 220
-- Name: jetties_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.jetties_id_seq OWNED BY public.jetties.id;


--
-- TOC entry 236 (class 1259 OID 116542)
-- Name: ml_predictions_log; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ml_predictions_log (
    id integer NOT NULL,
    assignment_id integer,
    vessel_id character varying,
    vessel_name character varying,
    prediction_type character varying NOT NULL,
    predicted_minutes integer,
    actual_minutes integer,
    confidence_score double precision,
    prediction_timestamp timestamp without time zone,
    actual_timestamp timestamp without time zone,
    accuracy_percentage double precision,
    absolute_error_minutes integer,
    terminal_id character varying NOT NULL,
    created_at timestamp without time zone
);


ALTER TABLE public.ml_predictions_log OWNER TO postgres;

--
-- TOC entry 235 (class 1259 OID 116541)
-- Name: ml_predictions_log_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ml_predictions_log_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ml_predictions_log_id_seq OWNER TO postgres;

--
-- TOC entry 3612 (class 0 OID 0)
-- Dependencies: 235
-- Name: ml_predictions_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ml_predictions_log_id_seq OWNED BY public.ml_predictions_log.id;


--
-- TOC entry 234 (class 1259 OID 116527)
-- Name: nominations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.nominations (
    id integer NOT NULL,
    terminal_id character varying NOT NULL,
    runtime_vessel_id character varying NOT NULL,
    name character varying NOT NULL,
    vessel_type character varying NOT NULL,
    length double precision,
    beam double precision,
    draft double precision,
    deadweight double precision,
    priority integer,
    capacity double precision,
    width double precision,
    customer character varying,
    status character varying NOT NULL,
    eta timestamp without time zone,
    etd timestamp without time zone,
    mmsi character varying,
    imo character varying,
    cargoes json,
    extra_data json,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.nominations OWNER TO postgres;

--
-- TOC entry 233 (class 1259 OID 116526)
-- Name: nominations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.nominations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.nominations_id_seq OWNER TO postgres;

--
-- TOC entry 3613 (class 0 OID 0)
-- Dependencies: 233
-- Name: nominations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.nominations_id_seq OWNED BY public.nominations.id;


--
-- TOC entry 242 (class 1259 OID 116584)
-- Name: performance_alerts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.performance_alerts (
    id integer NOT NULL,
    alert_type character varying NOT NULL,
    metric_name character varying,
    threshold_value double precision,
    current_value double precision,
    severity character varying,
    description text,
    is_resolved boolean,
    terminal_id character varying,
    created_at timestamp without time zone,
    resolved_at timestamp without time zone,
    resolved_by character varying
);


ALTER TABLE public.performance_alerts OWNER TO postgres;

--
-- TOC entry 241 (class 1259 OID 116583)
-- Name: performance_alerts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.performance_alerts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.performance_alerts_id_seq OWNER TO postgres;

--
-- TOC entry 3614 (class 0 OID 0)
-- Dependencies: 241
-- Name: performance_alerts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.performance_alerts_id_seq OWNED BY public.performance_alerts.id;


--
-- TOC entry 238 (class 1259 OID 116556)
-- Name: planning_metrics; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.planning_metrics (
    id integer NOT NULL,
    date timestamp without time zone NOT NULL,
    terminal_id character varying NOT NULL,
    total_assignments integer,
    optimized_assignments integer,
    manual_changes integer,
    schedule_utilization_percent double precision,
    average_turnaround_hours double precision,
    throughput_efficiency double precision,
    total_vessels_processed integer,
    idle_time_hours double precision,
    created_at timestamp without time zone
);


ALTER TABLE public.planning_metrics OWNER TO postgres;

--
-- TOC entry 237 (class 1259 OID 116555)
-- Name: planning_metrics_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.planning_metrics_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.planning_metrics_id_seq OWNER TO postgres;

--
-- TOC entry 3615 (class 0 OID 0)
-- Dependencies: 237
-- Name: planning_metrics_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.planning_metrics_id_seq OWNED BY public.planning_metrics.id;


--
-- TOC entry 225 (class 1259 OID 108399)
-- Name: pumps; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.pumps (
    id integer NOT NULL,
    terminal_id character varying NOT NULL,
    name character varying NOT NULL,
    type character varying NOT NULL,
    flow_rate double precision NOT NULL,
    status character varying NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.pumps OWNER TO postgres;

--
-- TOC entry 224 (class 1259 OID 108398)
-- Name: pumps_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.pumps_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.pumps_id_seq OWNER TO postgres;

--
-- TOC entry 3616 (class 0 OID 0)
-- Dependencies: 224
-- Name: pumps_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.pumps_id_seq OWNED BY public.pumps.id;


--
-- TOC entry 215 (class 1259 OID 108330)
-- Name: settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.settings (
    key character varying NOT NULL,
    terminal_id character varying NOT NULL,
    value text NOT NULL,
    category character varying NOT NULL,
    is_sensitive boolean,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.settings OWNER TO postgres;

--
-- TOC entry 227 (class 1259 OID 108413)
-- Name: surveyors; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.surveyors (
    id integer NOT NULL,
    terminal_id character varying NOT NULL,
    name character varying NOT NULL,
    status character varying NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.surveyors OWNER TO postgres;

--
-- TOC entry 226 (class 1259 OID 108412)
-- Name: surveyors_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.surveyors_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.surveyors_id_seq OWNER TO postgres;

--
-- TOC entry 3617 (class 0 OID 0)
-- Dependencies: 226
-- Name: surveyors_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.surveyors_id_seq OWNED BY public.surveyors.id;


--
-- TOC entry 223 (class 1259 OID 108385)
-- Name: tanks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tanks (
    id integer NOT NULL,
    terminal_id character varying NOT NULL,
    name character varying NOT NULL,
    type character varying NOT NULL,
    capacity double precision NOT NULL,
    current_level double precision NOT NULL,
    product_type character varying NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.tanks OWNER TO postgres;

--
-- TOC entry 222 (class 1259 OID 108384)
-- Name: tanks_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tanks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tanks_id_seq OWNER TO postgres;

--
-- TOC entry 3618 (class 0 OID 0)
-- Dependencies: 222
-- Name: tanks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tanks_id_seq OWNED BY public.tanks.id;


--
-- TOC entry 214 (class 1259 OID 108323)
-- Name: terminals; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.terminals (
    id character varying NOT NULL,
    name character varying NOT NULL,
    location_lat double precision NOT NULL,
    location_lon double precision NOT NULL,
    total_capacity_cbm double precision NOT NULL,
    number_of_tanks integer NOT NULL,
    draft_meters double precision NOT NULL,
    operational_since timestamp without time zone NOT NULL,
    vessel_berths integer NOT NULL,
    barge_berths integer NOT NULL,
    timezone character varying NOT NULL,
    currency character varying NOT NULL,
    is_active boolean,
    created_at timestamp without time zone,
    updated_at timestamp without time zone
);


ALTER TABLE public.terminals OWNER TO postgres;

--
-- TOC entry 248 (class 1259 OID 116630)
-- Name: vessel_ais_data; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.vessel_ais_data (
    id integer NOT NULL,
    vessel_id integer,
    mmsi character varying(9) NOT NULL,
    latitude double precision,
    longitude double precision,
    course double precision,
    speed double precision,
    heading integer,
    navigation_status integer,
    rate_of_turn integer,
    position_accuracy integer,
    draught double precision,
    destination character varying(100),
    eta_raw integer,
    eta_parsed timestamp without time zone,
    "timestamp" timestamp without time zone NOT NULL,
    age_seconds integer,
    signal_quality integer,
    created_at timestamp without time zone,
    source character varying(50)
);


ALTER TABLE public.vessel_ais_data OWNER TO postgres;

--
-- TOC entry 247 (class 1259 OID 116629)
-- Name: vessel_ais_data_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.vessel_ais_data_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.vessel_ais_data_id_seq OWNER TO postgres;

--
-- TOC entry 3619 (class 0 OID 0)
-- Dependencies: 247
-- Name: vessel_ais_data_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.vessel_ais_data_id_seq OWNED BY public.vessel_ais_data.id;


--
-- TOC entry 244 (class 1259 OID 116598)
-- Name: vessel_registry; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.vessel_registry (
    id integer NOT NULL,
    imo character varying(10),
    mmsi character varying(9),
    call_sign character varying(10),
    name character varying(255) NOT NULL,
    previous_names json,
    vessel_type character varying(50) NOT NULL,
    vessel_subtype character varying(50),
    deadweight double precision,
    gross_tonnage double precision,
    length_overall double precision,
    beam double precision,
    maximum_draft double precision,
    flag_state character varying(3),
    port_of_registry character varying(100),
    owner character varying(255),
    operator character varying(255),
    manager character varying(255),
    build_year integer,
    shipyard character varying(255),
    hull_number character varying(50),
    status character varying(20),
    is_blacklisted boolean,
    data_source character varying(50),
    confidence_score integer,
    last_ais_update timestamp without time zone,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    created_by character varying(100),
    notes text
);


ALTER TABLE public.vessel_registry OWNER TO postgres;

--
-- TOC entry 243 (class 1259 OID 116597)
-- Name: vessel_registry_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.vessel_registry_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.vessel_registry_id_seq OWNER TO postgres;

--
-- TOC entry 3620 (class 0 OID 0)
-- Dependencies: 243
-- Name: vessel_registry_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.vessel_registry_id_seq OWNED BY public.vessel_registry.id;


--
-- TOC entry 246 (class 1259 OID 116611)
-- Name: vessel_visits; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.vessel_visits (
    id integer NOT NULL,
    vessel_id integer,
    terminal_id character varying,
    visit_number integer,
    external_reference character varying(100),
    estimated_arrival timestamp without time zone,
    actual_arrival timestamp without time zone,
    estimated_departure timestamp without time zone,
    actual_departure timestamp without time zone,
    operation_type character varying(20),
    cargo_types json,
    total_cargo_volume double precision,
    status character varying(20),
    berth_assignments json,
    customer character varying(255),
    agent character varying(255),
    priority integer,
    actual_berth_time integer,
    actual_operation_time integer,
    total_port_time integer,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    notes text
);


ALTER TABLE public.vessel_visits OWNER TO postgres;

--
-- TOC entry 245 (class 1259 OID 116610)
-- Name: vessel_visits_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.vessel_visits_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.vessel_visits_id_seq OWNER TO postgres;

--
-- TOC entry 3621 (class 0 OID 0)
-- Dependencies: 245
-- Name: vessel_visits_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.vessel_visits_id_seq OWNED BY public.vessel_visits.id;


--
-- TOC entry 219 (class 1259 OID 108357)
-- Name: vessels; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.vessels (
    id integer NOT NULL,
    terminal_id character varying NOT NULL,
    name character varying NOT NULL,
    type character varying NOT NULL,
    created_at timestamp without time zone
);


ALTER TABLE public.vessels OWNER TO postgres;

--
-- TOC entry 218 (class 1259 OID 108356)
-- Name: vessels_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.vessels_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.vessels_id_seq OWNER TO postgres;

--
-- TOC entry 3622 (class 0 OID 0)
-- Dependencies: 218
-- Name: vessels_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.vessels_id_seq OWNED BY public.vessels.id;


--
-- TOC entry 3354 (class 2604 OID 108430)
-- Name: assignment_changes id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.assignment_changes ALTER COLUMN id SET DEFAULT nextval('public.assignment_changes_id_seq'::regclass);


--
-- TOC entry 3347 (class 2604 OID 108346)
-- Name: assignments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.assignments ALTER COLUMN id SET DEFAULT nextval('public.assignments_id_seq'::regclass);


--
-- TOC entry 3355 (class 2604 OID 108444)
-- Name: cargoes id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cargoes ALTER COLUMN id SET DEFAULT nextval('public.cargoes_id_seq'::regclass);


--
-- TOC entry 3359 (class 2604 OID 116573)
-- Name: change_analysis id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.change_analysis ALTER COLUMN id SET DEFAULT nextval('public.change_analysis_id_seq'::regclass);


--
-- TOC entry 3350 (class 2604 OID 108374)
-- Name: jetties id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.jetties ALTER COLUMN id SET DEFAULT nextval('public.jetties_id_seq'::regclass);


--
-- TOC entry 3357 (class 2604 OID 116545)
-- Name: ml_predictions_log id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ml_predictions_log ALTER COLUMN id SET DEFAULT nextval('public.ml_predictions_log_id_seq'::regclass);


--
-- TOC entry 3356 (class 2604 OID 116530)
-- Name: nominations id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.nominations ALTER COLUMN id SET DEFAULT nextval('public.nominations_id_seq'::regclass);


--
-- TOC entry 3360 (class 2604 OID 116587)
-- Name: performance_alerts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.performance_alerts ALTER COLUMN id SET DEFAULT nextval('public.performance_alerts_id_seq'::regclass);


--
-- TOC entry 3358 (class 2604 OID 116559)
-- Name: planning_metrics id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.planning_metrics ALTER COLUMN id SET DEFAULT nextval('public.planning_metrics_id_seq'::regclass);


--
-- TOC entry 3352 (class 2604 OID 108402)
-- Name: pumps id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.pumps ALTER COLUMN id SET DEFAULT nextval('public.pumps_id_seq'::regclass);


--
-- TOC entry 3353 (class 2604 OID 108416)
-- Name: surveyors id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.surveyors ALTER COLUMN id SET DEFAULT nextval('public.surveyors_id_seq'::regclass);


--
-- TOC entry 3351 (class 2604 OID 108388)
-- Name: tanks id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tanks ALTER COLUMN id SET DEFAULT nextval('public.tanks_id_seq'::regclass);


--
-- TOC entry 3363 (class 2604 OID 116633)
-- Name: vessel_ais_data id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_ais_data ALTER COLUMN id SET DEFAULT nextval('public.vessel_ais_data_id_seq'::regclass);


--
-- TOC entry 3361 (class 2604 OID 116601)
-- Name: vessel_registry id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_registry ALTER COLUMN id SET DEFAULT nextval('public.vessel_registry_id_seq'::regclass);


--
-- TOC entry 3362 (class 2604 OID 116614)
-- Name: vessel_visits id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_visits ALTER COLUMN id SET DEFAULT nextval('public.vessel_visits_id_seq'::regclass);


--
-- TOC entry 3349 (class 2604 OID 108360)
-- Name: vessels id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessels ALTER COLUMN id SET DEFAULT nextval('public.vessels_id_seq'::regclass);


--
-- TOC entry 3584 (class 0 OID 108465)
-- Dependencies: 232
-- Data for Name: alembic_version; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.alembic_version (version_num) FROM stdin;
2f9a04a27344
\.


--
-- TOC entry 3581 (class 0 OID 108427)
-- Dependencies: 229
-- Data for Name: assignment_changes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.assignment_changes (id, terminal_id, assignment_id, vessel_id, vessel_name, jetty_name, old_start_time, old_end_time, new_start_time, new_end_time, reason, changed_by, changed_at) FROM stdin;
1	TNZN	11	NV001	BARBOUNI	Jetty 5	2025-09-03 07:00:00	2025-09-04 16:00:00	2024-01-15 11:00:00	2024-01-15 15:00:00	test	user	2025-09-03 08:30:27.975866
2	TNZN	12	NV001	BOW CAROLINE	Jetty 5	2025-09-03 14:00:00	2025-09-04 23:00:00	2025-09-03 15:00:00	2025-09-04 20:00:00	Schedule adjustment	user	2025-09-03 08:35:12.317582
3	TNZN	13	NV001	BARBOUNI	Jetty 4	2025-09-03 16:00:00	2025-09-05 01:00:00	2025-09-03 14:00:00	2025-09-04 23:00:00	Jetty change: Jetty 5 → Jetty 4. Tide/draft restrictions	user	2025-09-03 10:58:18.999144
4	TNZN	13	NV001	BARBOUNI	Jetty 5	2025-09-03 14:00:00	2025-09-04 23:00:00	2025-09-03 12:00:00	2025-09-04 21:00:00	Jetty change: Jetty 4 → Jetty 5. Pilot availability	user	2025-09-03 11:35:15.230929
5	TNZN	13	NV001	BARBOUNI	Jetty 3	2025-09-03 12:00:00	2025-09-04 21:00:00	2025-09-03 10:00:00	2025-09-04 19:00:00	Jetty change: Jetty 5 → Jetty 3. Port congestion	user	2025-09-03 11:35:46.756096
6	TNZN	13	NV001	BARBOUNI	Jetty 5	2025-09-03 10:00:00	2025-09-04 19:00:00	2025-09-03 08:00:00	2025-09-04 17:00:00	Jetty change: Jetty 3 → Jetty 5. TEST	user	2025-09-03 11:36:06.293361
7	TNZN	13	NV001	BARBOUNI	Jetty 5	2025-09-03 08:00:00	2025-09-04 17:00:00	2025-09-03 06:00:00	2025-09-04 19:38:12.993	Pilot availability	user	2025-09-03 11:44:37.085828
8	TNZN	13	NV001	BARBOUNI	Jetty 3	2025-09-03 06:00:00	2025-09-04 19:38:12.993	2025-09-03 04:00:00	2025-09-04 17:38:12.993	Jetty change: Jetty 5 → Jetty 3. Weather conditions delay	user	2025-09-03 12:04:15.122687
9	TNZN	13	NV001	BARBOUNI	Jetty 3	2025-09-03 04:00:00	2025-09-04 17:38:12.993	2025-09-03 08:32:49.411	2025-09-04 08:28:20.051	Pilot availability	user	2025-09-03 12:04:22.050171
10	TNZN	14	NV001	BENTAYGA	Jetty 5	2025-09-03 18:00:00	2025-09-05 00:00:00	2025-09-03 16:00:00	2025-09-04 22:00:00	Jetty change: Jetty 6 → Jetty 5. Pilot availability	user	2025-09-03 12:34:20.501002
11	TNZN	15	NV001	BENTAYGA	Jetty 4	2025-09-03 18:00:00	2025-09-05 00:00:00	2025-09-03 16:00:00	2025-09-04 22:00:00	Jetty change: Jetty 6 → Jetty 4. Port authority directive	user	2025-09-03 12:34:39.72886
12	TNZN	17	NV001	BENTAYGA	Jetty 3	2025-09-03 18:00:00	2025-09-05 03:00:00	2025-09-03 16:00:00	2025-09-05 01:00:00	Jetty change: Jetty 6 → Jetty 3. Tide/draft restrictions	user	2025-09-03 12:58:41.115832
13	TNZN	19	NV001	AMADEUS TITANIUM	Jetty 2	2025-09-03 23:00:00	2025-09-05 05:00:00	2025-09-03 21:00:00	2025-09-05 03:00:00	Jetty change: Jetty 4 → Jetty 2. Pilot availability	user	2025-09-03 17:43:47.516309
14	TNZN	0	NV001	QUADRANS 2	Jetty 6	\N	\N	2025-09-03 23:00:00	2025-09-05 05:00:00	Vessel moved from nomination to planned via OR-tools optimization	System - OR-tools Optimizer	2025-09-03 17:58:01.978851
15	TNZN	0	NV001	COLORADO	Jetty 5	\N	\N	2025-09-04 00:00:00	2025-09-05 09:00:00	Vessel moved from nomination to planned via OR-tools optimization	System - OR-tools Optimizer	2025-09-03 18:27:13.27436
16	TNZN	26	NV001	COLORADO	Jetty 2	2025-09-04 00:00:00	2025-09-05 09:00:00	2025-09-03 22:00:00	2025-09-05 07:00:00	Jetty change: Jetty 5 → Jetty 2. Weather conditions delay	user	2025-09-03 18:30:05.414221
17	TNZN	26	NV001	COLORADO	Jetty 2	2025-09-03 22:00:00	2025-09-05 07:00:00	2025-09-04 01:42:53.486	2025-09-05 05:00:00	Weather conditions delay	user	2025-09-03 18:30:12.328088
18	TNZN	26	NV001	COLORADO	Jetty 3	2025-09-04 01:42:53.486	2025-09-05 05:00:00	2025-09-03 23:42:53.486	2025-09-05 03:00:00	Jetty change: Jetty 2 → Jetty 3. Environmental restrictions	user	2025-09-04 08:24:15.095672
19	TNZN	26	NV001	COLORADO	Jetty 3	2025-09-03 23:42:53.486	2025-09-05 03:00:00	2025-09-03 21:42:53.486	2025-09-05 11:30:00	Environmental restrictions	user	2025-09-04 08:24:18.419822
20	TNZN	27	NV001	FEBE	Jetty 6	\N	\N	2025-09-04 14:00:00	2025-09-05 21:00:00	Vessel moved from nomination to planned via OR-tools optimization	System - OR-tools Optimizer	2025-09-04 08:29:25.980992
21	TNZN	28	NV002	ALSIA SWAN	Jetty 6	\N	\N	2025-09-04 14:00:00	2025-09-05 09:00:00	Vessel moved from nomination to planned via OR-tools optimization	System - OR-tools Optimizer	2025-09-04 08:31:34.674045
22	TNZN	28	NV002	ALSIA SWAN	Jetty 4	2025-09-04 14:00:00	2025-09-05 09:00:00	2025-09-04 12:00:00	2025-09-05 07:00:00	Jetty change: Jetty 6 → Jetty 4. Tide/draft restrictions	user	2025-09-04 08:31:47.239179
23	TNZN	28	NV002	ALSIA SWAN	Jetty 4	2025-09-04 12:00:00	2025-09-05 07:00:00	\N	\N	Assignment deleted	user	2025-09-04 08:58:14.26449
\.


--
-- TOC entry 3569 (class 0 OID 108343)
-- Dependencies: 217
-- Data for Name: assignments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.assignments (id, terminal_id, vessel_id, vessel_name, vessel_type, jetty_name, cargo_product, cargo_volume, start_time, end_time, status, created_at, updated_at, vessel_db_id, visit_id, nomination_reference, assignment_type) FROM stdin;
\.


--
-- TOC entry 3583 (class 0 OID 108441)
-- Dependencies: 231
-- Data for Name: cargoes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cargoes (id, terminal_id, vessel_id, product, volume, is_loading, connection_size, vapor_return, nitrogen_purge, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3592 (class 0 OID 116570)
-- Dependencies: 240
-- Data for Name: change_analysis; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.change_analysis (id, assignment_id, change_type, change_category, reason_category, reason_text, original_value, new_value, change_impact_minutes, change_frequency_score, vessel_id, vessel_name, terminal_id, changed_by, changed_at) FROM stdin;
1	14	start_time	external_factor	operational	Jetty change: Jetty 6 → Jetty 5. Pilot availability	2025-09-03T18:00:00	2025-09-03T16:00:00+00:00	\N	\N	NV001	BENTAYGA	TNZN	user	2025-09-03 12:34:20.516643
2	14	end_time	external_factor	operational	Jetty change: Jetty 6 → Jetty 5. Pilot availability	2025-09-05T00:00:00	2025-09-04T22:00:00+00:00	\N	\N	NV001	BENTAYGA	TNZN	user	2025-09-03 12:34:20.532137
3	14	jetty	external_factor	operational	Jetty change: Jetty 6 → Jetty 5. Pilot availability	Jetty 6	Jetty 5	\N	\N	NV001	BENTAYGA	TNZN	user	2025-09-03 12:34:20.542908
4	15	start_time	unknown	regulatory	Jetty change: Jetty 6 → Jetty 4. Port authority directive	2025-09-03T18:00:00	2025-09-03T16:00:00+00:00	\N	\N	NV001	BENTAYGA	TNZN	user	2025-09-03 12:34:39.74389
5	15	end_time	unknown	regulatory	Jetty change: Jetty 6 → Jetty 4. Port authority directive	2025-09-05T00:00:00	2025-09-04T22:00:00+00:00	\N	\N	NV001	BENTAYGA	TNZN	user	2025-09-03 12:34:39.759932
6	15	jetty	unknown	regulatory	Jetty change: Jetty 6 → Jetty 4. Port authority directive	Jetty 6	Jetty 4	\N	\N	NV001	BENTAYGA	TNZN	user	2025-09-03 12:34:39.775186
7	17	start_time	external_factor	operational	Jetty change: Jetty 6 → Jetty 3. Tide/draft restrictions	2025-09-03T18:00:00+00:00	2025-09-03T16:00:00+00:00	120	\N	NV001	BENTAYGA	TNZN	user	2025-09-03 12:58:41.164273
8	17	end_time	external_factor	operational	Jetty change: Jetty 6 → Jetty 3. Tide/draft restrictions	2025-09-05T03:00:00+00:00	2025-09-05T01:00:00+00:00	120	\N	NV001	BENTAYGA	TNZN	user	2025-09-03 12:58:41.18371
9	17	jetty	external_factor	operational	Jetty change: Jetty 6 → Jetty 3. Tide/draft restrictions	Jetty 6	Jetty 3	\N	\N	NV001	BENTAYGA	TNZN	user	2025-09-03 12:58:41.195022
10	19	start_time	external_factor	operational	Jetty change: Jetty 4 → Jetty 2. Pilot availability	2025-09-03T23:00:00+00:00	2025-09-03T21:00:00+00:00	120	\N	NV001	AMADEUS TITANIUM	TNZN	user	2025-09-03 17:43:47.532539
11	19	end_time	external_factor	operational	Jetty change: Jetty 4 → Jetty 2. Pilot availability	2025-09-05T05:00:00+00:00	2025-09-05T03:00:00+00:00	120	\N	NV001	AMADEUS TITANIUM	TNZN	user	2025-09-03 17:43:47.549043
12	19	jetty	external_factor	operational	Jetty change: Jetty 4 → Jetty 2. Pilot availability	Jetty 4	Jetty 2	\N	\N	NV001	AMADEUS TITANIUM	TNZN	user	2025-09-03 17:43:47.558368
13	26	start_time	external_factor	operational	Jetty change: Jetty 5 → Jetty 2. Weather conditions delay	2025-09-04T00:00:00+00:00	2025-09-03T22:00:00+00:00	120	\N	NV001	COLORADO	TNZN	user	2025-09-03 18:30:05.42733
14	26	end_time	external_factor	operational	Jetty change: Jetty 5 → Jetty 2. Weather conditions delay	2025-09-05T09:00:00+00:00	2025-09-05T07:00:00+00:00	120	\N	NV001	COLORADO	TNZN	user	2025-09-03 18:30:05.443601
15	26	jetty	external_factor	operational	Jetty change: Jetty 5 → Jetty 2. Weather conditions delay	Jetty 5	Jetty 2	\N	\N	NV001	COLORADO	TNZN	user	2025-09-03 18:30:05.458109
16	26	start_time	external_factor	operational	Weather conditions delay	2025-09-03T22:00:00+00:00	2025-09-04T01:42:53.486000+00:00	222	\N	NV001	COLORADO	TNZN	user	2025-09-03 18:30:12.339347
17	26	end_time	external_factor	operational	Weather conditions delay	2025-09-05T07:00:00+00:00	2025-09-05T05:00:00+00:00	120	\N	NV001	COLORADO	TNZN	user	2025-09-03 18:30:12.348382
18	26	start_time	external_factor	regulatory	Jetty change: Jetty 2 → Jetty 3. Environmental restrictions	2025-09-04T01:42:53.486000+00:00	2025-09-03T23:42:53.486000+00:00	120	\N	NV001	COLORADO	TNZN	user	2025-09-04 08:24:15.119347
19	26	end_time	external_factor	regulatory	Jetty change: Jetty 2 → Jetty 3. Environmental restrictions	2025-09-05T05:00:00+00:00	2025-09-05T03:00:00+00:00	120	\N	NV001	COLORADO	TNZN	user	2025-09-04 08:24:15.135594
20	26	jetty	external_factor	regulatory	Jetty change: Jetty 2 → Jetty 3. Environmental restrictions	Jetty 2	Jetty 3	\N	\N	NV001	COLORADO	TNZN	user	2025-09-04 08:24:15.14843
21	26	start_time	external_factor	regulatory	Environmental restrictions	2025-09-03T23:42:53.486000+00:00	2025-09-03T21:42:53.486000+00:00	120	\N	NV001	COLORADO	TNZN	user	2025-09-04 08:24:18.430256
22	26	end_time	external_factor	regulatory	Environmental restrictions	2025-09-05T03:00:00+00:00	2025-09-05T11:30:00+00:00	510	\N	NV001	COLORADO	TNZN	user	2025-09-04 08:24:18.443454
23	28	start_time	external_factor	operational	Jetty change: Jetty 6 → Jetty 4. Tide/draft restrictions	2025-09-04T14:00:00+00:00	2025-09-04T12:00:00+00:00	120	\N	NV002	ALSIA SWAN	TNZN	user	2025-09-04 08:31:47.252295
24	28	end_time	external_factor	operational	Jetty change: Jetty 6 → Jetty 4. Tide/draft restrictions	2025-09-05T09:00:00+00:00	2025-09-05T07:00:00+00:00	120	\N	NV002	ALSIA SWAN	TNZN	user	2025-09-04 08:31:47.264662
25	28	jetty	external_factor	operational	Jetty change: Jetty 6 → Jetty 4. Tide/draft restrictions	Jetty 6	Jetty 4	\N	\N	NV002	ALSIA SWAN	TNZN	user	2025-09-04 08:31:47.276134
\.


--
-- TOC entry 3573 (class 0 OID 108371)
-- Dependencies: 221
-- Data for Name: jetties; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.jetties (id, terminal_id, name, type, vessel_type_restriction, min_dwt, max_dwt, min_loa, max_loa, max_beam, max_draft, primary_use, max_flow_rate, is_operational, created_at) FROM stdin;
1	TNZN	Jetty 1	VESSEL	seagoing	1000	60000	57	236	34	12.8	Multiple products	2000	t	2025-09-02 08:32:24.583185
2	TNZN	Jetty 2	BARGE	inland	1000	7000	57	135	17	4.5	Chemicals/Hydrocarbons	800	t	2025-09-02 08:32:24.58319
3	TNZN	Jetty 3	VESSEL	both	1000	15000	85	135	22	12.8	Minerals/Benzene	1800	t	2025-09-02 08:32:24.583192
4	TNZN	Jetty 4	BARGE	inland	1000	9000	85	135	22	4.4	Minerals only	600	t	2025-09-02 08:32:24.583194
5	TNZN	Jetty 5	VESSEL	seagoing	1000	150000	105	275	50	15	Minerals only	4000	t	2025-09-02 08:32:24.583195
6	TNZN	Jetty 6	VESSEL	both	1000	20000	105	150	25	9.9	Minerals/Butane	1200	t	2025-09-02 08:32:24.583197
\.


--
-- TOC entry 3588 (class 0 OID 116542)
-- Dependencies: 236
-- Data for Name: ml_predictions_log; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.ml_predictions_log (id, assignment_id, vessel_id, vessel_name, prediction_type, predicted_minutes, actual_minutes, confidence_score, prediction_timestamp, actual_timestamp, accuracy_percentage, absolute_error_minutes, terminal_id, created_at) FROM stdin;
\.


--
-- TOC entry 3586 (class 0 OID 116527)
-- Dependencies: 234
-- Data for Name: nominations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.nominations (id, terminal_id, runtime_vessel_id, name, vessel_type, length, beam, draft, deadweight, priority, capacity, width, customer, status, eta, etd, mmsi, imo, cargoes, extra_data, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3594 (class 0 OID 116584)
-- Dependencies: 242
-- Data for Name: performance_alerts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.performance_alerts (id, alert_type, metric_name, threshold_value, current_value, severity, description, is_resolved, terminal_id, created_at, resolved_at, resolved_by) FROM stdin;
\.


--
-- TOC entry 3590 (class 0 OID 116556)
-- Dependencies: 238
-- Data for Name: planning_metrics; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.planning_metrics (id, date, terminal_id, total_assignments, optimized_assignments, manual_changes, schedule_utilization_percent, average_turnaround_hours, throughput_efficiency, total_vessels_processed, idle_time_hours, created_at) FROM stdin;
\.


--
-- TOC entry 3577 (class 0 OID 108399)
-- Dependencies: 225
-- Data for Name: pumps; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.pumps (id, terminal_id, name, type, flow_rate, status, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3567 (class 0 OID 108330)
-- Dependencies: 215
-- Data for Name: settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.settings (key, terminal_id, value, category, is_sensitive, created_at, updated_at) FROM stdin;
terminal_name	TNZN	EVOS Terneuzen	terminal	f	2025-09-02 08:32:24.598757	2025-09-02 08:32:24.598761
terminal_location_lat	TNZN	51.3294	terminal	f	2025-09-02 08:32:24.598763	2025-09-02 08:32:24.598764
terminal_location_lon	TNZN	3.8091	terminal	f	2025-09-02 08:32:24.598766	2025-09-02 08:32:24.598768
time_zone	TNZN	Europe/Brussels	preferences	f	2025-09-02 08:32:24.598769	2025-09-02 08:32:24.598771
date_format	TNZN	DD-MM-YYYY	preferences	f	2025-09-02 08:32:24.598772	2025-09-02 08:32:24.598774
language	TNZN	nl-NL	preferences	f	2025-09-02 08:32:24.598775	2025-09-02 08:32:24.598777
dark_mode	TNZN	false	preferences	f	2025-09-02 08:32:24.598779	2025-09-02 08:32:24.59878
auto_refresh	TNZN	true	preferences	f	2025-09-02 08:32:24.598781	2025-09-02 08:32:24.598783
refresh_interval	TNZN	30	preferences	f	2025-09-02 08:32:24.598784	2025-09-02 08:32:24.598786
aisstream_api_key	TNZN	501e28080107bf1d5a3d4e10380cfb6af87cd357	api_keys	t	2025-09-02 08:32:24.598787	2025-09-02 08:32:24.598789
weather_api_key	TNZN		api_keys	t	2025-09-02 08:32:24.59879	2025-09-02 08:32:24.598792
claude_api_key	TNZN		api_keys	t	2025-09-02 08:32:24.598793	2025-09-02 08:32:24.598794
weather_api_provider	TNZN	openmeteo	weather	f	2025-09-02 08:32:24.598796	2025-09-02 08:32:24.598797
weather_display_units	TNZN	metric	weather	f	2025-09-02 08:32:24.598799	2025-09-02 08:32:24.5988
weather_refresh_interval	TNZN	15	weather	f	2025-09-02 08:32:24.598802	2025-09-02 08:32:24.598803
show_wind_alerts	TNZN	true	weather	f	2025-09-02 08:32:24.598805	2025-09-02 08:32:24.598806
wind_caution_threshold	TNZN	12	weather	f	2025-09-02 08:32:24.598808	2025-09-02 08:32:24.598809
wind_danger_threshold	TNZN	17	weather	f	2025-09-02 08:32:24.598811	2025-09-02 08:32:24.598812
show_12h_forecast	TNZN	true	weather	f	2025-09-02 08:32:24.598814	2025-09-02 08:32:24.598815
solver_time_limit	TNZN	60	solver	f	2025-09-02 08:32:24.598816	2025-09-02 08:32:24.598818
solver_strategy	TNZN	AUTOMATIC	solver	f	2025-09-02 08:32:24.598819	2025-09-02 08:32:24.598821
parallel_solving	TNZN	true	solver	f	2025-09-02 08:32:24.598822	2025-09-02 08:32:24.598824
enable_email_notifications	TNZN	false	notifications	f	2025-09-02 08:32:24.598825	2025-09-02 08:32:24.598826
notify_on_vessel_arrival	TNZN	true	notifications	f	2025-09-02 08:32:24.598828	2025-09-02 08:32:24.598829
notify_on_vessel_departure	TNZN	true	notifications	f	2025-09-02 08:32:24.598831	2025-09-02 08:32:24.598832
notify_on_assignment_change	TNZN	true	notifications	f	2025-09-02 08:32:24.598834	2025-09-02 08:32:24.598835
notify_on_weather_alert	TNZN	true	notifications	f	2025-09-02 08:32:24.598837	2025-09-02 08:32:24.598838
\.


--
-- TOC entry 3579 (class 0 OID 108413)
-- Dependencies: 227
-- Data for Name: surveyors; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.surveyors (id, terminal_id, name, status, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3575 (class 0 OID 108385)
-- Dependencies: 223
-- Data for Name: tanks; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tanks (id, terminal_id, name, type, capacity, current_level, product_type, created_at, updated_at) FROM stdin;
\.


--
-- TOC entry 3566 (class 0 OID 108323)
-- Dependencies: 214
-- Data for Name: terminals; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.terminals (id, name, location_lat, location_lon, total_capacity_cbm, number_of_tanks, draft_meters, operational_since, vessel_berths, barge_berths, timezone, currency, is_active, created_at, updated_at) FROM stdin;
TNZN	Evos Terneuzen	51.3294	3.8091	537000	42	15	2005-01-01 00:00:00	3	3	Europe/Brussels	EUR	t	\N	2025-09-02 14:10:12.00733
\.


--
-- TOC entry 3600 (class 0 OID 116630)
-- Dependencies: 248
-- Data for Name: vessel_ais_data; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.vessel_ais_data (id, vessel_id, mmsi, latitude, longitude, course, speed, heading, navigation_status, rate_of_turn, position_accuracy, draught, destination, eta_raw, eta_parsed, "timestamp", age_seconds, signal_quality, created_at, source) FROM stdin;
\.


--
-- TOC entry 3596 (class 0 OID 116598)
-- Dependencies: 244
-- Data for Name: vessel_registry; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.vessel_registry (id, imo, mmsi, call_sign, name, previous_names, vessel_type, vessel_subtype, deadweight, gross_tonnage, length_overall, beam, maximum_draft, flag_state, port_of_registry, owner, operator, manager, build_year, shipyard, hull_number, status, is_blacklisted, data_source, confidence_score, last_ais_update, created_at, updated_at, created_by, notes) FROM stdin;
\.


--
-- TOC entry 3598 (class 0 OID 116611)
-- Dependencies: 246
-- Data for Name: vessel_visits; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.vessel_visits (id, vessel_id, terminal_id, visit_number, external_reference, estimated_arrival, actual_arrival, estimated_departure, actual_departure, operation_type, cargo_types, total_cargo_volume, status, berth_assignments, customer, agent, priority, actual_berth_time, actual_operation_time, total_port_time, created_at, updated_at, notes) FROM stdin;
\.


--
-- TOC entry 3571 (class 0 OID 108357)
-- Dependencies: 219
-- Data for Name: vessels; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.vessels (id, terminal_id, name, type, created_at) FROM stdin;
\.


--
-- TOC entry 3623 (class 0 OID 0)
-- Dependencies: 228
-- Name: assignment_changes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.assignment_changes_id_seq', 23, true);


--
-- TOC entry 3624 (class 0 OID 0)
-- Dependencies: 216
-- Name: assignments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.assignments_id_seq', 28, true);


--
-- TOC entry 3625 (class 0 OID 0)
-- Dependencies: 230
-- Name: cargoes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.cargoes_id_seq', 17, true);


--
-- TOC entry 3626 (class 0 OID 0)
-- Dependencies: 239
-- Name: change_analysis_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.change_analysis_id_seq', 25, true);


--
-- TOC entry 3627 (class 0 OID 0)
-- Dependencies: 220
-- Name: jetties_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.jetties_id_seq', 33, true);


--
-- TOC entry 3628 (class 0 OID 0)
-- Dependencies: 235
-- Name: ml_predictions_log_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.ml_predictions_log_id_seq', 1, false);


--
-- TOC entry 3629 (class 0 OID 0)
-- Dependencies: 233
-- Name: nominations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.nominations_id_seq', 1, false);


--
-- TOC entry 3630 (class 0 OID 0)
-- Dependencies: 241
-- Name: performance_alerts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.performance_alerts_id_seq', 1, false);


--
-- TOC entry 3631 (class 0 OID 0)
-- Dependencies: 237
-- Name: planning_metrics_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.planning_metrics_id_seq', 1, false);


--
-- TOC entry 3632 (class 0 OID 0)
-- Dependencies: 224
-- Name: pumps_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.pumps_id_seq', 1, false);


--
-- TOC entry 3633 (class 0 OID 0)
-- Dependencies: 226
-- Name: surveyors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.surveyors_id_seq', 1, false);


--
-- TOC entry 3634 (class 0 OID 0)
-- Dependencies: 222
-- Name: tanks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.tanks_id_seq', 1, false);


--
-- TOC entry 3635 (class 0 OID 0)
-- Dependencies: 247
-- Name: vessel_ais_data_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.vessel_ais_data_id_seq', 1, false);


--
-- TOC entry 3636 (class 0 OID 0)
-- Dependencies: 243
-- Name: vessel_registry_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.vessel_registry_id_seq', 1, false);


--
-- TOC entry 3637 (class 0 OID 0)
-- Dependencies: 245
-- Name: vessel_visits_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.vessel_visits_id_seq', 1, false);


--
-- TOC entry 3638 (class 0 OID 0)
-- Dependencies: 218
-- Name: vessels_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.vessels_id_seq', 17, true);


--
-- TOC entry 3385 (class 2606 OID 108469)
-- Name: alembic_version alembic_version_pkc; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.alembic_version
    ADD CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num);


--
-- TOC entry 3381 (class 2606 OID 108434)
-- Name: assignment_changes assignment_changes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.assignment_changes
    ADD CONSTRAINT assignment_changes_pkey PRIMARY KEY (id);


--
-- TOC entry 3369 (class 2606 OID 108350)
-- Name: assignments assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_pkey PRIMARY KEY (id);


--
-- TOC entry 3383 (class 2606 OID 108448)
-- Name: cargoes cargoes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cargoes
    ADD CONSTRAINT cargoes_pkey PRIMARY KEY (id);


--
-- TOC entry 3393 (class 2606 OID 116577)
-- Name: change_analysis change_analysis_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.change_analysis
    ADD CONSTRAINT change_analysis_pkey PRIMARY KEY (id);


--
-- TOC entry 3373 (class 2606 OID 108378)
-- Name: jetties jetties_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.jetties
    ADD CONSTRAINT jetties_pkey PRIMARY KEY (id);


--
-- TOC entry 3389 (class 2606 OID 116549)
-- Name: ml_predictions_log ml_predictions_log_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ml_predictions_log
    ADD CONSTRAINT ml_predictions_log_pkey PRIMARY KEY (id);


--
-- TOC entry 3387 (class 2606 OID 116534)
-- Name: nominations nominations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.nominations
    ADD CONSTRAINT nominations_pkey PRIMARY KEY (id);


--
-- TOC entry 3395 (class 2606 OID 116591)
-- Name: performance_alerts performance_alerts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.performance_alerts
    ADD CONSTRAINT performance_alerts_pkey PRIMARY KEY (id);


--
-- TOC entry 3391 (class 2606 OID 116563)
-- Name: planning_metrics planning_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.planning_metrics
    ADD CONSTRAINT planning_metrics_pkey PRIMARY KEY (id);


--
-- TOC entry 3377 (class 2606 OID 108406)
-- Name: pumps pumps_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.pumps
    ADD CONSTRAINT pumps_pkey PRIMARY KEY (id);


--
-- TOC entry 3367 (class 2606 OID 108336)
-- Name: settings settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.settings
    ADD CONSTRAINT settings_pkey PRIMARY KEY (key, terminal_id);


--
-- TOC entry 3379 (class 2606 OID 108420)
-- Name: surveyors surveyors_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.surveyors
    ADD CONSTRAINT surveyors_pkey PRIMARY KEY (id);


--
-- TOC entry 3375 (class 2606 OID 108392)
-- Name: tanks tanks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tanks
    ADD CONSTRAINT tanks_pkey PRIMARY KEY (id);


--
-- TOC entry 3365 (class 2606 OID 108329)
-- Name: terminals terminals_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.terminals
    ADD CONSTRAINT terminals_pkey PRIMARY KEY (id);


--
-- TOC entry 3405 (class 2606 OID 116635)
-- Name: vessel_ais_data vessel_ais_data_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_ais_data
    ADD CONSTRAINT vessel_ais_data_pkey PRIMARY KEY (id);


--
-- TOC entry 3397 (class 2606 OID 116607)
-- Name: vessel_registry vessel_registry_imo_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_registry
    ADD CONSTRAINT vessel_registry_imo_key UNIQUE (imo);


--
-- TOC entry 3399 (class 2606 OID 116609)
-- Name: vessel_registry vessel_registry_mmsi_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_registry
    ADD CONSTRAINT vessel_registry_mmsi_key UNIQUE (mmsi);


--
-- TOC entry 3401 (class 2606 OID 116605)
-- Name: vessel_registry vessel_registry_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_registry
    ADD CONSTRAINT vessel_registry_pkey PRIMARY KEY (id);


--
-- TOC entry 3403 (class 2606 OID 116618)
-- Name: vessel_visits vessel_visits_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_visits
    ADD CONSTRAINT vessel_visits_pkey PRIMARY KEY (id);


--
-- TOC entry 3371 (class 2606 OID 108364)
-- Name: vessels vessels_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessels
    ADD CONSTRAINT vessels_pkey PRIMARY KEY (id);


--
-- TOC entry 3413 (class 2606 OID 108435)
-- Name: assignment_changes assignment_changes_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.assignment_changes
    ADD CONSTRAINT assignment_changes_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3407 (class 2606 OID 108351)
-- Name: assignments assignments_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.assignments
    ADD CONSTRAINT assignments_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3414 (class 2606 OID 108449)
-- Name: cargoes cargoes_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cargoes
    ADD CONSTRAINT cargoes_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3415 (class 2606 OID 108454)
-- Name: cargoes cargoes_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cargoes
    ADD CONSTRAINT cargoes_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessels(id);


--
-- TOC entry 3419 (class 2606 OID 116578)
-- Name: change_analysis change_analysis_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.change_analysis
    ADD CONSTRAINT change_analysis_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3409 (class 2606 OID 108379)
-- Name: jetties jetties_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.jetties
    ADD CONSTRAINT jetties_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3417 (class 2606 OID 116550)
-- Name: ml_predictions_log ml_predictions_log_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ml_predictions_log
    ADD CONSTRAINT ml_predictions_log_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3416 (class 2606 OID 116535)
-- Name: nominations nominations_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.nominations
    ADD CONSTRAINT nominations_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3420 (class 2606 OID 116592)
-- Name: performance_alerts performance_alerts_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.performance_alerts
    ADD CONSTRAINT performance_alerts_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3418 (class 2606 OID 116564)
-- Name: planning_metrics planning_metrics_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.planning_metrics
    ADD CONSTRAINT planning_metrics_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3411 (class 2606 OID 108407)
-- Name: pumps pumps_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.pumps
    ADD CONSTRAINT pumps_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3406 (class 2606 OID 108337)
-- Name: settings settings_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.settings
    ADD CONSTRAINT settings_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3412 (class 2606 OID 108421)
-- Name: surveyors surveyors_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.surveyors
    ADD CONSTRAINT surveyors_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3410 (class 2606 OID 108393)
-- Name: tanks tanks_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tanks
    ADD CONSTRAINT tanks_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3423 (class 2606 OID 116636)
-- Name: vessel_ais_data vessel_ais_data_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_ais_data
    ADD CONSTRAINT vessel_ais_data_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessel_registry(id) ON DELETE CASCADE;


--
-- TOC entry 3421 (class 2606 OID 116624)
-- Name: vessel_visits vessel_visits_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_visits
    ADD CONSTRAINT vessel_visits_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


--
-- TOC entry 3422 (class 2606 OID 116619)
-- Name: vessel_visits vessel_visits_vessel_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessel_visits
    ADD CONSTRAINT vessel_visits_vessel_id_fkey FOREIGN KEY (vessel_id) REFERENCES public.vessel_registry(id) ON DELETE CASCADE;


--
-- TOC entry 3408 (class 2606 OID 108365)
-- Name: vessels vessels_terminal_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vessels
    ADD CONSTRAINT vessels_terminal_id_fkey FOREIGN KEY (terminal_id) REFERENCES public.terminals(id);


-- Completed on 2025-09-04 09:16:47 UTC

--
-- PostgreSQL database dump complete
--

