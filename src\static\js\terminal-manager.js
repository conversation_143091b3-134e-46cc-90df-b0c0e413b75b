/**
 * Terminal Manager - Simplified for Terneuzen Only
 * 
 * <PERSON>les loading Terneuzen terminal data and updating UI components.
 * Simplified from multi-terminal version to focus only on EVOS Terneuzen.
 */

class TerminalManager {
    constructor() {
        this.terminal = null;
        this.isLoading = false;
        
        // Initialize when DOM is ready
        this.init();
    }

    /**
     * Initialize terminal manager
     */
    async init() {
        console.log('Initializing Terneuzen Terminal Manager...');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            await this.setup();
        }
    }

    /**
     * Setup terminal manager after DOM is ready
     */
    async setup() {
        try {
            // Load Terneuzen terminal data
            await this.loadTerminal();
            
            // Update UI with terminal info
            this.updateUI();
            
            console.log('Terneuzen Terminal Manager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Terminal Manager:', error);
            this.showError('Failed to load terminal data');
        }
    }

    /**
     * Load Terneuzen terminal data
     */
    async loadTerminal() {
        this.isLoading = true;
        
        try {
            console.log('Loading Terneuzen terminal data...');
            
            const response = await fetch('/api/terminals/active/current');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            this.terminal = await response.json();
            console.log('Loaded terminal:', this.terminal);
            
        } catch (error) {
            console.error('Error loading terminal:', error);
            throw error;
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Update UI components with terminal information
     */
    updateUI() {
        this.updateTerminalTitle();
        this.updatePageElements();
    }

    /**
     * Update terminal title in sidebar
     */
    updateTerminalTitle() {
        const terminalTitle = document.getElementById('terminalTitle');
        if (terminalTitle) {
            terminalTitle.textContent = 'Jetty Planner';
        }
    }

    /**
     * Update page-specific elements with terminal data
     */
    updatePageElements() {
        // Dispatch custom event for pages that need terminal data
        if (this.terminal) {
            const event = new CustomEvent('terminalLoaded', {
                detail: { terminal: this.terminal }
            });
            document.dispatchEvent(event);
        }
    }

    /**
     * Show error message to user
     */
    showError(message) {
        console.error('Terminal Manager Error:', message);
        
        // Try to show error in UI if there's an error container
        const errorContainer = document.getElementById('error-container');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    ${message}
                </div>
            `;
            errorContainer.style.display = 'block';
        }
    }

    /**
     * Get current terminal data
     */
    getTerminal() {
        return this.terminal;
    }

    /**
     * Refresh terminal data
     */
    async refresh() {
        await this.loadTerminal();
        this.updateUI();
    }
}

// Initialize global terminal manager
const terminalManager = new TerminalManager();

// Export for use in other modules
window.terminalManager = terminalManager;