/**
 * Status Badge Component
 * Handles creation and interaction with status badges across the application
 */

class StatusBadge {
    constructor() {
        this.initEventListeners();
    }

    /**
     * Initialize event listeners for status badges
     */
    initEventListeners() {
        // Event delegation for status legend toggles
        document.addEventListener('click', (event) => {
            const toggle = event.target.closest('.status-legend-toggle');
            if (toggle) {
                const badge = toggle.closest('.status-badge');
                const legend = badge.querySelector('.status-legend');
                if (legend) {
                    legend.classList.toggle('active');
                }
            }
            
            // Close legends when clicking outside
            if (!event.target.closest('.status-badge')) {
                document.querySelectorAll('.status-legend.active').forEach(legend => {
                    legend.classList.remove('active');
                });
            }
        });
    }

    /**
     * Creates a status badge element
     * @param {string} status - The status text
     * @param {string} type - The type of status (vessel, assignment)
     * @param {number} count - Optional count to display
     * @param {string} icon - Optional icon class
     * @returns {HTMLElement} The status badge element
     */
    createBadge(status, type = 'vessel', count = null, icon = null) {
        // Normalize status (lowercase, replace spaces with underscores)
        const normalizedStatus = this.normalizeStatus(status);
        
        // Create badge element
        const badge = document.createElement('div');
        badge.className = `status-badge ${normalizedStatus}`;
        badge.setAttribute('data-status', normalizedStatus);
        badge.setAttribute('data-type', type);
        
        // Add icon if provided
        if (icon) {
            const iconEl = document.createElement('i');
            iconEl.className = `icon ${icon}`;
            badge.appendChild(iconEl);
        }
        
        // Add status text
        const statusText = document.createElement('span');
        statusText.className = 'status-text';
        statusText.textContent = this.formatStatus(status);
        badge.appendChild(statusText);
        
        // Add count if provided
        if (count !== null && count > 0) {
            const countEl = document.createElement('span');
            countEl.className = 'count';
            countEl.textContent = count;
            badge.appendChild(countEl);
        }
        
        // Add info icon for legend toggle
        const infoToggle = document.createElement('span');
        infoToggle.className = 'status-legend-toggle';
        infoToggle.innerHTML = '<i class="fas fa-info-circle"></i>';
        badge.appendChild(infoToggle);
        
        // Create and append the legend
        badge.appendChild(this.createLegend(normalizedStatus, type));
        
        return badge;
    }
    
    /**
     * Creates a status legend element
     * @param {string} status - The normalized status
     * @param {string} type - The type of status
     * @returns {HTMLElement} The legend element
     */
    createLegend(status, type) {
        const legend = document.createElement('div');
        legend.className = 'status-legend';
        
        const title = document.createElement('div');
        title.className = 'status-legend-title';
        title.textContent = `${this.capitalize(type)} Status Information`;
        legend.appendChild(title);
        
        // Add descriptions based on status and type
        const descriptions = this.getStatusDescriptions(status, type);
        descriptions.forEach(desc => {
            const item = document.createElement('div');
            item.className = 'status-legend-item';
            
            const indicator = document.createElement('div');
            indicator.className = `status-legend-indicator ${desc.status}`;
            item.appendChild(indicator);
            
            const label = document.createElement('div');
            label.className = 'status-legend-label';
            label.textContent = `${this.formatStatus(desc.status)}: ${desc.description}`;
            item.appendChild(label);
            
            legend.appendChild(item);
        });
        
        return legend;
    }
    
    /**
     * Get descriptions for statuses
     * @param {string} status - The current status
     * @param {string} type - The type of status
     * @returns {Array} Array of status objects with descriptions
     */
    getStatusDescriptions(status, type) {
        // Main status description
        const descriptions = [{
            status: status,
            description: this.getStatusDescription(status, type)
        }];
        
        // Add related statuses
        const related = this.getRelatedStatuses(status, type);
        related.forEach(relStatus => {
            descriptions.push({
                status: relStatus,
                description: this.getStatusDescription(relStatus, type)
            });
        });
        
        return descriptions;
    }
    
    /**
     * Get description for a specific status
     * @param {string} status - The status
     * @param {string} type - The type of status
     * @returns {string} Description of the status
     */
    getStatusDescription(status, type) {
        const descriptions = {
            vessel: {
                scheduled: "Vessel has been scheduled but has not yet started its journey",
                approaching: "Vessel is en route to the terminal",
                arrived: "Vessel has arrived at the terminal but not yet docked",
                docked: "Vessel is securely moored at the jetty",
                loading: "Vessel is being loaded with cargo",
                unloading: "Vessel is discharging cargo",
                departing: "Vessel is preparing to leave the terminal",
                departed: "Vessel has left the terminal",
                delayed: "Vessel's arrival or operations are delayed",
                cancelled: "Vessel's scheduled visit has been cancelled"
            },
            assignment: {
                active: "Assignment is currently in progress",
                completed: "Assignment has been successfully completed",
                pending_approval: "Assignment is waiting for approval"
            }
        };
        
        return descriptions[type]?.[status] || "No description available";
    }
    
    /**
     * Get related statuses for a specific status
     * @param {string} status - The current status
     * @param {string} type - The type of status
     * @returns {Array} Array of related status strings
     */
    getRelatedStatuses(status, type) {
        const relationMap = {
            vessel: {
                scheduled: ["approaching"],
                approaching: ["scheduled", "arrived"],
                arrived: ["approaching", "docked"],
                docked: ["arrived", "loading", "unloading"],
                loading: ["docked", "departing"],
                unloading: ["docked", "departing"],
                departing: ["departed"],
                departed: [],
                delayed: [],
                cancelled: []
            },
            assignment: {
                active: ["completed"],
                completed: [],
                pending_approval: ["active"]
            }
        };
        
        return relationMap[type]?.[status] || [];
    }
    
    /**
     * Replace existing status badges with normalized ones
     */
    applyToExistingBadges() {
        document.querySelectorAll('[data-status]').forEach(element => {
            const status = element.getAttribute('data-status');
            const type = element.getAttribute('data-type') || 'vessel';
            
            if (status && !element.classList.contains('status-badge')) {
                const parent = element.parentNode;
                const badge = this.createBadge(status, type);
                parent.replaceChild(badge, element);
            }
        });
    }
    
    /**
     * Normalize status string for CSS classes
     * @param {string} status - The status string to normalize
     * @returns {string} Normalized status string
     */
    normalizeStatus(status) {
        return status.toLowerCase().replace(/[\s-]/g, '_');
    }
    
    /**
     * Format status for display
     * @param {string} status - The status to format
     * @returns {string} Formatted status string
     */
    formatStatus(status) {
        return status
            .replace(/_/g, ' ')
            .split(' ')
            .map(word => this.capitalize(word))
            .join(' ');
    }
    
    /**
     * Capitalize first letter of a string
     * @param {string} string - String to capitalize
     * @returns {string} Capitalized string
     */
    capitalize(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }
}

// Initialize the status badge component
const statusBadge = new StatusBadge();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = statusBadge;
} 