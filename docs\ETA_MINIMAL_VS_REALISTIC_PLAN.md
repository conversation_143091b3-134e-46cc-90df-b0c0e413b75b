# ETA Minimal vs Realistic — Research and Plan

This document traces how ETA is entered, stored, surfaced, and used today; identifies inconsistencies; and outlines a plan to evolve toward a consistent “minimal vs realistic” ETA model across API, UI, and optimization logic.

Note: Occurrences of “you” refer to the project maintainer/implementer.

## Flow Overview

- Nomination form: [src/templates/nomination.html](src/templates/nomination.html:1587) has the ETA input (name="eta"). Client logic pre-fills defaults and can fill from AIS search results if present.
- Client submit: [src/static/js/nomination.js](src/static/js/nomination.js:908-933), [src/static/js/nomination.js](src/static/js/nomination.js:980-1019) posts to /api/nominations.
- Backend create: [src/api/fastapi_app.py](src/api/fastapi_app.py:2816-3037)
  - Accepts eta in NewNominationRequest ([src/api/fastapi_app.py](src/api/fastapi_app.py:393-411)).
  - Creates a Vessel and sets vessel.eta and arrival_time from nomination ([src/api/fastapi_app.py](src/api/fastapi_app.py:2878-2884)).
  - Persists a nominations row with ETA fields ([src/api/fastapi_app.py](src/api/fastapi_app.py:2903-2920)).
  - Creates registry/visit links with ETA fields ([src/api/fastapi_app.py](src/api/fastapi_app.py:2928-2946)).
- Unscheduled view: [src/templates/schedule.html](src/templates/schedule.html) fetches /api/vessels and renders unscheduled vessels (uses only eta).
- Optimization: OR-Tools uses an “effective ETA” constraint based on calculated_eta and confidence, else eta (details below).
- Other pages:
  - Dashboard “Incoming/Waiting”: uses eta only.
  - Nominated vessels: shows both “Original ETA” and “Smart ETA” (client-calculated).

## Data Model (standardized ETA fields)

- Nomination: [src/models/nomination.py](src/models/nomination.py:42-46)
  - eta (user/customer), etd, calculated_eta (system), eta_confidence, eta_source.
- Vessel: [src/models/vessel.py](src/models/vessel.py:88-95) same fields, plus actual_arrival shim via arrival_time property.
- DB schemas:
  - [src/db/models.py](src/db/models.py:251-253) (nominations) and [src/db/models.py](src/db/models.py:428-430) (visits) store calculated_eta, eta_confidence, eta_source.

## Where ETAs Are Calculated

- Dynamic ETA (DB-side, periodic): [src/services/dynamic_eta_service.py](src/services/dynamic_eta_service.py)
  - Computes new_calculated_eta with AIS/traffic/weather/historical heuristics and persists nominations.calculated_eta, eta_confidence, eta_source ([src/services/dynamic_eta_service.py](src/services/dynamic_eta_service.py:300-336)).
- ML ETA (optional training/predict): [src/services/ml_eta_service.py](src/services/ml_eta_service.py) produces a predicted ETA (not directly wired to nominations by default).
- Ship tracking “live” ETA: [src/services/ship_tracking_service.py](src/services/ship_tracking_service.py)
  - Maintains tracked ships and calculates calculated_eta from position/speed plus simple tidal/lock heuristics ([src/services/ship_tracking_service.py](src/services/ship_tracking_service.py:196-209), [src/services/ship_tracking_service.py](src/services/ship_tracking_service.py:240-282)).

## Effective ETA for optimization

- [_get_effective_eta()](src/optimization/scheduler.py:417-425): choose calculated_eta if eta_confidence >= 60, else eta.
- Hard constraint: assignment start >= effective ETA + approach_time_hours (“EBR”) ([src/optimization/scheduler.py](src/optimization/scheduler.py:387-399)).
- Objective penalties related to waiting since EBR and deviation from user ETA ([src/optimization/scheduler.py](src/optimization/scheduler.py:596-737), [src/optimization/scheduler.py](src/optimization/scheduler.py:889-927)).
- Call-in timing helper: [calculate_optimal_call_in_time()](src/optimization/scheduler.py:430-483) uses confidence/source to derive a call-in time.

## How ETAs Surface in the UI/APIs

- /api/vessels: returns both eta and calculated_eta for DB vessels, in-memory vessels, and nominations ([src/api/fastapi_app.py](src/api/fastapi_app.py:1249-1318), [src/api/fastapi_app.py](src/api/fastapi_app.py:1324-1331), [src/api/fastapi_app.py](src/api/fastapi_app.py:1369-1391)).
- Schedule (unscheduled): only shows eta ([src/templates/schedule.html](src/templates/schedule.html:4320); mapping comes from unscheduled build [src/templates/schedule.html](src/templates/schedule.html:2667-2706), [src/templates/schedule.html](src/templates/schedule.html:2684)).
- Dashboard “Incoming/Waiting”: only uses v.eta for countdown and sorting ([src/templates/index.html](src/templates/index.html:510-512), [src/templates/index.html](src/templates/index.html:546-556)).
- Nominated Vessels (Smart ETA): shows “Original ETA” vs “Smart ETA” (client calculated_eta) with confidence and geofences ([src/templates/nominated_vessels.html](src/templates/nominated_vessels.html:146-154), [src/templates/nominated_vessels.html](src/templates/nominated_vessels.html:199-220); logic in [src/static/js/nominated-vessels.js](src/static/js/nominated-vessels.js:240-271), [src/static/js/nominated-vessels.js](src/static/js/nominated-vessels.js:600-700)).
- ETA shortlist endpoints collate nomination ETA + latest visit ETA: [src/api/fastapi_app.py](src/api/fastapi_app.py:658-688).

## Inconsistencies Identified

- UI inconsistency:
  - Schedule unscheduled and Dashboard Incoming/Waiting render only eta (user-entered), ignoring calculated_eta even when confidence is high.
  - Nominated Vessels shows both and already applies a smarter calculation.
- Optimization fallback path inconsistency:
  - The “fill_unassigned” greedy step uses v.eta only, not the same “effective eta” logic as the main model ([src/api/fastapi_app.py](src/api/fastapi_app.py:4359-4392)). This can diverge from the solver’s treatment.
- Prefill behavior:
  - Nomination prefill uses AIS-provided vessel.eta if the AIS search returns it ([src/static/js/nomination.js](src/static/js/nomination.js:1473-1500)). Many AIS ETAs are “to next port” and not trustworthy. No built-in fallback to compute a minimal travel time based on position when eta is missing.

## Minimal vs Realistic ETA (proposal)

Your requested distinction aligns well with concepts already in the code:

- Minimal (call-in) ETA: “if we call now, earliest arrival” = now + travel_time (+ mandatory external buffers like approach/locks/tide). This exists implicitly as “EBR” in the scheduler (effective ETA + approach).
- Realistic (en-route) ETA: “the best prediction while the vessel is genuinely on its way” = AIS/ML driven calculated_eta.

## Recommended mapping and enhancements

- Keep eta as “user/nomination ETA”.
- Use calculated_eta as the “realistic/en-route ETA” when eta_source ∈ {ais_calculated, ml_predicted} with confidence.
- Add a new derived field server-side for consistency:
  - effective_eta: mirror of scheduler logic (calculated_eta if eta_confidence >= 60 else eta) for API consumers.
  - ebr_time: effective_eta + approach_time_hours for operational displays and schedule gating.
- Optionally add separate persisted fields if you want a durable distinction:
  - min_eta (or call_in_eta): earliest feasible arrival if called now.
  - enroute_eta: realistic ETA given current movement.
- These can live first in nominations.extra_data (no migration), then later become columns.

## Concrete Fixes to Align Behavior

- Server/API: return consistent fields
  - Add effective_eta and ebr_time to /api/vessels responses using the same logic as [_get_effective_eta()](src/optimization/scheduler.py:417-425) so all UIs can consume one consistent field.
  - File: [src/api/fastapi_app.py](src/api/fastapi_app.py:1249-1318) in get_vessels_api after each vessel dict is formed; also [src/api/fastapi_app.py](src/api/fastapi_app.py:1369-1391).
- UI: use effective_eta everywhere
  - Schedule page unscheduled table: display effective_eta and optionally a badge indicating source (“User” vs “Smart”) with confidence.
  - File: [src/templates/schedule.html](src/templates/schedule.html:4320) and list building around [src/templates/schedule.html](src/templates/schedule.html:2667-2706).
  - Dashboard Incoming/Waiting: sort and display using effective_eta rather than eta.
  - File: [src/templates/index.html](src/templates/index.html:510-512), [src/templates/index.html](src/templates/index.html:546-556).
- Optimization consistency:
  - In the greedy “fill_unassigned” path, use scheduler._get_effective_eta(v) rather than v.eta ([src/api/fastapi_app.py](src/api/fastapi_app.py:4359-4392)). This aligns post-solver fill with the model’s constraints.
- Prefill ETA on AIS select (minimal ETA)
  - In [src/static/js/nomination.js](src/static/js/nomination.js:1473-1500) inside selectVessel() / populateVesselForm():
    - If vessel.eta is missing or looks non-sensical, compute a minimal ETA from AIS position:
      - Haversine distance to terminal, apply a waterways factor, divide by speed (fallback to 8 kn), add simple lock/tide buffers (like in [src/static/js/nominated-vessels.js](src/static/js/nominated-vessels.js:280-340)), and set #vessel-eta accordingly.
    - Optionally show a toggle: “Use Smart Minimal ETA” vs “Keep Entered ETA”.
  - This gives a solid “minimal ETA” at nomination time without waiting for backend dynamic services.

## Where to Implement (precise references)

- Add effective fields in API:
  - [src/api/fastapi_app.py](src/api/fastapi_app.py:1249-1318) and [src/api/fastapi_app.py](src/api/fastapi_app.py:1369-1391):
    - After building each vessel dict, compute:
      - effective_eta = calculated_eta if eta_confidence >= 60 else eta
      - ebr_time = (effective_eta + approach_time_hours), with approach_time_hours from a setting or default 2.
- Fix greedy fill to use effective ETA:
  - [src/api/fastapi_app.py](src/api/fastapi_app.py:4359-4392):
    - Replace ebr = getattr(v, 'eta', None) block with:
      - eff = scheduler._get_effective_eta(v) or scheduler.start_time
      - ebr = eff + timedelta(hours=int(scheduler.approach_time_hours))
- Update schedule/index pages:
  - [src/templates/schedule.html](src/templates/schedule.html:4320) and associated mapping creation [src/templates/schedule.html](src/templates/schedule.html:2667-2706) to use effective_eta.
  - [src/templates/index.html](src/templates/index.html:510-512) and [src/templates/index.html](src/templates/index.html:546-556) to use effective_eta for display/sort.
- Prefill minimal ETA in nomination:
  - [src/static/js/nomination.js](src/static/js/nomination.js:1473-1500) (inside populateVesselForm) and/or [src/static/js/nomination.js](src/static/js/nomination.js:1555) (after setting identifiers):
    - When AIS vessel.eta absent/unreliable, calculate minimal ETA (copy small helper functions from [src/static/js/nominated-vessels.js](src/static/js/nominated-vessels.js:292-340) simplified for nomination step).

## Notes on Current Calculators

- The “Smart ETA” page already does the right thing for visualization: it computes a client-side calculated_eta and uses smartETA for display ([src/static/js/nominated-vessels.js](src/static/js/nominated-vessels.js:240-271), [src/static/js/nominated-vessels.js](src/static/js/nominated-vessels.js:629-663)).
- DB-side dynamic ETA service updates nominations.calculated_eta (even if heuristics are currently simplistic), which the scheduler already respects (confidence ≥ 60).

## Optional—minimal vs realistic ETAs as first-class fields

- Near-term without DB migrations:
  - Emit server-side effective_eta, ebr_time in /api/vessels.
  - Emit optional min_eta into each vessel’s metadata based on distance/speed if AIS position exists.
- Longer-term (DB-backed):
  - Add min_eta and enroute_eta columns to nominations and propagate to visits for history.
  - Set eta_source to minimal vs ais_calculated/ml_predicted based on mode.

## Offer to implement

Options:

- Add effective_eta/ebr_time to /api/vessels and switch Schedule/Dashboard to use it?
- Patch the greedy “fill_unassigned” to use effective ETA?
- Add nomination prefill of minimal ETA from AIS selection?

These changes are small and surgical and will remove the cross-page inconsistencies while providing the desired minimal vs realistic distinction.