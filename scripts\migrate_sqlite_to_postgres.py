#!/usr/bin/env python3
"""
SQLite to PostgreSQL Migration Script

This script migrates data from the existing SQLite database to PostgreSQL
using SQLAlchemy models and sessions. It handles the migration in the correct
order to respect foreign key constraints.

Usage:
    python scripts/migrate_sqlite_to_postgres.py [--dry-run] [--batch-size=1000]

Environment variables required:
    DATABASE_URL: PostgreSQL connection string
    DB_BACKEND: Set to "postgres" for the migration target

The script will:
1. Read data from the SQLite database (data/jetty_planner.db)
2. Insert data into PostgreSQL in the correct order
3. Provide verification counts and reports
4. Handle any data type conversions needed
"""

import argparse
import os
import sys
import sqlite3
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from db.models import (
    Terminal, Setting, Assignment, Vessel, Jetty, Tank, <PERSON>ump, 
    Surveyor, Cargo, AssignmentChange
)
from db.session import get_db_session, create_tables

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SQLiteToPostgreSQLMigrator:
    """Handles the migration from SQLite to PostgreSQL."""
    
    def __init__(self, sqlite_path: str = "data/jetty_planner.db", batch_size: int = 1000):
        self.sqlite_path = sqlite_path
        self.batch_size = batch_size
        self.verification_results = {}
        
    def get_sqlite_connection(self):
        """Get connection to SQLite database."""
        return sqlite3.connect(self.sqlite_path)
    
    def get_postgres_session(self):
        """Get PostgreSQL session."""
        return get_db_session()
    
    def migrate_terminals(self, dry_run: bool = False) -> int:
        """Migrate terminals table."""
        logger.info("Migrating terminals...")
        
        with self.get_sqlite_connection() as sqlite_conn:
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT id, name, location_lat, location_lon, total_capacity_cbm, 
                       number_of_tanks, draft_meters, operational_since, 
                       vessel_berths, barge_berths, timezone, currency, is_active,
                       created_at, updated_at
                FROM terminals
            """)
            
            rows = cursor.fetchall()
            
        if dry_run:
            logger.info(f"Would migrate {len(rows)} terminals")
            return len(rows)
            
        migrated_count = 0
        with self.get_postgres_session() as pg_session:
            for row in rows:
                # Convert date string to datetime if needed
                operational_since = row[7]
                if isinstance(operational_since, str):
                    try:
                        operational_since = datetime.fromisoformat(operational_since)
                    except ValueError:
                        operational_since = datetime.strptime(operational_since, '%Y-%m-%d')
                
                # Convert timestamps
                created_at = self._parse_timestamp(row[13])
                updated_at = self._parse_timestamp(row[14])
                
                terminal = Terminal(
                    id=row[0],
                    name=row[1],
                    location_lat=row[2],
                    location_lon=row[3],
                    total_capacity_cbm=row[4],
                    number_of_tanks=row[5],
                    draft_meters=row[6],
                    operational_since=operational_since,
                    vessel_berths=row[8],
                    barge_berths=row[9],
                    timezone=row[10],
                    currency=row[11],
                    is_active=bool(row[12]),
                    created_at=created_at,
                    updated_at=updated_at
                )
                
                # Use merge to handle duplicates
                existing = pg_session.query(Terminal).filter_by(id=row[0]).first()
                if existing:
                    logger.info(f"Updating existing terminal: {row[0]}")
                    for attr in ['name', 'location_lat', 'location_lon', 'total_capacity_cbm', 
                               'number_of_tanks', 'draft_meters', 'operational_since', 'vessel_berths',
                               'barge_berths', 'timezone', 'currency', 'is_active']:
                        setattr(existing, attr, getattr(terminal, attr))
                else:
                    pg_session.add(terminal)
                    
                migrated_count += 1
                
            pg_session.commit()
            
        logger.info(f"Migrated {migrated_count} terminals")
        return migrated_count
    
    def migrate_settings(self, dry_run: bool = False) -> int:
        """Migrate settings table."""
        logger.info("Migrating settings...")
        
        with self.get_sqlite_connection() as sqlite_conn:
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT key, terminal_id, value, category, is_sensitive,
                       created_at, updated_at
                FROM settings
            """)
            
            rows = cursor.fetchall()
            
        if dry_run:
            logger.info(f"Would migrate {len(rows)} settings")
            return len(rows)
            
        migrated_count = 0
        with self.get_postgres_session() as pg_session:
            for row in rows:
                created_at = self._parse_timestamp(row[5])
                updated_at = self._parse_timestamp(row[6])
                
                setting = Setting(
                    key=row[0],
                    terminal_id=row[1],
                    value=row[2],
                    category=row[3],
                    is_sensitive=bool(row[4]),
                    created_at=created_at,
                    updated_at=updated_at
                )
                
                # Use merge to handle duplicates
                existing = pg_session.query(Setting).filter_by(key=row[0], terminal_id=row[1]).first()
                if existing:
                    logger.debug(f"Updating existing setting: {row[0]} for terminal {row[1]}")
                    existing.value = row[2]
                    existing.category = row[3]
                    existing.is_sensitive = bool(row[4])
                    existing.updated_at = updated_at
                else:
                    pg_session.add(setting)
                    
                migrated_count += 1
                
            pg_session.commit()
            
        logger.info(f"Migrated {migrated_count} settings")
        return migrated_count
    
    def migrate_vessels(self, dry_run: bool = False) -> int:
        """Migrate vessels table."""
        logger.info("Migrating vessels...")
        
        with self.get_sqlite_connection() as sqlite_conn:
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT id, terminal_id, name, type, created_at
                FROM vessels
            """)
            
            rows = cursor.fetchall()
            
        if dry_run:
            logger.info(f"Would migrate {len(rows)} vessels")
            return len(rows)
            
        migrated_count = 0
        with self.get_postgres_session() as pg_session:
            for row in rows:
                created_at = self._parse_timestamp(row[4])
                
                vessel = Vessel(
                    id=row[0],
                    terminal_id=row[1],
                    name=row[2],
                    type=row[3],
                    created_at=created_at
                )
                
                # Use merge to handle duplicates
                existing = pg_session.query(Vessel).filter_by(id=row[0]).first()
                if existing:
                    logger.debug(f"Updating existing vessel: {row[0]}")
                    existing.terminal_id = row[1]
                    existing.name = row[2]
                    existing.type = row[3]
                else:
                    pg_session.add(vessel)
                    
                migrated_count += 1
                
            pg_session.commit()
            
        logger.info(f"Migrated {migrated_count} vessels")
        return migrated_count
    
    def migrate_jetties(self, dry_run: bool = False) -> int:
        """Migrate jetties table."""
        logger.info("Migrating jetties...")
        
        with self.get_sqlite_connection() as sqlite_conn:
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT id, terminal_id, name, type, vessel_type_restriction,
                       min_dwt, max_dwt, min_loa, max_loa, max_beam, max_draft,
                       primary_use, max_flow_rate, is_operational, created_at
                FROM jetties
            """)
            
            rows = cursor.fetchall()
            
        if dry_run:
            logger.info(f"Would migrate {len(rows)} jetties")
            return len(rows)
            
        migrated_count = 0
        with self.get_postgres_session() as pg_session:
            for row in rows:
                created_at = self._parse_timestamp(row[14])
                
                jetty = Jetty(
                    id=row[0],
                    terminal_id=row[1],
                    name=row[2],
                    type=row[3],
                    vessel_type_restriction=row[4],
                    min_dwt=row[5],
                    max_dwt=row[6],
                    min_loa=row[7],
                    max_loa=row[8],
                    max_beam=row[9],
                    max_draft=row[10],
                    primary_use=row[11],
                    max_flow_rate=row[12],
                    is_operational=bool(row[13]),
                    created_at=created_at
                )
                
                # Use merge to handle duplicates
                existing = pg_session.query(Jetty).filter_by(id=row[0]).first()
                if existing:
                    logger.debug(f"Updating existing jetty: {row[0]}")
                    for attr in ['terminal_id', 'name', 'type', 'vessel_type_restriction',
                               'min_dwt', 'max_dwt', 'min_loa', 'max_loa', 'max_beam',
                               'max_draft', 'primary_use', 'max_flow_rate', 'is_operational']:
                        setattr(existing, attr, getattr(jetty, attr))
                else:
                    pg_session.add(jetty)
                    
                migrated_count += 1
                
            pg_session.commit()
            
        logger.info(f"Migrated {migrated_count} jetties")
        return migrated_count
    
    def migrate_tanks(self, dry_run: bool = False) -> int:
        """Migrate tanks table."""
        logger.info("Migrating tanks...")
        
        with self.get_sqlite_connection() as sqlite_conn:
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT id, terminal_id, name, type, capacity, current_level,
                       product_type, created_at, updated_at
                FROM tanks
            """)
            
            rows = cursor.fetchall()
            
        if dry_run:
            logger.info(f"Would migrate {len(rows)} tanks")
            return len(rows)
            
        migrated_count = 0
        with self.get_postgres_session() as pg_session:
            for row in rows:
                created_at = self._parse_timestamp(row[7])
                updated_at = self._parse_timestamp(row[8])
                
                tank = Tank(
                    id=row[0],
                    terminal_id=row[1],
                    name=row[2],
                    type=row[3],
                    capacity=row[4],
                    current_level=row[5],
                    product_type=row[6],
                    created_at=created_at,
                    updated_at=updated_at
                )
                
                pg_session.merge(tank)
                migrated_count += 1
                
            pg_session.commit()
            
        logger.info(f"Migrated {migrated_count} tanks")
        return migrated_count
    
    def migrate_pumps(self, dry_run: bool = False) -> int:
        """Migrate pumps table."""
        logger.info("Migrating pumps...")
        
        with self.get_sqlite_connection() as sqlite_conn:
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT id, terminal_id, name, type, flow_rate, status,
                       created_at, updated_at
                FROM pumps
            """)
            
            rows = cursor.fetchall()
            
        if dry_run:
            logger.info(f"Would migrate {len(rows)} pumps")
            return len(rows)
            
        migrated_count = 0
        with self.get_postgres_session() as pg_session:
            for row in rows:
                created_at = self._parse_timestamp(row[6])
                updated_at = self._parse_timestamp(row[7])
                
                pump = Pump(
                    id=row[0],
                    terminal_id=row[1],
                    name=row[2],
                    type=row[3],
                    flow_rate=row[4],
                    status=row[5],
                    created_at=created_at,
                    updated_at=updated_at
                )
                
                pg_session.merge(pump)
                migrated_count += 1
                
            pg_session.commit()
            
        logger.info(f"Migrated {migrated_count} pumps")
        return migrated_count
    
    def migrate_surveyors(self, dry_run: bool = False) -> int:
        """Migrate surveyors table."""
        logger.info("Migrating surveyors...")
        
        with self.get_sqlite_connection() as sqlite_conn:
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT id, terminal_id, name, status, created_at, updated_at
                FROM surveyors
            """)
            
            rows = cursor.fetchall()
            
        if dry_run:
            logger.info(f"Would migrate {len(rows)} surveyors")
            return len(rows)
            
        migrated_count = 0
        with self.get_postgres_session() as pg_session:
            for row in rows:
                created_at = self._parse_timestamp(row[4])
                updated_at = self._parse_timestamp(row[5])
                
                surveyor = Surveyor(
                    id=row[0],
                    terminal_id=row[1],
                    name=row[2],
                    status=row[3],
                    created_at=created_at,
                    updated_at=updated_at
                )
                
                pg_session.merge(surveyor)
                migrated_count += 1
                
            pg_session.commit()
            
        logger.info(f"Migrated {migrated_count} surveyors")
        return migrated_count
    
    def migrate_cargoes(self, dry_run: bool = False) -> int:
        """Migrate cargoes table."""
        logger.info("Migrating cargoes...")
        
        with self.get_sqlite_connection() as sqlite_conn:
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT id, terminal_id, vessel_id, product, volume, is_loading,
                       connection_size, vapor_return, nitrogen_purge,
                       created_at, updated_at
                FROM cargoes
            """)
            
            rows = cursor.fetchall()
            
        if dry_run:
            logger.info(f"Would migrate {len(rows)} cargoes")
            return len(rows)
            
        migrated_count = 0
        with self.get_postgres_session() as pg_session:
            for row in rows:
                created_at = self._parse_timestamp(row[9])
                updated_at = self._parse_timestamp(row[10])
                
                cargo = Cargo(
                    id=row[0],
                    terminal_id=row[1],
                    vessel_id=row[2],
                    product=row[3],
                    volume=row[4],
                    is_loading=bool(row[5]),
                    connection_size=row[6],
                    vapor_return=bool(row[7]),
                    nitrogen_purge=bool(row[8]),
                    created_at=created_at,
                    updated_at=updated_at
                )
                
                pg_session.merge(cargo)
                migrated_count += 1
                
            pg_session.commit()
            
        logger.info(f"Migrated {migrated_count} cargoes")
        return migrated_count
    
    def migrate_assignments(self, dry_run: bool = False) -> int:
        """Migrate assignments table."""
        logger.info("Migrating assignments...")
        
        with self.get_sqlite_connection() as sqlite_conn:
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT id, terminal_id, vessel_id, vessel_name, vessel_type,
                       jetty_name, cargo_product, cargo_volume,
                       start_time, end_time, status, created_at, updated_at
                FROM assignments
            """)
            
            rows = cursor.fetchall()
            
        if dry_run:
            logger.info(f"Would migrate {len(rows)} assignments")
            return len(rows)
            
        migrated_count = 0
        with self.get_postgres_session() as pg_session:
            for row in rows:
                start_time = self._parse_timestamp(row[8])
                end_time = self._parse_timestamp(row[9])
                created_at = self._parse_timestamp(row[11])
                updated_at = self._parse_timestamp(row[12])
                
                assignment = Assignment(
                    id=row[0],
                    terminal_id=row[1],
                    vessel_id=row[2],
                    vessel_name=row[3],
                    vessel_type=row[4],
                    jetty_name=row[5],
                    cargo_product=row[6],
                    cargo_volume=row[7],
                    start_time=start_time,
                    end_time=end_time,
                    status=row[10],
                    created_at=created_at,
                    updated_at=updated_at
                )
                
                pg_session.merge(assignment)
                migrated_count += 1
                
            pg_session.commit()
            
        logger.info(f"Migrated {migrated_count} assignments")
        return migrated_count
    
    def migrate_assignment_changes(self, dry_run: bool = False) -> int:
        """Migrate assignment_changes table."""
        logger.info("Migrating assignment changes...")
        
        with self.get_sqlite_connection() as sqlite_conn:
            cursor = sqlite_conn.cursor()
            cursor.execute("""
                SELECT id, terminal_id, assignment_id, vessel_id, vessel_name,
                       jetty_name, old_start_time, old_end_time,
                       new_start_time, new_end_time, reason, changed_by, changed_at
                FROM assignment_changes
            """)
            
            rows = cursor.fetchall()
            
        if dry_run:
            logger.info(f"Would migrate {len(rows)} assignment changes")
            return len(rows)
            
        migrated_count = 0
        with self.get_postgres_session() as pg_session:
            for row in rows:
                old_start_time = self._parse_timestamp(row[6])
                old_end_time = self._parse_timestamp(row[7])
                new_start_time = self._parse_timestamp(row[8])
                new_end_time = self._parse_timestamp(row[9])
                changed_at = self._parse_timestamp(row[12])
                
                change = AssignmentChange(
                    id=row[0],
                    terminal_id=row[1],
                    assignment_id=row[2],
                    vessel_id=row[3],
                    vessel_name=row[4],
                    jetty_name=row[5],
                    old_start_time=old_start_time,
                    old_end_time=old_end_time,
                    new_start_time=new_start_time,
                    new_end_time=new_end_time,
                    reason=row[10],
                    changed_by=row[11],
                    changed_at=changed_at
                )
                
                pg_session.merge(change)
                migrated_count += 1
                
            pg_session.commit()
            
        logger.info(f"Migrated {migrated_count} assignment changes")
        return migrated_count
    
    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """Parse timestamp string to datetime object."""
        if not timestamp_str:
            return None
            
        # Handle various timestamp formats
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%dT%H:%M:%S.%f',
            '%Y-%m-%dT%H:%M:%S.%fZ'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(timestamp_str, fmt)
            except ValueError:
                continue
                
        # If none of the formats work, try fromisoformat
        try:
            return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        except ValueError:
            logger.warning(f"Could not parse timestamp: {timestamp_str}")
            return datetime.utcnow()
    
    def verify_migration(self) -> Dict[str, Dict[str, int]]:
        """Verify migration by comparing record counts."""
        logger.info("Verifying migration...")
        
        verification_results = {}
        
        # Check each table
        tables = [
            ('terminals', Terminal),
            ('settings', Setting),
            ('vessels', Vessel),
            ('jetties', Jetty),
            ('tanks', Tank),
            ('pumps', Pump),
            ('surveyors', Surveyor),
            ('cargoes', Cargo),
            ('assignments', Assignment),
            ('assignment_changes', AssignmentChange)
        ]
        
        with self.get_sqlite_connection() as sqlite_conn:
            sqlite_cursor = sqlite_conn.cursor()
            
            with self.get_postgres_session() as pg_session:
                for table_name, model_class in tables:
                    # Get SQLite count
                    try:
                        sqlite_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        sqlite_count = sqlite_cursor.fetchone()[0]
                    except sqlite3.OperationalError:
                        sqlite_count = 0
                    
                    # Get PostgreSQL count
                    pg_count = pg_session.query(model_class).count()
                    
                    verification_results[table_name] = {
                        'sqlite': sqlite_count,
                        'postgres': pg_count,
                        'match': sqlite_count == pg_count
                    }
                    
                    logger.info(f"{table_name}: SQLite={sqlite_count}, PostgreSQL={pg_count}, Match={sqlite_count == pg_count}")
        
        return verification_results
    
    def run_full_migration(self, dry_run: bool = False) -> Dict[str, int]:
        """Run the complete migration in the correct order."""
        logger.info(f"Starting {'dry run' if dry_run else 'full migration'}...")
        
        if not dry_run:
            # Create tables first
            create_tables()
            logger.info("Created database tables")
        
        results = {}
        
        # Migration order respects foreign key dependencies
        migration_steps = [
            ('terminals', self.migrate_terminals),
            ('settings', self.migrate_settings),
            ('vessels', self.migrate_vessels),
            ('jetties', self.migrate_jetties),
            ('tanks', self.migrate_tanks),
            ('pumps', self.migrate_pumps),
            ('surveyors', self.migrate_surveyors),
            ('cargoes', self.migrate_cargoes),
            ('assignments', self.migrate_assignments),
            ('assignment_changes', self.migrate_assignment_changes)
        ]
        
        try:
            for step_name, migration_func in migration_steps:
                results[step_name] = migration_func(dry_run)
                
            if not dry_run:
                # Verify the migration
                verification = self.verify_migration()
                results['verification'] = verification
                
                # Write verification report
                self._write_verification_report(verification)
                
        except Exception as e:
            logger.error(f"Migration failed: {str(e)}")
            raise
        
        logger.info(f"Migration {'dry run' if dry_run else 'completed'} successfully")
        return results
    
    def _write_verification_report(self, verification: Dict[str, Dict[str, int]]):
        """Write verification report to file."""
        os.makedirs('reports', exist_ok=True)
        report_path = f'reports/migration_verification_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'
        
        with open(report_path, 'w') as f:
            f.write(f"SQLite to PostgreSQL Migration Verification Report\\n")
            f.write(f"Generated: {datetime.now().isoformat()}\\n")
            f.write(f"{'='*60}\\n\\n")
            
            total_sqlite = 0
            total_postgres = 0
            all_match = True
            
            for table_name, counts in verification.items():
                sqlite_count = counts['sqlite']
                postgres_count = counts['postgres']
                match = counts['match']
                
                total_sqlite += sqlite_count
                total_postgres += postgres_count
                
                if not match:
                    all_match = False
                
                status = "✓ MATCH" if match else "✗ MISMATCH"
                f.write(f"{table_name:20} | SQLite: {sqlite_count:6} | PostgreSQL: {postgres_count:6} | {status}\\n")
            
            f.write(f"\\n{'='*60}\\n")
            f.write(f"{'TOTALS':20} | SQLite: {total_sqlite:6} | PostgreSQL: {total_postgres:6} | {'✓ ALL MATCH' if all_match else '✗ MISMATCHES FOUND'}\\n")
            
            if all_match:
                f.write(f"\\n✅ Migration verification PASSED - all record counts match\\n")
            else:
                f.write(f"\\n❌ Migration verification FAILED - record count mismatches found\\n")
        
        logger.info(f"Verification report written to: {report_path}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Migrate data from SQLite to PostgreSQL")
    parser.add_argument('--dry-run', action='store_true',
                       help='Perform a dry run without actually migrating data')
    parser.add_argument('--batch-size', type=int, default=1000,
                       help='Batch size for bulk operations (default: 1000)')
    parser.add_argument('--sqlite-path', default='data/jetty_planner.db',
                       help='Path to SQLite database file')
    
    args = parser.parse_args()
    
    # Check prerequisites
    if not os.path.exists(args.sqlite_path):
        logger.error(f"SQLite database not found: {args.sqlite_path}")
        sys.exit(1)
    
    if not args.dry_run:
        if not os.getenv('DATABASE_URL'):
            logger.error("DATABASE_URL environment variable is required for migration")
            sys.exit(1)
        
        if os.getenv('DB_BACKEND', '').lower() != 'postgres':
            logger.error("DB_BACKEND must be set to 'postgres' for migration")
            sys.exit(1)
    
    # Run migration
    migrator = SQLiteToPostgreSQLMigrator(
        sqlite_path=args.sqlite_path,
        batch_size=args.batch_size
    )
    
    try:
        results = migrator.run_full_migration(dry_run=args.dry_run)
        
        # Print summary
        total_migrated = sum(count for key, count in results.items() 
                           if key != 'verification' and isinstance(count, int))
        
        if args.dry_run:
            print(f"\\n🔍 Dry run completed - would migrate {total_migrated} total records")
        else:
            print(f"\\n✅ Migration completed successfully - migrated {total_migrated} total records")
            
            if 'verification' in results:
                verification = results['verification']
                all_match = all(counts['match'] for counts in verification.values())
                if all_match:
                    print("✅ Verification passed - all record counts match")
                else:
                    print("⚠️  Verification found mismatches - check the verification report")
                    
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()