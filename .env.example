﻿# Application
PRODUCTION=true
API_HOST=0.0.0.0
API_PORT=7000
ALLOWED_ORIGINS=https://planner.evosgpt.eu
CORS_ALLOW_CREDENTIALS=false

# API keys (fill in real values or leave blank for test mode)
VESSELFINDER_API_KEY=
WEATHER_API_KEY=
ANTHROPIC_API_KEY=

# PostgreSQL Database Configuration
DB_HOST=localhost
DB_PORT=7432
DB_NAME=planner
DB_USER=postgres
DB_PASSWORD=
SCHEDULER_ENABLED=false

# PostgreSQL connection URL (preferred by the app)
# If left unset, the app will compose this from the variables above
DATABASE_URL=postgresql+psycopg2://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}

# Database connection pool settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# Database debugging
DB_ECHO=false
