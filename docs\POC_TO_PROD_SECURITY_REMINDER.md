# POC → Work Server Security Reminder

When migrating from a personal computer proof‑of‑concept to a work server, apply these changes before go‑live.

## Checklist

- Secrets & Credentials
  - Rotate all keys (<PERSON><PERSON>, Anthropic, DB); revoke old ones.
  - Move secrets out of repo/local .env into a managed source (Compose `--env-file` outside repo, Docker secrets, or vault).
  - Limit DB user to least privilege; separate app, admin, and backup roles.

- Networking & TLS
  - Serve only via HTTPS behind Nginx/Cloudflare; enable HSTS.
  - Bind API to `0.0.0.0` in containers; restrict exposure via reverse proxy / network policy.
  - Ensure WebSocket upgrade headers (AIS/streaming) and proxy headers (`X-Forwarded-*`).

- CORS & Cookies
  - Restrict CORS to `https://planner.evosgpt.eu` (or final domain).
  - Set `CORS_ALLOW_CREDENTIALS` only if cross‑site cookies are required; otherwise keep false.

- Background Jobs
  - Run a dedicated scheduler service (`SCHEDULER_ENABLED=true`) and keep API replicas with `SCHEDULER_ENABLED=false` to avoid duplicate jobs.

- Database
  - Use internal service host/port in Docker (`postgres:5432`); avoid publishing DB to the public internet.
  - Enable backups (already configured) and test restore procedures.
  - Consider TLS for DB if traversing untrusted networks.

- Observability
  - Add health/readiness endpoints to Nginx/CF probes.
  - Centralize logs; rotate; avoid sensitive data in logs.
  - Add basic metrics for job runs, solver runs, API errors.

- App Hardening
  - Tighten Content Security Policy; self‑host fonts if feasible.
  - Review rate limits and request size limits at Nginx.
  - Review data retention and access controls for compliance requirements.

- Documentation & Runbooks
  - Document deployment steps, secret locations, backup/restore, and on‑call procedures.

Keep this file as a pre‑deployment gate before moving to the work server.
