# Emergency Database Restoration Script
# Quick restoration for Jetty Planner database emergencies

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupFile,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipEmergencyBackup = $false
)

# Configuration
$ProjectPath = "C:\Users\<USER>\Jettyplanner"
$BackupPath = "$ProjectPath\backups"
$LogPath = "$ProjectPath\logs"
$AppContainer = "jetty-planning-app"
$BackupContainer = "jetty-postgres-backup"

# Ensure we're in the right directory
Set-Location $ProjectPath

# Logging function
function Write-RestoreLog {
    param($Message, $Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] [$Level] $Message"
    Write-Output $LogMessage
    if (Test-Path $LogPath) {
        Add-Content -Path "$LogPath\emergency-restore.log" -Value $LogMessage
    }
}

function Test-BackupFile {
    param($FilePath)
    
    if (-not (Test-Path $FilePath)) {
        Write-RestoreLog "Backup file not found: $FilePath" "ERROR"
        return $false
    }
    
    $FileSize = (Get-Item $FilePath).Length
    if ($FileSize -lt 1KB) {
        Write-RestoreLog "Backup file appears too small: $FileSize bytes" "ERROR"
        return $false
    }
    
    Write-RestoreLog "Backup file validated: $FilePath ($([math]::Round($FileSize/1KB, 2)) KB)" "INFO"
    return $true
}

function Stop-ApplicationSafely {
    Write-RestoreLog "Stopping application container..." "INFO"
    
    try {
        $Status = docker ps --filter "name=$AppContainer" --format "{{.Status}}"
        if ($Status) {
            docker stop $AppContainer
            Write-RestoreLog "Application stopped successfully" "SUCCESS"
        } else {
            Write-RestoreLog "Application container was not running" "INFO"
        }
        return $true
    }
    catch {
        Write-RestoreLog "Failed to stop application: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function New-EmergencyBackup {
    if ($SkipEmergencyBackup) {
        Write-RestoreLog "Skipping emergency backup as requested" "INFO"
        return $true
    }
    
    Write-RestoreLog "Creating emergency backup before restoration..." "INFO"
    
    try {
        # Check if backup container is running
        $BackupStatus = docker ps --filter "name=$BackupContainer" --format "{{.Status}}"
        if (-not $BackupStatus) {
            Write-RestoreLog "Starting backup container..." "INFO"
            docker-compose -f docker-compose.backup.yml up -d
            Start-Sleep -Seconds 10
        }
        
        # Create emergency backup
        $EmergencyBackupResult = docker exec $BackupContainer backup
        if ($LASTEXITCODE -eq 0) {
            Write-RestoreLog "Emergency backup created successfully" "SUCCESS"
            return $true
        } else {
            Write-RestoreLog "Emergency backup failed, but continuing with restoration..." "WARN"
            return $true  # Don't fail restoration due to emergency backup failure
        }
    }
    catch {
        Write-RestoreLog "Emergency backup error: $($_.Exception.Message)" "WARN"
        return $true  # Don't fail restoration due to emergency backup failure
    }
}

function Invoke-DatabaseRestore {
    param($BackupFilePath)
    
    Write-RestoreLog "Starting database restoration from: $BackupFilePath" "INFO"
    
    try {
        # Ensure backup container is running
        $BackupStatus = docker ps --filter "name=$BackupContainer" --format "{{.Status}}"
        if (-not $BackupStatus) {
            Write-RestoreLog "Starting backup container for restoration..." "INFO"
            docker-compose -f docker-compose.backup.yml up -d
            Start-Sleep -Seconds 15
        }
        
        # Copy backup file to container if it's not already there
        $ContainerBackupPath = "/backups/$(Split-Path $BackupFilePath -Leaf)"
        
        # Method 1: Try using restore command if available
        Write-RestoreLog "Attempting restore using backup container..." "INFO"
        $RestoreResult = docker exec $BackupContainer sh -c "
            if command -v restore >/dev/null 2>&1; then
                restore '$ContainerBackupPath'
            else
                # Fallback to manual psql restore
                psql -h \$POSTGRES_HOST -U \$POSTGRES_USER -d postgres -c 'DROP DATABASE IF EXISTS \$POSTGRES_DB;'
                psql -h \$POSTGRES_HOST -U \$POSTGRES_USER -d postgres -c 'CREATE DATABASE \$POSTGRES_DB;'
                psql -h \$POSTGRES_HOST -U \$POSTGRES_USER -d \$POSTGRES_DB -f '$ContainerBackupPath'
            fi
        "
        
        if ($LASTEXITCODE -eq 0) {
            Write-RestoreLog "Database restoration completed successfully" "SUCCESS"
            return $true
        } else {
            Write-RestoreLog "Database restoration failed with exit code: $LASTEXITCODE" "ERROR"
            return $false
        }
    }
    catch {
        Write-RestoreLog "Database restoration error: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Start-ApplicationSafely {
    Write-RestoreLog "Starting application container..." "INFO"
    
    try {
        docker start $AppContainer
        
        # Wait for startup
        Write-RestoreLog "Waiting for application startup..." "INFO"
        Start-Sleep -Seconds 15
        
        # Check if it started successfully
        $Status = docker ps --filter "name=$AppContainer" --format "{{.Status}}"
        if ($Status -and $Status.Contains("Up")) {
            Write-RestoreLog "Application started successfully: $Status" "SUCCESS"
            return $true
        } else {
            Write-RestoreLog "Application failed to start properly" "ERROR"
            return $false
        }
    }
    catch {
        Write-RestoreLog "Failed to start application: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-ApplicationHealth {
    Write-RestoreLog "Testing application health..." "INFO"
    
    try {
        # Test database connectivity
        $DbTest = docker exec $AppContainer python -c "
from src.database import engine
from sqlalchemy import text
try:
    with engine.connect() as conn:
        result = conn.execute(text('SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ''public'';'))
        print(f'Database tables: {result.scalar()}')
    print('Database connection: OK')
except Exception as e:
    print(f'Database connection: FAILED - {e}')
    exit(1)
"
        
        if ($LASTEXITCODE -eq 0) {
            Write-RestoreLog "Application health check passed" "SUCCESS"
            Write-RestoreLog "Database test result: $DbTest" "INFO"
            return $true
        } else {
            Write-RestoreLog "Application health check failed" "ERROR"
            return $false
        }
    }
    catch {
        Write-RestoreLog "Health check error: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main execution
try {
    Write-RestoreLog "=== EMERGENCY RESTORATION STARTED ===" "INFO"
    Write-RestoreLog "Backup file: $BackupFile" "INFO"
    Write-RestoreLog "Force mode: $Force" "INFO"
    
    # Resolve backup file path
    if (-not [System.IO.Path]::IsPathRooted($BackupFile)) {
        $BackupFilePath = Join-Path $BackupPath $BackupFile
    } else {
        $BackupFilePath = $BackupFile
    }
    
    # Validation
    if (-not (Test-BackupFile $BackupFilePath)) {
        Write-RestoreLog "Backup file validation failed" "ERROR"
        exit 1
    }
    
    # Confirmation (unless force mode)
    if (-not $Force) {
        Write-Output ""
        Write-Output "⚠️  WARNING: This will restore your database from backup!"
        Write-Output "   Backup file: $BackupFilePath"
        Write-Output "   This will OVERWRITE your current database!"
        Write-Output ""
        $Confirmation = Read-Host "Type 'RESTORE' to confirm"
        
        if ($Confirmation -ne 'RESTORE') {
            Write-RestoreLog "Restoration cancelled by user" "INFO"
            exit 0
        }
    }
    
    # Restoration steps
    Write-RestoreLog "Starting restoration process..." "INFO"
    
    # Step 1: Stop application
    if (-not (Stop-ApplicationSafely)) {
        Write-RestoreLog "Failed to stop application safely" "ERROR"
        exit 1
    }
    
    # Step 2: Create emergency backup
    if (-not (New-EmergencyBackup)) {
        Write-RestoreLog "Emergency backup failed" "ERROR"
        if (-not $Force) { exit 1 }
    }
    
    # Step 3: Restore database
    if (-not (Invoke-DatabaseRestore $BackupFilePath)) {
        Write-RestoreLog "Database restoration failed" "ERROR"
        exit 1
    }
    
    # Step 4: Start application
    if (-not (Start-ApplicationSafely)) {
        Write-RestoreLog "Failed to start application after restoration" "ERROR"
        exit 1
    }
    
    # Step 5: Health check
    if (-not (Test-ApplicationHealth)) {
        Write-RestoreLog "Application health check failed after restoration" "ERROR"
        Write-RestoreLog "Consider manual investigation or trying a different backup" "ERROR"
        exit 1
    }
    
    Write-RestoreLog "=== EMERGENCY RESTORATION COMPLETED SUCCESSFULLY ===" "SUCCESS"
    Write-Output ""
    Write-Output "✅ Restoration completed successfully!"
    Write-Output "   Application: http://localhost:7000"
    Write-Output "   Web access: https://planner.evosgpt.eu"
    Write-Output "   Logs: $LogPath\emergency-restore.log"
    Write-Output ""
    
    exit 0
}
catch {
    Write-RestoreLog "Unexpected error during restoration: $($_.Exception.Message)" "ERROR"
    Write-RestoreLog "=== EMERGENCY RESTORATION FAILED ===" "ERROR"
    exit 1
}
