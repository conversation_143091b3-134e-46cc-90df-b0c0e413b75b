# PostgreSQL Database for Local Development
# Run with: docker-compose -f docker-compose.postgres.yml up -d

services:
  postgres:
    image: postgres:16-alpine
    container_name: jetty-postgres-dev
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${DB_NAME:-planner}
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-devpassword}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8
    ports:
      - "${DB_PORT:-4432}:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      # Optional: Initialize with schema
      - ./docker/postgres/initdb:/docker-entrypoint-initdb.d:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -d ${DB_NAME:-planner}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  postgres-data:
    driver: local
