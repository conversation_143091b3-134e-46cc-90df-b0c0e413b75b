#!/usr/bin/env python3
"""
Fix corrupted vessel_id data in assignments table.

This script addresses the issue where some assignments have the literal string 'vessel_id'
instead of an actual vessel identifier. This appears to be a PostgreSQL migration issue.
"""

import sys
import os
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.database import Database

logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

def fix_corrupted_vessel_ids():
    """Fix assignments with corrupted vessel_id values."""
    db = Database()
    
    try:
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            logger.error("No active terminal found")
            return False
            
        # Get all assignments
        assignments = db.get_assignments(terminal_id)
        
        corrupted_assignments = []
        fixed_count = 0
        
        logger.info(f"Checking {len(assignments)} assignments for corrupted vessel_id values...")
        
        for assignment in assignments:
            vessel_id = assignment.get('vessel_id', '')
            assignment_id = assignment.get('id')
            
            # Check for corrupted vessel_id values
            if vessel_id in ['vessel_id', '', None] or (isinstance(vessel_id, str) and vessel_id.strip() == ''):
                corrupted_assignments.append(assignment)
                logger.warning(f"Found corrupted vessel_id '{vessel_id}' in assignment {assignment_id}")
                
                # Try to generate a meaningful vessel_id from available data
                vessel_name = assignment.get('vessel_name', '')
                jetty_name = assignment.get('jetty_name', '')
                
                # Create a fallback vessel_id
                if vessel_name and vessel_name != 'vessel_id' and vessel_name.strip():
                    new_vessel_id = vessel_name.strip()
                elif jetty_name and jetty_name.strip():
                    new_vessel_id = f"vessel_at_{jetty_name.strip().replace(' ', '_')}"
                else:
                    new_vessel_id = f"vessel_{assignment_id}"
                
                logger.info(f"Attempting to fix assignment {assignment_id} with new vessel_id: '{new_vessel_id}'")
                
                # Update the assignment
                try:
                    success = db.update_assignment(assignment_id, {
                        'vessel_id': new_vessel_id,
                        'vessel_name': new_vessel_id if not vessel_name or vessel_name == 'vessel_id' else vessel_name,
                        'vessel_type': assignment.get('vessel_type', 'TANKER'),
                        'jetty_name': assignment.get('jetty_name', ''),
                        'start_time': assignment.get('start_time', ''),
                        'end_time': assignment.get('end_time', ''),
                        'status': assignment.get('status', 'SCHEDULED')
                    })
                    
                    if success:
                        fixed_count += 1
                        logger.info(f"✓ Fixed assignment {assignment_id}")
                        
                        # Log the fix
                        try:
                            db.log_assignment_change(
                                assignment_id=assignment_id,
                                old_start_time=assignment.get('start_time'),
                                old_end_time=assignment.get('end_time'),
                                new_start_time=assignment.get('start_time'),
                                new_end_time=assignment.get('end_time'),
                                reason=f"Fixed corrupted vessel_id from '{vessel_id}' to '{new_vessel_id}'",
                                vessel_id=new_vessel_id,
                                vessel_name=assignment.get('vessel_name', new_vessel_id),
                                jetty_name=assignment.get('jetty_name', ''),
                                changed_by='system_fix_script',
                                terminal_id=terminal_id
                            )
                        except Exception as log_error:
                            logger.warning(f"Failed to log fix for assignment {assignment_id}: {log_error}")
                    else:
                        logger.error(f"✗ Failed to fix assignment {assignment_id}")
                        
                except Exception as e:
                    logger.error(f"✗ Error fixing assignment {assignment_id}: {e}")
        
        logger.info(f"\n=== Fix Summary ===")
        logger.info(f"Total assignments checked: {len(assignments)}")
        logger.info(f"Corrupted assignments found: {len(corrupted_assignments)}")
        logger.info(f"Assignments fixed: {fixed_count}")
        
        if fixed_count > 0:
            logger.info(f"\n✓ Successfully fixed {fixed_count} assignments with corrupted vessel_id values")
        
        if len(corrupted_assignments) > fixed_count:
            logger.warning(f"⚠ {len(corrupted_assignments) - fixed_count} assignments could not be fixed")
            
        return fixed_count > 0
        
    except Exception as e:
        logger.error(f"Error during vessel_id fix: {e}")
        return False

def main():
    """Main function."""
    print("=" * 60)
    print("Vessel ID Corruption Fix Script")
    print("=" * 60)
    
    # Confirm with user
    response = input("\nThis script will fix corrupted vessel_id values in the database.\nContinue? (y/N): ")
    if response.lower() != 'y':
        print("Operation cancelled.")
        return
    
    success = fix_corrupted_vessel_ids()
    
    if success:
        print("\n✓ Fix completed successfully!")
    else:
        print("\n✗ Fix completed with issues. Check the logs above.")

if __name__ == "__main__":
    main()
