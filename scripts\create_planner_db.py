﻿import os, sys, re
from dotenv import load_dotenv
load_dotenv()
import psycopg2
host = os.getenv("DB_HOST", "localhost")
port = int(os.getenv("DB_PORT", "4432"))
user = os.getenv("DB_USER", "postgres")
password = os.getenv("DB_PASSWORD", "")
dbname = os.getenv("DB_NAME", "planner")
try:
    if not re.match(r'^[A-Za-z0-9_]+$', dbname):
        raise ValueError(f"Invalid DB_NAME: {dbname}")
    conn = psycopg2.connect(host=host, port=port, user=user, password=password, dbname="postgres")
    conn.autocommit = True
    cur = conn.cursor()
    cur.execute("SELECT 1 FROM pg_database WHERE datname=%s", (dbname,))
    exists = cur.fetchone() is not None
    if exists:
        print("exists")
    else:
        cur.execute(f"CREATE DATABASE {dbname} WITH ENCODING 'UTF8' TEMPLATE template0")
        print("created")
    cur.close()
    conn.close()
except Exception as e:
    print(f"error: {e}")
    sys.exit(1)
