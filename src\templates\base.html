<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Terneuzen Terminal Jetty Planning{% endblock %}</title>
    
    <!-- External Google Fonts with preconnect for optimal loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&family=Blinker:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome (local) -->
    <link rel="stylesheet" href="/static/vendor/css/fontawesome-6.4.0.all.min.css">
    
    <!-- Base Styles -->
    <link rel="stylesheet" href="/static/style.css">
    
    <!-- Page Specific Styles -->
    {% block head %}{% endblock %}
</head>
<body>
    <div class="container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="brand">
                <div class="evos-logo">
                    <img src="/static/logo.png" alt="EVOS Logo" class="logo-image">
                    <h2 id="terminalTitle">Jetty Planner</h2>
                </div>
            </div>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="/" class="nav-link">
                        <i class="fas fa-chart-line"></i>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/schedule" class="nav-link">
                        <i class="fas fa-calendar-alt"></i>
                        Schedule
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/nomination" class="nav-link">
                        <i class="fas fa-clipboard-list"></i>
                        Nomination
                    </a>
                </li>

                <li class="nav-item">
                    <a href="/nominated-vessels" class="nav-link">
                        <i class="fas fa-route"></i>
                        Smart ETA
                    </a>
                </li>
                <!-- Terminal comparison removed - using only Terneuzen terminal -->
                <li class="nav-item">
                    <a href="/weather" class="nav-link">
                        <i class="fas fa-cloud-sun"></i>
                        Weather
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/optimize" class="nav-link">
                        <i class="fas fa-cogs"></i>
                        Optimize
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/assistant" class="nav-link">
                        <i class="fas fa-robot"></i>
                        Assistant
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/analytics" class="nav-link">
                        <i class="fas fa-chart-bar"></i>
                        Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/logs" class="nav-link">
                        <i class="fas fa-history"></i>
                        Logs
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/settings" class="nav-link">
                        <i class="fas fa-cog"></i>
                        Settings
                    </a>
                </li>
            </ul>
        </aside>

        <!-- Header -->
        <header class="header">
            <h1>{% block header %}{% endblock %}</h1>
            <div class="header-controls">
                <div class="user-actions">
                    <a href="/nominated-vessels" class="btn btn-info btn-sm">
                        <i class="fas fa-route"></i>
                        Smart ETA
                    </a>
                    {% block user_actions %}{% endblock %}
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main">
            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Toast Container -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Base Scripts (local) -->
    <script src="/static/vendor/js/jquery-3.6.0.min.js"></script>
    <script src="/static/vendor/js/popper.min.js"></script>
    <script src="/static/vendor/js/bootstrap.min.js"></script>
    
    <!-- Terminal Management Scripts -->
    <script src="/static/js/terminal-manager.js"></script>
    
    <!-- Unified Toast System -->
    <script src="/static/js/toast.js"></script>
    
    <!-- Navigation Active State Management -->
    <script nonce="{{ nonce }}">
        document.addEventListener('DOMContentLoaded', function() {
            // Get current pathname
            const currentPath = window.location.pathname;
            
            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Add active class to current page nav link
            const currentNavLink = document.querySelector(`.nav-link[href="${currentPath}"]`);
            if (currentNavLink) {
                currentNavLink.classList.add('active');
            }
        });
    </script>
    
    <!-- Page Specific Scripts -->
    {% block scripts %}{% endblock %}
</body>
</html>
