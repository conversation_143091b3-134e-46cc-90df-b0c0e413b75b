"""
Schedule Model

This module defines the Schedule and Assignment classes for representing jetty allocations.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Tuple, TYPE_CHECKING
from datetime import datetime, timedelta

from src.models.terminal import Jetty
from src.models.vessel import VesselBase
from src.utils.status_utils import (
    normalize_status,
    is_valid_assignment_status,
    is_valid_assignment_transition
)

if TYPE_CHECKING:
    from src.models.terminal import Terminal
    from src.ml.models import TimePrediction


@dataclass
class Assignment:
    """Assignment of a vessel to a jetty for a specific time period"""
    id: str
    jetty: Jetty
    vessel: VesselBase
    start_time: datetime
    end_time: datetime
    status: str = "PENDING_APPROVAL"  # Default status as string
    actual_start_time: Optional[datetime] = None
    actual_end_time: Optional[datetime] = None
    surveyor_ids: List[str] = field(default_factory=list)  # IDs of assigned surveyors
    pump_ids: List[str] = field(default_factory=list)  # IDs of assigned pumps
    tank_ids: List[str] = field(default_factory=list)  # IDs of assigned tanks
    notes: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    prepump_time: timedelta = field(default_factory=lambda: timedelta(hours=0))
    postpump_time: timedelta = field(default_factory=lambda: timedelta(hours=0))
    
    # ML Prediction fields
    ml_predicted_prepump_time: Optional[timedelta] = None
    ml_predicted_pump_time: Optional[timedelta] = None
    ml_predicted_postpump_time: Optional[timedelta] = None
    ml_predicted_terminal_time: Optional[timedelta] = None
    
    # ML Confidence scores (0-1)
    ml_prepump_confidence: float = 0.0
    ml_pump_confidence: float = 0.0
    ml_postpump_confidence: float = 0.0
    ml_terminal_confidence: float = 0.0
    
    # ML Metadata
    ml_model_version: Optional[str] = None
    ml_prediction_timestamp: Optional[datetime] = None
    ml_jetty_recommendation: Optional[str] = None
    uses_ml_prediction: bool = False
    
    def __post_init__(self):
        """Validate and normalize status after initialization"""
        if self.status:
            normalized_status = normalize_status(self.status)
            if not is_valid_assignment_status(normalized_status):
                raise ValueError(f"Invalid assignment status: {self.status}")
            self.status = normalized_status
    
    @property
    def duration(self) -> timedelta:
        """Get planned duration"""
        return self.end_time - self.start_time
    
    @property
    def operation_start_time(self) -> datetime:
        """Get the actual operation start time (after pre-pump)"""
        return self.start_time + self.prepump_time
    
    @property
    def operation_end_time(self) -> datetime:
        """Get the operation end time (before post-pump)"""
        return self.end_time - self.postpump_time
    
    @property
    def actual_duration(self) -> Optional[timedelta]:
        """Get actual duration if available"""
        if self.actual_start_time and self.actual_end_time:
            return self.actual_end_time - self.actual_start_time
        return None
    
    @property
    def is_active(self) -> bool:
        """Check if assignment is currently active"""
        return self.status == "ACTIVE"
    
    @property
    def is_completed(self) -> bool:
        """Check if assignment is completed"""
        return self.status == "COMPLETED"
    
    @property
    def is_cancelled(self) -> bool:
        """Check if assignment is cancelled"""
        return self.status == "CANCELLED"
    
    def overlaps_with(self, other: 'Assignment') -> bool:
        """Check if this assignment overlaps with another assignment"""
        # Assignments on different jetties cannot overlap
        if self.jetty.id != other.jetty.id:
            return False
        
        # Check for time overlap
        return (self.start_time < other.end_time and self.end_time > other.start_time)
    
    def update_status(self, new_status: str) -> Tuple[bool, str]:
        """
        Update the status of the assignment with validation
        
        Args:
            new_status: The new status string
            
        Returns:
            Tuple of (success, message)
        """
        normalized_new_status = normalize_status(new_status)
        is_valid, message = is_valid_assignment_transition(self.status, normalized_new_status)
        
        if is_valid:
            self.status = normalized_new_status
            return True, ""
        
        return False, message
    
    def activate(self, actual_start_time: Optional[datetime] = None):
        """Activate this assignment"""
        success, message = self.update_status("ACTIVE")
        if success:
            self.actual_start_time = actual_start_time or datetime.now()
            # Update vessel status - vessel.update_status now handles string statuses
            self.vessel.update_status("DOCKED")
            self.vessel.current_jetty = self.jetty.id
        else:
            raise ValueError(f"Cannot activate assignment: {message}")
    
    def complete(self, actual_end_time: Optional[datetime] = None):
        """Complete this assignment"""
        success, message = self.update_status("COMPLETED")
        if success:
            self.actual_end_time = actual_end_time or datetime.now()
            # Update vessel status
            self.vessel.update_status("DEPARTED")
            self.vessel.current_jetty = None
        else:
            raise ValueError(f"Cannot complete assignment: {message}")
    
    def cancel(self, reason: Optional[str] = None):
        """Cancel this assignment"""
        success, message = self.update_status("CANCELLED")
        if success:
            if reason:
                self.notes = reason if not self.notes else f"{self.notes}\nCancelled: {reason}"
            # Update vessel status if it was already docked
            if normalize_status(self.vessel.status) == "DOCKED":
                self.vessel.update_status("ARRIVED")
                self.vessel.current_jetty = None
        else:
            raise ValueError(f"Cannot cancel assignment: {message}")
    
    def apply_ml_predictions(self, predictions: 'TimePrediction', jetty_recommendation: Optional[str] = None):
        """
        Apply ML predictions to this assignment
        
        Args:
            predictions: TimePrediction object from ML service
            jetty_recommendation: Optional jetty recommendation
        """
        # TimePrediction import handled at module level with TYPE_CHECKING
        
        self.ml_predicted_prepump_time = predictions.pre_pump_time
        self.ml_predicted_pump_time = predictions.pump_time
        self.ml_predicted_postpump_time = predictions.post_pump_time
        self.ml_predicted_terminal_time = predictions.terminal_time
        
        self.ml_prepump_confidence = predictions.pre_pump_confidence
        self.ml_pump_confidence = predictions.pump_confidence
        self.ml_postpump_confidence = predictions.post_pump_confidence
        self.ml_terminal_confidence = predictions.terminal_confidence
        
        self.ml_model_version = predictions.model_version
        self.ml_prediction_timestamp = datetime.fromisoformat(predictions.prediction_timestamp) if predictions.prediction_timestamp else datetime.now()
        self.ml_jetty_recommendation = jetty_recommendation
        self.uses_ml_prediction = True
        
        # Update the actual prepump and postpump times if ML predictions are available
        if self.ml_predicted_prepump_time:
            self.prepump_time = self.ml_predicted_prepump_time
        if self.ml_predicted_postpump_time:
            self.postpump_time = self.ml_predicted_postpump_time
    
    @property
    def effective_prepump_time(self) -> timedelta:
        """Get the effective pre-pump time (ML prediction if available, otherwise default)"""
        return self.ml_predicted_prepump_time or self.prepump_time
    
    @property
    def effective_postpump_time(self) -> timedelta:
        """Get the effective post-pump time (ML prediction if available, otherwise default)"""
        return self.ml_predicted_postpump_time or self.postpump_time
    
    @property
    def effective_pump_time(self) -> Optional[timedelta]:
        """Get the effective pump time (ML prediction if available)"""
        return self.ml_predicted_pump_time
    
    @property
    def effective_terminal_time(self) -> Optional[timedelta]:
        """Get the effective total terminal time (ML prediction if available)"""
        return self.ml_predicted_terminal_time
    
    @property
    def ml_average_confidence(self) -> float:
        """Get average ML prediction confidence"""
        if not self.uses_ml_prediction:
            return 0.0
        return (self.ml_prepump_confidence + self.ml_pump_confidence + 
                self.ml_postpump_confidence + self.ml_terminal_confidence) / 4
    
    def get_time_breakdown(self) -> Dict[str, Any]:
        """Get detailed time breakdown including ML predictions"""
        breakdown = {
            'scheduled_duration': self.duration,
            'prepump_time': self.prepump_time,
            'postpump_time': self.postpump_time,
            'uses_ml_prediction': self.uses_ml_prediction
        }
        
        if self.uses_ml_prediction:
            breakdown.update({
                'ml_prepump_time': self.ml_predicted_prepump_time,
                'ml_pump_time': self.ml_predicted_pump_time,
                'ml_postpump_time': self.ml_predicted_postpump_time,
                'ml_terminal_time': self.ml_predicted_terminal_time,
                'ml_average_confidence': self.ml_average_confidence,
                'ml_model_version': self.ml_model_version
            })
        
        if self.actual_start_time and self.actual_end_time:
            breakdown['actual_duration'] = self.actual_duration
        
        return breakdown

    def to_dict(self) -> dict:
        """Convert assignment to dictionary for serialization"""
        result = {
            "id": self.id,
            "jetty": self.jetty.to_dict(),
            "vessel": self.vessel.to_dict(),
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "status": self.status,  # Now a string
            "actual_start_time": self.actual_start_time.isoformat() if self.actual_start_time else None,
            "actual_end_time": self.actual_end_time.isoformat() if self.actual_end_time else None,
            "surveyor_ids": self.surveyor_ids,
            "pump_ids": self.pump_ids,
            "tank_ids": self.tank_ids,
            "notes": self.notes,
            "metadata": self.metadata,
            "prepump_time": str(self.prepump_time),
            "postpump_time": str(self.postpump_time),
            
            # ML Prediction fields
            "uses_ml_prediction": self.uses_ml_prediction,
            "ml_predicted_prepump_time": str(self.ml_predicted_prepump_time) if self.ml_predicted_prepump_time else None,
            "ml_predicted_pump_time": str(self.ml_predicted_pump_time) if self.ml_predicted_pump_time else None,
            "ml_predicted_postpump_time": str(self.ml_predicted_postpump_time) if self.ml_predicted_postpump_time else None,
            "ml_predicted_terminal_time": str(self.ml_predicted_terminal_time) if self.ml_predicted_terminal_time else None,
            "ml_prepump_confidence": self.ml_prepump_confidence,
            "ml_pump_confidence": self.ml_pump_confidence,
            "ml_postpump_confidence": self.ml_postpump_confidence,
            "ml_terminal_confidence": self.ml_terminal_confidence,
            "ml_model_version": self.ml_model_version,
            "ml_prediction_timestamp": self.ml_prediction_timestamp.isoformat() if self.ml_prediction_timestamp else None,
            "ml_jetty_recommendation": self.ml_jetty_recommendation
        }
        return result

    @classmethod
    def from_dict(cls, data: dict, terminal: Optional['Terminal'] = None) -> 'Assignment':
        """Create assignment from dictionary"""
        # If terminal is provided, use it to look up jetty
        if terminal:
            jetty = terminal.get_jetty_by_id(data["jetty"]["id"])
        else:
            from ..models.terminal import Jetty
            jetty = Jetty.from_dict(data["jetty"])

        from ..models.vessel import Vessel, VesselType
        if data["vessel"]["vessel_type"] == VesselType.BARGE.value:
            from ..models.vessel import Barge
            vessel = Barge.from_dict(data["vessel"])
        else:
            vessel = Vessel.from_dict(data["vessel"])

        # Parse timedelta strings safely
        def parse_timedelta(time_str):
            if not time_str or time_str == "None":
                return timedelta()
            try:
                parts = time_str.split(":")
                hours = float(parts[0])
                return timedelta(hours=hours)
            except (ValueError, IndexError):
                return timedelta()
        
        assignment = cls(
            id=data["id"],
            jetty=jetty,
            vessel=vessel,
            start_time=datetime.fromisoformat(data["start_time"]),
            end_time=datetime.fromisoformat(data["end_time"]),
            status=data["status"],  # Now directly using the string
            actual_start_time=datetime.fromisoformat(data["actual_start_time"]) if data["actual_start_time"] else None,
            actual_end_time=datetime.fromisoformat(data["actual_end_time"]) if data["actual_end_time"] else None,
            surveyor_ids=data["surveyor_ids"],
            pump_ids=data["pump_ids"],
            tank_ids=data["tank_ids"],
            notes=data["notes"],
            metadata=data["metadata"],
            prepump_time=parse_timedelta(data.get("prepump_time")),
            postpump_time=parse_timedelta(data.get("postpump_time")),
            
            # ML Prediction fields (with safe defaults)
            uses_ml_prediction=data.get("uses_ml_prediction", False),
            ml_predicted_prepump_time=parse_timedelta(data.get("ml_predicted_prepump_time")) if data.get("ml_predicted_prepump_time") else None,
            ml_predicted_pump_time=parse_timedelta(data.get("ml_predicted_pump_time")) if data.get("ml_predicted_pump_time") else None,
            ml_predicted_postpump_time=parse_timedelta(data.get("ml_predicted_postpump_time")) if data.get("ml_predicted_postpump_time") else None,
            ml_predicted_terminal_time=parse_timedelta(data.get("ml_predicted_terminal_time")) if data.get("ml_predicted_terminal_time") else None,
            ml_prepump_confidence=data.get("ml_prepump_confidence", 0.0),
            ml_pump_confidence=data.get("ml_pump_confidence", 0.0),
            ml_postpump_confidence=data.get("ml_postpump_confidence", 0.0),
            ml_terminal_confidence=data.get("ml_terminal_confidence", 0.0),
            ml_model_version=data.get("ml_model_version"),
            ml_prediction_timestamp=datetime.fromisoformat(data["ml_prediction_timestamp"]) if data.get("ml_prediction_timestamp") else None,
            ml_jetty_recommendation=data.get("ml_jetty_recommendation")
        )
        
        return assignment


@dataclass
class Schedule:
    """Schedule of jetty assignments"""
    assignments: List[Assignment] = field(default_factory=list)
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    objective_value: float = 0.0  # Value of objective function from optimization
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        # Set default end time if not provided
        if not self.end_time:
            self.end_time = self.start_time + timedelta(days=7)  # Default to one week schedule
        
        # Sort assignments by start time
        self.assignments.sort(key=lambda a: a.start_time)
    
    def add_assignment(self, assignment: Assignment) -> bool:
        """Add an assignment to the schedule if it doesn't conflict with existing assignments"""
        # Check for conflicts
        for existing in self.assignments:
            if existing.overlaps_with(assignment):
                return False
        
        self.assignments.append(assignment)
        # Re-sort assignments
        self.assignments.sort(key=lambda a: a.start_time)
        return True
    
    def remove_assignment(self, assignment_id: str) -> bool:
        """Remove an assignment from the schedule"""
        for i, assignment in enumerate(self.assignments):
            if assignment.id == assignment_id:
                self.assignments.pop(i)
                return True
        return False
    
    def get_assignment_by_id(self, assignment_id: str) -> Optional[Assignment]:
        """Get an assignment by ID"""
        for assignment in self.assignments:
            if assignment.id == assignment_id:
                return assignment
        return None
    
    def get_assignments_for_jetty(self, jetty_id: str) -> List[Assignment]:
        """Get all assignments for a specific jetty"""
        return [a for a in self.assignments if a.jetty.id == jetty_id]
    
    def get_assignments_for_vessel(self, vessel_id: str) -> List[Assignment]:
        """Get all assignments for a specific vessel"""
        return [a for a in self.assignments if a.vessel.id == vessel_id]
    
    def get_active_assignments(self) -> List[Assignment]:
        """Get all currently active assignments"""
        return [a for a in self.assignments if a.is_active]
    
    def get_pending_assignments(self) -> List[Assignment]:
        """Get all pending (planned) assignments"""
        return [a for a in self.assignments if a.status == "PENDING_APPROVAL"]
    
    def get_completed_assignments(self) -> List[Assignment]:
        """Get all completed assignments"""
        return [a for a in self.assignments if a.is_completed]
    
    def get_canceled_assignments(self) -> List[Assignment]:
        """Get all canceled assignments"""
        return [a for a in self.assignments if a.is_cancelled]
    
    def is_valid(self) -> bool:
        """Check if the schedule is valid (no overlaps)"""
        for i, a1 in enumerate(self.assignments):
            for j, a2 in enumerate(self.assignments):
                if i < j and a1.overlaps_with(a2):
                    return False
        return True
    
    def get_jetty_utilization(self) -> Dict[str, float]:
        """Calculate jetty utilization (percentage of time allocated)"""
        if not self.assignments:
            return {}
        
        total_duration = (self.end_time - self.start_time).total_seconds()
        if total_duration <= 0:
            return {}
        
        # Initialize utilization dictionary
        jetty_utilization = {}
        
        # Group assignments by jetty
        jetty_assignments = {}
        for assignment in self.assignments:
            jetty_id = assignment.jetty.id
            if jetty_id not in jetty_assignments:
                jetty_assignments[jetty_id] = []
            jetty_assignments[jetty_id].append(assignment)
        
        # Calculate utilization for each jetty
        for jetty_id, assignments in jetty_assignments.items():
            # Sort assignments by start time
            assignments.sort(key=lambda a: a.start_time)
            
            # Merge overlapping time periods
            merged_intervals = []
            for assignment in assignments:
                start = max(assignment.start_time, self.start_time)
                end = min(assignment.end_time, self.end_time)
                
                if not merged_intervals or merged_intervals[-1][1] < start:
                    # No overlap with the last interval
                    merged_intervals.append([start, end])
                else:
                    # Overlap with the last interval, merge them
                    merged_intervals[-1][1] = max(merged_intervals[-1][1], end)
            
            # Calculate total allocated time
            allocated_time = sum((end - start).total_seconds() for start, end in merged_intervals)
            
            # Calculate utilization percentage
            jetty_utilization[jetty_id] = (allocated_time / total_duration) * 100.0
        
        return jetty_utilization

    def to_dict(self) -> dict:
        """Convert schedule to dictionary for serialization"""
        return {
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat(),
            "assignments": [a.to_dict() for a in self.assignments],
            "objective_value": self.objective_value
        }

    @classmethod
    def from_dict(cls, data: dict, terminal: Optional['Terminal'] = None) -> 'Schedule':
        """Create schedule from dictionary"""
        schedule = cls(
            start_time=datetime.fromisoformat(data["start_time"]),
            end_time=datetime.fromisoformat(data["end_time"])
        )
        schedule.assignments = [Assignment.from_dict(a, terminal) for a in data["assignments"]]
        schedule.objective_value = data.get("objective_value", 0.0)
        return schedule
