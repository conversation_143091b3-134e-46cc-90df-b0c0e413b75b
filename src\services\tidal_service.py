"""
Tidal Information Service

Integration with Dutch RWS (Rijkswaterstaat) Waterwebservices for real-time tidal data.
Provides water levels, tide predictions, and navigation windows for vessel planning.
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any
import aiohttp
from pyproj import Transformer
import xml.etree.ElementTree as ET
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class TideData:
    """Tidal data point"""
    datetime: datetime
    water_level: float  # meters above NAP (Normal Amsterdam Level)
    station: str
    measurement_type: str  # "measured", "predicted", "astronomical"


@dataclass
class TideStation:
    """Tidal monitoring station information"""
    code: str
    name: str
    latitude: float
    longitude: float
    parameters: List[str]  # Available parameters (WATHTE, WATHTEVERWACHT, etc.)


class TidalService:
    """Service for fetching tidal information from RWS Waterwebservices"""
    
    def __init__(self):
        """Initialize the tidal service with RWS API configuration"""
        self.base_url = "https://waterwebservices.rijkswaterstaat.nl/ONLINEWAARNEMINGENSERVICES_DBO"
        self.metadata_url = "https://waterwebservices.rijkswaterstaat.nl/METADATASERVICES_DBO"
        
        # Key stations for Westerschelde navigation with correct RWS codes
        self.stations = {
            "VLISSGN": TideStation("VLISSGN", "Vlissingen", 51.4425, 3.5961, 
                                  ["WATHTE"]),  # Only measured water height available
            "TERNZN": TideStation("TERNZN", "Terneuzen", 51.3294, 3.8091, 
                                 ["WATHTE"]),   # Only measured water height available
            "HANSWT": TideStation("HANSWT", "Hansweert", 51.4458, 4.0094, 
                                 ["WATHTE"]),   # Only measured water height available
            "BATH": TideStation("BATH", "Bath", 51.3981, 4.2172, 
                               ["WATHTE"])      # Only measured water height available
        }
        
        # Alternative stations that might have more parameters
        self.alternative_stations = {
            "VLISSGN_PRED": "VLISSGGN",  # Alternative code for predictions
            "CADZD": "CADZD",           # Cadzand - might have more data
            "WESTDORPE": "WESTD"        # Alternative nearby station
        }
        
        # Critical tide levels for navigation (meters above NAP)
        self.navigation_levels = {
            "minimum_safe": 1.0,  # Minimum for most vessels
            "deep_draft_minimum": 2.5,  # For vessels >12m draft
            "optimal_window": 3.0,  # Optimal tide level for large vessels
        }

        # Known EPSG:25831 coordinates for key stations (X,Y)
        # Provided by user/examples; used if available, else computed from lat/lon
        self.station_xy_25831: Dict[str, Dict[str, float]] = {
            "VLIS": {"X": 541518.745919649, "Y": 5699254.96425966},
            "VLISSGN": {"X": 541425.983214885, "Y": 5699181.90968435},
        }

        # Coordinate transformer WGS84 (lon,lat) -> EPSG:25831 (X,Y)
        # Always pass (lon, lat) in that order
        self._to_epsg_25831 = Transformer.from_crs("EPSG:4326", "EPSG:25831", always_xy=True)
        
        logger.info("Tidal service initialized with RWS Waterwebservices integration")
    
    def _snippet(self, text: str, limit: int = 200) -> str:
        """Return a single-line snippet of text for logging."""
        try:
            return text[:limit].replace("\n", " ").replace("\r", " ")
        except Exception:
            return ""
    
    async def get_current_water_level(self, station_code: str = "VLISSGN") -> Optional[TideData]:
        """
        Get current water level for a station.
        
        Args:
            station_code: RWS station code (default: VLISSGN for Vlissingen)
            
        Returns:
            Current TideData or None if unavailable
        """
        try:
            async with aiohttp.ClientSession() as session:
                # Get current time in proper timezone (Europe/Amsterdam with DST)
                current_time = datetime.now(timezone.utc)
                import pytz
                amsterdam_tz = pytz.timezone('Europe/Amsterdam')
                local_time = current_time.astimezone(amsterdam_tz)
                
                # WATHTE = measured water level. Resolve station via catalog if needed.
                request_station = await self._resolve_station_code(session, station_code, "WATHTE")
                begin_local = (local_time - timedelta(hours=24)).strftime('%Y-%m-%dT%H:%M:%S.000%z')
                end_local = local_time.strftime('%Y-%m-%dT%H:%M:%S.000%z')
                # Convert +0200 -> +02:00
                begin_local = begin_local[:-2] + ":" + begin_local[-2:]
                end_local = end_local[:-2] + ":" + end_local[-2:]

                body = {
                    "Locatie": self._build_locatie(request_station, station_code, "WATHTE"),
                    "AquoPlusWaarnemingMetadata": {
                        "AquoMetadata": {
                            "Compartiment": {"Code": "OW"},
                            "Grootheid": {"Code": "WATHTE"}
                        }
                    },
                    "Periode": {
                        "Begindatumtijd": begin_local,
                        "Einddatumtijd": end_local
                    }
                }
                url = f"{self.base_url}/OphalenWaarnemingen"
                
                logger.info(f"Requesting tidal data (POST JSON) for {request_station} at {body['Periode']}")
                
                payload = await self._post_rws(session, url, body)
                if payload:
                    # Try JSON first, fall back to XML
                    if payload.strip().startswith('{') or payload.strip().startswith('['):
                        result = self._parse_water_level_json(payload, station_code)
                    else:
                        result = self._parse_water_level_xml(payload, station_code)
                    if result:
                        logger.info(f"Successfully fetched real tidal data for {station_code}")
                        return result
                    logger.warning(f"No valid data in RWS response for {station_code}; trying SOAP/XML fallback")

                # SOAP/XML fallback
                xml_text = await self._post_rws_xml(
                    session=session,
                    url=url,
                    station_code=request_station,
                    series="WATHTE",
                    begin_iso=begin_local,
                    end_iso=end_local
                )
                if xml_text:
                    result = self._parse_water_level_xml(xml_text, station_code)
                    if result:
                        logger.info(f"Successfully fetched tidal data via SOAP/XML for {station_code}")
                        return result
                # Do not use mock; return None so API can signal 4xx/5xx and UI shows offline
                return None
                        
        except Exception as e:
            logger.error(f"Error fetching current water level: {e}")
            return None
    
    async def get_tide_forecast(self, station_code: str = "VLISSGN", 
                               hours_ahead: int = 24) -> List[TideData]:
        """
        Get tide forecast for specified hours ahead using WATHTEVERWACHT (predictions).
        """
        try:
            async with aiohttp.ClientSession() as session:
                start_time = datetime.now(timezone.utc)
                end_time = start_time + timedelta(hours=hours_ahead)
                import pytz
                amsterdam_tz = pytz.timezone('Europe/Amsterdam')
                start_local = start_time.astimezone(amsterdam_tz).strftime('%Y-%m-%dT%H:%M:%S.000%z')
                end_local = end_time.astimezone(amsterdam_tz).strftime('%Y-%m-%dT%H:%M:%S.000%z')
                start_local = start_local[:-2] + ":" + start_local[-2:]
                end_local = end_local[:-2] + ":" + end_local[-2:]
                
                series = "WATHTEVERWACHT"
                request_station = await self._resolve_station_code(session, station_code, series)
                
                body = {
                    "Locatie": self._build_locatie(request_station, station_code, series),
                    "AquoPlusWaarnemingMetadata": {
                        "AquoMetadata": {
                            "Compartiment": {"Code": "OW"},
                            "Grootheid": {"Code": series}
                        }
                    },
                    "Periode": {
                        "Begindatumtijd": start_local,
                        "Einddatumtijd": end_local
                    }
                }
                url = f"{self.base_url}/OphalenWaarnemingen"
                
                payload = await self._post_rws(session, url, body)
                if not payload:
                    logger.info(f"Using mock forecast data for station {station_code} ({hours_ahead}h)")
                    return self._get_mock_forecast(station_code, hours_ahead)
                
                if payload.strip().startswith('{') or payload.strip().startswith('['):
                    parsed = self._parse_forecast_json(payload, station_code, "predicted")
                else:
                    parsed = self._parse_forecast_xml(payload, station_code, "predicted")
                return parsed if parsed else self._get_mock_forecast(station_code, hours_ahead)
        except Exception as e:
            logger.error(f"Error fetching tide forecast: {e}")
            return self._get_mock_forecast(station_code, hours_ahead)
    
    async def get_astronomical_tide(self, station_code: str = "VLISSGN", 
                                   hours_ahead: int = 24) -> List[TideData]:
        """Get astronomical tide predictions (WATHTBRKD) using POST+JSON."""
        try:
            async with aiohttp.ClientSession() as session:
                start_time = datetime.now(timezone.utc)
                end_time = start_time + timedelta(hours=hours_ahead)
                import pytz
                amsterdam_tz = pytz.timezone('Europe/Amsterdam')
                start_local = start_time.astimezone(amsterdam_tz).strftime('%Y-%m-%dT%H:%M:%S.000%z')
                end_local = end_time.astimezone(amsterdam_tz).strftime('%Y-%m-%dT%H:%M:%S.000%z')
                start_local = start_local[:-2] + ":" + start_local[-2:]
                end_local = end_local[:-2] + ":" + end_local[-2:]
                
                series = "WATHTBRKD"
                request_station = await self._resolve_station_code(session, station_code, series)
                
                url = f"{self.base_url}/OphalenWaarnemingen"
                body = {
                    "Locatie": self._build_locatie(request_station, station_code, series),
                    "AquoPlusWaarnemingMetadata": {
                        "AquoMetadata": {
                            "Compartiment": {"Code": "OW"},
                            "Grootheid": {"Code": series}
                        }
                    },
                    "Periode": {
                        "Begindatumtijd": start_local,
                        "Einddatumtijd": end_local
                    }
                }
                
                payload = await self._post_rws(session, url, body)
                if not payload:
                    return []
                if payload.strip().startswith('{') or payload.strip().startswith('['):
                    return self._parse_forecast_json(payload, station_code, "astronomical")
                return self._parse_forecast_xml(payload, station_code, "astronomical")
        except Exception as e:
            logger.error(f"Error fetching astronomical tide: {e}")
            return []

    async def _post_rws(self, session: aiohttp.ClientSession, url: str, body: Dict[str, Any]) -> Optional[str]:
        """Perform POST to RWS endpoint and return response text.
        - Sends JSON body; RWS may respond with JSON or XML.
        - Logs HTTP status, reason, and short snippet of response on failure.
        - Tries a couple of alternative shapes if first attempt fails.
        """
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, application/xml;q=0.8, */*;q=0.5",
            "User-Agent": "Jettyplanner/1.0"
        }
        try:
            async with session.post(url, json=body, headers=headers, timeout=20) as response:
                text = await response.text()
                if response.status == 200:
                    logger.debug(
                        f"RWS primary OK 200; content-type={response.headers.get('Content-Type')} "
                        f"len={len(text)} snippet={self._snippet(text)}"
                    )
                    # If JSON indicates failure, try alternative shapes
                    try:
                        import json as _json
                        parsed = _json.loads(text)
                        if isinstance(parsed, dict) and parsed.get("Succesvol") is False:
                            logger.warning(f"RWS response indicates failure: {parsed.get('Foutmelding')}")
                        else:
                            return text
                    except Exception:
                        # Not JSON or no Succesvol flag; return as-is (likely XML)
                        return text
                logger.warning(
                    f"RWS POST {url} HTTP {response.status} {response.reason}; ct={response.headers.get('Content-Type')} "
                    f"snippet={self._snippet(text)}"
                )
        except Exception as e:
            logger.warning(f"Primary RWS POST exception: {e}")
        
        # Try an alternative body shape: adjust metadata and encode as strings
        try:
            alt_body = dict(body)
            # Some endpoints may prefer AquoPlusWaarnemingMetadata as bool true
            alt_body["AquoPlusWaarnemingMetadata"] = True
            async with session.post(url, json=alt_body, headers=headers, timeout=20) as response:
                text = await response.text()
                if response.status == 200:
                    logger.debug("RWS alternative body OK 200")
                    return text
                logger.warning(
                    f"RWS POST alternative HTTP {response.status} {response.reason}; ct={response.headers.get('Content-Type')} "
                    f"snippet={self._snippet(text)}"
                )
        except Exception as e:
            logger.error(f"Alternative RWS POST exception: {e}")

        # Try with WaarnemingsLocatie wrapper but keep full metadata object
        try:
            wrapped = dict(body)
            loc_code = body.get("Locatie", {}).get("Code") if isinstance(body.get("Locatie"), dict) else body.get("Locatie")
            wrapped["Locatie"] = {"WaarnemingsLocatie": {"Code": loc_code}}
            # Also try local time key names as alternative
            periode = body.get("Periode", {}) or {}
            beg = periode.get("Begindatumtijd") or periode.get("BegindatumtijdLokaal")
            eind = periode.get("Einddatumtijd") or periode.get("EinddatumtijdLokaal")
            wrapped["Periode"] = {
                "BegindatumtijdLokaal": beg,
                "EinddatumtijdLokaal": eind
            }
            async with session.post(url, json=wrapped, headers=headers, timeout=20) as response:
                text = await response.text()
                if response.status == 200:
                    logger.debug("RWS wrapped Locatie OK 200")
                    return text
                logger.warning(
                    f"RWS POST wrapped Locatie HTTP {response.status} {response.reason}; ct={response.headers.get('Content-Type')} "
                    f"snippet={self._snippet(text)}"
                )
        except Exception as e:
            logger.error(f"Wrapped Locatie variant exception: {e}")

        # Try a nested Code-based structure often used in RWS examples
        try:
            periode = body.get("Periode", {}) or {}
            beg = periode.get("Begindatumtijd") or periode.get("BegindatumtijdLokaal")
            eind = periode.get("Einddatumtijd") or periode.get("EinddatumtijdLokaal")
            # Extract codes from original body
            loc_code = body.get("Locatie", {}).get("Code") if isinstance(body.get("Locatie"), dict) else body.get("Locatie")
            grootheid_code = "WATHTE"
            compartiment_code = "OW"
            aq = body.get("AquoPlusWaarnemingMetadata")
            if isinstance(aq, dict):
                try:
                    meta = aq.get("AquoMetadata", {})
                    grootheid_code = meta.get("Grootheid", {}).get("Code", grootheid_code)
                    compartiment_code = meta.get("Compartiment", {}).get("Code", compartiment_code)
                except Exception:
                    pass
            nested = {
                "AquoPlusWaarnemingMetadata": True,
                "Locatie": {"Code": loc_code},
                "Periode": {
                    "BegindatumtijdLokaal": beg,
                    "EinddatumtijdLokaal": eind
                },
                "AquoPlusParameter": {
                    "Compartiment": {"Code": compartiment_code},
                    "Grootheid": {"Code": grootheid_code}
                }
            }
            async with session.post(url, json=nested, headers=headers, timeout=20) as response:
                text = await response.text()
                if response.status == 200:
                    logger.debug("RWS nested body OK 200")
                    return text
                logger.error(
                    f"RWS POST nested failed HTTP {response.status} {response.reason}; ct={response.headers.get('Content-Type')} "
                    f"snippet={self._snippet(text)}"
                )
        except Exception as e:
            logger.error(f"Nested RWS POST exception: {e}")

        # Try variant with WaarnemingsLocatie wrapper
        try:
            periode = body.get("Periode", {}) or {}
            beg = periode.get("Begindatumtijd") or periode.get("BegindatumtijdLokaal")
            eind = periode.get("Einddatumtijd") or periode.get("EinddatumtijdLokaal")
            loc_code = body.get("Locatie", {}).get("Code") if isinstance(body.get("Locatie"), dict) else body.get("Locatie")
            aq = body.get("AquoPlusWaarnemingMetadata")
            meta = aq.get("AquoMetadata", {}) if isinstance(aq, dict) else {}
            grootheid_code = meta.get("Grootheid", {}).get("Code", "WATHTE")
            compartiment_code = meta.get("Compartiment", {}).get("Code", "OW")

            variant = {
                "AquoPlusWaarnemingMetadata": True,
                "Locatie": {"WaarnemingsLocatie": {"Code": loc_code}},
                "Periode": {
                    "BegindatumtijdLokaal": beg,
                    "EinddatumtijdLokaal": eind
                },
                "AquoPlusParameter": {
                    "Compartiment": {"Code": compartiment_code},
                    "Grootheid": {"Code": grootheid_code}
                }
            }
            async with session.post(url, json=variant, headers=headers, timeout=20) as response:
                text = await response.text()
                if response.status == 200:
                    logger.debug("RWS waarnemingslocatie variant OK 200")
                    return text
                logger.error(
                    f"RWS POST waarnemingslocatie failed HTTP {response.status} {response.reason}; ct={response.headers.get('Content-Type')} "
                    f"snippet={self._snippet(text)}"
                )
        except Exception as e:
            logger.error(f"Waarnemingslocatie variant exception: {e}")

        # Try top-level WaarnemingsLocatie + AquoPlusParameter (no AquoPlusWaarnemingMetadata)
        try:
            periode = body.get("Periode", {}) or {}
            beg = periode.get("Begindatumtijd") or periode.get("BegindatumtijdLokaal")
            eind = periode.get("Einddatumtijd") or periode.get("EinddatumtijdLokaal")
            loc_code = body.get("Locatie", {}).get("Code") if isinstance(body.get("Locatie"), dict) else body.get("Locatie")
            aq = body.get("AquoPlusWaarnemingMetadata")
            meta = aq.get("AquoMetadata", {}) if isinstance(aq, dict) else {}
            grootheid_code = meta.get("Grootheid", {}).get("Code", "WATHTE")
            compartiment_code = meta.get("Compartiment", {}).get("Code", "OW")

            variant2 = {
                "WaarnemingsLocatie": {"Code": loc_code},
                "AquoPlusParameter": {
                    "Compartiment": {"Code": compartiment_code},
                    "Grootheid": {"Code": grootheid_code}
                },
                "Periode": {
                    "BegindatumtijdLokaal": beg,
                    "EinddatumtijdLokaal": eind
                }
            }
            async with session.post(url, json=variant2, headers=headers, timeout=20) as response:
                text = await response.text()
                if response.status == 200:
                    logger.debug("RWS top-level WaarnemingsLocatie variant OK 200")
                    return text
                logger.error(
                    f"RWS POST top-level WaarnemingsLocatie failed HTTP {response.status} {response.reason}; ct={response.headers.get('Content-Type')} "
                    f"snippet={self._snippet(text)}"
                )
        except Exception as e:
            logger.error(f"Top-level WaarnemingsLocatie variant exception: {e}")

        # Try variant with Locatie nested inside AquoPlusParameter
        try:
            periode = body.get("Periode", {}) or {}
            beg = periode.get("Begindatumtijd") or periode.get("BegindatumtijdLokaal")
            eind = periode.get("Einddatumtijd") or periode.get("EinddatumtijdLokaal")
            loc_code = body.get("Locatie", {}).get("Code") if isinstance(body.get("Locatie"), dict) else body.get("Locatie")
            aq = body.get("AquoPlusWaarnemingMetadata")
            meta = aq.get("AquoMetadata", {}) if isinstance(aq, dict) else {}
            grootheid_code = meta.get("Grootheid", {}).get("Code", "WATHTE")
            compartiment_code = meta.get("Compartiment", {}).get("Code", "OW")

            variant3 = {
                "AquoPlusParameter": {
                    "Locatie": {"Code": loc_code},
                    "Compartiment": {"Code": compartiment_code},
                    "Grootheid": {"Code": grootheid_code}
                },
                "Periode": {
                    "Begindatumtijd": beg,
                    "Einddatumtijd": eind
                }
            }
            async with session.post(url, json=variant3, headers=headers, timeout=20) as response:
                text = await response.text()
                if response.status == 200:
                    logger.debug("RWS variant3 (Locatie under AquoPlusParameter) OK 200")
                    return text
                logger.error(
                    f"RWS POST variant3 failed HTTP {response.status} {response.reason}; ct={response.headers.get('Content-Type')} "
                    f"snippet={self._snippet(text)}"
                )
        except Exception as e:
            logger.error(f"Variant3 exception: {e}")

        # Try variant with Lokatie (alt spelling) nested under AquoPlusParameter
        try:
            periode = body.get("Periode", {}) or {}
            beg = periode.get("Begindatumtijd") or periode.get("BegindatumtijdLokaal")
            eind = periode.get("Einddatumtijd") or periode.get("EinddatumtijdLokaal")
            loc_code = body.get("Locatie", {}).get("Code") if isinstance(body.get("Locatie"), dict) else body.get("Locatie")
            aq = body.get("AquoPlusWaarnemingMetadata")
            meta = aq.get("AquoMetadata", {}) if isinstance(aq, dict) else {}
            grootheid_code = meta.get("Grootheid", {}).get("Code", "WATHTE")
            compartiment_code = meta.get("Compartiment", {}).get("Code", "OW")

            variant4 = {
                "AquoPlusParameter": {
                    "Lokatie": {"Code": loc_code},
                    "Compartiment": {"Code": compartiment_code},
                    "Grootheid": {"Code": grootheid_code}
                },
                "Periode": {
                    "Begindatumtijd": beg,
                    "Einddatumtijd": eind
                }
            }
            async with session.post(url, json=variant4, headers=headers, timeout=20) as response:
                text = await response.text()
                if response.status == 200:
                    logger.debug("RWS variant4 (Lokatie under AquoPlusParameter) OK 200")
                    return text
                logger.error(
                    f"RWS POST variant4 failed HTTP {response.status} {response.reason}; ct={response.headers.get('Content-Type')} "
                    f"snippet={self._snippet(text)}"
                )
        except Exception as e:
            logger.error(f"Variant4 exception: {e}")
        return None

    async def _post_rws_xml(
        self,
        session: aiohttp.ClientSession,
        url: str,
        station_code: str,
        series: str,
        begin_iso: str,
        end_iso: str
    ) -> Optional[str]:
        """Perform SOAP/XML POST to the RWS endpoint for OphalenWaarnemingen.
        Tries without and with SOAPAction header variations.
        """
        def build_envelope() -> str:
            # Minimal SOAP 1.1 envelope with parameter block
            return (
                "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
                "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" "
                "xmlns:ws=\"http://www.rijkswaterstaat.nl/waterwebservices/\">"
                "<soapenv:Header/>"
                "<soapenv:Body>"
                "<ws:OphalenWaarnemingen>"
                "  <ws:Locatie>"
                "    <ws:Code>" + station_code + "</ws:Code>"
                "  </ws:Locatie>"
                "  <ws:AquoPlusWaarnemingMetadata>"
                "    <ws:AquoMetadata>"
                "      <ws:Compartiment><ws:Code>OW</ws:Code></ws:Compartiment>"
                "      <ws:Grootheid><ws:Code>" + series + "</ws:Code></ws:Grootheid>"
                "    </ws:AquoMetadata>"
                "  </ws:AquoPlusWaarnemingMetadata>"
                "  <ws:Periode>"
                "    <ws:Begindatumtijd>" + begin_iso + "</ws:Begindatumtijd>"
                "    <ws:Einddatumtijd>" + end_iso + "</ws:Einddatumtijd>"
                "  </ws:Periode>"
                "</ws:OphalenWaarnemingen>"
                "</soapenv:Body>"
                "</soapenv:Envelope>"
            )

        xml_body = build_envelope()
        headers_base = {
            "Content-Type": "text/xml; charset=utf-8",
            "User-Agent": "Jettyplanner/1.0"
        }
        # Try without SOAPAction
        try:
            async with session.post(url, data=xml_body.encode("utf-8"), headers=headers_base, timeout=20) as response:
                text = await response.text()
                if response.status == 200:
                    logger.debug(
                        f"RWS SOAP/XML OK 200; ct={response.headers.get('Content-Type')} len={len(text)}"
                    )
                    return text
                logger.warning(
                    f"RWS SOAP/XML HTTP {response.status} {response.reason}; ct={response.headers.get('Content-Type')} "
                    f"snippet={self._snippet(text)}"
                )
        except Exception as e:
            logger.error(f"RWS SOAP/XML request failed: {e}")

        # Try with a SOAPAction guess
        try:
            headers = dict(headers_base)
            headers["SOAPAction"] = "OphalenWaarnemingen"
            async with session.post(url, data=xml_body.encode("utf-8"), headers=headers, timeout=20) as response:
                text = await response.text()
                if response.status == 200:
                    logger.debug("RWS SOAP/XML with SOAPAction OK 200")
                    return text
                logger.warning(
                    f"RWS SOAP/XML (SOAPAction) HTTP {response.status} {response.reason}; ct={response.headers.get('Content-Type')} "
                    f"snippet={self._snippet(text)}"
                )
        except Exception as e:
            logger.error(f"RWS SOAP/XML (SOAPAction) request failed: {e}")

        return None

    def _map_station_for_series(self, requested_station: str, series: str) -> str:
        """Map station codes depending on series, per RWS examples.
        - For Vlissingen: measured (WATHTE) and forecast (WATHTEVERWACHT) often use "VLIS".
        - Astronomical (WATHTBRKD) example uses "VLISSGN".
        """
        code = (requested_station or "").strip().upper()
        if code == "VLISSGN" and series in ("WATHTE", "WATHTEVERWACHT"):
            return "VLIS"
        if code == "TERNZN" and series in ("WATHTE", "WATHTEVERWACHT"):
            return "TERN"
        return code

    def _build_locatie(self, code_to_send: str, original_station: str, series: str) -> Dict[str, Any]:
        """Build Locatie object with Code and required EPSG:25831 X/Y.
        Prefers known X/Y for codes; otherwise computes from station lat/lon.
        """
        code_to_send = (code_to_send or "").upper()
        xy = self.station_xy_25831.get(code_to_send)
        if xy:
            return {"Code": code_to_send, "X": xy["X"], "Y": xy["Y"]}
        # Compute from lat/lon of the best-known station entry
        station_obj = self.stations.get(code_to_send) or self.stations.get(original_station) or None
        if station_obj and station_obj.longitude and station_obj.latitude:
            try:
                x, y = self._to_epsg_25831.transform(station_obj.longitude, station_obj.latitude)
                return {"Code": code_to_send, "X": float(x), "Y": float(y)}
            except Exception as e:
                logger.warning(f"Failed to transform coordinates for {code_to_send}: {e}")
        # Fallback: send code only (may fail server-side); logged for visibility
        logger.warning(f"Missing EPSG:25831 coordinates for Locatie {code_to_send}; sending Code only")
        return {"Code": code_to_send}

    async def _resolve_station_code(self, session: aiohttp.ClientSession, requested_station: str, series: str) -> str:
        """Resolve station code via catalog if unknown or if mapping is ambiguous.
        Returns a code suitable for Locatie.Code and populates XY cache if available.
        """
        code = self._map_station_for_series(requested_station, series)
        # If we already have XY for this code, return it directly
        if code in self.station_xy_25831:
            return code
        try:
            info = await self._fetch_catalog_station(session, code)
            if info and isinstance(info, dict):
                xy = info.get("xy25831") or {}
                if "X" in xy and "Y" in xy:
                    self.station_xy_25831[code] = {"X": xy["X"], "Y": xy["Y"]}
                # If catalog suggests a canonical code, use it
                canonical = info.get("code") or code
                return str(canonical)
        except Exception as e:
            logger.warning(f"Catalog resolution failed for {code}: {e}")
        return code

    async def _fetch_catalog_station(self, session: aiohttp.ClientSession, code_or_name: str) -> Optional[Dict[str, Any]]:
        """Fetch station metadata from OphalenCatalogus (beta) and return matching station.
        Uses minimal filter per docs and generically scans for stations with Code, Naam, X, Y.
        """
        url = f"{self.metadata_url}/OphalenCatalogus"
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, application/xml;q=0.8, */*;q=0.5",
            "User-Agent": "Jettyplanner/1.0"
        }
        # Minimal catalog filter from docs
        body = {
            "CatalogusFilter": {
                "Compartimenten": True,
                "Grootheden": True
            }
        }
        try:
            async with session.post(url, json=body, headers=headers, timeout=20) as response:
                text = await response.text()
                if response.status != 200:
                    logger.warning(f"Catalog HTTP {response.status} {response.reason}; snippet={self._snippet(text)}")
                    return None
                try:
                    import json as _json
                    data = _json.loads(text)
                except Exception:
                    logger.warning("Catalog response not JSON")
                    return None

                token = (code_or_name or "").strip().upper()

                # Generic DFS to find station-like dicts
                def iter_dicts(obj):
                    if isinstance(obj, dict):
                        yield obj
                        for v in obj.values():
                            yield from iter_dicts(v)
                    elif isinstance(obj, list):
                        for it in obj:
                            yield from iter_dicts(it)

                candidates = []
                for d in iter_dicts(data):
                    code = d.get("Code")
                    naam = d.get("Naam") or d.get("Name")
                    x = d.get("X")
                    y = d.get("Y")
                    if isinstance(code, str) and naam and isinstance(x, (int, float)) and isinstance(y, (int, float)):
                        candidates.append({
                            "code": code.strip().upper(),
                            "name": str(naam).strip(),
                            "xy25831": {"X": float(x), "Y": float(y)}
                        })

                # Prefer exact code match, then name contains token
                for c in candidates:
                    if c["code"] == token:
                        return c
                for c in candidates:
                    if token and token in c["name"].upper():
                        return c

                return None
        except Exception as e:
            logger.warning(f"Catalog request failed: {e}")
            return None
    
    def _parse_water_level_xml(self, xml_data: str, station_code: str) -> Optional[TideData]:
        """Parse current water level from RWS XML response (namespace-agnostic)."""
        try:
            root = ET.fromstring(xml_data)

            def local_name(tag: str) -> str:
                return tag.split('}', 1)[-1] if '}' in tag else tag

            # Iterate over all elements to find Waarneming blocks regardless of namespaces
            for waarneming in root.iter():
                if local_name(waarneming.tag).lower() != 'waarneming':
                    continue
                tijd_text = None
                waarde_text = None
                # Search descendants for time and value
                for elem in waarneming.iter():
                    ln = local_name(elem.tag).lower()
                    if ln in ('tijdstip', 'datumtijd') and elem.text:
                        tijd_text = elem.text.strip()
                    if ln == 'waarde' and elem.text:
                        waarde_text = elem.text.strip()
                if tijd_text and waarde_text is not None:
                    try:
                        ts = datetime.fromisoformat(tijd_text.replace('Z', '+00:00'))
                        # Some RWS feeds return values in centimeters; normalize to meters
                        raw_val = float(str(waarde_text).replace(',', '.'))
                        wl = self._normalize_water_level(raw_val, None)
                        return TideData(
                            datetime=ts,
                            water_level=wl,
                            station=station_code,
                            measurement_type="measured"
                        )
                    except Exception:
                        continue

            logger.warning(f"No valid water level data found in XML for {station_code}")
            return None

        except Exception as e:
            logger.error(f"Error parsing water level XML: {e}")
            return None
    
    def _parse_forecast_xml(self, xml_data: str, station_code: str, 
                           forecast_type: str = "predicted") -> List[TideData]:
        """Parse forecast data from RWS XML response"""
        try:
            root = ET.fromstring(xml_data)
            tide_data = []
            
            for waarneming in root.findall('.//Waarneming'):
                tijd_elem = waarneming.find('.//Tijdstip')
                waarde_elem = waarneming.find('.//Meetwaarde/Waarde')
                
                if tijd_elem is not None and waarde_elem is not None:
                    timestamp = datetime.fromisoformat(tijd_elem.text.replace('Z', '+00:00'))
                    water_level = float(waarde_elem.text)
                    
                    tide_data.append(TideData(
                        datetime=timestamp,
                        water_level=water_level,
                        station=station_code,
                        measurement_type=forecast_type
                    ))
            
            return sorted(tide_data, key=lambda x: x.datetime)
            
        except Exception as e:
            logger.error(f"Error parsing forecast XML: {e}")
            return []

    def _parse_water_level_json(self, json_text: str, station_code: str) -> Optional[TideData]:
        """Parse current water level from RWS JSON response (WaarnemingenLijst)."""
        try:
            import json as _json
            data = _json.loads(json_text)
            waarnemingen = data.get("WaarnemingenLijst") or []
            if not isinstance(waarnemingen, list) or not waarnemingen:
                return None

            latest_ts: Optional[datetime] = None
            latest_value: Optional[float] = None

            # One-time sample logger: print keys of first MetingenLijst item to help stabilize parser
            try:
                first = waarnemingen[0]
                met = first.get("MetingenLijst")
                sample = None
                if isinstance(met, list) and met:
                    sample = met[0]
                elif isinstance(first, dict):
                    sample = first
                if isinstance(sample, dict):
                    keys_preview = list(sample.keys())[:10]
                    snippet_val = sample.get("Meetwaarde") or sample.get("Waarde")
                    logger.info(f"RWS sample MetingenLijst keys={keys_preview} value_snippet={str(snippet_val)[:60]}")
            except Exception:
                pass

            for item in waarnemingen:
                # Two possible shapes: direct measurement on item, or nested under MetingenLijst
                metingen = item.get("MetingenLijst")
                if isinstance(metingen, list) and metingen:
                    candidates = metingen
                else:
                    candidates = [item]

                for m in candidates:
                    tijd = m.get("Tijdstip") or m.get("Datumtijd")
                    waarde = None
                    meetwaarde = m.get("Meetwaarde") or {}
                    if isinstance(meetwaarde, dict):
                        # Prefer Waarde, but sometimes value may live under a nested dict/value
                        if meetwaarde.get("Waarde") is not None:
                            waarde = meetwaarde.get("Waarde")
                        else:
                            # fallback: find first numeric-like field in meetwaarde
                            for k, v in meetwaarde.items():
                                if isinstance(v, (int, float)):
                                    waarde = v
                                    break
                    # Sometimes value may be directly under 'Waarde'
                    if waarde is None and m.get("Waarde") is not None:
                        waarde = m.get("Waarde")
                    if tijd and waarde is not None:
                        try:
                            ts = datetime.fromisoformat(str(tijd).replace('Z', '+00:00'))
                            # Extract unit if available and normalize to meters
                            unit = self._extract_unit_code(meetwaarde) if isinstance(meetwaarde, dict) else None
                            val = self._normalize_water_level(float(str(waarde).replace(',', '.')), unit)
                        except Exception:
                            continue
                        if latest_ts is None or ts > latest_ts:
                            latest_ts = ts
                            latest_value = val

            if latest_ts is not None and latest_value is not None:
                return TideData(
                    datetime=latest_ts,
                    water_level=latest_value,
                    station=station_code,
                    measurement_type="measured"
                )
            return None
        except Exception as e:
            logger.error(f"Error parsing water level JSON: {e}")
            return None

    def _parse_forecast_json(self, json_text: str, station_code: str, forecast_type: str) -> List[TideData]:
        """Parse forecast JSON into TideData list."""
        try:
            import json as _json
            data = _json.loads(json_text)
            waarnemingen = data.get("WaarnemingenLijst") or []
            out: List[TideData] = []
            for item in waarnemingen:
                tijd = item.get("Tijdstip") or item.get("Datumtijd")
                meetwaarde = item.get("Meetwaarde") or {}
                waarde = meetwaarde.get("Waarde") if isinstance(meetwaarde, dict) else None
                if tijd and waarde is not None:
                    ts = datetime.fromisoformat(str(tijd).replace('Z', '+00:00'))
                    out.append(TideData(
                        datetime=ts,
                        water_level=float(waarde),
                        station=station_code,
                        measurement_type=forecast_type
                    ))
            return sorted(out, key=lambda x: x.datetime)
        except Exception as e:
            logger.error(f"Error parsing forecast JSON: {e}")
            return []
    
    def _extract_unit_code(self, meetwaarde: Any) -> Optional[str]:
        """Extract unit code from a Meetwaarde object if present.
        Supports shapes like { 'Eenheid': { 'Code': 'cm' } } or { 'Eenheid': 'm' }.
        Returns lowercase code like 'm', 'cm', 'mm' or None.
        """
        try:
            eenheid = meetwaarde.get("Eenheid")
            if eenheid is None:
                return None
            if isinstance(eenheid, dict):
                code = eenheid.get("Code") or eenheid.get("code") or eenheid.get("Naam") or eenheid.get("name")
                if isinstance(code, str):
                    return code.strip().lower()
            if isinstance(eenheid, list) and eenheid:
                first = eenheid[0]
                if isinstance(first, dict):
                    code = first.get("Code") or first.get("code")
                    if isinstance(code, str):
                        return code.strip().lower()
            if isinstance(eenheid, str):
                return eenheid.strip().lower()
        except Exception:
            return None
        return None

    def _normalize_water_level(self, raw_value: float, unit: Optional[str]) -> float:
        """Normalize raw water level to meters.
        - If unit is provided, convert accordingly.
        - If unit missing, heuristically downscale values outside plausible meter ranges.
        """
        try:
            if unit:
                u = unit.strip().lower()
                if u in ("m", "meter", "meters", "mtr"):
                    return raw_value
                if u in ("cm", "centimeter", "centimeters", "cmt"):
                    return raw_value / 100.0
                if u in ("mm", "millimeter", "millimeters", "mmt"):
                    return raw_value / 1000.0
                if u in ("dm", "decimeter", "decimeters", "dmt"):
                    return raw_value / 10.0
            # Heuristic: realistic water levels (NAP) are within ±10 m generally.
            # If value magnitude is suspiciously large, assume centimeters.
            if abs(raw_value) > 20 and abs(raw_value) < 2000:
                return raw_value / 100.0
            if abs(raw_value) >= 2000:
                return raw_value / 1000.0
        except Exception:
            pass
        return raw_value

    def _get_mock_current_level(self, station_code: str) -> TideData:
        """Generate mock current water level for development/testing"""
        import math
        
        # Simulate tidal cycle (approximately 12.5 hour period)
        current_time = datetime.now(timezone.utc)
        hours_since_midnight = current_time.hour + current_time.minute / 60.0
        
        # Simple sinusoidal tide model
        tide_amplitude = 2.0  # meters
        tide_mean = 2.1  # meters above NAP
        tide_period = 12.5  # hours
        
        tide_phase = (hours_since_midnight / tide_period) * 2 * math.pi
        water_level = tide_mean + tide_amplitude * math.sin(tide_phase)
        
        return TideData(
            datetime=current_time,
            water_level=round(water_level, 2),
            station=station_code,
            measurement_type="measured"
        )
    
    def _get_mock_forecast(self, station_code: str, hours_ahead: int) -> List[TideData]:
        """Generate mock forecast data for development/testing"""
        import math
        
        forecast_data = []
        current_time = datetime.now(timezone.utc)
        
        for hour in range(hours_ahead):
            forecast_time = current_time + timedelta(hours=hour)
            hours_since_midnight = forecast_time.hour + forecast_time.minute / 60.0
            
            # Simple sinusoidal tide model
            tide_amplitude = 2.0
            tide_mean = 2.1
            tide_period = 12.5
            
            tide_phase = (hours_since_midnight / tide_period) * 2 * math.pi
            water_level = tide_mean + tide_amplitude * math.sin(tide_phase)
            
            forecast_data.append(TideData(
                datetime=forecast_time,
                water_level=round(water_level, 2),
                station=station_code,
                measurement_type="predicted"
            ))
        
        return forecast_data
    
    def is_navigation_safe(self, water_level: float, vessel_draft: float) -> bool:
        """
        Determine if current water level is safe for navigation.
        
        Args:
            water_level: Current water level in meters above NAP
            vessel_draft: Vessel draft in meters
            
        Returns:
            True if navigation is safe
        """
        # Simple safety check: water level should be at least 1.5m above vessel draft
        safety_margin = 1.5  # meters
        required_level = vessel_draft - 3.0 + safety_margin  # NAP is ~3m below mean low water
        
        return water_level >= required_level
    
    def get_next_favorable_tide(self, forecast_data: List[TideData], 
                               vessel_draft: float) -> Optional[TideData]:
        """
        Find the next favorable tide window for a vessel.
        
        Args:
            forecast_data: List of TideData points
            vessel_draft: Vessel draft in meters
            
        Returns:
            Next favorable TideData point or None
        """
        for tide_point in forecast_data:
            if self.is_navigation_safe(tide_point.water_level, vessel_draft):
                return tide_point
        
        return None
    
    async def get_station_info(self, station_code: str) -> Optional[TideStation]:
        """
        Get information about a tidal station.
        
        Args:
            station_code: RWS station code
            
        Returns:
            TideStation object or None if not found
        """
        return self.stations.get(station_code)
    
    def get_available_stations(self) -> List[TideStation]:
        """
        Get list of all available tidal stations.
        
        Returns:
            List of TideStation objects
        """
        return list(self.stations.values())
