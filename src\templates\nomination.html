{% extends "base.html" %}

{% block title %}Vessel Nomination - Terneuzen Terminal Jetty Planning{% endblock %}

{% block head %}
    <style>
        :root {
            --primary-color: var(--primary-teal);
            --primary-hover: var(--dark-teal);
            --secondary-color: var(--secondary-teal);
            --success-color: var(--success-green);
            --danger-color: var(--error-red);
            --warning-color: var(--warning-amber);
            --surface-color: var(--text-light);
            --background-color: var(--background-light);
            --border-color: var(--background-medium);
            --text-primary: var(--text-dark);
            --text-secondary: var(--text-medium);
            --text-muted: var(--text-medium);
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }
        
        .content-header {
            margin-bottom: 2rem;
            padding: 0 0.5rem;
        }
        
        .header-left h1 {
            margin: 0;
            color: var(--text-primary);
            font-size: 2.25rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header-left p {
            margin: 0.5rem 0 0 0;
            color: var(--text-secondary);
            font-size: 1.125rem;
            line-height: 1.6;
        }
        
        .nomination-form {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }
        
        .form-steps {
            display: flex;
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-bottom: 1px solid var(--border-color);
            padding: 0;
            margin: 0;
            position: relative;
        }
        
        .form-steps::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-teal), var(--accent-orange));
        }
        
        .step {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem 1rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            border-right: 1px solid var(--border-color);
        }
        
        .step:last-child {
            border-right: none;
        }
        
        .step i {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .step div {
            font-weight: 600;
            font-size: 0.875rem;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .step:hover:not(.active) {
            background: rgba(0, 169, 160, 0.08);
            transform: translateY(-2px);
        }
        
        .step.active {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            transform: scale(1.02);
        }
        
        .step.completed {
            background: linear-gradient(135deg, var(--success-color), #047857);
            color: white;
        }
        
        .step.active::after,
        .step.completed::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid transparent;
            border-right: 8px solid transparent;
            border-top: 8px solid currentColor;
        }
        
        .step-content {
            display: none;
            padding: 2.5rem;
            animation: fadeIn 0.5s ease-in-out;
        }
        
        .step-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .section-title {
            color: var(--text-primary);
            margin-bottom: 2rem;
            font-size: 1.75rem;
            font-weight: 700;
            position: relative;
            padding-bottom: 0.75rem;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 3rem;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            border-radius: 2px;
        }
        
        .form-group {
            margin-bottom: 1.75rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .form-group input, 
        .form-group select, 
        .form-group textarea {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: var(--surface-color);
            color: var(--text-primary);
        }
        /* Checkbox alignment and styling */
        .form-group input[type="checkbox"] {
            width: auto;
            padding: 0;
            border: none;
            box-shadow: none;
            transform: none;
            accent-color: var(--primary-color);
        }
        .checkbox-label {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0;
            cursor: pointer;
            text-transform: none;
        }
        
        .form-group input:focus, 
        .form-group select:focus, 
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            transform: translateY(-1px);
        }
        
        .form-group input:hover:not(:focus),
        .form-group select:hover:not(:focus),
        .form-group textarea:hover:not(:focus) {
            border-color: var(--primary-color);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }
        
        .form-row-thirds {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1.5rem;
        }
        
        .form-row-quarters {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
        }
        
        .form-help-text, .help-text {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: 0.375rem;
            line-height: 1.4;
        }
        /* Align bottom actions with global form-actions pattern */
        .form-actions--split { display: flex; justify-content: space-between; align-items: center; }
        .form-actions--split .left-actions,
        .form-actions--split .right-actions { display: flex; gap: 0.75rem; align-items: center; }
        
        .required {
            color: var(--danger-color);
        }
        
        .predictions-panel {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .predictions-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-teal), var(--accent-orange));
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }
        
        .prediction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.875rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .prediction-item:last-child {
            border-bottom: none;
        }
        
        .prediction-label {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .prediction-value {
            font-size: 1.125rem;
            font-weight: 700;
            color: var(--success-color);
        }
        
        .confidence-bar {
            width: 100px;
            height: 8px;
            background: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin-left: 0.625rem;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--danger-color), var(--warning-color), var(--success-color));
            transition: width 0.3s ease;
        }
        
        .model-info-panel {
            margin: 1rem 0;
            padding: 0.75rem 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            font-size: 0.85rem;
        }
        
        .model-info-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .model-info-item {
            display: flex;
            align-items: center;
            gap: 0.4rem;
            color: #6c757d;
        }
        
        .model-info-item i {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .model-label {
            font-weight: 500;
        }
        
        .model-value {
            font-weight: 600;
            color: var(--text-primary);
            font-family: 'Courier New', monospace;
        }
        
        /* Model Selection Styles */
        .model-selection-section {
            background: linear-gradient(135deg, #eef8f8 0%, #dff3f2 100%);
            border: 1px solid var(--primary-teal);
            border-radius: var(--radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-md);
        }
        
        .model-selection-section:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .model-selection-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-teal), var(--secondary-teal), var(--dark-teal));
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }
        
        .model-selection-section h4 {
            color: var(--dark-teal);
            margin-bottom: 1rem;
            font-weight: 700;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .model-selection-section h4 i {
            font-size: 1.5rem;
            background: linear-gradient(135deg, var(--primary-teal), var(--secondary-teal));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .model-selection-section .help-text {
            margin-bottom: 1.5rem;
            color: var(--dark-teal);
            font-weight: 500;
        }
        
        #model-select {
            background: var(--surface-color);
            border: 2px solid var(--primary-teal);
            border-radius: var(--radius-md);
            padding: 1rem 1.25rem;
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background-image: linear-gradient(45deg, transparent 40%, var(--primary-teal) 40%, var(--primary-teal) 60%, transparent 60%);
            background-size: 12px 12px;
            background-repeat: no-repeat;
            background-position: right 12px center;
            appearance: none;
            cursor: pointer;
        }
        
        #model-select:hover {
            border-color: var(--secondary-teal);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 169, 160, 0.2);
        }
        
        #model-select:focus {
            outline: none;
            border-color: var(--dark-teal);
            box-shadow: 0 0 0 4px rgba(0, 169, 160, 0.15);
            transform: translateY(-1px);
        }
        
        .model-details-panel {
            margin-top: 1.5rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--surface-color) 0%, #f8fafc 100%);
            border: 1px solid #e0f2fe;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            position: relative;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .model-details-panel.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .model-details-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-teal), var(--secondary-teal));
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }
        
        .model-details-panel .model-info-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.25rem;
            align-items: start;
        }
        
        .model-details-panel .model-info-item {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 1rem;
            background: var(--surface-color);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            border: 1px solid #e0f2fe;
        }
        
        .model-details-panel .model-info-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: #0ea5e9;
        }
        
        .model-details-panel .model-info-item i {
            font-size: 1.25rem;
            color: var(--primary-teal);
            margin-bottom: 0.25rem;
        }
        
        .model-details-panel .model-label {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .model-details-panel .model-value {
            font-size: 0.95rem;
            font-weight: 600;
            color: var(--text-primary);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            word-break: break-all;
        }
        
        /* Jetty Selection Section */
        .jetty-selection-section {
            background: linear-gradient(135deg, #e6fffb 0%, #ccfbf6 100%);
            border: 1px solid var(--primary-teal);
            border-radius: var(--radius-lg);
            padding: 2rem;
            margin-bottom: 2rem;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-md);
        }
        
        .jetty-selection-section:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .jetty-selection-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-teal), var(--secondary-teal), var(--dark-teal));
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }
        
        .jetty-selection-section h4 {
            color: var(--dark-teal);
            margin-bottom: 1rem;
            font-weight: 700;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .jetty-selection-section h4 i {
            font-size: 1.5rem;
            background: linear-gradient(135deg, var(--primary-teal), var(--secondary-teal));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .jetty-selection-section .help-text {
            margin-bottom: 1.5rem;
            color: var(--dark-teal);
            font-weight: 500;
        }
        
        #preferred-jetty {
            background: var(--surface-color);
            border: 2px solid var(--primary-teal);
            border-radius: var(--radius-md);
            padding: 1rem 1.25rem;
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-primary);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }
        
        #preferred-jetty:hover {
            border-color: var(--secondary-teal);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 169, 160, 0.2);
        }
        
        #preferred-jetty:focus {
            outline: none;
            border-color: var(--dark-teal);
            box-shadow: 0 0 0 4px rgba(0, 169, 160, 0.15);
            transform: translateY(-1px);
        }

        .jetty-recommendations {
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            margin-bottom: 1.5rem;
            border: 1px solid var(--border-color);
        }
        
        .jetty-card {
            padding: 1.125rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .jetty-card:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transform: translateX(4px);
        }
        
        .jetty-card.selected {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-left: 4px solid var(--primary-color);
        }
        
        .jetty-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        
        .jetty-name {
            font-weight: 700;
            color: var(--text-primary);
            font-size: 1.125rem;
        }
        
        .suitability-score {
            background: linear-gradient(135deg, var(--success-color), #047857);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 700;
        }
        
        .jetty-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .business-rules {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: var(--radius-lg);
            padding: 1.125rem;
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .business-rules::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--warning-color), #f59e0b);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }
        
        .rule-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .rule-item:last-child {
            margin-bottom: 0;
        }
        
        .rule-status {
            margin-right: 0.75rem;
            font-size: 1rem;
        }
        
        .rule-status.pass {
            color: var(--success-color);
        }
        
        .rule-status.warning {
            color: var(--warning-color);
        }
        
        .rule-status.fail {
            color: var(--danger-color);
        }
        
        
        .step-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-top: 1px solid var(--border-color);
        }
        
        .loading-spinner {
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1.5rem;
            border-radius: var(--radius-md);
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 1px solid var(--success-color);
            color: #047857;
        }
        
        .alert-warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid var(--warning-color);
            color: #92400e;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
            border: 1px solid var(--danger-color);
            color: #991b1b;
        }
        
        /* Vessel Search Styles */
        .vessel-search-section {
            background: linear-gradient(135deg, #e8fbf9 0%, #d8f7f3 100%);
            border: 1px solid var(--primary-teal);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            margin-bottom: 2rem;
            position: relative;
        }
        
        .vessel-search-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-teal), var(--dark-teal));
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }
        
        .vessel-search-section h4 {
            color: var(--dark-teal);
            margin-bottom: 0.75rem;
            font-weight: 700;
            font-size: 1.125rem;
        }
        
        .search-input-group {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }
        
        .search-input {
            flex: 1;
            padding: 0.875rem 1rem;
            border: 2px solid var(--primary-teal);
            border-radius: var(--radius-md);
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--surface-color);
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-teal);
            box-shadow: 0 0 0 3px rgba(0, 169, 160, 0.1);
        }
        
        .search-results {
            background: var(--surface-color);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            margin-top: 0.75rem;
            max-height: 300px;
            overflow-y: auto;
            box-shadow: var(--shadow-md);
        }
        
        .vessel-result {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .vessel-result:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transform: translateX(4px);
        }
        
        .vessel-result:last-child {
            border-bottom: none;
        }
        
        .vessel-result.selected {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-left: 4px solid var(--primary-color);
        }
        
        .vessel-name {
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
            font-size: 1rem;
        }
        
        .vessel-details {
            font-size: 0.75rem;
            color: var(--text-muted);
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.375rem;
            line-height: 1.4;
        }
        
        .search-help {
            text-align: center;
            margin-top: 0.75rem;
        }
        
        .search-help small {
            color: var(--text-muted);
            font-style: italic;
        }
        
        .manual-entry-toggle {
            text-align: center;
            margin: 1.5rem 0;
        }
        
        .toggle-button {
            background: linear-gradient(135deg, var(--secondary-color), #475569);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            padding: 0.75rem 1.5rem;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .toggle-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .vessel-form {
            margin-top: 1.5rem;
        }
        
        .vessel-form h4 {
            color: var(--text-primary);
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--border-color);
            font-weight: 700;
        }
        
        .selected-vessel-info {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 1px solid #6ee7b7;
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .selected-vessel-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--success-color), #047857);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }
        
        .selected-vessel-info h5 {
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            font-weight: 700;
            font-size: 1.125rem;
        }
        
        .selected-vessel-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
            font-size: 0.875rem;
        }
        
        .info-box {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            box-shadow: var(--shadow-sm);
            position: relative;
        }
        
        .info-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color));
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }
        
        .info-box i {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin-top: 0.125rem;
        }
        
        .info-box h4 {
            margin: 0 0 0.75rem 0;
            color: var(--text-primary);
            font-size: 1.125rem;
            font-weight: 700;
        }
        
        .info-box p {
            margin: 0 0 0.75rem 0;
            color: var(--text-secondary);
            line-height: 1.6;
        }
        
        .info-box ul {
            margin: 0;
            padding-left: 1.25rem;
            color: var(--text-secondary);
        }
        
        .info-box li {
            margin-bottom: 0.375rem;
            line-height: 1.5;
        }
        
        /* Model Upload Styles */
        .model-upload-section {
            background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
            border: 1px solid #a855f7;
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            margin-bottom: 2rem;
            position: relative;
        }
        
        .model-upload-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #8b5cf6, #a855f7);
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }
        
        .model-upload-section h4 {
            color: #7c3aed;
            margin-bottom: 0.75rem;
            font-weight: 700;
            font-size: 1.125rem;
        }
        
        .upload-container {
            margin-top: 1rem;
        }
        
        .upload-area {
            border: 2px dashed #a855f7;
            border-radius: var(--radius-lg);
            padding: 2rem;
            text-align: center;
            background: var(--surface-color);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .upload-area:hover {
            border-color: #8b5cf6;
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            transform: translateY(-2px);
        }
        
        .upload-area.dragover {
            border-color: #059669;
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            transform: scale(1.02);
        }
        
        .upload-content i {
            font-size: 3rem;
            color: #a855f7;
            margin-bottom: 1rem;
            display: block;
        }
        
        .upload-content p {
            font-size: 1.125rem;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .upload-link {
            background: none;
            border: none;
            color: #8b5cf6;
            font-weight: 600;
            text-decoration: underline;
            cursor: pointer;
            font-size: inherit;
        }
        
        .upload-link:hover {
            color: #7c3aed;
        }
        
        .upload-content small {
            color: var(--text-muted);
            font-style: italic;
        }
        
        .upload-progress {
            margin-top: 1rem;
            padding: 1rem;
            background: var(--surface-color);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #8b5cf6, #a855f7);
            width: 0%;
            transition: width 0.3s ease;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .progress-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .upload-result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: var(--radius-md);
            border: 1px solid;
        }
        
        .upload-result.success {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border-color: var(--success-color);
            color: #047857;
        }
        
        .upload-result.error {
            background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
            border-color: var(--danger-color);
            color: #991b1b;
        }
        
        .upload-result h5 {
            margin: 0 0 0.5rem 0;
            font-weight: 700;
        }
        
        .upload-result ul {
            margin: 0.5rem 0 0 1.25rem;
            padding: 0;
        }
        
        .upload-result li {
            margin-bottom: 0.25rem;
        }
        
        /* Prediction Control Section */
        .prediction-control-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
        }
        .prediction-control-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-teal), var(--accent-orange));
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }
        .prediction-control-section { position: relative; }

        /* ML layout grids to reduce scrolling on wide screens */
        .ml-sections { display: grid; grid-template-columns: repeat(12, 1fr); gap: 1rem; }
        .ml-sections .model-selection-section { grid-column: span 6; }
        .ml-sections .model-upload-section { grid-column: span 6; }
        .ml-sections .prediction-control-section { grid-column: span 12; }
        .ml-sections #predictions-loading { grid-column: span 12; }
        .ml-sections #predictions-content { grid-column: span 12; }

        #predictions-content .predictions-grid { display: grid; grid-template-columns: repeat(12, 1fr); gap: 1rem; }
        #predictions-content .predictions-panel { grid-column: span 6; }
        #predictions-content .model-info-panel { grid-column: span 6; }
        #predictions-content .jetty-selection-section { grid-column: span 6; }
        #predictions-content .jetty-recommendations { grid-column: span 6; }
        #predictions-content .business-rules { grid-column: span 12; }

        @media (max-width: 1024px) {
            .ml-sections > * { grid-column: span 12 !important; }
            #predictions-content .predictions-grid > * { grid-column: span 12 !important; }
        }
        
        /* Review grid to reduce scrolling */
        .review-grid { display: grid; grid-template-columns: repeat(12, 1fr); gap: 1rem; }
        .review-grid .form-section { background: var(--surface-color); border: 1px solid var(--border-color); border-radius: var(--radius-md); padding: 1rem; }
        .review-grid .form-section h4 { margin-top: 0; }
        .review-grid .vessel-section { grid-column: span 6; }
        .review-grid .cargo-section { grid-column: span 6; }
        .review-grid .ml-section { grid-column: span 6; }
        .review-grid .jetty-section { grid-column: span 6; }
        @media (max-width: 1024px) { .review-grid > * { grid-column: span 12 !important; } }
        
        .prediction-control-section h4 {
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-weight: 700;
        }
        
        .prediction-buttons {
            margin-top: 1rem;
            display: flex;
            gap: 1rem;
            align-items: center;
        }
        
        .model-change-notice {
            margin-top: 1rem;
        }
        
        .model-change-notice .alert {
            margin: 0;
        }
        
        #generate-predictions-btn.calculating {
            background: var(--warning-color);
            border-color: var(--warning-color);
            cursor: not-allowed;
        }
        
        #generate-predictions-btn.completed {
            background: var(--success-color);
            border-color: var(--success-color);
        }
        
        /* Tank Selection Styles */
        .tank-selection {
            margin-top: 1.5rem;
            padding: 1.25rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
        }

        .tank-selection h5 {
            color: var(--text-primary);
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .tank-selection h5 i {
            color: var(--primary-color);
        }

        .tank-selection .help-text {
            margin-bottom: 1rem;
            color: var(--text-secondary);
            font-style: italic;
        }

        .tank-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .tank-card {
            background: var(--surface-color);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            box-shadow: var(--shadow-sm);
        }

        .tank-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .tank-card.selected {
            border-color: var(--success-color);
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .tank-card.selected::after {
            content: '✓';
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: var(--success-color);
            color: white;
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .tank-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border-color);
        }

        .tank-name {
            font-weight: 700;
            color: var(--text-primary);
            font-size: 1rem;
        }

        .tank-id {
            background: var(--primary-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            font-size: 0.7rem;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .tank-details {
            margin-bottom: 0.75rem;
        }

        .tank-metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.375rem;
            font-size: 0.85rem;
        }

        .tank-metric:last-child {
            margin-bottom: 0;
        }

        .metric-label {
            color: var(--text-secondary);
            font-weight: 500;
        }

        .metric-value {
            color: var(--text-primary);
            font-weight: 600;
            text-align: right;
        }

        .tank-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding-top: 0.5rem;
            border-top: 1px solid var(--border-color);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .status-indicator.operational {
            background: var(--success-color);
            box-shadow: 0 0 6px rgba(5, 150, 105, 0.4);
        }

        .status-indicator.maintenance {
            background: var(--warning-color);
            box-shadow: 0 0 6px rgba(217, 119, 6, 0.4);
        }

        .status-text {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .no-tanks-available {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
            background: var(--surface-color);
            border-radius: var(--radius-lg);
            border: 2px dashed var(--border-color);
        }

        .no-tanks-available i {
            font-size: 2rem;
            color: var(--warning-color);
            margin-bottom: 0.5rem;
            display: block;
        }

        .no-tanks-available p {
            margin: 0;
            font-weight: 500;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .content-header {
                margin-bottom: 1.5rem;
            }

            .header-left h1 {
                font-size: 1.75rem;
            }

            .step-content {
                padding: 1.5rem;
            }

            .form-grid,
            .form-row,
            .form-row-thirds,
            .form-row-quarters {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .step-navigation {
                padding: 1rem 1.5rem;
                flex-direction: column;
                gap: 1rem;
            }

            .selected-vessel-details,
            .jetty-details,
            .vessel-details,
            .tank-grid {
                grid-template-columns: 1fr;
            }

            .tank-card {
                padding: 0.875rem;
            }

            .tank-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
    <!-- Leaflet for AIS map -->
    <link rel="stylesheet" href="/static/vendor/css/leaflet-1.9.4.min.css"/>
    <script src="/static/vendor/js/leaflet-1.9.4.min.js"></script>
    <script src="/static/js/ship-map.js" nonce="{{ nonce }}"></script>
{% endblock %}

{% block header %}Vessel Nomination{% endblock %}
{% block content %}

    <div class="main-content">
        <div class="nomination-form card">
            <!-- Step Navigation -->
            <div class="form-steps">
                <div class="step active" data-step="1">
                    <i class="fas fa-ship"></i>
                    <div>Vessel Details</div>
                </div>
                <div class="step" data-step="2">
                    <i class="fas fa-boxes"></i>
                    <div>Cargo Information</div>
                </div>
                <div class="step" data-step="3">
                    <i class="fas fa-brain"></i>
                    <div>ML Predictions</div>
                </div>
                <div class="step" data-step="4">
                    <i class="fas fa-check-circle"></i>
                    <div>Review & Submit</div>
                </div>
            </div>

            <form id="nomination-form">
                <!-- Hidden identifiers populated when selecting from AIS -->
                <input type="hidden" id="vessel-mmsi" name="mmsi" />
                <input type="hidden" id="vessel-imo" name="imo" />
                <!-- Step 1: Vessel Details -->
                <div class="step-content active" data-step="1">
                    <h3 class="section-title">Vessel Information</h3>
                    
                    <!-- AIS Vessel Search Section -->
                    <div class="vessel-search-section">
                        <h4><i class="fas fa-search"></i> Search Existing Vessels (AIS Data)</h4>
                        <p class="help-text">Search for vessels currently tracked by AIS to auto-populate vessel details</p>
                        
                        <div class="card" style="margin-bottom: 1rem;">
                            <div class="card-body" style="padding: 0;">
                                <div id="nomination-ais-map" style="height: 320px; border-radius: 8px; overflow: hidden;"></div>
                            </div>
                        </div>

                        <div class="search-container">
                            <div class="search-input-group">
                                <input type="text" id="vessel-search" placeholder="Search by vessel name, MMSI, or IMO..." class="search-input">
                                <button type="button" id="search-vessels-btn" class="btn btn-secondary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                            <div class="search-filters" style="margin-top: 8px; display: flex; gap: 12px; align-items: center;">
                                <label style="display:flex; align-items:center; gap:6px;">
                                    <input type="checkbox" id="filter-only-liquid">
                                    <span>Only liquid cargo</span>
                                </label>
                                <div style="display:flex; align-items:center; gap:6px;">
                                    <label for="filter-type-substrings" style="white-space:nowrap;">Type contains</label>
                                    <input id="filter-type-substrings" type="text" class="search-input" style="height:32px; padding:6px 10px; width:240px;" placeholder="e.g. tanker, oil, inland">
                                </div>
                                <label style="display:flex; align-items:center; gap:6px;">
                                    <input type="checkbox" id="filter-extended-search">
                                    <span>Extended search (names outside radius)</span>
                                </label>
                            </div>
                            
                            <div id="vessel-search-results" class="search-results" style="display: none;">
                                <!-- Search results will appear here -->
                            </div>
                        </div>
                        
                        <div class="search-help">
                            <small><i class="fas fa-info-circle"></i> Can't find your vessel? Use manual entry below</small>
                        </div>
                    </div>

                    <!-- Manual Entry Toggle -->
                    <div class="manual-entry-toggle">
                        <button type="button" id="toggle-manual-entry" class="btn btn-secondary">
                            <i class="fas fa-edit"></i> Enter Vessel Details Manually
                        </button>
                    </div>

                    <!-- Vessel Details Form -->
                    <div id="vessel-details-form" class="vessel-form" style="display: none;">
                        <h4><i class="fas fa-ship"></i> Vessel Details</h4>
                        <div class="form-grid">
                        <div class="form-group">
                            <label for="vessel-name">Vessel Name <span class="required">*</span></label>
                            <input type="text" id="vessel-name" name="vessel_name" required 
                                   placeholder="Enter vessel name">
                            <div class="form-help-text">Official vessel name as registered</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="vessel-type">Vessel Type <span class="required">*</span></label>
                            <select id="vessel-type" name="vessel_type" required>
                                <option value="">Select vessel type</option>
                                <option value="tanker">Vessel (seagoing)</option>
                                <option value="barge">Barge</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="vessel-length">Length Overall (m) <span class="required">*</span></label>
                            <input type="number" id="vessel-length" name="length" required 
                                   min="50" max="235" step="0.1" placeholder="180">
                            <div class="form-help-text">Maximum 235m (terminal jetty limit)</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="vessel-beam">Beam (m) <span class="required">*</span></label>
                            <input type="number" id="vessel-beam" name="beam" required 
                                   min="10" max="34" step="0.1" placeholder="25">
                            <div class="form-help-text">Maximum 34m (terminal jetty limit)</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="vessel-draft">Draft (m) <span class="required">*</span></label>
                            <input type="number" id="vessel-draft" name="draft" required 
                                   min="2" max="12.8" step="0.1" placeholder="8.5">
                            <div class="form-help-text">Maximum 12.8m (terminal jetty limit)</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="vessel-dwt">Deadweight (tonnes) <span class="required">*</span></label>
                            <input type="number" id="vessel-dwt" name="deadweight" required 
                                   min="1000" max="60000" step="100" placeholder="25000">
                            <div class="form-help-text">Maximum 60,000 tonnes (terminal jetty limit)</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="vessel-eta">Estimated Time of Arrival <span class="required">*</span></label>
                            <input type="datetime-local" id="vessel-eta" name="eta" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="vessel-customer">Customer <span class="required">*</span></label>
                            <select id="vessel-customer" name="customer" required>
                                <option value="">Select customer</option>
                                <option value="Trafigura PTE LTD">Trafigura PTE LTD</option>
                                <option value="Shell Trading Rotterdam BV">Shell Trading Rotterdam BV</option>
                                <option value="Dow Benelux BV">Dow Benelux BV</option>
                                <option value="Chevron B.V.">Chevron B.V.</option>
                                <option value="Trinseo netherlands BV">Trinseo netherlands BV</option>
                                <option value="LYB B.V. on behalf of Covestro PO LLC">LYB B.V. on behalf of Covestro PO LLC</option>
                                <option value="Lyondell Chemie Nederland B.V.">Lyondell Chemie Nederland B.V.</option>
                                <option value="BA Trading BV">BA Trading BV</option>
                                <option value="Basell Polyolefine GmbH">Basell Polyolefine GmbH</option>
                            </select>
                            <div class="form-help-text">Select the customer company for this nomination</div>
                        </div>
                    </div>
                    
                        <div class="form-group">
                            <label for="vessel-notes">Additional Notes</label>
                            <textarea id="vessel-notes" name="notes" rows="3" 
                                      placeholder="Any special requirements, first visit, etc."></textarea>
                        </div>
                    </div>
                    </div>
                </div>

                <!-- Step 2: Cargo Information -->
                <div class="step-content" data-step="2">
                    <h3 class="section-title">Cargo Details</h3>
                    
                    <div id="cargo-container">
                        <div class="cargo-item" data-cargo="0">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>Product Type <span class="required">*</span></label>
                                    <select name="cargo_product_0" required>
                                        <option value="">Select product</option>
                                        <option value="NAPHTA" selected>NAPHTA</option>
                                        <option value="BENZEEN E">BENZEEN E</option>
                                        <option value="PROPYLEENOXIDE">PROPYLEENOXIDE</option>
                                        <option value="SBP 100/140">SBP 100/140</option>
                                        <option value="CHEMFEED NAPHTA">CHEMFEED NAPHTA</option>
                                        <option value="GTL FLUID G100">GTL FLUID G100</option>
                                        <option value="1-HEXEEN">1-HEXEEN</option>
                                        <option value="GAS CONDENSATES">GAS CONDENSATES</option>
                                        <option value="BENZENE HEARTCUT">BENZENE HEARTCUT</option>
                                        <option value="PYGAS UNHYDROGENATED">PYGAS UNHYDROGENATED</option>
                                        <option value="Pygas">Pygas</option>
                                        <option value="NAPHTA FRSR">NAPHTA FRSR</option>
                                        <option value="ACRYLONITRIL">ACRYLONITRIL</option>
                                        <option value="BENZENE HEARTCUT-BHC (ZR)">BENZENE HEARTCUT-BHC (ZR)</option>
                                        <option value="NAPHTHA CATALYTIC REFORMED">NAPHTHA CATALYTIC REFORMED</option>
                                        <option value="LIGHT NAPHTA">LIGHT NAPHTA</option>
                                        <option value="NESTE CIRCULAR MIDDLE DISTILLATE">NESTE CIRCULAR MIDDLE DISTILLATE</option>
                                        <option value="HEAVY NAPHTA (petroleum) HYDROCRACKED">HEAVY NAPHTA (petroleum) HYDROCRACKED</option>
                                        <option value="NAPHTA - GASOLINE">NAPHTA - GASOLINE</option>
                                        <option value="NAPHTA (Hydro,Heavy)">NAPHTA (Hydro,Heavy)</option>
                                        <option value="GTL SARALINE 185V">GTL SARALINE 185V</option>
                                        <option value="NATURAL GAS CONDENSATES">NATURAL GAS CONDENSATES</option>
                                        <option value="DSD 482.02 DEVELOPMENTAL POLYOL">DSD 482.02 DEVELOPMENTAL POLYOL</option>
                                    </select>
                                    <div class="form-help-text">Select the specific product type as it appears in operational records</div>
                                </div>
                                
                                <div class="form-group">
                                    <label>Volume (m³) <span class="required">*</span></label>
                                    <input type="number" name="cargo_volume_0" required 
                                           min="100" max="50000" step="10" value="15000">
                                </div>
                                
                                <div class="form-group">
                                    <label>Operation Type <span class="required">*</span></label>
                                    <select name="cargo_operation_0" required>
                                        <option value="">Select operation</option>
                                        <option value="Ex">Export (Ex) - Loading from terminal</option>
                                        <option value="In" selected>Import (In) - Discharge to terminal</option>
                                    </select>
                                    <div class="form-help-text">Export = vessel loads cargo from terminal, Import = vessel discharges cargo to terminal</div>
                                </div>
                                
                                <div class="form-group">
                                    <label>Connection Size</label>
                                    <select name="cargo_connection_0">
                                        <option value="4\"">4" ANSI</option>
                                        <option value="6\"">6" ANSI</option>
                                        <option value="8\"">8" ANSI</option>
                                        <option value="10\"">10" ANSI</option>
                                        <option value="12\"" selected>12" ANSI</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="cargo_vapor_return_0">
                                    Requires Vapor Return
                                </label>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="cargo_nitrogen_purge_0">
                                    Requires Nitrogen Purging
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-secondary" id="add-cargo">
                        <i class="fas fa-plus"></i> Add Another Cargo
                    </button>
                </div>

                <!-- Step 3: ML Predictions -->
                <div class="step-content" data-step="3">
                    <h3 class="section-title">ML-Powered Predictions & Recommendations</h3>
                    
                    <div class="ml-sections">
                    <!-- Model Selection Section -->
                    <div class="model-selection-section">
                        <h4><i class="fas fa-cogs"></i> Select ML Model</h4>
                        <p class="help-text">Choose which ML model to use for predictions, or use the default model</p>
                        
                        <div class="form-group">
                            <label for="model-select">Prediction Model <span class="required">*</span></label>
                            <select id="model-select" name="selected_model" required>
                                <option value="">Loading available models...</option>
                            </select>
                            <div class="form-help-text">Select from available trained models. Newly uploaded models will appear here after upload.</div>
                        </div>
                        
                        <div class="model-details-panel" id="model-details" style="display: none;">
                            <div class="model-info-content">
                                <div class="model-info-item">
                                    <i class="fas fa-tag"></i>
                                    <span class="model-label">Model Type:</span>
                                    <span class="model-value" id="selected-model-type">-</span>
                                </div>
                                <div class="model-info-item">
                                    <i class="fas fa-file"></i>
                                    <span class="model-label">Filename:</span>
                                    <span class="model-value" id="selected-model-filename">-</span>
                                </div>
                                <div class="model-info-item">
                                    <i class="fas fa-info-circle"></i>
                                    <span class="model-label">Description:</span>
                                    <span class="model-value" id="selected-model-description">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Model Upload Section -->
                    <div class="model-upload-section">
                        <h4><i class="fas fa-upload"></i> Upload New ML Model (Optional)</h4>
                        <p class="help-text">Upload a trained ML model (.zip or .joblib) to improve prediction accuracy for this nomination</p>
                        
                        <div class="upload-container">
                            <div class="upload-area" id="model-upload-area">
                                <input type="file" id="model-file-input" accept=".zip,.joblib" style="display: none;">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>Drag & drop your model file here, or <button type="button" class="upload-link">browse files</button></p>
                                    <small>Supports .zip and .joblib files</small>
                                </div>
                            </div>
                            
                            <div id="upload-progress" class="upload-progress" style="display: none;">
                                <div class="progress-bar">
                                    <div class="progress-fill"></div>
                                </div>
                                <span class="progress-text">Uploading...</span>
                            </div>
                            
                            <div id="upload-result" class="upload-result" style="display: none;">
                                <!-- Upload result will appear here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Prediction Control Section -->
                    <div class="prediction-control-section">
                        <h4><i class="fas fa-play-circle"></i> Generate Predictions</h4>
                        <p class="help-text">Click the button below to generate ML predictions and jetty recommendations based on your vessel and cargo details.</p>
                        
                        <div class="prediction-buttons">
                            <button type="button" id="generate-predictions-btn" class="btn btn-primary">
                                <i class="fas fa-calculator"></i> Calculate Predictions
                            </button>
                            <button type="button" id="recalculate-predictions-btn" class="btn btn-secondary" style="display: none;">
                                <i class="fas fa-redo"></i> Recalculate with New Model
                            </button>
                        </div>
                        
                        <div class="model-change-notice" id="model-change-notice" style="display: none;">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> 
                                Model selection has changed. Click "Recalculate" to update predictions with the new model.
                            </div>
                        </div>
                    </div>
                    
                    <div id="predictions-loading" style="display: none;">
                        <div class="alert alert-info">
                            <i class="fas fa-spinner fa-spin"></i> 
                            Generating ML predictions and analyzing business rules...
                        </div>
                    </div>
                    
                    <div id="predictions-content" style="display: none;">
                        <div class="predictions-grid">
                        <!-- Time Predictions -->
                        <div class="predictions-panel">
                            <h4><i class="fas fa-clock"></i> Predicted Terminal Time</h4>
                            <div class="prediction-item">
                                <span class="prediction-label">Total Terminal Time:</span>
                                <div style="display: flex; align-items: center;">
                                    <span class="prediction-value" id="pred-total">-</span>
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" id="conf-total"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Model Information -->
                        <div class="model-info-panel">
                            <div class="model-info-content">
                                <div class="model-info-item">
                                    <i class="fas fa-robot"></i>
                                    <span class="model-label">Active Model:</span>
                                    <span class="model-value" id="model-version">-</span>
                                </div>
                                <div class="model-info-item">
                                    <i class="fas fa-clock"></i>
                                    <span class="model-label">Prediction Time:</span>
                                    <span class="model-value" id="prediction-timestamp">-</span>
                                </div>
                            </div>
                        </div>

                        <!-- Preferred Jetty Selection -->
                        <div class="jetty-selection-section">
                            <h4><i class="fas fa-map-pin"></i> Preferred Jetty Selection</h4>
                            <p class="help-text">Select a preferred jetty for more accurate time predictions and better location mapping</p>
                            
                            <div class="form-group">
                                <label for="preferred-jetty">Preferred Jetty (Optional)</label>
                                <select id="preferred-jetty" name="preferred_jetty">
                                    <option value="">Auto-recommend based on vessel characteristics</option>
                                    <option value="jetty1">Jetty 1</option>
                                    <option value="jetty2">Jetty 2</option>
                                    <option value="jetty3">Jetty 3</option>
                                    <option value="jetty4">Jetty 4</option>
                                    <option value="jetty5">Jetty 5</option>
                                    <option value="jetty6">Jetty 6</option>
                                </select>
                                <div class="form-help-text">If specified, predictions will be calculated for this specific jetty</div>
                            </div>
                        </div>

                        <!-- Jetty Recommendations -->
                        <div class="jetty-recommendations">
                            <h4><i class="fas fa-anchor"></i> Compatible Jetties & Recommendations</h4>
                            <div id="jetty-list">
                                <!-- Jetty cards will be populated here -->
                            </div>
                        </div>

                        <!-- Business Rules Validation -->
                        <div class="business-rules">
                            <h4><i class="fas fa-check-circle"></i> Business Rules Validation</h4>
                            <div id="business-rules-list">
                                <!-- Business rule checks will be populated here -->
                            </div>
                        </div>
                        </div>
                    </div>
                    </div>
                </div>

                <!-- Step 4: Review & Submit -->
                <div class="step-content" data-step="4">
                    <h3 class="section-title">Review Nomination</h3>
                    
                    <div id="nomination-summary" class="review-grid">
                        <!-- Summary will be populated here into grid cards -->
                    </div>
                    
                    <div class="form-group" style="display:none;"><!-- replaced by toast guidance --></div>
                </div>

                <!-- Step Navigation -->
                <div class="form-actions form-actions--split">
                    <div class="left-actions">
                        <button type="button" class="btn btn-secondary" id="prev-step" style="display: none;">
                            <i class="fas fa-arrow-left"></i> Previous
                        </button>
                    </div>
                    <div class="right-actions">
                        <button type="button" class="btn btn-secondary" id="save-draft">
                            <i class="fas fa-save"></i> Save Draft
                        </button>
                        <button type="button" class="btn btn-primary" id="next-step">
                            Next <i class="fas fa-arrow-right"></i>
                        </button>
                        <button type="submit" class="btn btn-primary" id="submit-nomination" style="display: none;">
                            <i class="fas fa-ship"></i> Create Unscheduled Vessel
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script src="/static/js/nomination.js" nonce="{{ nonce }}"></script>
    <script nonce="{{ nonce }}">
        document.addEventListener('DOMContentLoaded', () => {
            if (window.ShipMap) {
                window.nominationMap = new ShipMap('nomination-ais-map', {
                    onShipClick: (ship) => {
                        const input = document.getElementById('vessel-search');
                        if (input && ship.mmsi) {
                            input.value = ship.mmsi;
                            const btn = document.getElementById('search-vessels-btn');
                            if (btn) btn.click();
                        }
                        // Also center map on clicked ship
                        if (ship && ship.mmsi && window.nominationMap && window.nominationMap.focusOnShip) {
                            window.nominationMap.setSelectedShip(ship.mmsi);
                            window.nominationMap.focusOnShip(ship.mmsi);
                        }
                    }
                });
                const refreshNominationMap = async () => {
                    try {
                        const selectedMmsi = document.getElementById('vessel-mmsi')?.value;
                        const url = selectedMmsi ? `/api/tracking/ships?selected_mmsi=${encodeURIComponent(selectedMmsi)}` : '/api/tracking/ships';
                        const res = await fetch(url);
                        if (res.ok) {
                            const data = await res.json();
                            // Debug: surface ship count
                            try { console.debug('Nomination map ships:', (data && data.ships ? data.ships.length : 0), data); } catch (e) {}
                            let ships = data.ships || [];
                            const mmsi = selectedMmsi ? String(selectedMmsi) : null;
                            // Skip synthesizing temporary markers when no AIS position is available.
                            nominationMap.updateShips(ships);
                            // Also highlight currently nominated vessels
                            try {
                                const vesselsRes = await fetch('/api/vessels?status=EN_ROUTE&status=APPROACHING&status=ARRIVED&status=WAITING');
                                if (vesselsRes.ok) {
                                    const vessels = await vesselsRes.json();
                                    const nominatedMmsi = vessels
                                        .map(v => v.mmsi)
                                        .filter(Boolean)
                                        .map(String);
                                    nominationMap.setNominated(nominatedMmsi);
                                }
                            } catch (e) { /* ignore */ }
                            // Reuse selectedMmsi from earlier in this function to avoid TDZ issues
                            if (selectedMmsi && nominationMap.focusOnShip) {
                                nominationMap.setSelectedShip(selectedMmsi);
                                nominationMap.focusOnShip(selectedMmsi, false);
                            }
                        }
                    } catch (e) { console.warn('Failed updating nomination AIS map', e); }
                };
                // Expose for other scripts to trigger immediate refresh after selection
                window.refreshNominationMap = refreshNominationMap;
                refreshNominationMap();
                setInterval(refreshNominationMap, 60000);
            }
        });

        // Tanks: fetched from API (fallback to empty if none)
        let availableTanks = [];
        async function loadTanks() {
            try {
                const res = await fetch('/api/tanks');
                if (!res.ok) return;
                const data = await res.json();
                availableTanks = (Array.isArray(data) ? data : []).map(t => ({
                    id: t.id,
                    name: t.name,
                    capacity: t.capacity ?? t.max_capacity ?? 0,
                    currentLevel: t.current_volume ?? t.current_level ?? 0,
                    productType: t.product_type || null,
                    status: (t.is_operational === false ? 'maintenance' : 'operational')
                }));
            } catch (_) {
                availableTanks = [];
            }
        }

        // Function to update tank selection based on operation type and jetty
        function updateTankSelection(cargoIndex) {
            const cargoItem = document.querySelector(`[data-cargo="${cargoIndex}"]`);
            if (!cargoItem) return;

            const operationType = cargoItem.querySelector(`[name="cargo_operation_${cargoIndex}"]`).value;
            const productType = cargoItem.querySelector(`[name="cargo_product_${cargoIndex}"]`).value;

            // Remove existing tank selection
            const existingTankSelection = cargoItem.querySelector('.tank-selection');
            if (existingTankSelection) {
                existingTankSelection.remove();
            }

            // Create tank selection section
            const tankSelectionDiv = document.createElement('div');
            tankSelectionDiv.className = 'tank-selection';

            const opKind = (operationType === 'Ex' || operationType === 'loading') ? 'loading' : 'unloading';
            if (opKind === 'loading') {
                // For loading: select source tanks (where cargo comes from)
                tankSelectionDiv.innerHTML = `
                    <h5><i class="fas fa-arrow-up"></i> Source Tank(s) - Loading from Terminal</h5>
                    <p class="help-text">Select which terminal tank(s) to load ${productType} from</p>
                    <div class="tank-grid" id="source-tanks-${cargoIndex}">
                        <!-- Source tanks will be populated here -->
                    </div>
                `;
            } else {
                // For unloading: select destination tanks (where cargo goes to)
                tankSelectionDiv.innerHTML = `
                    <h5><i class="fas fa-arrow-down"></i> Destination Tank(s) - Unloading to Terminal</h5>
                    <p class="help-text">Select which terminal tank(s) to unload ${productType} to</p>
                    <div class="tank-grid" id="destination-tanks-${cargoIndex}">
                        <!-- Destination tanks will be populated here -->
                    </div>
                `;
            }

            // Insert after cargo operation selection
            const operationSelect = cargoItem.querySelector(`[name="cargo_operation_${cargoIndex}"]`);
            operationSelect.parentNode.parentNode.appendChild(tankSelectionDiv);

            // Populate tanks based on operation type
            populateTankOptions(cargoIndex, opKind, productType);
        }

        // Function to populate tank options
        function populateTankOptions(cargoIndex, operationType, productType) {
            const tanks = Array.isArray(availableTanks) ? availableTanks : [];

            const containerId = operationType === 'loading' ? `source-tanks-${cargoIndex}` : `destination-tanks-${cargoIndex}`;
            const container = document.getElementById(containerId);

            if (!container) return;

            container.innerHTML = '';

            // Filter tanks based on operation type and product compatibility
            let filteredTanks = tanks.filter(tank => {
                if (tank.status !== 'operational') return false;
                if (operationType === 'loading') {
                    // For loading: tank must contain the product and have content
                    return (tank.productType === productType) && (Number(tank.currentLevel) > 0);
                } else {
                    // For unloading: tank must be empty or contain same product
                    return (tank.productType === null) || (tank.productType === productType);
                }
            });

            if (filteredTanks.length === 0) {
                container.innerHTML = `
                    <div class="no-tanks-available">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>No suitable tanks available for ${operationType} ${productType}</p>
                    </div>
                `;
                return;
            }

            // Create tank selection cards
            filteredTanks.forEach(tank => {
                const tankCard = document.createElement('div');
                tankCard.className = 'tank-card';
                tankCard.setAttribute('data-id', String(tank.id));
                tankCard.onclick = () => selectTank(cargoIndex, tank.id, operationType);

                const utilizationPercent = (Number(tank.currentLevel) / (Number(tank.capacity) || 1) * 100).toFixed(1);
                const availableVolume = Math.max(0, Number(tank.capacity) - Number(tank.currentLevel));

                tankCard.innerHTML = `
                    <div class="tank-header">
                        <span class="tank-name">${tank.name}</span>
                        <span class="tank-id">${tank.id}</span>
                    </div>
                    <div class="tank-details">
                        <div class="tank-metric">
                            <span class="metric-label">Capacity:</span>
                            <span class="metric-value">${tank.capacity.toLocaleString()} m³</span>
                        </div>
                        <div class="tank-metric">
                            <span class="metric-label">Current:</span>
                            <span class="metric-value">${tank.current_level.toLocaleString()} m³ (${utilizationPercent}%)</span>
                        </div>
                        <div class="tank-metric">
                            <span class="metric-label">Available:</span>
                            <span class="metric-value">${availableVolume.toLocaleString()} m³</span>
                        </div>
                    </div>
                    <div class="tank-status">
                        <span class="status-indicator ${tank.status}"></span>
                        <span class="status-text">${tank.status}</span>
                    </div>
                `;

                container.appendChild(tankCard);
            });
        }

        // Function to handle tank selection
        function selectTank(cargoIndex, tankId, operationType) {
            const containerId = operationType === 'loading' ? `source-tanks-${cargoIndex}` : `destination-tanks-${cargoIndex}`;
            const container = document.getElementById(containerId);

            // Toggle selection
            const tankCard = container.querySelector(`.tank-card[data-id="${tankId}"]`);
            if (tankCard) {
                tankCard.classList.toggle('selected');

                // Update hidden input with selected tanks
                updateSelectedTanks(cargoIndex, operationType);
            }
        }

        // Function to update selected tanks in form
        function updateSelectedTanks(cargoIndex, operationType) {
            const containerId = operationType === 'loading' ? `source-tanks-${cargoIndex}` : `destination-tanks-${cargoIndex}`;
            const container = document.getElementById(containerId);
            const selectedTanks = container.querySelectorAll('.tank-card.selected');

            const tankIds = Array.from(selectedTanks).map(card => {
                return card.querySelector('.tank-id').textContent;
            });

            // Update hidden input
            let hiddenInput = document.getElementById(`selected-tanks-${cargoIndex}`);
            if (!hiddenInput) {
                hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.id = `selected-tanks-${cargoIndex}`;
                hiddenInput.name = `cargo_tanks_${cargoIndex}`;
                container.parentNode.appendChild(hiddenInput);
            }
            hiddenInput.value = tankIds.join(',');
        }

        // Update tank selection when operation type or product changes
        document.addEventListener('change', function(e) {
            if (e.target.name && e.target.name.startsWith('cargo_operation_')) {
                const cargoIndex = e.target.name.split('_')[2];
                updateTankSelection(cargoIndex);
            }
            if (e.target.name && e.target.name.startsWith('cargo_product_')) {
                const cargoIndex = e.target.name.split('_')[2];
                updateTankSelection(cargoIndex);
            }
        });

        // Initialize tank selection for first cargo item
        document.addEventListener('DOMContentLoaded', function() {
            loadTanks().finally(() => updateTankSelection(0));
        });
    </script>
{% endblock %}
