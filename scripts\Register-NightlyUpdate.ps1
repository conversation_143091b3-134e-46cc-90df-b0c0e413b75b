Param(
    [string]$Time = "02:30",   # 24h, local time
    [string]$Branch = "main",
    [string]$TaskName = "JettyPlanner Nightly Update"
)

$ErrorActionPreference = 'Stop'

# Compute script paths
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$updateScript = Join-Path $scriptDir "Update-Prod.ps1"

if (-not (Test-Path $updateScript)) {
    Write-Error "Update script not found at $updateScript"
}

# Build the action to run PowerShell non-interactively
$psExe = (Get-Command powershell).Source
$repoRoot = (Resolve-Path (Join-Path $scriptDir "..")).Path
$action = New-ScheduledTaskAction -Execute $psExe -Argument "-NoProfile -ExecutionPolicy Bypass -File `"$updateScript`" -Branch `"$Branch`""

# Daily trigger
$trigger = New-ScheduledTaskTrigger -Daily -At ([DateTime]::Parse($Time))

# Run with highest privileges
$principal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive -RunLevel Highest

# Register task
try {
    Register-ScheduledTask -TaskName $TaskName -Action $action -Trigger $trigger -Principal $principal -Force | Out-Null
    Write-Host "Scheduled task '$TaskName' registered to run daily at $Time" -ForegroundColor Green
} catch {
    Write-Error $_
}

# Show next run time
Get-ScheduledTask -TaskName $TaskName | Get-ScheduledTaskInfo | Select-Object TaskName, NextRunTime | Format-Table | Out-String | Write-Host

