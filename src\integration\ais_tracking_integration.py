"""
AIS Tracking Integration

Enhanced integration between AIS Stream and Ship Tracking Service that prevents
API overloading while providing real-time position updates for nominated vessels.
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Set
import json
from dataclasses import dataclass

from .aisstream_client import AISStreamClient
from ..services.ship_tracking_service import ShipTrackingService, ShipPosition

logger = logging.getLogger(__name__)


@dataclass
class AISThrottleConfig:
    """Configuration for AIS API throttling and rate limiting"""
    max_concurrent_connections: int = 1  # Limit to single connection
    position_update_interval: int = 30  # Seconds between position updates
    tracking_session_duration: int = 3600  # 1 hour tracking sessions
    mmsi_filter_enabled: bool = True  # Only track specific MMSIs
    bounding_box_tight: bool = True  # Use tight bounding box around terminal


class AISTrackingIntegration:
    """Integration service between AIS Stream and Ship Tracking"""
    
    def __init__(self, ais_client: AISStreamClient, tracking_service: ShipTrackingService):
        """
        Initialize AIS tracking integration.
        
        Args:
            ais_client: AIS Stream client
            tracking_service: Ship tracking service
        """
        self.ais_client = ais_client
        self.tracking_service = tracking_service
        self.config = AISThrottleConfig()
        
        # Throttling and rate limiting
        self.last_position_updates: Dict[str, datetime] = {}
        self.active_mmsi_filters: Set[str] = set()
        self.tracking_session_start: Optional[datetime] = None
        self.is_active_tracking: bool = False
        
        # Statistics for monitoring
        self.total_ais_messages: int = 0
        self.filtered_updates: int = 0
        self.successful_updates: int = 0
        
        logger.info("AIS tracking integration initialized with throttling enabled")
    
    def start_tracking_session(self, duration_minutes: int = 60):
        """
        Start a tracked vessel monitoring session.
        
        Args:
            duration_minutes: Duration of tracking session in minutes
        """
        if self.is_active_tracking:
            logger.warning("Tracking session already active")
            return
        
        # Get MMSIs of currently tracked ships
        tracked_mmsis = set(self.tracking_service.tracked_ships.keys())
        
        if not tracked_mmsis:
            logger.info("No ships to track - session not started")
            return
        
        self.active_mmsi_filters = tracked_mmsis
        self.tracking_session_start = datetime.now(timezone.utc)
        self.is_active_tracking = True
        
        # Configure AIS client for optimized tracking
        # 1) Ensure we receive both Class A and B position messages
        try:
            self.ais_client.set_message_types([
                "PositionReport",
                "ExtendedClassBPositionReport",
                "StandardClassBPositionReport",
                "ShipStaticData",
            ])
        except Exception:
            pass

        # 2) Relax the bounding box while MMSI filters are active (global coverage)
        try:
            # Save previous bbox to restore when session stops
            self._saved_bounding_boxes = getattr(self.ais_client, "bounding_boxes", None)
            # World-wide bbox [[S,W],[N,E]]; AIS will still filter by MMSI server-side
            self.ais_client.bounding_boxes = [[-90.0, -180.0], [90.0, 180.0]]
        except Exception:
            self._saved_bounding_boxes = None

        if self.config.mmsi_filter_enabled and tracked_mmsis:
            # Start streaming with MMSI filters to reduce data volume
            self.ais_client.start_streaming_with_filters(
                mmsi_filters=list(tracked_mmsis),
                duration_minutes=duration_minutes
            )
        else:
            # Use standard on-demand streaming with tight bounding box
            self.ais_client.start_streaming_on_demand(duration_minutes)
        
        # Set up message handler
        self.ais_client.set_message_handler(self._handle_ais_message)
        
        logger.info(f"Started AIS tracking session for {len(tracked_mmsis)} vessels ({duration_minutes} minutes)")
    
    def stop_tracking_session(self):
        """Stop the current tracking session"""
        if not self.is_active_tracking:
            return
        
        self.ais_client.stop_streaming()
        # Restore original bounding boxes if we relaxed them during the session
        try:
            if hasattr(self, "_saved_bounding_boxes"):
                self.ais_client.bounding_boxes = getattr(self, "_saved_bounding_boxes", None)
                self._saved_bounding_boxes = None
        except Exception:
            pass
        self.is_active_tracking = False
        self.tracking_session_start = None
        self.active_mmsi_filters.clear()
        
        logger.info("Stopped AIS tracking session")
    
    def add_vessel_to_tracking(self, mmsi: str):
        """
        Add a new vessel MMSI to active tracking.
        
        Args:
            mmsi: MMSI to add to tracking
        """
        if mmsi not in self.active_mmsi_filters:
            self.active_mmsi_filters.add(mmsi)
            
            # If we're in an active session, update the filters
            if self.is_active_tracking and self.config.mmsi_filter_enabled:
                # Restart with updated filters (AIS Stream doesn't support dynamic filter updates)
                remaining_time = self._get_remaining_session_time()
                if remaining_time > 0:
                    self.stop_tracking_session()
                    self.start_tracking_session(remaining_time)
            
            logger.info(f"Added MMSI {mmsi} to active tracking")
    
    def remove_vessel_from_tracking(self, mmsi: str):
        """
        Remove a vessel MMSI from active tracking.
        
        Args:
            mmsi: MMSI to remove from tracking
        """
        if mmsi in self.active_mmsi_filters:
            self.active_mmsi_filters.discard(mmsi)
            logger.info(f"Removed MMSI {mmsi} from active tracking")
    
    def _get_remaining_session_time(self) -> int:
        """Get remaining time in current tracking session (minutes)"""
        if not self.tracking_session_start:
            return 0
        
        elapsed = datetime.now(timezone.utc) - self.tracking_session_start
        remaining = self.config.tracking_session_duration - elapsed.total_seconds()
        return max(0, int(remaining / 60))
    
    def _handle_ais_message(self, message_data: Dict):
        """
        Handle incoming AIS message and update ship tracking.
        
        Args:
            message_data: AIS message data from the stream
        """
        self.total_ais_messages += 1
        
        try:
            # Extract MMSI from message
            mmsi = self._extract_mmsi_from_message(message_data)
            if not mmsi:
                return
            
            # Check if this MMSI is being tracked
            if mmsi not in self.tracking_service.tracked_ships:
                self.filtered_updates += 1
                return
            
            # Apply rate limiting per MMSI
            if not self._should_update_position(mmsi):
                self.filtered_updates += 1
                return
            
            # Extract position data
            position = self._extract_position_from_message(message_data, mmsi)
            if not position:
                return
            
            # Update ship tracking
            updated_ship = self.tracking_service.update_ship_position(mmsi, position)
            if updated_ship:
                self.successful_updates += 1
                self.last_position_updates[mmsi] = datetime.now(timezone.utc)
                
                logger.debug(f"Updated position for {updated_ship.vessel_name} (MMSI: {mmsi})")
                
                # Log zone changes
                if hasattr(updated_ship, '_previous_zone') and updated_ship._previous_zone != updated_ship.current_zone:
                    logger.info(f"Vessel {updated_ship.vessel_name} moved from {updated_ship._previous_zone.value} to {updated_ship.current_zone.value}")
            
        except Exception as e:
            logger.error(f"Error processing AIS message: {e}")
    
    def _extract_mmsi_from_message(self, message_data: Dict) -> Optional[str]:
        """Extract MMSI from AIS message"""
        try:
            if "Message" in message_data:
                message = message_data["Message"]
                
                # Position Report
                if "PositionReport" in message:
                    return str(message["PositionReport"].get("UserID", ""))
                
                # Ship Static Data
                elif "ShipStaticData" in message:
                    return str(message["ShipStaticData"].get("UserID", ""))
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting MMSI: {e}")
            return None
    
    def _extract_position_from_message(self, message_data: Dict, mmsi: str) -> Optional[ShipPosition]:
        """Extract position data from AIS message"""
        try:
            if "Message" not in message_data:
                return None
            
            message = message_data["Message"]
            timestamp = datetime.fromisoformat(
                message_data.get("Timestamp", datetime.now(timezone.utc).isoformat()).replace('Z', '+00:00')
            )
            
            # Handle Position Report (Class A and B variants)
            pos_report = None
            if "PositionReport" in message:
                pos_report = message["PositionReport"]
            elif "ExtendedClassBPositionReport" in message:
                pos_report = message["ExtendedClassBPositionReport"]
            elif "StandardClassBPositionReport" in message:
                pos_report = message["StandardClassBPositionReport"]
            
            if pos_report is not None:
                
                return ShipPosition(
                    mmsi=mmsi,
                    latitude=pos_report.get("Latitude"),
                    longitude=pos_report.get("Longitude"), 
                    course=pos_report.get("Cog", 0),  # Course over ground
                    speed=pos_report.get("Sog", 0),   # Speed over ground
                    timestamp=timestamp,
                    heading=pos_report.get("TrueHeading"),
                    status=pos_report.get("NavigationalStatus", "Unknown")
                )
            
            # Ship Static Data doesn't contain position, but we might want to update other info
            elif "ShipStaticData" in message:
                # For now, just return None as we need position data
                return None
            
            return None
            
        except Exception as e:
            logger.error(f"Error extracting position data: {e}")
            return None
    
    def _should_update_position(self, mmsi: str) -> bool:
        """
        Check if position should be updated based on rate limiting.
        
        Args:
            mmsi: MMSI to check
            
        Returns:
            True if position should be updated
        """
        if mmsi not in self.last_position_updates:
            return True
        
        last_update = self.last_position_updates[mmsi]
        elapsed = (datetime.now(timezone.utc) - last_update).total_seconds()
        
        return elapsed >= self.config.position_update_interval
    
    def get_tracking_statistics(self) -> Dict:
        """Get tracking session statistics"""
        session_duration = 0
        if self.tracking_session_start:
            session_duration = (datetime.now(timezone.utc) - self.tracking_session_start).total_seconds()
        
        return {
            "is_active": self.is_active_tracking,
            "session_duration_seconds": session_duration,
            "tracked_vessels": len(self.active_mmsi_filters),
            "active_mmsi_filters": list(self.active_mmsi_filters),
            "total_ais_messages": self.total_ais_messages,
            "filtered_updates": self.filtered_updates,
            "successful_updates": self.successful_updates,
            "throttle_config": {
                "update_interval_seconds": self.config.position_update_interval,
                "mmsi_filter_enabled": self.config.mmsi_filter_enabled,
                "max_concurrent_connections": self.config.max_concurrent_connections
            },
            "last_position_updates": {
                mmsi: timestamp.isoformat() 
                for mmsi, timestamp in self.last_position_updates.items()
            }
        }
    
    def update_throttle_config(self, **kwargs):
        """
        Update throttling configuration.
        
        Args:
            **kwargs: Configuration parameters to update
        """
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                logger.info(f"Updated throttle config: {key} = {value}")
        
        # If we're in an active session and significant parameters changed, restart
        if self.is_active_tracking and any(key in kwargs for key in ['mmsi_filter_enabled', 'max_concurrent_connections']):
            remaining_time = self._get_remaining_session_time()
            if remaining_time > 0:
                logger.info("Restarting tracking session with updated configuration")
                self.stop_tracking_session()
                self.start_tracking_session(remaining_time)


# Enhanced AIS Client methods for better integration
def start_streaming_with_filters(self, mmsi_filters: List[str], duration_minutes: int = 60):
    """
    Start streaming with MMSI filters to reduce data volume.
    This method should be added to AISStreamClient class.
    """
    if self.use_mock:
        logger.info("Mock mode - skipping filtered streaming")
        return
    
    # Store filters for subscription
    self._current_mmsi_filters = mmsi_filters
    
    # Start regular streaming (AIS Stream API will filter server-side)
    self.start_streaming_on_demand(duration_minutes)
    
    logger.info(f"Started AIS streaming with {len(mmsi_filters)} MMSI filters")


def set_message_handler(self, handler_function):
    """
    Set custom message handler for AIS messages.
    This method should be added to AISStreamClient class.
    """
    self._custom_message_handler = handler_function


# Function to enhance existing AIS client
def enhance_ais_client_for_tracking(ais_client: AISStreamClient):
    """
    Add tracking-specific methods to existing AIS client.
    
    Args:
        ais_client: Existing AIS client to enhance
    """
    # Add the new methods
    ais_client.start_streaming_with_filters = lambda mmsi_filters, duration_minutes=60: start_streaming_with_filters(ais_client, mmsi_filters, duration_minutes)
    ais_client.set_message_handler = lambda handler: set_message_handler(ais_client, handler)
    
    # Initialize custom handler storage
    ais_client._custom_message_handler = None
    ais_client._current_mmsi_filters = []
    
    # Enhance message handling in existing _handle_websocket_message
    original_handle_message = ais_client._handle_websocket_message
    
    async def enhanced_handle_message(message):
        # Call original handler
        await original_handle_message(message)
        
        # Call custom handler if set
        if hasattr(ais_client, '_custom_message_handler') and ais_client._custom_message_handler:
            try:
                message_data = json.loads(message) if isinstance(message, str) else message
                ais_client._custom_message_handler(message_data)
            except Exception as e:
                logger.error(f"Error in custom message handler: {e}")
    
    ais_client._handle_websocket_message = enhanced_handle_message
    
    logger.info("Enhanced AIS client for tracking integration")
