"""add assignment lock fields

Revision ID: f1c2d3e4abcd
Revises: ea2b9a4c2d11
Create Date: 2025-09-04 00:00:00.000000
"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f1c2d3e4abcd'
down_revision = 'ea2b9a4c2d11'
branch_labels = None
depends_on = None


def upgrade():
    try:
        op.add_column('assignments', sa.Column('lock_status', sa.String(length=20), nullable=False, server_default='UNLOCKED'))
    except Exception:
        pass
    try:
        op.add_column('assignments', sa.Column('lock_reason', sa.Text(), nullable=True))
    except Exception:
        pass
    try:
        op.add_column('assignments', sa.Column('locked_by', sa.String(length=100), nullable=True))
    except Exception:
        pass
    try:
        op.add_column('assignments', sa.Column('locked_at', sa.DateTime(), nullable=True))
    except Exception:
        pass

    # Remove server_default after backfilling
    try:
        with op.batch_alter_table('assignments') as batch_op:
            batch_op.alter_column('lock_status', server_default=None)
    except Exception:
        pass


def downgrade():
    try:
        op.drop_column('assignments', 'locked_at')
    except Exception:
        pass
    try:
        op.drop_column('assignments', 'locked_by')
    except Exception:
        pass
    try:
        op.drop_column('assignments', 'lock_reason')
    except Exception:
        pass
    try:
        op.drop_column('assignments', 'lock_status')
    except Exception:
        pass


