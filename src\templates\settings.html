{% extends "base.html" %}

{% block title %}Settings - Terneuzen Terminal Jetty Planning{% endblock %}

{% block user_actions %}
    <span id="save-status" class="save-status"></span>
    <button class="btn btn-primary" id="save-settings">
        <i class="fas fa-save"></i>
        Save Settings
    </button>
{% endblock %}

{% block header %}System Settings{% endblock %}

{% block content %}
<!-- Toast Container is now in base.html -->

<!-- General Settings -->
<div class="card">
    <div class="card-header">
        <h3>General Settings</h3>
    </div>
    <div class="card-body">
        <div class="settings-section">
            <div class="form-row">
                <div class="form-group half">
                    <label for="terminal-name">Terminal Name</label>
                    <input type="text" id="terminal-name" name="terminal_name" data-setting="terminal_name" placeholder="Enter terminal name">
                </div>

                <div class="form-group half">
                    <label for="terminal-location">Terminal Location</label>
                    <div class="location-inputs">
                        <input type="text" id="terminal-location-lat" name="terminal_lat" data-setting="terminal_lat" placeholder="Latitude">
                        <input type="text" id="terminal-location-lon" name="terminal_lon" data-setting="terminal_lon" placeholder="Longitude">
                    </div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group half">
                    <label for="time-zone">Time Zone</label>
                    <select id="time-zone" name="time_zone" data-setting="time_zone">
                        <option value="UTC">UTC</option>
                        <option value="Europe/Brussels">Europe/Brussels</option>
                        <option value="Europe/Amsterdam">Europe/Amsterdam</option>
                        <option value="Europe/London">Europe/London</option>
                        <option value="America/New_York">America/New_York</option>
                        <option value="Asia/Singapore">Asia/Singapore</option>
                        <option value="Australia/Sydney">Australia/Sydney</option>
                    </select>
                </div>

                <div class="form-group half">
                    <label for="date-format">Date Format</label>
                    <select id="date-format" name="date_format" data-setting="date_format">
                        <option value="DD-MM-YYYY">DD-MM-YYYY (Dutch)</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY (US)</option>
                        <option value="DD/MM/YYYY">DD/MM/YYYY (UK)</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD (ISO)</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group half">
                    <label for="language">Language</label>
                    <select id="language" name="language" data-setting="language">
                        <option value="nl-NL">Dutch (Netherlands)</option>
                        <option value="en-US">English (US)</option>
                        <option value="en-GB">English (UK)</option>
                        <option value="de-DE">German (Germany)</option>
                        <option value="fr-FR">French (France)</option>
                    </select>
                    <p class="setting-description">Changes the language used throughout the application.</p>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="dark-mode" name="dark_mode" data-setting="dark_mode">
                        Enable Dark Mode
                    </label>
                </div>

                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="auto-refresh" name="auto_refresh" data-setting="auto_refresh">
                        Auto-refresh Data
                    </label>
                    <div class="conditional-setting" id="refresh-interval-container">
                        <label for="refresh-interval">Refresh Interval (seconds)</label>
                        <input type="number" id="refresh-interval" name="refresh_interval" data-setting="refresh_interval" min="5" max="3600" value="30">
                    </div>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="ais-preload" name="ais_preload" data-setting="ais_preload">
                        Preload AIS on Startup
                    </label>
                    <p class="setting-description">If enabled, the app will start AIS streaming on startup using the configured radius.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Connections -->
<div class="card">
    <div class="card-header">
        <h3>API Connections</h3>
    </div>
    <div class="card-body">
        <div class="settings-section">
            <div class="api-connection">
                <h4>AIS Stream API</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="aisstream-api-key">API Key</label>
                        <div class="api-key-input">
                            <input type="password" id="aisstream-api-key" name="aisstream_api_key" data-setting="aisstream_api_key" placeholder="Enter AIS Stream API key">
                            <button class="btn btn-sm btn-secondary toggle-password" type="button">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group half">
                        <label for="ais-radius-km">AIS Radius (km)</label>
                        <input type="number" id="ais-radius-km" min="10" max="500" value="100">
                        <p class="setting-description">Limit AIS ingestion to a radius around the terminal. Increase to cover Antwerp (~100 km).</p>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <button class="btn btn-sm btn-secondary test-connection" data-api="vessel-finder" type="button">
                            Test Connection
                        </button>
                        <span class="connection-status" id="vessel-finder-status"></span>
                    </div>
                </div>
            </div>

            <div class="api-connection">
                <h4>Weather API</h4>
                <div class="form-row">
                    <div class="form-group">
                        <label for="weather-api-provider">Weather API Provider</label>
                        <select id="weather-api-provider" disabled>
                            <option value="openmeteo">Open-Meteo</option>
                        </select>
                        <p class="setting-description">Using Open-Meteo weather service - free and reliable with no API key required.</p>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <button class="btn btn-sm btn-secondary test-connection" data-api="weather">
                            Test Connection
                        </button>
                        <span class="connection-status" id="weather-status"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Weather Display Settings -->
<div class="card">
    <div class="card-header">
        <h3>Weather Display Settings</h3>
    </div>
    <div class="card-body">
        <div class="settings-section">
            <div class="form-row">
                <div class="form-group half">
                    <label for="weather-display-units">Display Units</label>
                    <select id="weather-display-units">
                        <option value="metric">Metric (°C, m/s)</option>
                        <option value="imperial">Imperial (°F, mph)</option>
                        <option value="nautical">Nautical (°C, knots)</option>
                    </select>
                </div>

                <div class="form-group half">
                    <label for="weather-refresh-interval">Refresh Interval (minutes)</label>
                    <input type="number" id="weather-refresh-interval" min="5" max="60" value="15">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="show-wind-alerts" checked>
                        Show Wind Speed Alerts
                    </label>
                    <p class="setting-description">Display alerts when wind speed exceeds operational thresholds.</p>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group half">
                    <label for="wind-caution-threshold">Wind Caution Threshold (m/s)</label>
                    <input type="number" id="wind-caution-threshold" min="1" max="30" value="12">
                    <p class="setting-description">Wind speed at which caution is advised (default: 12 m/s).</p>
                </div>

                <div class="form-group half">
                    <label for="wind-danger-threshold">Wind Danger Threshold (m/s)</label>
                    <input type="number" id="wind-danger-threshold" min="1" max="50" value="17">
                    <p class="setting-description">Wind speed at which operations are restricted (default: 17 m/s).</p>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="show-12h-forecast" checked>
                        Show 12-Hour Detailed Forecast
                    </label>
                    <p class="setting-description">Display detailed 12-hour forecast with graph visualization.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notifications -->
<div class="card">
    <div class="card-header">
        <h3>Notifications</h3>
    </div>
    <div class="card-body">
        <div class="settings-section">
            <div class="form-row">
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="enable-email-notifications">
                        Enable Email Notifications
                    </label>
                </div>
            </div>

            <div class="form-row conditional-setting" id="email-settings" style="display: none;">
                <div class="form-group half">
                    <label for="email-recipients">Email Recipients</label>
                    <input type="text" id="email-recipients" placeholder="Enter email addresses (comma-separated)">
                </div>

                <div class="form-group half">
                    <label>Notification Events</label>
                    <div class="checkbox-list">
                        <label>
                            <input type="checkbox" name="notification-event" value="optimization-complete">
                            Optimization Complete
                        </label>
                        <label>
                            <input type="checkbox" name="notification-event" value="vessel-arrival">
                            Vessel Arrival
                        </label>
                        <label>
                            <input type="checkbox" name="notification-event" value="schedule-conflict">
                            Schedule Conflicts
                        </label>
                        <label>
                            <input type="checkbox" name="notification-event" value="weather-alert">
                            Weather Alerts
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Backup and Restore -->
<div class="card">
    <div class="card-header">
        <h3>Backup and Restore</h3>
    </div>
    <div class="card-body">
        <div class="settings-section">
            <div class="form-row">
                <div class="form-group">
                    <button class="btn btn-secondary" id="export-data">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                    <p class="setting-description">Export all schedule, vessel, and terminal data as a JSON file.</p>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="import-data">Import Data</label>
                    <div class="file-upload">
                        <input type="file" id="import-data" accept=".json">
                        <button class="btn btn-secondary" id="import-button">
                            <i class="fas fa-upload"></i> Select File
                        </button>
                        <span id="file-name">No file selected</span>
                    </div>
                    <p class="setting-description">Import previously exported data. This will replace all current data.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Assistant API Configuration -->
<div class="card">
    <div class="card-header">
        <h3>Assistant API Configuration</h3>
    </div>
    <div class="card-body">
        <div class="settings-section">
            <div class="form-row">
                <div class="form-group">
                    <label for="claude-api-key">Claude API Key</label>
                    <div class="api-key-input">
                        <input type="password" id="claude-api-key" placeholder="Enter Claude API key">
                        <button class="btn btn-sm btn-secondary toggle-password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <p class="setting-description">API key for Claude AI assistant. Required for the assistant functionality.</p>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="claude-model">Claude Model</label>
                    <select id="claude-model">
                        <option value="claude-3-5-sonnet-20240620">Claude 3.5 Sonnet (Default)</option>
                        <option value="claude-3-opus-20240229">Claude 3 Opus</option>
                        <option value="claude-3-sonnet-20240229">Claude 3 Sonnet</option>
                        <option value="claude-3-haiku-20240307">Claude 3 Haiku</option>
                    </select>
                    <p class="setting-description">Select the Claude model to use for the assistant.</p>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <button class="btn btn-sm btn-secondary test-connection" data-api="claude">
                        Test Connection
                    </button>
                    <span class="connection-status" id="claude-status"></span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group checkbox-group">
                    <label>
                        <input type="checkbox" id="use-mock-responses">
                        Use Mock Responses
                    </label>
                    <p class="setting-description">Use predefined responses instead of calling the Claude API. Useful for testing.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Optimization Configuration -->
<div class="card">
    <div class="card-header">
        <h3>Optimization Configuration</h3>
    </div>
    <div class="card-body">
        <div class="settings-section">
            <!-- Note: Optimization presets configuration has been moved to the Optimize page -->

            <!-- Solver Settings -->
            <div class="subsection">
                <h4>Solver Settings</h4>
                <div class="form-row">
                    <div class="form-group half">
                        <label for="solver-time-limit">Solver Time Limit (seconds)</label>
                        <input type="number" id="solver-time-limit" data-setting="solver_time_limit" min="1" max="3600" value="60">
                        <p class="setting-description">Maximum time the solver will run before returning the best solution found.</p>
                    </div>

                    <div class="form-group half">
                        <label for="solver-strategy">Solver Strategy</label>
                        <select id="solver-strategy" data-setting="solver_strategy">
                            <option value="AUTOMATIC">Automatic</option>
                            <option value="BASIC">Basic</option>
                            <option value="HEURISTIC">Heuristic</option>
                            <option value="OPTIMIZE">Fully Optimize</option>
                        </select>
                        <p class="setting-description">Strategy used by the OR-Tools solver. Automatic is recommended for most cases.</p>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group checkbox-group">
                        <label>
                            <input type="checkbox" id="parallel-solving" data-setting="parallel_solving">
                            Enable Parallel Solving
                        </label>
                        <p class="setting-description">Use multiple processor cores for solving. May improve performance on complex schedules.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style>
/* Subsection styling for solver settings */
.subsection {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.subsection:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.subsection h4 {
    color: #003b6f;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 600;
}
</style>

{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', path='/js/settings.js') }}" nonce="{{ nonce }}"></script>
{% endblock %}