"""merge heads

Revision ID: 67518952a705
Revises: 2f9a04a27344, 9b1f6c7d3a21, f1c2d3e4abcd
Create Date: 2025-09-04 14:33:42.858006

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '67518952a705'
down_revision: Union[str, Sequence[str], None] = ('2f9a04a27344', '9b1f6c7d3a21', 'f1c2d3e4abcd')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
