"""
ML Prediction API Module

This module provides API endpoints for ML predictions and model management.
"""

import logging
import os
import shutil
import zipfile
import tempfile
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from pydantic import BaseModel
from datetime import datetime, timedelta, timezone

from ..ml.prediction_service import MLPredictionService
from ..ml.models import PredictionRequest, VesselFeatures, TimePrediction
from ..models.vessel import VesselBase
from ..models.terminal import Terminal

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ml", tags=["ml-predictions"])


def extract_model_info_from_filename(filename, model_name):
    """
    Extract model type and training timestamp from filename.
    
    Args:
        filename: The model filename (e.g., 'random_forest_20250819_141943.joblib')
        model_name: The internal model name (e.g., 'terminal', 'pre_pump')
    
    Returns:
        tuple: (display_name, model_type, timestamp_for_sorting)
    """
    if filename == "unknown":
        return f"{model_name.replace('_', ' ').title()}", "unknown", "0"
    
    # Remove .joblib extension
    base_name = filename.replace('.joblib', '')
    
    # Extract model type and timestamp
    model_type = "unknown"
    timestamp_for_sorting = "0"
    
    # Handle extracted models (from zip uploads)
    if base_name.startswith('extracted_'):
        # Pattern: extracted_20250820_122053_gradient_boosting_20250820_120027
        parts = base_name.split('_')
        if len(parts) >= 6:
            # Find model type
            for i in range(3, len(parts) - 1):
                if len(parts[i]) == 8 and parts[i].isdigit():
                    # Found date, model type is everything before this
                    model_type = '_'.join(parts[3:i])
                    # Use the training timestamp (last two parts)
                    if len(parts) >= 2:
                        date_str = parts[-2]  # YYYYMMDD
                        time_str = parts[-1]  # HHMMSS
                        timestamp_for_sorting = f"{date_str}{time_str}"
                    break
    else:
        # Handle regular model files: random_forest_20250819_141943
        parts = base_name.split('_')
        model_type_parts = []
        
        for i, part in enumerate(parts):
            if len(part) == 8 and part.isdigit():  # Found date
                model_type = '_'.join(model_type_parts)
                if i + 1 < len(parts) and len(parts[i + 1]) == 6 and parts[i + 1].isdigit():
                    timestamp_for_sorting = f"{part}{parts[i + 1]}"
                else:
                    timestamp_for_sorting = f"{part}000000"
                break
            else:
                model_type_parts.append(part)
    
    # Fallback model type detection
    if model_type == "unknown":
        if 'random_forest' in filename.lower():
            model_type = 'random_forest'
        elif 'gradient_boosting' in filename.lower():
            model_type = 'gradient_boosting'
        elif 'xgboost' in filename.lower():
            model_type = 'xgboost'
    
    # Format display name
    if timestamp_for_sorting != "0" and len(timestamp_for_sorting) >= 8:
        try:
            date_part = timestamp_for_sorting[:8]
            time_part = timestamp_for_sorting[8:14] if len(timestamp_for_sorting) >= 14 else "000000"
            formatted_date = f"{date_part[:4]}-{date_part[4:6]}-{date_part[6:8]}"
            formatted_time = f"{time_part[:2]}:{time_part[2:4]}"
            display_name = f"{model_type.replace('_', ' ').title()} ({formatted_date} {formatted_time})"
        except:
            display_name = f"{model_type.replace('_', ' ').title()}"
    else:
        display_name = f"{model_type.replace('_', ' ').title()}"
    
    return display_name, model_type, timestamp_for_sorting

# Global ML service instance
ml_service = MLPredictionService()

# Pydantic models for API requests/responses
class PredictionRequestAPI(BaseModel):
    vessel_id: str
    jetty_id: Optional[str] = None
    override_jetty_selection: bool = False

class VesselFeaturesAPI(BaseModel):
    dwt: float
    loa: float
    beam: float
    draft: float
    vessel_type: str
    cargo_volume: float
    product_types: List[str]
    operation_types: List[str] = []
    customer_name: Optional[str] = None
    preferred_jetty: Optional[str] = None
    is_first_visit: bool = False
    connection_size: str = "12\""

class TimePredictionResponse(BaseModel):
    vessel_id: str
    jetty_id: Optional[str]
    pre_pump_time_hours: float
    pump_time_hours: float
    post_pump_time_hours: float
    terminal_time_hours: float
    pre_pump_confidence: float
    pump_confidence: float
    post_pump_confidence: float
    terminal_confidence: float
    average_confidence: float
    model_version: str
    prediction_timestamp: str
    compatible_jetties: List[str]
    recommendation_reason: str
    success: bool
    error_message: Optional[str] = None

class ValidationReport(BaseModel):
    is_valid: bool
    issues: List[str]
    expected_features: List[str]
    critical_feature_count: int

class ModelStatusResponse(BaseModel):
    models_loaded: Dict[str, bool]
    model_versions: Dict[str, str]
    service_ready: bool
    last_updated: Optional[str] = None

class ModelUploadResponse(BaseModel):
    success: bool
    message: str
    filename: str
    model_type: Optional[str] = None
    models_found: List[str] = []
    timestamp: str


def create_fallback_terminal():
    """Create a fallback terminal configuration based on EVOS Terneuzen business rules"""
    from ..models.terminal import Terminal, Jetty, JettyType, LoadingArm
    
    # Create jetties based on evos-terminal-business-rules.md - COMPLETE TERNEUZEN SPECIFICATIONS
    jetties = [
        # Jetty 1: Seagoing, Multiple products, 1,000-60,000 DWT, 57*-236m LOA, ≤34m beam, ≤12.8m draft
        Jetty(
            id="J1", name="Jetty 1", jetty_type=JettyType.VESSEL_BERTH,
            max_length=236.0, max_draft=12.8, max_deadweight=60000.0,
            loading_arms=[
                LoadingArm(id="LA1_1", name="12\" ANSI Hydrocarbons/Naphtha", flow_rate=780.0, 
                          compatible_products=["hydrocarbons"]),
                LoadingArm(id="LA1_2", name="8\" ANSI Benzene", flow_rate=405.0,
                          compatible_products=["benzene"], has_vapor_return=True),
                LoadingArm(id="LA1_3", name="6\" ANSI Acrylonitrile", flow_rate=230.0,
                          compatible_products=["acrylonitrile"], has_vapor_return=True),
                LoadingArm(id="LA1_4", name="10\" ANSI Propylene Oxide", flow_rate=630.0,
                          compatible_products=["propylene_oxide"], has_vapor_return=True)
            ], 
            connected_tanks=[], connected_pumps=[], is_operational=True
        ),
        
        # Jetty 2: Inland only, Chemicals/Hydrocarbons, 1,000-7,000 DWT, 57*-135m LOA, ≤17.0m beam, ≤4.5m draft  
        Jetty(
            id="J2", name="Jetty 2", jetty_type=JettyType.BARGE_BERTH, 
            max_length=135.0, max_draft=4.5, max_deadweight=7000.0,
            loading_arms=[
                LoadingArm(id="LA2_1", name="8\" ANSI Hydrocarbons/Naphtha", flow_rate=405.0,
                          compatible_products=["hydrocarbons"]),
                LoadingArm(id="LA2_2", name="8\" ANSI Benzene", flow_rate=405.0,
                          compatible_products=["benzene"], has_vapor_return=True),
                LoadingArm(id="LA2_3", name="6\" ANSI Propylene Oxide", flow_rate=230.0,
                          compatible_products=["propylene_oxide"], has_vapor_return=True)
            ],
            connected_tanks=[], connected_pumps=[], is_operational=True
        ),
        
        # Jetty 3: Both, Minerals/Benzene, 1,000-15,000 DWT, 85-135m LOA, ≤22.0m beam, ≤12.8m draft
        Jetty(
            id="J3", name="Jetty 3", jetty_type=JettyType.VESSEL_BERTH,
            max_length=135.0, max_draft=12.8, max_deadweight=15000.0, 
            loading_arms=[
                LoadingArm(id="LA3_1", name="12\" ANSI Minerals", flow_rate=780.0,
                          compatible_products=["minerals"]),
                LoadingArm(id="LA3_2", name="6\" ANSI Benzene", flow_rate=230.0,
                          compatible_products=["benzene"], has_vapor_return=True)
            ],
            connected_tanks=[], connected_pumps=[], is_operational=True
        ),
        
        # Jetty 4: Inland only, Minerals only, 1,000-9,000 DWT, 85*-135m LOA, ≤22.0m beam, ≤4.4m draft
        Jetty(
            id="J4", name="Jetty 4", jetty_type=JettyType.BARGE_BERTH,
            max_length=135.0, max_draft=4.4, max_deadweight=9000.0,
            loading_arms=[
                LoadingArm(id="LA4_1", name="6\" ANSI Minerals (hose only)", flow_rate=230.0,
                          compatible_products=["minerals"])
            ],
            connected_tanks=[], connected_pumps=[], is_operational=True
        ),
        
        # Jetty 5: Seagoing, Minerals only, 1,000-150,000 DWT (SUEZMAX ~132k), 105*-275m LOA, ≤50.0m beam, ≤15.0m draft
        Jetty(
            id="J5", name="Jetty 5", jetty_type=JettyType.VESSEL_BERTH,
            max_length=275.0, max_draft=15.0, max_deadweight=150000.0,
            loading_arms=[
                LoadingArm(id="LA5_1", name="12\" ANSI Minerals Arm 1", flow_rate=780.0,
                          compatible_products=["minerals"]),
                LoadingArm(id="LA5_2", name="12\" ANSI Minerals Arm 2", flow_rate=780.0,
                          compatible_products=["minerals"])
            ],
            connected_tanks=[], connected_pumps=[], is_operational=True
        ),
        
        # Jetty 6: Both, Minerals/Butane, 1,000-20,000 DWT, 105*-150m LOA, ≤25.0m beam, ≤9.9m draft
        Jetty(
            id="J6", name="Jetty 6", jetty_type=JettyType.VESSEL_BERTH, 
            max_length=150.0, max_draft=9.9, max_deadweight=20000.0,
            loading_arms=[
                LoadingArm(id="LA6_1", name="12\" ANSI Minerals", flow_rate=780.0,
                          compatible_products=["minerals"]),
                LoadingArm(id="LA6_2", name="6\" ANSI Butane", flow_rate=230.0,
                          compatible_products=["butane"])
            ],
            connected_tanks=[], connected_pumps=[], is_operational=True
        )
    ]
    
    return Terminal(
        name="EVOS Terneuzen",
        location=(51.34543250288062, 3.751466718019277),  # EVOS Terneuzen coordinates
        jetties=jetties
    )


def get_ml_service() -> MLPredictionService:
    """Dependency to get ML service instance"""
    return ml_service


async def get_data():
    """Dependency to get application data (vessels, terminal, etc.)"""
    # Create a basic terminal structure from business rules
    try:
        # Try to get data from main app
        from ..api.fastapi_app import get_data as main_get_data
        data = await main_get_data()
        if data.get("terminal"):
            return data
    except:
        pass
    
    # Fallback: create a basic terminal structure from business rules
    terminal = create_fallback_terminal()
    
    return {
        "vessels": [],
        "terminal": terminal
    }


@router.get("/status", response_model=ModelStatusResponse)
async def get_ml_status():
    """Get ML service and model status"""
    try:
        models_loaded = {
            'pre_pump': ml_service.models.get('pre_pump') is not None,
            'pump': ml_service.models.get('pump') is not None,
            'post_pump': ml_service.models.get('post_pump') is not None,
            'terminal': ml_service.models.get('terminal') is not None
        }
        
        model_versions = {}
        for model_name in models_loaded.keys():
            if models_loaded[model_name]:
                model_versions[model_name] = ml_service.model_metadata.get(f"{model_name}_version", "unknown")
            else:
                model_versions[model_name] = "not_loaded"
        
        return ModelStatusResponse(
            models_loaded=models_loaded,
            model_versions=model_versions,
            service_ready=any(models_loaded.values()),
            last_updated=ml_service.model_metadata.get('last_updated')
        )
    except Exception as e:
        logger.error(f"Error getting ML status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting ML status: {str(e)}")


@router.post("/validate", response_model=ValidationReport)
async def validate_vessel_for_ml(
    request: PredictionRequestAPI,
    data: Dict = Depends(get_data),
    ml_service: MLPredictionService = Depends(get_ml_service)
):
    """Validate that a vessel has the critical features required for ML predictions."""
    try:
        vessel = next((v for v in data["vessels"] if v.id == request.vessel_id), None)
        if not vessel:
            raise HTTPException(status_code=404, detail=f"Vessel {request.vessel_id} not found")

        terminal = data.get("terminal") or create_fallback_terminal()
        jetty = terminal.get_jetty_by_id(request.jetty_id) if request.jetty_id else None
        features = ml_service.extract_features(vessel, jetty)

        from ..ml.feature_validator import MLFeatureValidator
        validator = MLFeatureValidator()
        validation = validator.validate_critical_features(features)

        feature_info = validator.get_feature_info()
        return ValidationReport(
            is_valid=bool(validation.get('is_valid', False)),
            issues=list(validation.get('issues', [])),
            expected_features=list(feature_info.get('expected_features', [])),
            critical_feature_count=int(validation.get('critical_feature_count', 0))
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating vessel {request.vessel_id} for ML: {e}")
        raise HTTPException(status_code=500, detail=f"Error validating vessel: {str(e)}")


@router.post("/predict", response_model=TimePredictionResponse)
async def predict_vessel_times(
    request: PredictionRequestAPI,
    data: Dict = Depends(get_data),
    ml_service: MLPredictionService = Depends(get_ml_service)
):
    """Generate time predictions for a vessel"""
    try:
        # Find vessel
        vessel = next((v for v in data["vessels"] if v.id == request.vessel_id), None)
        if not vessel:
            raise HTTPException(status_code=404, detail=f"Vessel {request.vessel_id} not found")
        
        terminal = data.get("terminal")
        if not terminal:
            logger.warning("Terminal not found in data, using fallback terminal configuration")
            terminal = create_fallback_terminal()
        elif isinstance(terminal, dict):
            # Terminal from database is a dictionary, try to load as proper object first
            logger.info("Terminal from database is a dictionary, attempting to load proper terminal object")
            try:
                from ..database import db
                import src.database_extension
                if hasattr(db, 'get_terminal_as_object'):
                    terminal_obj = db.get_terminal_as_object()
                    if terminal_obj and hasattr(terminal_obj, 'jetties'):
                        terminal = terminal_obj
                        logger.info(f"Successfully loaded terminal object with {len(terminal.jetties)} jetties")
                    else:
                        logger.warning("Could not load terminal object, using fallback")
                        terminal = create_fallback_terminal()
                else:
                    logger.warning("get_terminal_as_object not available, using fallback")
                    terminal = create_fallback_terminal()
            except Exception as e:
                logger.error(f"Error loading terminal object: {e}, using fallback")
                terminal = create_fallback_terminal()
        elif not hasattr(terminal, 'jetties'):
            logger.warning("Terminal object has no jetties attribute, using fallback terminal configuration")
            terminal = create_fallback_terminal()
        
        # Extract features
        jetty = None
        if request.jetty_id:
            jetty = terminal.get_jetty_by_id(request.jetty_id)
            if not jetty:
                raise HTTPException(status_code=404, detail=f"Jetty {request.jetty_id} not found")
        
        features = ml_service.extract_features(vessel, jetty)
        
        # Create prediction request
        prediction_request = PredictionRequest(
            vessel_id=vessel.id,
            features=features,
            jetty_id=request.jetty_id,
            override_jetty_selection=request.override_jetty_selection
        )
        
        # Get prediction
        response = ml_service.predict_for_vessel(prediction_request, terminal)
        
        if response.success:
            pred = response.predictions
            return TimePredictionResponse(
                vessel_id=response.vessel_id,
                jetty_id=response.jetty_id,
                pre_pump_time_hours=pred.pre_pump_time.total_seconds() / 3600,
                pump_time_hours=pred.pump_time.total_seconds() / 3600,
                post_pump_time_hours=pred.post_pump_time.total_seconds() / 3600,
                terminal_time_hours=pred.terminal_time.total_seconds() / 3600,
                pre_pump_confidence=pred.pre_pump_confidence,
                pump_confidence=pred.pump_confidence,
                post_pump_confidence=pred.post_pump_confidence,
                terminal_confidence=pred.terminal_confidence,
                average_confidence=pred.average_confidence,
                model_version=pred.model_version,
                prediction_timestamp=pred.prediction_timestamp or datetime.now(timezone.utc).isoformat(),
                compatible_jetties=response.compatible_jetties,
                recommendation_reason=response.recommendation_reason,
                success=True
            )
        else:
            return TimePredictionResponse(
                vessel_id=response.vessel_id,
                jetty_id=response.jetty_id,
                pre_pump_time_hours=0,
                pump_time_hours=0,
                post_pump_time_hours=0,
                terminal_time_hours=0,
                pre_pump_confidence=0,
                pump_confidence=0,
                post_pump_confidence=0,
                terminal_confidence=0,
                average_confidence=0,
                model_version="unknown",
                prediction_timestamp=datetime.now(timezone.utc).isoformat(),
                compatible_jetties=[],
                recommendation_reason=response.recommendation_reason,
                success=False,
                error_message=response.error_message
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating predictions for vessel {request.vessel_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating predictions: {str(e)}")


@router.post("/predict-features", response_model=TimePredictionResponse)
async def predict_from_features(
    features: VesselFeaturesAPI,
    jetty_id: Optional[str] = None,
    data: Dict = Depends(get_data),
    ml_service: MLPredictionService = Depends(get_ml_service)
):
    """Generate time predictions from vessel features directly"""
    try:
        terminal = data.get("terminal")
        if not terminal:
            logger.warning("Terminal not found in data, using fallback terminal configuration")
            terminal = create_fallback_terminal()
        elif isinstance(terminal, dict):
            # Terminal from database is a dictionary, try to load as proper object first
            logger.info("Terminal from database is a dictionary, attempting to load proper terminal object")
            try:
                from ..database import db
                import src.database_extension
                if hasattr(db, 'get_terminal_as_object'):
                    terminal_obj = db.get_terminal_as_object()
                    if terminal_obj and hasattr(terminal_obj, 'jetties'):
                        terminal = terminal_obj
                        logger.info(f"Successfully loaded terminal object with {len(terminal.jetties)} jetties")
                    else:
                        logger.warning("Could not load terminal object, using fallback")
                        terminal = create_fallback_terminal()
                else:
                    logger.warning("get_terminal_as_object not available, using fallback")
                    terminal = create_fallback_terminal()
            except Exception as e:
                logger.error(f"Error loading terminal object: {e}, using fallback")
                terminal = create_fallback_terminal()
        elif not hasattr(terminal, 'jetties'):
            logger.warning("Terminal object has no jetties attribute, using fallback terminal configuration")
            terminal = create_fallback_terminal()
        
        # Convert API features to internal format
        # Normalize vessel_type input to expected uppercase values
        vt_in = (features.vessel_type or '').strip().lower()
        vessel_type_norm = 'TANKER' if vt_in == 'tanker' else ('BARGE' if vt_in == 'barge' else 'TANKER')

        vessel_features = VesselFeatures(
            dwt=features.dwt,
            loa=features.loa,
            beam=features.beam,
            draft=features.draft,
            vessel_type=vessel_type_norm,
            cargo_volume=features.cargo_volume,
            product_types=features.product_types,
            product_hazard_level=ml_service._classify_product_hazard(features.product_types),
            is_first_visit=features.is_first_visit,
            requires_vapor_return=ml_service._requires_vapor_return(features.product_types),
            requires_nitrogen_purge=ml_service._requires_nitrogen_purge(features.product_types),
            multiple_products=len(features.product_types) > 1,
            connection_size=features.connection_size,
            customer_name=features.customer_name,
            operation_types=features.operation_types,
            preferred_jetty=features.preferred_jetty,
            weather_risk_score=0.5,  # Default
            season=ml_service._get_season(),
            time_of_day="day",
            jetty_id=jetty_id,
            max_flow_rate=None,
            jetty_product_compatible=None,
            connection_size_match=None
        )
        
        # Create prediction request
        prediction_request = PredictionRequest(
            vessel_id="unknown",
            features=vessel_features,
            jetty_id=jetty_id,
            override_jetty_selection=jetty_id is not None
        )
        
        # Get prediction
        response = ml_service.predict_for_vessel(prediction_request, terminal)
        
        if response.success:
            pred = response.predictions
            # Persist ML prediction to analytics log for nomination-driven predictions (no assignment yet)
            try:
                from ..database import get_database
                db = get_database()
                # Convert timedelta to minutes
                terminal_minutes = int((pred.terminal_time.total_seconds() or 0) // 60)
                db.log_ml_prediction(
                    assignment_id=None,
                    vessel_id="unknown",
                    vessel_name=features.customer_name or "NOMINATION",
                    prediction_type="terminal",
                    predicted_minutes=terminal_minutes,
                    confidence_score=pred.terminal_confidence,
                )
            except Exception:
                # Best effort; do not block the API on analytics logging
                pass
            return TimePredictionResponse(
                vessel_id="unknown",
                jetty_id=response.jetty_id,
                pre_pump_time_hours=pred.pre_pump_time.total_seconds() / 3600,
                pump_time_hours=pred.pump_time.total_seconds() / 3600,
                post_pump_time_hours=pred.post_pump_time.total_seconds() / 3600,
                terminal_time_hours=pred.terminal_time.total_seconds() / 3600,
                pre_pump_confidence=pred.pre_pump_confidence,
                pump_confidence=pred.pump_confidence,
                post_pump_confidence=pred.post_pump_confidence,
                terminal_confidence=pred.terminal_confidence,
                average_confidence=pred.average_confidence,
                model_version=pred.model_version,
                prediction_timestamp=pred.prediction_timestamp or datetime.now(timezone.utc).isoformat(),
                compatible_jetties=response.compatible_jetties,
                recommendation_reason=response.recommendation_reason,
                success=True
            )
        else:
            raise HTTPException(status_code=400, detail=response.error_message or "Prediction failed")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating predictions from features: {e}")
        raise HTTPException(status_code=500, detail=f"Error generating predictions: {str(e)}")


@router.get("/jetty-recommendations/{vessel_id}")
async def get_jetty_recommendations(
    vessel_id: str,
    data: Dict = Depends(get_data),
    ml_service: MLPredictionService = Depends(get_ml_service)
):
    """Get jetty recommendations for a vessel"""
    try:
        vessel = next((v for v in data["vessels"] if v.id == vessel_id), None)
        if not vessel:
            raise HTTPException(status_code=404, detail=f"Vessel {vessel_id} not found")
        
        terminal = data.get("terminal")
        if not terminal:
            logger.warning("Terminal not found in data, using fallback terminal configuration")
            terminal = create_fallback_terminal()
        elif isinstance(terminal, dict):
            # Terminal from database is a dictionary, try to load as proper object first
            logger.info("Terminal from database is a dictionary, attempting to load proper terminal object")
            try:
                from ..database import db
                import src.database_extension
                if hasattr(db, 'get_terminal_as_object'):
                    terminal_obj = db.get_terminal_as_object()
                    if terminal_obj and hasattr(terminal_obj, 'jetties'):
                        terminal = terminal_obj
                        logger.info(f"Successfully loaded terminal object with {len(terminal.jetties)} jetties")
                    else:
                        logger.warning("Could not load terminal object, using fallback")
                        terminal = create_fallback_terminal()
                else:
                    logger.warning("get_terminal_as_object not available, using fallback")
                    terminal = create_fallback_terminal()
            except Exception as e:
                logger.error(f"Error loading terminal object: {e}, using fallback")
                terminal = create_fallback_terminal()
        elif not hasattr(terminal, 'jetties'):
            logger.warning("Terminal object has no jetties attribute, using fallback terminal configuration")
            terminal = create_fallback_terminal()
        
        # Extract features
        features = ml_service.extract_features(vessel)
        
        # Find compatible jetties
        compatible_jetties = ml_service._find_compatible_jetties(features, terminal)
        
        # Get predictions for each compatible jetty
        recommendations = []
        for jetty in compatible_jetties:
            # Create a COPY of features for each jetty to avoid reference issues
            import copy
            features_with_jetty = copy.deepcopy(features)
            features_with_jetty.jetty_id = jetty.id
            features_with_jetty.max_flow_rate = jetty.max_flow_rate
            
            # Use the same prediction path as the main endpoint for consistency
            prediction_request = PredictionRequest(
                vessel_id=vessel.id,
                features=features_with_jetty,
                jetty_id=jetty.id,
                override_jetty_selection=False
            )
            
            prediction_response = ml_service.predict_for_vessel(prediction_request, terminal)
            
            if prediction_response.success:
                predictions = prediction_response.predictions
                recommendations.append({
                    "jetty_id": jetty.id,
                    "jetty_name": getattr(jetty, 'name', jetty.id),
                    "terminal_time_hours": predictions.terminal_time.total_seconds() / 3600,
                    "pump_time_hours": predictions.pump_time.total_seconds() / 3600,
                    "average_confidence": predictions.average_confidence,
                    "max_flow_rate": jetty.max_flow_rate
                })
            else:
                # Fallback for failed predictions
                logger.warning(f"ML prediction failed for jetty {jetty.id}, using fallback")
                recommendations.append({
                    "jetty_id": jetty.id,
                    "jetty_name": getattr(jetty, 'name', jetty.id),
                    "terminal_time_hours": 24.0,  # 24 hour fallback
                    "pump_time_hours": 12.0,  # 12 hour fallback  
                    "average_confidence": 0.1,
                    "max_flow_rate": jetty.max_flow_rate
                })
        
        # Normalize and compute suitability score in [0,1]
        try:
            max_flow = max((r.get("max_flow_rate", 0) or 0) for r in recommendations) or 0
        except ValueError:
            max_flow = 0
        for r in recommendations:
            flow_component = (r.get("max_flow_rate", 0) / max_flow) if max_flow > 0 else 0.0
            confidence = r.get("average_confidence", 0.0) or 0.0
            # Weighted blend: 60% confidence, 40% normalized flow
            score = 0.6 * confidence + 0.4 * flow_component
            # Clamp to [0,1]
            r["suitability_score"] = max(0.0, min(1.0, score))

        # Sort by suitability score
        recommendations.sort(key=lambda x: x["suitability_score"], reverse=True)
        
        return {
            "vessel_id": vessel_id,
            "compatible_jetties": len(compatible_jetties),
            "recommendations": recommendations,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting jetty recommendations for vessel {vessel_id}: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting recommendations: {str(e)}")


@router.post("/upload-model", response_model=ModelUploadResponse)
async def upload_model(file: UploadFile = File(...)):
    """Upload a new ML model (supports .joblib and .zip files)"""
    try:
        # Validate file type
        if not file.filename.endswith(('.joblib', '.zip')):
            raise HTTPException(
                status_code=400, 
                detail="Only .joblib and .zip files are supported"
            )
        
        # Create models directory if it doesn't exist
        models_dir = "models"
        os.makedirs(models_dir, exist_ok=True)
        
        # Save uploaded file
        file_path = os.path.join(models_dir, file.filename)
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        models_found = []
        model_type = None
        
        # Handle zip files - extract and find models
        if file.filename.endswith('.zip'):
            try:
                with zipfile.ZipFile(file_path, 'r') as zip_ref:
                    # Create temporary directory for extraction
                    with tempfile.TemporaryDirectory() as temp_dir:
                        zip_ref.extractall(temp_dir)
                        
                        # Find all model files in the extracted content
                        for root, dirs, files in os.walk(temp_dir):
                            for filename in files:
                                if filename.endswith('.joblib'):
                                    # Copy model file to models directory
                                    src_path = os.path.join(root, filename)
                                    # Create unique filename to avoid conflicts
                                    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
                                    new_filename = f"extracted_{timestamp}_{filename}"
                                    dst_path = os.path.join(models_dir, new_filename)
                                    shutil.copy2(src_path, dst_path)
                                    models_found.append(new_filename)
                                    logger.info(f"Extracted model: {new_filename}")
                
                # Determine model type from filename
                if 'gradient_boosting' in file.filename.lower():
                    model_type = 'gradient_boosting'
                elif 'random_forest' in file.filename.lower():
                    model_type = 'random_forest'
                elif 'xgboost' in file.filename.lower():
                    model_type = 'xgboost'
                else:
                    model_type = 'unknown'
                
            except zipfile.BadZipFile:
                # Remove the invalid zip file
                os.remove(file_path)
                raise HTTPException(status_code=400, detail="Invalid zip file format")
        
        # Handle joblib files directly
        elif file.filename.endswith('.joblib'):
            models_found = [file.filename]
            
            # Determine model type from filename
            if 'gradient_boosting' in file.filename.lower():
                model_type = 'gradient_boosting'
            elif 'random_forest' in file.filename.lower():
                model_type = 'random_forest'
            elif 'xgboost' in file.filename.lower():
                model_type = 'xgboost'
            else:
                model_type = 'unknown'
        
        # Reload ML service to pick up new models
        global ml_service
        ml_service = MLPredictionService()
        
        # Prepare response message
        if models_found:
            message = f"Successfully uploaded {len(models_found)} model(s): {', '.join(models_found)}"
        else:
            message = f"File uploaded but no model files found inside"
        
        return ModelUploadResponse(
            success=True,
            message=message,
            filename=file.filename,
            model_type=model_type,
            models_found=models_found,
            timestamp=datetime.now(timezone.utc).isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading model: {e}")
        # Clean up file if there was an error
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except:
            pass
        raise HTTPException(status_code=500, detail=f"Error uploading model: {str(e)}")


@router.post("/reload-models")
async def reload_models():
    """Reload ML models from disk"""
    try:
        global ml_service
        ml_service = MLPredictionService()  # Reinitialize to reload models
        
        # Check which models loaded successfully
        models_status = {
            model_name: model is not None 
            for model_name, model in ml_service.models.items()
        }
        
        return {
            "success": True,
            "message": "Models reloaded successfully",
            "models_loaded": models_status,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error reloading models: {e}")
        raise HTTPException(status_code=500, detail=f"Error reloading models: {str(e)}")


@router.get("/available-models")
async def get_available_models():
    """Get list of available ML models for selection"""
    try:
        # Get all loaded models from the ML service
        available_models = []
        
        model_list = []
        for model_name, model in ml_service.models.items():
            if model is not None:
                # Get metadata for this model
                metadata = ml_service.model_metadata.get(f"{model_name}_metadata", {})
                filename = ml_service.model_config.get(model_name, "unknown")
                
                # Extract model type and date from filename for better display
                display_name, model_type, timestamp = extract_model_info_from_filename(filename, model_name)
                
                model_list.append({
                    "name": model_name,
                    "display_name": display_name,
                    "type": model_type,
                    "filename": filename,
                    "loaded": True,
                    "description": f"Trained model for {model_name.replace('_', ' ')} prediction",
                    "target": model_name.replace('_', ' ').title(),
                    "timestamp": timestamp
                })
        
        # Find the most recent model based on timestamp
        if model_list:
            most_recent_timestamp = max(model_list, key=lambda x: x["timestamp"])["timestamp"]
            for model in model_list:
                if model["timestamp"] == most_recent_timestamp and model["timestamp"] != "0":
                    model["display_name"] += " (Most Recent)"
                    model["is_most_recent"] = True
                else:
                    model["is_most_recent"] = False
                available_models.append(model)
        
        # Also scan for any additional uploaded models
        models_dir = "models"
        if os.path.exists(models_dir):
            for filename in os.listdir(models_dir):
                if filename.endswith('.joblib'):
                    # Check if this model is already in our loaded models
                    already_loaded = any(
                        ml_service.model_config.get(model_name) == filename 
                        for model_name in ml_service.model_config.keys()
                    )
                    
                    if not already_loaded:
                        # This is an uploaded model not in the default config
                        model_name = filename.replace('.joblib', '')
                        display_name, model_type, timestamp = extract_model_info_from_filename(filename, model_name)
                        
                        available_models.append({
                            "name": model_name,
                            "display_name": f"{display_name} (Uploaded, Not Loaded)",
                            "type": model_type,
                            "filename": filename,
                            "loaded": False,  # Not currently loaded in service
                            "description": f"Uploaded {model_type} model - requires restart to load",
                            "timestamp": timestamp,
                            "is_most_recent": False
                        })
        
        return {
            "success": True,
            "models": available_models,
            "total_count": len(available_models),
            "loaded_count": len([m for m in available_models if m["loaded"]]),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting available models: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting available models: {str(e)}")


@router.post("/jetty-recommendations-features")
async def get_jetty_recommendations_from_features(
    features: VesselFeaturesAPI,
    data: Dict = Depends(get_data),
    ml_service: MLPredictionService = Depends(get_ml_service)
):
    """Get jetty recommendations directly from vessel features"""
    logger.info("NEW ENDPOINT: jetty-recommendations-features called!")
    try:
        terminal = data.get("terminal")
        if not terminal:
            logger.warning("Terminal not found in data, attempting to load proper terminal object")
            try:
                from ..database import db
                import src.database_extension
                if hasattr(db, 'get_terminal_as_object'):
                    terminal_obj = db.get_terminal_as_object()
                    if terminal_obj and hasattr(terminal_obj, 'jetties'):
                        terminal = terminal_obj
                        logger.info(f"Successfully loaded terminal object with {len(terminal.jetties)} jetties")
                    else:
                        logger.warning("Could not load terminal object, using fallback")
                        terminal = create_fallback_terminal()
                else:
                    logger.warning("get_terminal_as_object not available, using fallback")
                    terminal = create_fallback_terminal()
            except Exception as e:
                logger.error(f"Error loading terminal object: {e}, using fallback")
                terminal = create_fallback_terminal()
        elif isinstance(terminal, dict):
            # Terminal from database is a dictionary, try to load as proper object first
            logger.info("Terminal from database is a dictionary, attempting to load proper terminal object")
            try:
                from ..database import db
                import src.database_extension
                if hasattr(db, 'get_terminal_as_object'):
                    terminal_obj = db.get_terminal_as_object()
                    if terminal_obj and hasattr(terminal_obj, 'jetties'):
                        terminal = terminal_obj
                        logger.info(f"Successfully loaded terminal object with {len(terminal.jetties)} jetties")
                    else:
                        logger.warning("Could not load terminal object, using fallback")
                        terminal = create_fallback_terminal()
                else:
                    logger.warning("get_terminal_as_object not available, using fallback")
                    terminal = create_fallback_terminal()
            except Exception as e:
                logger.error(f"Error loading terminal object: {e}, using fallback")
                terminal = create_fallback_terminal()
        elif not hasattr(terminal, 'jetties'):
            logger.warning("Terminal object has no jetties attribute, using fallback terminal configuration")
            terminal = create_fallback_terminal()
        
        # Convert API features to internal format (same as predict-features endpoint)
        # Normalize vessel_type input to expected uppercase values
        vt_in = (features.vessel_type or '').strip().lower()
        vessel_type_norm = 'TANKER' if vt_in == 'tanker' else ('BARGE' if vt_in == 'barge' else 'TANKER')
        
        vessel_features = VesselFeatures(
            dwt=features.dwt,
            loa=features.loa,
            beam=features.beam,
            draft=features.draft,
            vessel_type=vessel_type_norm,
            cargo_volume=features.cargo_volume,
            product_types=features.product_types,
            product_hazard_level=ml_service._classify_product_hazard(features.product_types),
            is_first_visit=features.is_first_visit,
            requires_vapor_return=ml_service._requires_vapor_return(features.product_types),
            requires_nitrogen_purge=ml_service._requires_nitrogen_purge(features.product_types),
            multiple_products=len(features.product_types) > 1,
            connection_size=features.connection_size,
            customer_name=features.customer_name,
            operation_types=features.operation_types,
            preferred_jetty=features.preferred_jetty,
            weather_risk_score=0.5,  # Default
            season=ml_service._get_season(),
            time_of_day="day",
            jetty_id=None,
            max_flow_rate=None,
            jetty_product_compatible=None,
            connection_size_match=None
        )
        
        # Find compatible jetties
        compatible_jetties = ml_service._find_compatible_jetties(vessel_features, terminal)
        
        recommendations = []
        for jetty in compatible_jetties:
            # Create a COPY of features for each jetty to avoid reference issues
            import copy
            features_with_jetty = copy.deepcopy(vessel_features)
            features_with_jetty.jetty_id = jetty.id
            features_with_jetty.max_flow_rate = jetty.max_flow_rate
            
            # Use the same prediction path as the main endpoint for consistency
            prediction_request = PredictionRequest(
                vessel_id="features_based",  # Placeholder vessel ID
                features=features_with_jetty,
                jetty_id=jetty.id,
                override_jetty_selection=False
            )
            
            prediction_response = ml_service.predict_for_vessel(prediction_request, terminal)
            
            if prediction_response.success:
                predictions = prediction_response.predictions
                terminal_time_hrs = predictions.terminal_time.total_seconds() / 3600
                pump_time_hrs = predictions.pump_time.total_seconds() / 3600
                
                logger.info(f"JETTY {jetty.id}: ML predicted terminal={terminal_time_hrs:.1f}h, pump={pump_time_hrs:.1f}h, flow_rate={jetty.max_flow_rate}")
                
                recommendations.append({
                    "jetty_id": jetty.id,
                    "jetty_name": getattr(jetty, 'name', jetty.id),
                    "terminal_time_hours": terminal_time_hrs,
                    "pump_time_hours": pump_time_hrs,
                    "average_confidence": predictions.average_confidence,
                    "max_flow_rate": jetty.max_flow_rate
                })
            else:
                # Fallback for failed predictions
                logger.warning(f"ML prediction failed for jetty {jetty.id}, using fallback")
                recommendations.append({
                    "jetty_id": jetty.id,
                    "jetty_name": getattr(jetty, 'name', jetty.id),
                    "terminal_time_hours": 12.0,
                    "pump_time_hours": 8.0,
                    "average_confidence": 0.5,
                    "max_flow_rate": jetty.max_flow_rate
                })
        
        # Normalize and compute suitability score in [0,1]
        try:
            max_flow = max((r.get("max_flow_rate", 0) or 0) for r in recommendations) or 0
        except ValueError:
            max_flow = 0
        for r in recommendations:
            flow_component = (r.get("max_flow_rate", 0) / max_flow) if max_flow > 0 else 0.0
            confidence = r.get("average_confidence", 0.0) or 0.0
            score = 0.6 * confidence + 0.4 * flow_component
            r["suitability_score"] = max(0.0, min(1.0, score))

        # Sort by suitability score (highest first)
        recommendations.sort(key=lambda x: x["suitability_score"], reverse=True)
        
        logger.info(f"Returning {len(recommendations)} jetty recommendations with REAL DATABASE flow rates!")
        
        return recommendations
        
    except Exception as e:
        logger.error(f"Error generating jetty recommendations from features: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating jetty recommendations: {str(e)}")
