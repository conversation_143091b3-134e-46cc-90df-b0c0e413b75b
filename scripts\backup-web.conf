events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    server {
        listen 80;
        server_name localhost;
        
        # Backup files browser
        location /backups/ {
            root /usr/share/nginx/html;
            autoindex on;
            autoindex_exact_size off;
            autoindex_localtime on;
            
            # Security headers
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            
            # Allow downloading backup files
            location ~* \.(sql|gz|tar)$ {
                add_header Content-Disposition 'attachment';
            }
        }
        
        # Simple status page
        location / {
            return 200 '
<!DOCTYPE html>
<html>
<head>
    <title>Jetty Planner - Backup Status</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .status { padding: 20px; border-radius: 5px; margin: 20px 0; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <h1>Jetty Planner - Backup System</h1>
    
    <div class="status success">
        <h3>✓ Backup Service Running</h3>
        <p>Automated PostgreSQL backups are configured and running.</p>
    </div>
    
    <div class="status info">
        <h3>📋 Backup Schedule</h3>
        <ul>
            <li><strong>Daily:</strong> 2:00 AM (kept for 30 days)</li>
            <li><strong>Weekly:</strong> Sundays (kept for 8 weeks)</li>
            <li><strong>Monthly:</strong> 1st of month (kept for 6 months)</li>
        </ul>
    </div>
    
    <div class="status info">
        <h3>📁 Backup Files</h3>
        <p><a href="/backups/">Browse backup files →</a></p>
        <p>Download and restore backup files as needed.</p>
    </div>
    
    <div class="status info">
        <h3>🔧 Manual Backup</h3>
        <p>To create an immediate backup:</p>
        <pre><code>docker exec jetty-postgres-backup backup</code></pre>
    </div>
</body>
</html>
            ';
            add_header Content-Type text/html;
        }
    }
}