# Check Maintenance Mode Status for Jetty Planner
# Displays current maintenance status and provides management options

$ErrorActionPreference = "Stop"

$ProjectRoot = Split-Path -Parent $PSScriptRoot
$FlagFile = Join-Path $ProjectRoot "maintenance.flag"
$LogFile = Join-Path $ProjectRoot "logs/maintenance.log"

function Show-MaintenanceStatus {
    Write-Host "🔍 Evos Jetty Planner - Maintenance Status Check" -ForegroundColor Cyan
    Write-Host "=" * 50 -ForegroundColor Gray
    Write-Host ""
    
    if (Test-Path $FlagFile) {
        Write-Host "🔧 STATUS: MAINTENANCE MODE ENABLED" -ForegroundColor Yellow -BackgroundColor DarkRed
        Write-Host ""
        
        # Show maintenance details
        $Content = Get-Content $FlagFile -Raw -ErrorAction SilentlyContinue
        if ($Content) {
            Write-Host "📋 Maintenance Details:" -ForegroundColor White
            Write-Host $Content -ForegroundColor Gray
        }
        
        Write-Host ""
        Write-Host "🌐 Users currently see: Maintenance page" -ForegroundColor Yellow
        Write-Host "🔗 URL: https://planner.evosgpt.eu" -ForegroundColor Cyan
        
    } else {
        Write-Host "✅ STATUS: NORMAL OPERATION" -ForegroundColor Green -BackgroundColor DarkGreen
        Write-Host ""
        Write-Host "🌐 Users currently see: Normal application" -ForegroundColor Green
        Write-Host "🔗 URL: https://planner.evosgpt.eu" -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Host "📁 Flag file location: $FlagFile" -ForegroundColor Gray
    
    # Show recent maintenance log entries if available
    if (Test-Path $LogFile) {
        Write-Host ""
        Write-Host "📋 Recent maintenance activity:" -ForegroundColor White
        $RecentLogs = Get-Content $LogFile -Tail 5 -ErrorAction SilentlyContinue
        if ($RecentLogs) {
            $RecentLogs | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
        } else {
            Write-Host "   No recent activity logged" -ForegroundColor Gray
        }
    }
}

function Show-QuickActions {
    Write-Host ""
    Write-Host "🔧 Quick Actions:" -ForegroundColor White
    Write-Host ""
    
    if (Test-Path $FlagFile) {
        Write-Host "1. Disable maintenance mode:" -ForegroundColor Green
        Write-Host "   .\scripts\Disable-MaintenanceMode.ps1" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "2. Update maintenance info:" -ForegroundColor Yellow
        Write-Host "   .\scripts\Enable-MaintenanceMode.ps1 -Reason 'New reason' -EstimatedDuration '10 minutes'" -ForegroundColor Cyan
    } else {
        Write-Host "1. Enable maintenance mode:" -ForegroundColor Yellow
        Write-Host "   .\scripts\Enable-MaintenanceMode.ps1" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "2. Enable with custom message:" -ForegroundColor Yellow
        Write-Host "   .\scripts\Enable-MaintenanceMode.ps1 -Reason 'Database upgrade' -EstimatedDuration '1 hour'" -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Host "3. Monitor application logs:" -ForegroundColor Blue
    Write-Host "   Get-Content logs/error.log -Tail 10 -Wait" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "4. Check Docker container status:" -ForegroundColor Blue
    Write-Host "   docker ps | findstr jetty" -ForegroundColor Cyan
}

try {
    Show-MaintenanceStatus
    Show-QuickActions
    
} catch {
    Write-Host "❌ Error checking maintenance status: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
