# Setup Windows Task Scheduler for Automated Backups
# Run this script as Administrator to create backup schedules

param(
    [switch]$Remove = $false  # Set to $true to remove existing schedules
)

# Configuration
$TaskPrefix = "JettyPlanner"
$ScriptPath = "C:\Users\<USER>\Jettyplanner\scripts\automated-backup-scheduler.ps1"
$WorkingDirectory = "C:\Users\<USER>\Jettyplanner"
$LogPath = "C:\Users\<USER>\Jettyplanner\logs\task-scheduler.log"

# Logging function
function Write-SetupLog {
    param($Message, $Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] [$Level] $Message"
    Write-Output $LogMessage
    if (Test-Path (Split-Path $LogPath)) {
        Add-Content -Path $LogPath -Value $LogMessage
    }
}

# Function to remove existing tasks
function Remove-BackupTasks {
    Write-SetupLog "Removing existing backup tasks..." "INFO"
    
    $TaskNames = @(
        "$TaskPrefix-Daily-Backup",
        "$TaskPrefix-Weekly-Backup", 
        "$TaskPrefix-Monthly-Backup",
        "$TaskPrefix-Health-Check"
    )
    
    foreach ($TaskName in $TaskNames) {
        try {
            $ExistingTask = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
            if ($ExistingTask) {
                Unregister-ScheduledTask -TaskName $TaskName -Confirm:$false
                Write-SetupLog "Removed task: $TaskName" "SUCCESS"
            } else {
                Write-SetupLog "Task not found: $TaskName" "INFO"
            }
        }
        catch {
            Write-SetupLog "Error removing task $TaskName`: $($_.Exception.Message)" "ERROR"
        }
    }
}

# Function to create backup tasks
function New-BackupTasks {
    Write-SetupLog "Creating backup scheduled tasks..." "INFO"
    
    # Check if script exists
    if (-not (Test-Path $ScriptPath)) {
        Write-SetupLog "Backup script not found: $ScriptPath" "ERROR"
        return $false
    }
    
    # Task definitions
    $Tasks = @(
        @{
            Name = "$TaskPrefix-Daily-Backup"
            Description = "Daily PostgreSQL backup for Jetty Planner"
            Schedule = "Daily"
            Time = "02:00"
            Arguments = "-BackupType daily"
        },
        @{
            Name = "$TaskPrefix-Weekly-Backup"
            Description = "Weekly PostgreSQL backup for Jetty Planner"
            Schedule = "Weekly"
            Time = "02:30"
            DayOfWeek = "Sunday"
            Arguments = "-BackupType weekly"
        },
        @{
            Name = "$TaskPrefix-Monthly-Backup"
            Description = "Monthly PostgreSQL backup for Jetty Planner"
            Schedule = "Monthly"
            Time = "03:00"
            DayOfMonth = 1
            Arguments = "-BackupType monthly"
        },
        @{
            Name = "$TaskPrefix-Health-Check"
            Description = "Backup system health check for Jetty Planner"
            Schedule = "Daily"
            Time = "01:30"
            Arguments = "-BackupType health-check"
        }
    )
    
    foreach ($TaskDef in $Tasks) {
        try {
            Write-SetupLog "Creating task: $($TaskDef.Name)" "INFO"
            
            # Create action
            $Action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File `"$ScriptPath`" $($TaskDef.Arguments)" -WorkingDirectory $WorkingDirectory
            
            # Create trigger based on schedule type
            switch ($TaskDef.Schedule) {
                "Daily" {
                    $Trigger = New-ScheduledTaskTrigger -Daily -At $TaskDef.Time
                }
                "Weekly" {
                    $Trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek $TaskDef.DayOfWeek -At $TaskDef.Time
                }
                "Monthly" {
                    # Monthly on the 1st day
                    $Trigger = New-ScheduledTaskTrigger -Daily -At $TaskDef.Time
                    # Note: Monthly triggers in PowerShell are complex, using daily with additional logic in script
                }
            }
            
            # Create settings
            $Settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -DontStopOnIdleEnd
            
            # Create principal (run as SYSTEM for reliability)
            $Principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
            
            # Register task
            Register-ScheduledTask -TaskName $TaskDef.Name -Action $Action -Trigger $Trigger -Settings $Settings -Principal $Principal -Description $TaskDef.Description
            
            Write-SetupLog "Successfully created task: $($TaskDef.Name)" "SUCCESS"
        }
        catch {
            Write-SetupLog "Error creating task $($TaskDef.Name): $($_.Exception.Message)" "ERROR"
        }
    }
}

# Function to verify tasks
function Test-BackupTasks {
    Write-SetupLog "Verifying backup tasks..." "INFO"
    
    $TaskNames = @(
        "$TaskPrefix-Daily-Backup",
        "$TaskPrefix-Weekly-Backup",
        "$TaskPrefix-Monthly-Backup",
        "$TaskPrefix-Health-Check"
    )
    
    $AllTasksCreated = $true
    
    foreach ($TaskName in $TaskNames) {
        try {
            $Task = Get-ScheduledTask -TaskName $TaskName -ErrorAction SilentlyContinue
            if ($Task) {
                $NextRun = (Get-ScheduledTask -TaskName $TaskName | Get-ScheduledTaskInfo).NextRunTime
                Write-SetupLog "✓ $TaskName - Next run: $NextRun" "SUCCESS"
            } else {
                Write-SetupLog "✗ $TaskName - Not found" "ERROR"
                $AllTasksCreated = $false
            }
        }
        catch {
            Write-SetupLog "Error checking task $TaskName`: $($_.Exception.Message)" "ERROR"
            $AllTasksCreated = $false
        }
    }
    
    return $AllTasksCreated
}

# Main execution
try {
    Write-SetupLog "=== Backup Schedule Setup Started ===" "INFO"
    
    # Check if running as administrator
    $CurrentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $Principal = New-Object Security.Principal.WindowsPrincipal($CurrentUser)
    $IsAdmin = $Principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    
    if (-not $IsAdmin) {
        Write-SetupLog "ERROR: This script must be run as Administrator" "ERROR"
        Write-Output "Please run PowerShell as Administrator and try again."
        exit 1
    }
    
    if ($Remove) {
        Remove-BackupTasks
        Write-SetupLog "=== Backup Schedule Removal Completed ===" "INFO"
    } else {
        # Remove existing tasks first
        Remove-BackupTasks
        Start-Sleep -Seconds 2
        
        # Create new tasks
        New-BackupTasks
        Start-Sleep -Seconds 2
        
        # Verify tasks
        $Success = Test-BackupTasks
        
        if ($Success) {
            Write-SetupLog "=== Backup Schedule Setup Completed Successfully ===" "SUCCESS"
            Write-Output ""
            Write-Output "✓ Backup schedules created successfully!"
            Write-Output "Daily backup:   02:00 (every day)"
            Write-Output "Weekly backup:  02:30 (Sundays)"
            Write-Output "Monthly backup: 03:00 (1st of month)"
            Write-Output "Health check:   01:30 (every day)"
            Write-Output ""
            Write-Output "View tasks: Get-ScheduledTask -TaskName 'JettyPlanner*'"
            Write-Output "Logs location: $LogPath"
        } else {
            Write-SetupLog "=== Backup Schedule Setup Failed ===" "ERROR"
            exit 1
        }
    }
}
catch {
    Write-SetupLog "Unexpected error: $($_.Exception.Message)" "ERROR"
    Write-SetupLog "=== Backup Schedule Setup Failed with Exception ===" "ERROR"
    exit 1
}
