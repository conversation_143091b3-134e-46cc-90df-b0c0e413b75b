"""Add nominations table

Revision ID: 33d8ae7a1d9a
Revises: ea2b9a4c2d11
Create Date: 2025-09-02 13:40:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '33d8ae7a1d9a'
down_revision: Union[str, Sequence[str], None] = 'ea2b9a4c2d11'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.create_table(
        'nominations',
        sa.Column('id', sa.Integer(), primary_key=True, autoincrement=True),
        sa.Column('terminal_id', sa.String(), sa.<PERSON>ey('terminals.id'), nullable=False),
        sa.Column('runtime_vessel_id', sa.String(), nullable=False),

        sa.Column('name', sa.String(), nullable=False),
        sa.Column('vessel_type', sa.String(), nullable=False),
        sa.Column('length', sa.Float()),
        sa.Column('beam', sa.Float()),
        sa.Column('draft', sa.Float()),
        sa.Column('deadweight', sa.Float()),
        sa.Column('priority', sa.Integer(), server_default=sa.text('0')),
        sa.Column('capacity', sa.Float()),
        sa.Column('width', sa.Float()),
        sa.Column('customer', sa.String()),

        sa.Column('status', sa.String(), nullable=False, server_default=sa.text("'pending'")),
        sa.Column('eta', sa.DateTime()),
        sa.Column('etd', sa.DateTime()),

        sa.Column('mmsi', sa.String()),
        sa.Column('imo', sa.String()),

        sa.Column('cargoes', sa.JSON()),
        sa.Column('metadata', sa.JSON()),

        sa.Column('created_at', sa.DateTime(), server_default=sa.text('NOW()')),
        sa.Column('updated_at', sa.DateTime(), server_default=sa.text('NOW()')),
    )
    op.create_index('ix_nominations_terminal_status', 'nominations', ['terminal_id', 'status'])
    op.create_index('ix_nominations_runtime_vessel_id', 'nominations', ['runtime_vessel_id'])


def downgrade() -> None:
    op.drop_index('ix_nominations_runtime_vessel_id', table_name='nominations')
    op.drop_index('ix_nominations_terminal_status', table_name='nominations')
    op.drop_table('nominations')


