// Minimal client helpers for locking and selective optimization

async function lockAssignment(assignmentId, lockStatus, lockReason, lockedBy) {
  const res = await fetch(`/api/assignments/${assignmentId}/lock`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ lock_status: lockStatus, lock_reason: lockReason || null, locked_by: lockedBy || null })
  });
  if (!res.ok) throw new Error('Failed to lock assignment');
  return res.json();
}

async function unlockAssignment(assignmentId, unlockReason, unlockedBy) {
  const res = await fetch(`/api/assignments/${assignmentId}/unlock`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ unlock_reason: unlockReason || null, unlocked_by: unlockedBy || null })
  });
  if (!res.ok) throw new Error('Failed to unlock assignment');
  return res.json();
}

async function bulkLockAssignments(ids, lockStatus, lockReason, lockedBy) {
  const res = await fetch(`/api/assignments/bulk-lock`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ assignment_ids: ids, lock_status: lockStatus, lock_reason: lockReason || null, locked_by: lockedBy || null })
  });
  if (!res.ok) throw new Error('Failed to bulk lock assignments');
  return res.json();
}

async function startOptimization(params) {
  const res = await fetch(`/api/optimize`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params || {})
  });
  if (!res.ok) throw new Error('Failed to start optimization');
  return res.json();
}

async function startSelectiveOptimization(vesselIds, params) {
  const res = await fetch(`/api/optimize/selective`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ vessel_ids: vesselIds, parameters: params || {} })
  });
  if (!res.ok) throw new Error('Failed to start selective optimization');
  return res.json();
}

async function startTimeWindowOptimization(startIso, endIso, params) {
  const res = await fetch(`/api/optimize/time-window`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ start: startIso, end: endIso, parameters: params || {} })
  });
  if (!res.ok) throw new Error('Failed to start time-window optimization');
  return res.json();
}

window.optimizationClient = {
  lockAssignment,
  unlockAssignment,
  bulkLockAssignments,
  startOptimization,
  startSelectiveOptimization,
  startTimeWindowOptimization,
};

// Preview API helper
async function previewOptimization(params) {
  const res = await fetch('/api/optimize/preview', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(params || {})
  });
  if (!res.ok) throw new Error('Failed to preview optimization');
  return res.json();
}
window.optimizationClient.previewOptimization = previewOptimization;


