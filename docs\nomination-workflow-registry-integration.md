# Nomination Workflow: Vessel Registry Integration (Skeleton)

## Goals
- Lookup or create a master vessel record using IMO/MMSI/Name
- Create or attach a `VesselVisit` for the active terminal
- Link planned `Assignment` to `vessel_db_id` and `visit_id`
- Prevent duplicates and warn on conflicts

## Flow
1. Collect nomination inputs (name, type, MMSI/IMO, ETA, cargoes)
2. `find_or_create_vessel(imo, mmsi, name, vessel_data)`
3. `create_visit(vessel_id, terminal_id, visit_data)` if not existing active visit
4. Create/Update `Assignment` with `vessel_db_id` and `visit_id`
5. If <PERSON><PERSON><PERSON> present, `link_ais_to_vessel(mmsi, vessel_id)`

## Duplicate Detection (initial)
- Strong: same IMO -> same vessel
- Medium: same MMSI and similar size -> likely duplicate
- Weak: same name and similar LOA/beam/DWT -> possible duplicate

## Open Tasks
- Add UI hooks to prefill nomination from AIS
- Add API endpoints: `/api/vessels`, `/api/visits`
- Add conflict checks against overlapping visits
