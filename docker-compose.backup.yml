version: '3.8'

services:
  # PostgreSQL Backup Service
  postgres-backup:
    image: prodrigestivill/postgres-backup-local:16
    container_name: jetty-postgres-backup
    restart: unless-stopped
    environment:
      # Database connection
      - POSTGRES_HOST=${DB_HOST:-host.docker.internal}
      - POSTGRES_PORT=${DB_PORT:-4432}
      - POSTGRES_DB=${DB_NAME:-planner}
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      
      # Backup schedule (daily at 2 AM)
      - SCHEDULE=0 2 * * *
      
      # Backup retention
      - BACKUP_KEEP_DAYS=30
      - BACKUP_KEEP_WEEKS=8
      - BACKUP_KEEP_MONTHS=6
      
      # Backup naming and compression
      - BACKUP_SUFFIX=.sql
      - POSTGRES_EXTRA_OPTS=-Z6 --schema=public --blobs
      
      # Health check settings
      - HEALTHCHECK_PORT=8080
    volumes:
      # Local backup storage
      - ./backups:/backups
      # Timezone for scheduled backups
      - /etc/localtime:/etc/localtime:ro
    networks:
      - jetty-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backup monitoring and web interface
  backup-web:
    image: nginx:alpine
    container_name: jetty-backup-web
    restart: unless-stopped
    ports:
      - "8081:80"
    volumes:
      - ./backups:/usr/share/nginx/html/backups:ro
      - ./scripts/backup-web.conf:/etc/nginx/nginx.conf:ro
    networks:
      - jetty-network
    depends_on:
      - postgres-backup

networks:
  jetty-network:
    external: false

volumes:
  backup-data: