"""
ETA Confidence Monitoring Service
Monitors and updates ETA confidence scores based on prediction accuracy
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict
import statistics

from ..database import Database

logger = logging.getLogger(__name__)

@dataclass
class ETAAccuracyRecord:
    """Record of ETA prediction accuracy"""
    vessel_id: str
    vessel_name: str
    eta_source: str
    predicted_eta: datetime
    actual_arrival: datetime
    error_hours: float
    confidence_at_prediction: int
    prediction_date: datetime

@dataclass
class ConfidenceAdjustment:
    """Confidence score adjustment"""
    vessel_id: str
    old_confidence: int
    new_confidence: int
    reason: str
    adjustment_time: datetime

class ETAConfidenceMonitor:
    """Service for monitoring and adjusting ETA confidence scores"""
    
    def __init__(self, database: Database, monitoring_interval_hours: int = 6):
        """
        Initialize ETA Confidence Monitor
        
        Args:
            database: Database connection
            monitoring_interval_hours: How often to run confidence analysis
        """
        self.database = database
        self.monitoring_interval_hours = monitoring_interval_hours
        
        # Accuracy tracking
        self.accuracy_history: Dict[str, List[ETAAccuracyRecord]] = defaultdict(list)
        self.source_accuracy: Dict[str, List[float]] = defaultdict(list)
        self.vessel_accuracy: Dict[str, List[float]] = defaultdict(list)
        
        # Configuration
        self.accuracy_window_days = 30  # Look at last 30 days
        self.min_samples_for_adjustment = 3  # Need 3+ samples to adjust confidence
        self.confidence_adjustment_factor = 0.3  # How aggressively to adjust
        
        # Tracking
        self.last_analysis_time = None
        self.total_adjustments = 0
        self.accuracy_improvements = 0
        
    async def start_monitoring(self):
        """Start the confidence monitoring loop"""
        logger.info(f"Starting ETA confidence monitoring (interval: {self.monitoring_interval_hours} hours)")
        
        while True:
            try:
                await self.analyze_and_update_confidence()
                await asyncio.sleep(self.monitoring_interval_hours * 3600)
                
            except Exception as e:
                logger.error(f"Error in confidence monitoring loop: {e}")
                await asyncio.sleep(3600)  # Wait 1 hour before retrying
    
    async def analyze_and_update_confidence(self) -> List[ConfidenceAdjustment]:
        """Analyze ETA accuracy and update confidence scores"""
        logger.info("Starting ETA confidence analysis")
        
        try:
            # Get recent accuracy data
            accuracy_records = await self._get_recent_accuracy_data()
            
            if not accuracy_records:
                logger.info("No recent accuracy data found")
                return []
            
            logger.info(f"Analyzing {len(accuracy_records)} accuracy records")
            
            # Update accuracy tracking
            self._update_accuracy_tracking(accuracy_records)
            
            # Calculate confidence adjustments
            adjustments = await self._calculate_confidence_adjustments()
            
            # Apply adjustments
            if adjustments:
                await self._apply_confidence_adjustments(adjustments)
                self.total_adjustments += len(adjustments)
            
            self.last_analysis_time = datetime.now(timezone.utc)
            
            # Generate accuracy report
            await self._generate_accuracy_report()
            
            logger.info(f"Confidence analysis complete: {len(adjustments)} adjustments made")
            return adjustments
            
        except Exception as e:
            logger.error(f"Confidence analysis failed: {e}")
            return []
    
    async def _get_recent_accuracy_data(self) -> List[ETAAccuracyRecord]:
        """Get recent ETA accuracy data from completed vessel visits"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.accuracy_window_days)
            
            with self.database.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT 
                            n.runtime_vessel_id,
                            n.name,
                            n.eta_source,
                            COALESCE(n.calculated_eta, n.eta) as predicted_eta,
                            vv.actual_arrival,
                            EXTRACT(EPOCH FROM (vv.actual_arrival - COALESCE(n.calculated_eta, n.eta)))/3600 as error_hours,
                            n.eta_confidence,
                            n.created_at
                        FROM nominations n
                        JOIN vessel_visits vv ON n.runtime_vessel_id = vv.vessel_id
                        WHERE vv.actual_arrival IS NOT NULL
                        AND vv.actual_arrival > %s
                        AND n.eta IS NOT NULL
                        AND vv.actual_arrival > COALESCE(n.calculated_eta, n.eta) - INTERVAL '24 hours'
                        AND vv.actual_arrival < COALESCE(n.calculated_eta, n.eta) + INTERVAL '24 hours'
                        ORDER BY vv.actual_arrival DESC;
                    """, (cutoff_date,))
                    
                    records = []
                    for row in cursor.fetchall():
                        record = ETAAccuracyRecord(
                            vessel_id=row[0],
                            vessel_name=row[1],
                            eta_source=row[2] or 'user',
                            predicted_eta=row[3],
                            actual_arrival=row[4],
                            error_hours=float(row[5]) if row[5] else 0.0,
                            confidence_at_prediction=int(row[6]) if row[6] else 50,
                            prediction_date=row[7]
                        )
                        records.append(record)
                    
                    return records
                    
        except Exception as e:
            logger.error(f"Failed to get accuracy data: {e}")
            return []
    
    def _update_accuracy_tracking(self, records: List[ETAAccuracyRecord]):
        """Update internal accuracy tracking with new records"""
        for record in records:
            # Track by source
            self.source_accuracy[record.eta_source].append(abs(record.error_hours))
            
            # Track by vessel
            self.vessel_accuracy[record.vessel_id].append(abs(record.error_hours))
            
            # Keep detailed history
            self.accuracy_history[record.vessel_id].append(record)
            
            # Limit history size
            if len(self.accuracy_history[record.vessel_id]) > 50:
                self.accuracy_history[record.vessel_id] = self.accuracy_history[record.vessel_id][-50:]
    
    async def _calculate_confidence_adjustments(self) -> List[ConfidenceAdjustment]:
        """Calculate confidence adjustments based on accuracy patterns"""
        adjustments = []
        
        try:
            # Get current vessels with ETAs
            current_vessels = await self._get_current_vessels_for_confidence_update()
            
            for vessel in current_vessels:
                vessel_id = vessel['runtime_vessel_id']
                current_confidence = vessel['eta_confidence']
                eta_source = vessel['eta_source']
                
                # Calculate new confidence based on accuracy
                new_confidence = await self._calculate_new_confidence(
                    vessel_id, eta_source, current_confidence
                )
                
                # Only adjust if change is significant
                if abs(new_confidence - current_confidence) >= 5:
                    reason = await self._get_adjustment_reason(vessel_id, eta_source)
                    
                    adjustments.append(ConfidenceAdjustment(
                        vessel_id=vessel_id,
                        old_confidence=current_confidence,
                        new_confidence=new_confidence,
                        reason=reason,
                        adjustment_time=datetime.now(timezone.utc)
                    ))
            
            return adjustments
            
        except Exception as e:
            logger.error(f"Failed to calculate confidence adjustments: {e}")
            return []
    
    async def _calculate_new_confidence(self, vessel_id: str, eta_source: str, current_confidence: int) -> int:
        """Calculate new confidence score for a vessel"""
        base_confidence = 50
        
        # Factor 1: Vessel-specific accuracy
        vessel_errors = self.vessel_accuracy.get(vessel_id, [])
        if len(vessel_errors) >= self.min_samples_for_adjustment:
            avg_error = statistics.mean(vessel_errors)
            
            # Good accuracy (< 1 hour average error) increases confidence
            if avg_error < 1.0:
                base_confidence += 20
            elif avg_error < 2.0:
                base_confidence += 10
            elif avg_error > 4.0:
                base_confidence -= 15
            elif avg_error > 2.0:
                base_confidence -= 5
        
        # Factor 2: Source-specific accuracy
        source_errors = self.source_accuracy.get(eta_source, [])
        if len(source_errors) >= 10:  # Need more samples for source analysis
            avg_source_error = statistics.mean(source_errors)
            
            if eta_source == 'ais_calculated':
                if avg_source_error < 1.5:
                    base_confidence += 15
                elif avg_source_error > 3.0:
                    base_confidence -= 10
            elif eta_source == 'ml_predicted':
                if avg_source_error < 2.0:
                    base_confidence += 10
                elif avg_source_error > 4.0:
                    base_confidence -= 15
            elif eta_source == 'user':
                if avg_source_error < 2.0:
                    base_confidence += 5
                elif avg_source_error > 3.0:
                    base_confidence -= 10
        
        # Factor 3: Consistency (low variance is good)
        if len(vessel_errors) >= 5:
            error_variance = statistics.variance(vessel_errors)
            if error_variance < 1.0:  # Very consistent
                base_confidence += 10
            elif error_variance > 4.0:  # Very inconsistent
                base_confidence -= 10
        
        # Factor 4: Recent trend
        recent_errors = vessel_errors[-5:] if len(vessel_errors) >= 5 else vessel_errors
        if len(recent_errors) >= 3:
            recent_avg = statistics.mean(recent_errors)
            overall_avg = statistics.mean(vessel_errors)
            
            if recent_avg < overall_avg * 0.8:  # Getting better
                base_confidence += 5
            elif recent_avg > overall_avg * 1.2:  # Getting worse
                base_confidence -= 5
        
        # Blend with current confidence (don't change too drastically)
        blended_confidence = int(
            current_confidence * (1 - self.confidence_adjustment_factor) + 
            base_confidence * self.confidence_adjustment_factor
        )
        
        # Ensure reasonable bounds
        return max(10, min(95, blended_confidence))
    
    async def _get_current_vessels_for_confidence_update(self) -> List[Dict]:
        """Get current vessels that need confidence updates"""
        try:
            with self.database.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("""
                        SELECT 
                            runtime_vessel_id, name, eta_source, eta_confidence
                        FROM nominations 
                        WHERE status = 'ACTIVE'
                        AND eta IS NOT NULL;
                    """)
                    
                    columns = ['runtime_vessel_id', 'name', 'eta_source', 'eta_confidence']
                    return [dict(zip(columns, row)) for row in cursor.fetchall()]
                    
        except Exception as e:
            logger.error(f"Failed to get vessels for confidence update: {e}")
            return []
    
    async def _get_adjustment_reason(self, vessel_id: str, eta_source: str) -> str:
        """Get human-readable reason for confidence adjustment"""
        vessel_errors = self.vessel_accuracy.get(vessel_id, [])
        source_errors = self.source_accuracy.get(eta_source, [])
        
        reasons = []
        
        if vessel_errors:
            avg_error = statistics.mean(vessel_errors)
            if avg_error < 1.0:
                reasons.append("excellent accuracy")
            elif avg_error < 2.0:
                reasons.append("good accuracy")
            elif avg_error > 4.0:
                reasons.append("poor accuracy")
            else:
                reasons.append("moderate accuracy")
        
        if len(vessel_errors) >= 5:
            variance = statistics.variance(vessel_errors)
            if variance < 1.0:
                reasons.append("consistent predictions")
            elif variance > 4.0:
                reasons.append("inconsistent predictions")
        
        if source_errors and len(source_errors) >= 10:
            avg_source_error = statistics.mean(source_errors)
            if avg_source_error < 2.0:
                reasons.append(f"reliable {eta_source} source")
            elif avg_source_error > 3.0:
                reasons.append(f"unreliable {eta_source} source")
        
        return "; ".join(reasons) if reasons else "insufficient data"
    
    async def _apply_confidence_adjustments(self, adjustments: List[ConfidenceAdjustment]):
        """Apply confidence adjustments to the database"""
        try:
            with self.database.get_connection() as conn:
                with conn.cursor() as cursor:
                    for adjustment in adjustments:
                        cursor.execute("""
                            UPDATE nominations 
                            SET eta_confidence = %s, updated_at = %s
                            WHERE runtime_vessel_id = %s;
                        """, (
                            adjustment.new_confidence,
                            adjustment.adjustment_time,
                            adjustment.vessel_id
                        ))
                        
                        logger.info(f"Updated confidence for vessel {adjustment.vessel_id}: "
                                  f"{adjustment.old_confidence}% → {adjustment.new_confidence}% "
                                  f"({adjustment.reason})")
                    
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"Failed to apply confidence adjustments: {e}")
            raise
    
    async def _generate_accuracy_report(self):
        """Generate accuracy report for monitoring"""
        try:
            report = {
                'timestamp': datetime.now(timezone.utc),
                'source_accuracy': {},
                'overall_stats': {}
            }
            
            # Source accuracy summary
            for source, errors in self.source_accuracy.items():
                if errors:
                    report['source_accuracy'][source] = {
                        'sample_count': len(errors),
                        'avg_error_hours': round(statistics.mean(errors), 2),
                        'median_error_hours': round(statistics.median(errors), 2),
                        'max_error_hours': round(max(errors), 2),
                        'accuracy_score': max(0, 100 - statistics.mean(errors) * 20)  # Rough score
                    }
            
            # Overall stats
            all_errors = [error for errors in self.source_accuracy.values() for error in errors]
            if all_errors:
                report['overall_stats'] = {
                    'total_samples': len(all_errors),
                    'avg_error_hours': round(statistics.mean(all_errors), 2),
                    'accuracy_within_1h': len([e for e in all_errors if e <= 1.0]) / len(all_errors) * 100,
                    'accuracy_within_2h': len([e for e in all_errors if e <= 2.0]) / len(all_errors) * 100,
                    'total_adjustments': self.total_adjustments
                }
            
            logger.info(f"Accuracy Report: {report['overall_stats']}")
            
        except Exception as e:
            logger.error(f"Failed to generate accuracy report: {e}")
    
    def get_accuracy_stats(self) -> Dict:
        """Get current accuracy statistics"""
        stats = {
            'last_analysis_time': self.last_analysis_time,
            'total_adjustments': self.total_adjustments,
            'source_accuracy': {},
            'monitoring_interval_hours': self.monitoring_interval_hours,
            'accuracy_window_days': self.accuracy_window_days
        }
        
        # Add source accuracy summaries
        for source, errors in self.source_accuracy.items():
            if errors:
                stats['source_accuracy'][source] = {
                    'sample_count': len(errors),
                    'avg_error_hours': round(statistics.mean(errors), 2),
                    'accuracy_within_2h_percent': len([e for e in errors if e <= 2.0]) / len(errors) * 100
                }
        
        return stats
