# Automated Backup Scheduler for Jetty Planner
# This script triggers database backups and handles logging

param(
    [Parameter(Mandatory=$false)]
    [string]$BackupType = "manual"  # manual, daily, weekly, monthly
)

# Configuration
$LogPath = "C:\Users\<USER>\Jettyplanner\logs"
$BackupPath = "C:\Users\<USER>\Jettyplanner\backups"
$ContainerName = "jetty-postgres-backup"
$MaxRetries = 3

# Ensure log directory exists
if (-not (Test-Path $LogPath)) {
    New-Item -ItemType Directory -Path $LogPath -Force
}

# Logging function
function Write-BackupLog {
    param($Message, $Level = "INFO")
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] [$Level] $Message"
    Write-Output $LogMessage
    Add-Content -Path "$LogPath\backup-scheduler.log" -Value $LogMessage
}

# Main backup function
function Invoke-DatabaseBackup {
    param($Type)
    
    Write-BackupLog "Starting $Type backup..." "INFO"
    
    # Check if container is running
    $ContainerStatus = docker ps --filter "name=$ContainerName" --format "{{.Status}}"
    if (-not $ContainerStatus) {
        Write-BackupLog "Container $ContainerName is not running. Starting backup services..." "WARN"
        
        # Start backup services
        try {
            Set-Location "C:\Users\<USER>\Jettyplanner"
            docker-compose -f docker-compose.backup.yml up -d
            Start-Sleep -Seconds 10  # Wait for containers to be ready
        }
        catch {
            Write-BackupLog "Failed to start backup services: $($_.Exception.Message)" "ERROR"
            return $false
        }
    }
    
    # Attempt backup with retries
    for ($i = 1; $i -le $MaxRetries; $i++) {
        try {
            Write-BackupLog "Backup attempt $i of $MaxRetries..." "INFO"
            
            # Execute backup
            $BackupResult = docker exec $ContainerName backup
            
            if ($LASTEXITCODE -eq 0) {
                Write-BackupLog "$Type backup completed successfully" "SUCCESS"
                
                # Get backup file info
                $LatestBackup = Get-ChildItem $BackupPath -Filter "*.sql" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
                if ($LatestBackup) {
                    $SizeKB = [math]::Round($LatestBackup.Length / 1KB, 2)
                    Write-BackupLog "Latest backup: $($LatestBackup.Name) ($SizeKB KB)" "INFO"
                }
                
                return $true
            }
            else {
                Write-BackupLog "Backup command failed with exit code $LASTEXITCODE" "ERROR"
            }
        }
        catch {
            Write-BackupLog "Backup attempt $i failed: $($_.Exception.Message)" "ERROR"
        }
        
        if ($i -lt $MaxRetries) {
            Write-BackupLog "Waiting 30 seconds before retry..." "INFO"
            Start-Sleep -Seconds 30
        }
    }
    
    Write-BackupLog "$Type backup failed after $MaxRetries attempts" "ERROR"
    return $false
}

# Health check function
function Test-BackupHealth {
    Write-BackupLog "Performing backup system health check..." "INFO"
    
    # Check container status
    $ContainerStatus = docker ps --filter "name=$ContainerName" --format "{{.Status}}"
    if ($ContainerStatus) {
        Write-BackupLog "Backup container status: $ContainerStatus" "INFO"
    } else {
        Write-BackupLog "Backup container is not running" "WARN"
    }
    
    # Check backup directory
    if (Test-Path $BackupPath) {
        $BackupCount = (Get-ChildItem $BackupPath -Filter "*.sql").Count
        Write-BackupLog "Backup directory contains $BackupCount backup files" "INFO"
    } else {
        Write-BackupLog "Backup directory not found: $BackupPath" "ERROR"
    }
    
    # Check disk space
    $Drive = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
    $FreeSpaceGB = [math]::Round($Drive.FreeSpace / 1GB, 2)
    Write-BackupLog "Available disk space: $FreeSpaceGB GB" "INFO"
    
    if ($FreeSpaceGB -lt 5) {
        Write-BackupLog "WARNING: Low disk space detected!" "WARN"
    }
}

# Main execution
try {
    Write-BackupLog "=== Automated Backup Scheduler Started ===" "INFO"
    Write-BackupLog "Backup type: $BackupType" "INFO"
    
    # Perform health check
    Test-BackupHealth
    
    # Execute backup
    $Success = Invoke-DatabaseBackup -Type $BackupType
    
    if ($Success) {
        Write-BackupLog "=== Backup Scheduler Completed Successfully ===" "SUCCESS"
        exit 0
    } else {
        Write-BackupLog "=== Backup Scheduler Failed ===" "ERROR"
        exit 1
    }
}
catch {
    Write-BackupLog "Unexpected error: $($_.Exception.Message)" "ERROR"
    Write-BackupLog "=== Backup Scheduler Failed with Exception ===" "ERROR"
    exit 1
}
