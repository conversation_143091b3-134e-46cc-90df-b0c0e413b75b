"""
Assignment API Module

This module provides API endpoints for managing assignments.
"""
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
import uuid
from datetime import datetime, timedelta
import logging

from src.models.terminal import Terminal
from src.models.vessel import Vessel, VesselType
from src.models.schedule import Assignment
from src.utils.status_utils import (
    normalize_status,
    is_valid_assignment_status,
    is_valid_assignment_transition
)
from src.database import Database

# Create a logger for this module
logger = logging.getLogger(__name__)

# Initialize database
db = Database()

router = APIRouter(prefix="/api/assignments", tags=["assignments"])

# Pydantic models for request/response validation
class AssignmentCreate(BaseModel):
    jetty_id: str
    vessel_id: str
    start_time: datetime
    end_time: datetime
    status: str = "PENDING_APPROVAL"
    notes: Optional[str] = None
    surveyor_ids: List[str] = []
    pump_ids: List[str] = []
    tank_ids: List[str] = []

class AssignmentUpdate(BaseModel):
    jetty_id: Optional[str] = None
    vessel_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    status: Optional[str] = None
    actual_start_time: Optional[datetime] = None
    actual_end_time: Optional[datetime] = None
    notes: Optional[str] = None
    surveyor_ids: Optional[List[str]] = None
    pump_ids: Optional[List[str]] = None
    tank_ids: Optional[List[str]] = None

class AssignmentResponse(BaseModel):
    id: str
    jetty_id: str
    vessel_id: str
    start_time: datetime
    end_time: datetime
    status: str
    actual_start_time: Optional[datetime] = None
    actual_end_time: Optional[datetime] = None
    notes: Optional[str] = None
    surveyor_ids: List[str] = []
    pump_ids: List[str] = []
    tank_ids: List[str] = []
    created_at: datetime
    updated_at: datetime
    # Lock fields (optional in response for forward-compat)
    lock_status: Optional[str] = None
    lock_reason: Optional[str] = None
    locked_by: Optional[str] = None
    locked_at: Optional[datetime] = None

# Helper function to convert Assignment to AssignmentResponse
def assignment_to_response(assignment: Assignment) -> AssignmentResponse:
    """Convert Assignment object to AssignmentResponse model"""
    return AssignmentResponse(
        id=assignment.id,
        jetty_id=assignment.jetty.id,
        vessel_id=assignment.vessel.id,
        start_time=assignment.start_time,
        end_time=assignment.end_time,
        status=assignment.status,
        actual_start_time=assignment.actual_start_time,
        actual_end_time=assignment.actual_end_time,
        notes=assignment.notes,
        surveyor_ids=assignment.surveyor_ids,
        pump_ids=assignment.pump_ids,
        tank_ids=assignment.tank_ids,
        created_at=datetime.now(),  # Placeholder - would be stored in real app
        updated_at=datetime.now()   # Placeholder - would be stored in real app
    )


class AssignmentLockRequest(BaseModel):
    lock_status: str
    lock_reason: Optional[str] = None
    locked_by: Optional[str] = None


class AssignmentUnlockRequest(BaseModel):
    unlock_reason: Optional[str] = None
    unlocked_by: Optional[str] = None


class BulkLockRequest(BaseModel):
    assignment_ids: List[int]
    lock_status: str
    lock_reason: Optional[str] = None
    locked_by: Optional[str] = None

@router.get("/", response_model=List[AssignmentResponse])
async def get_all_assignments(
    status: Optional[str] = Query(None, description="Filter by status"),
    vessel_id: Optional[str] = Query(None, description="Filter by vessel ID"),
    jetty_id: Optional[str] = Query(None, description="Filter by jetty ID"),
    start_time_min: Optional[datetime] = Query(None, description="Filter by minimum start time"),
    start_time_max: Optional[datetime] = Query(None, description="Filter by maximum start time")
):
    """Get all assignments with optional filtering"""
    try:
        # Get assignments from database
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            return []

        db_assignments = db.get_assignments(terminal_id)
        filtered_assignments = db_assignments

        # Apply filters
        if status:
            normalized_status = normalize_status(status)
            filtered_assignments = [a for a in filtered_assignments if a.get('status') == normalized_status]

        if vessel_id:
            filtered_assignments = [a for a in filtered_assignments if str(a.get('vessel_id')) == vessel_id]

        if jetty_id:
            filtered_assignments = [a for a in filtered_assignments if a.get('jetty_name') == jetty_id]

        if start_time_min:
            filtered_assignments = [a for a in filtered_assignments if a.get('start_time') and datetime.fromisoformat(a.get('start_time')) >= start_time_min]

        if start_time_max:
            filtered_assignments = [a for a in filtered_assignments if a.get('start_time') and datetime.fromisoformat(a.get('start_time')) <= start_time_max]

        # Convert to response format
        response_assignments = []
        for assignment in filtered_assignments:
            response_assignments.append(AssignmentResponse(
                id=assignment.get('id'),
                jetty_id=assignment.get('jetty_name'),  # Using jetty_name as jetty_id
                vessel_id=assignment.get('vessel_id'),
                start_time=datetime.fromisoformat(assignment.get('start_time')),
                end_time=datetime.fromisoformat(assignment.get('end_time')),
                status=assignment.get('status'),
                notes=None,  # Database doesn't store notes yet
                surveyor_ids=[],  # Database doesn't store these yet
                pump_ids=[],  # Database doesn't store these yet
                tank_ids=[],  # Database doesn't store these yet
                created_at=datetime.now(),  # Placeholder
                updated_at=datetime.now(),   # Placeholder
                lock_status=assignment.get('lock_status'),
                lock_reason=assignment.get('lock_reason'),
                locked_by=assignment.get('locked_by'),
                locked_at=datetime.fromisoformat(assignment['locked_at']) if assignment.get('locked_at') else None
            ))

        return response_assignments

    except Exception as e:
        logger.error(f"Error getting assignments: {e}")
        return []

@router.get("/{assignment_id}", response_model=AssignmentResponse)
async def get_assignment(assignment_id: str):
    """Get a specific assignment by ID"""
    try:
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=404, detail="No active terminal")

        # Get assignment from database by ID
        assignments = db.get_assignments(terminal_id)
        assignment = next((a for a in assignments if str(a.get('id')) == assignment_id), None)

        if not assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")

        return AssignmentResponse(
            id=assignment.get('id'),
            jetty_id=assignment.get('jetty_name'),  # Using jetty_name as jetty_id
            vessel_id=assignment.get('vessel_id'),
            start_time=datetime.fromisoformat(assignment.get('start_time')),
            end_time=datetime.fromisoformat(assignment.get('end_time')),
            status=assignment.get('status'),
            notes=None,  # Database doesn't store notes yet
            surveyor_ids=[],  # Database doesn't store these yet
            pump_ids=[],  # Database doesn't store these yet
            tank_ids=[],  # Database doesn't store these yet
            created_at=datetime.now(),  # Placeholder
            updated_at=datetime.now(),   # Placeholder
            lock_status=assignment.get('lock_status'),
            lock_reason=assignment.get('lock_reason'),
            locked_by=assignment.get('locked_by'),
            locked_at=assignment.get('locked_at')
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve assignment")

@router.post("/", response_model=AssignmentResponse)
async def create_assignment(assignment_data: AssignmentCreate):
    """Create a new assignment"""
    try:
        # Verify status is valid
        if assignment_data.status:
            normalized_status = normalize_status(assignment_data.status)
            if not is_valid_assignment_status(normalized_status):
                raise HTTPException(status_code=400, detail=f"Invalid status: {assignment_data.status}")
        else:
            normalized_status = "PENDING_APPROVAL"

        # Get active terminal
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=400, detail="No active terminal configured")

        # Generate a unique ID
        assignment_id = str(uuid.uuid4())

        # Prepare assignment data for database
        assignment_dict = {
            'terminal_id': terminal_id,
            'vessel_id': assignment_data.vessel_id,
            'vessel_name': f"Vessel {assignment_data.vessel_id}",  # Placeholder name
            'vessel_type': "TANKER",  # Default vessel type
            'jetty_name': assignment_data.jetty_id,
            'start_time': assignment_data.start_time.isoformat(),
            'end_time': assignment_data.end_time.isoformat(),
            'status': normalized_status
        }

        # Add assignment to database
        db_assignment_id = db.add_assignment(assignment_dict)

        if not db_assignment_id:
            raise HTTPException(status_code=500, detail="Failed to create assignment in database")

        # Return response
        return AssignmentResponse(
            id=db_assignment_id,
            jetty_id=assignment_data.jetty_id,
            vessel_id=assignment_data.vessel_id,
            start_time=assignment_data.start_time,
            end_time=assignment_data.end_time,
            status=normalized_status,
            notes=assignment_data.notes,
            surveyor_ids=assignment_data.surveyor_ids,
            pump_ids=assignment_data.pump_ids,
            tank_ids=assignment_data.tank_ids,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating assignment: {e}")
        raise HTTPException(status_code=500, detail="Failed to create assignment")

@router.put("/{assignment_id}", response_model=AssignmentResponse)
async def update_assignment(assignment_id: str, assignment_data: AssignmentUpdate):
    """Update an existing assignment"""
    try:
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=404, detail="No active terminal")

        # Get current assignment from database
        assignments = db.get_assignments(terminal_id)
        current_assignment = next((a for a in assignments if str(a.get('id')) == assignment_id), None)

        if not current_assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")

        # Prepare update data
        update_dict = {
            'vessel_id': assignment_data.vessel_id if assignment_data.vessel_id is not None else current_assignment.get('vessel_id'),
            'vessel_name': current_assignment.get('vessel_name'),  # Keep existing name
            'vessel_type': current_assignment.get('vessel_type'),  # Keep existing type
            'jetty_name': assignment_data.jetty_id if assignment_data.jetty_id is not None else current_assignment.get('jetty_name'),
            'start_time': assignment_data.start_time.isoformat() if assignment_data.start_time is not None else current_assignment.get('start_time'),
            'end_time': assignment_data.end_time.isoformat() if assignment_data.end_time is not None else current_assignment.get('end_time'),
            'status': normalize_status(assignment_data.status) if assignment_data.status is not None else current_assignment.get('status')
        }

        # Validate status transition if status is being updated
        if assignment_data.status is not None:
            current_status = current_assignment.get('status')
            new_status = update_dict['status']
            is_valid, message = is_valid_assignment_transition(current_status, new_status)
            if not is_valid:
                raise HTTPException(status_code=400, detail=message)

        # Update in database
        success = db.update_assignment(assignment_id, update_dict)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to update assignment in database")

        # Return updated assignment
        return AssignmentResponse(
            id=assignment_id,
            jetty_id=update_dict['jetty_name'],
            vessel_id=update_dict['vessel_id'],
            start_time=datetime.fromisoformat(update_dict['start_time']),
            end_time=datetime.fromisoformat(update_dict['end_time']),
            status=update_dict['status'],
            notes=assignment_data.notes,
            surveyor_ids=assignment_data.surveyor_ids or [],
            pump_ids=assignment_data.pump_ids or [],
            tank_ids=assignment_data.tank_ids or [],
            created_at=datetime.now(),  # Placeholder
            updated_at=datetime.now()   # Placeholder
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update assignment")

@router.delete("/{assignment_id}", response_model=dict)
async def delete_assignment(assignment_id: int):
    """Delete an assignment"""
    try:
        # Load assignment to log deletion details
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=404, detail="No active terminal")

        assignments = db.get_assignments(terminal_id)
        assignment = next((a for a in assignments if int(a.get('id')) == int(assignment_id)), None)
        if not assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")

        # Best-effort log to assignment change log
        try:
            from src.utils import datetime_utils as dtutils
            def _parse_dt(val):
                try:
                    return dtutils.normalize_iso_to_utc_microseconds(val)
                except Exception:
                    return None
            old_start_dt = _parse_dt(assignment.get('start_time')) or assignment.get('start_time')
            old_end_dt = _parse_dt(assignment.get('end_time')) or assignment.get('end_time')

            def _iso(v):
                try:
                    from datetime import datetime as _dt
                    if isinstance(v, _dt):
                        return v.isoformat(sep=' ')
                    if isinstance(v, str):
                        return v.replace('T', ' ')
                except Exception:
                    pass
                return v

            # Route through PostgreSQL database
            db.log_assignment_change(
                assignment_id=int(assignment_id),
                old_start_time=old_start_dt if hasattr(dtutils, 'normalize_iso_to_utc_microseconds') else _iso(old_start_dt),
                old_end_time=old_end_dt if hasattr(dtutils, 'normalize_iso_to_utc_microseconds') else _iso(old_end_dt),
                new_start_time=None,
                new_end_time=None,
                reason="Assignment deleted",
                vessel_id=assignment.get('vessel_id'),
                vessel_name=assignment.get('vessel_name'),
                jetty_name=assignment.get('jetty_name'),
                changed_by="user",
                terminal_id=terminal_id,
            )
        except Exception:
            pass

        # Delete from database
        success = db.delete_assignment(int(assignment_id))
        if not success:
            raise HTTPException(status_code=404, detail="Assignment not found")

        return {"message": "Assignment deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete assignment")

@router.post("/{assignment_id}/activate", response_model=AssignmentResponse)
async def activate_assignment(assignment_id: str):
    """Activate an assignment"""
    try:
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=404, detail="No active terminal")

        # Get current assignment from database
        assignments = db.get_assignments(terminal_id)
        current_assignment = next((a for a in assignments if str(a.get('id')) == assignment_id), None)

        if not current_assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")

        # Update status to IN_PROGRESS
        update_dict = {
            'vessel_id': current_assignment.get('vessel_id'),
            'vessel_name': current_assignment.get('vessel_name'),
            'vessel_type': current_assignment.get('vessel_type'),
            'jetty_name': current_assignment.get('jetty_name'),
            'start_time': current_assignment.get('start_time'),
            'end_time': current_assignment.get('end_time'),
            'status': 'IN_PROGRESS'
        }

        success = db.update_assignment(assignment_id, update_dict)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to activate assignment")

        # Return updated assignment
        return AssignmentResponse(
            id=assignment_id,
            jetty_id=update_dict['jetty_name'],
            vessel_id=update_dict['vessel_id'],
            start_time=datetime.fromisoformat(update_dict['start_time']),
            end_time=datetime.fromisoformat(update_dict['end_time']),
            status=update_dict['status'],
            notes=None,
            surveyor_ids=[],
            pump_ids=[],
            tank_ids=[],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error activating assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to activate assignment")

@router.post("/{assignment_id}/complete", response_model=AssignmentResponse)
async def complete_assignment(assignment_id: str):
    """Complete an assignment"""
    try:
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=404, detail="No active terminal")

        # Get current assignment from database
        assignments = db.get_assignments(terminal_id)
        current_assignment = next((a for a in assignments if str(a.get('id')) == assignment_id), None)

        if not current_assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")

        # Update status to COMPLETED
        update_dict = {
            'vessel_id': current_assignment.get('vessel_id'),
            'vessel_name': current_assignment.get('vessel_name'),
            'vessel_type': current_assignment.get('vessel_type'),
            'jetty_name': current_assignment.get('jetty_name'),
            'start_time': current_assignment.get('start_time'),
            'end_time': current_assignment.get('end_time'),
            'status': 'COMPLETED'
        }

        success = db.update_assignment(assignment_id, update_dict)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to complete assignment")

        # Return updated assignment
        return AssignmentResponse(
            id=assignment_id,
            jetty_id=update_dict['jetty_name'],
            vessel_id=update_dict['vessel_id'],
            start_time=datetime.fromisoformat(update_dict['start_time']),
            end_time=datetime.fromisoformat(update_dict['end_time']),
            status=update_dict['status'],
            notes=None,
            surveyor_ids=[],
            pump_ids=[],
            tank_ids=[],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to complete assignment")

@router.post("/{assignment_id}/cancel", response_model=AssignmentResponse)
async def cancel_assignment(assignment_id: str, reason: Optional[str] = None):
    """Cancel an assignment"""
    try:
        terminal_id = db.get_active_terminal_id()
        if not terminal_id:
            raise HTTPException(status_code=404, detail="No active terminal")

        # Get current assignment from database
        assignments = db.get_assignments(terminal_id)
        current_assignment = next((a for a in assignments if str(a.get('id')) == assignment_id), None)

        if not current_assignment:
            raise HTTPException(status_code=404, detail="Assignment not found")

        # Update status to CANCELLED
        update_dict = {
            'vessel_id': current_assignment.get('vessel_id'),
            'vessel_name': current_assignment.get('vessel_name'),
            'vessel_type': current_assignment.get('vessel_type'),
            'jetty_name': current_assignment.get('jetty_name'),
            'start_time': current_assignment.get('start_time'),
            'end_time': current_assignment.get('end_time'),
            'status': 'CANCELLED'
        }

        success = db.update_assignment(assignment_id, update_dict)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to cancel assignment")

        # Return updated assignment
        return AssignmentResponse(
            id=assignment_id,
            jetty_id=update_dict['jetty_name'],
            vessel_id=update_dict['vessel_id'],
            start_time=datetime.fromisoformat(update_dict['start_time']),
            end_time=datetime.fromisoformat(update_dict['end_time']),
            status=update_dict['status'],
            notes=reason,  # Use reason as notes
            surveyor_ids=[],
            pump_ids=[],
            tank_ids=[],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel assignment")


@router.post("/{assignment_id}/lock")
async def lock_assignment(assignment_id: int, lock_request: AssignmentLockRequest):
    """Lock an assignment with a specified lock status and optional reason."""
    try:
        # Validate lock status
        status_norm = (lock_request.lock_status or '').upper()
        if status_norm not in ("SOFT_LOCKED", "HARD_LOCKED", "TIME_LOCKED"):
            raise HTTPException(status_code=400, detail="Invalid lock_status")

        # Update database record
        updated = db.update_assignment(assignment_id, {
            'lock_status': status_norm,
            'lock_reason': lock_request.lock_reason,
            'locked_by': lock_request.locked_by,
            'locked_at': datetime.utcnow().isoformat()
        })
        if not updated:
            raise HTTPException(status_code=404, detail="Assignment not found")
        # Return latest assignment
        a = db.get_assignment(assignment_id)
        return {
            'id': a.get('id'),
            'lock_status': a.get('lock_status'),
            'lock_reason': a.get('lock_reason'),
            'locked_by': a.get('locked_by'),
            'locked_at': a.get('locked_at')
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error locking assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to lock assignment")


@router.post("/{assignment_id}/unlock")
async def unlock_assignment(assignment_id: int, unlock_request: AssignmentUnlockRequest):
    """Unlock an assignment (set lock_status to UNLOCKED)."""
    try:
        updated = db.update_assignment(assignment_id, {
            'lock_status': 'UNLOCKED',
            'locked_by': None,
            'locked_at': None,
            'lock_reason': unlock_request.unlock_reason  # keep last reason for audit if desired
        })
        if not updated:
            raise HTTPException(status_code=404, detail="Assignment not found")
        a = db.get_assignment(assignment_id)
        return {
            'id': a.get('id'),
            'lock_status': a.get('lock_status'),
            'lock_reason': a.get('lock_reason'),
            'locked_by': a.get('locked_by'),
            'locked_at': a.get('locked_at')
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error unlocking assignment {assignment_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to unlock assignment")


@router.post("/bulk-lock")
async def bulk_lock_assignments(bulk: BulkLockRequest):
    """Bulk lock multiple assignments."""
    try:
        status_norm = (bulk.lock_status or '').upper()
        if status_norm not in ("SOFT_LOCKED", "HARD_LOCKED", "TIME_LOCKED"):
            raise HTTPException(status_code=400, detail="Invalid lock_status")

        updated_ids: List[int] = []
        for aid in bulk.assignment_ids or []:
            ok = db.update_assignment(int(aid), {
                'lock_status': status_norm,
                'lock_reason': bulk.lock_reason,
                'locked_by': bulk.locked_by,
                'locked_at': datetime.utcnow().isoformat()
            })
            if ok:
                updated_ids.append(int(aid))
        return {"updated": updated_ids, "requested": bulk.assignment_ids}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in bulk lock: {e}")
        raise HTTPException(status_code=500, detail="Failed bulk lock operation")


@router.get("/locks")
async def get_locked_assignments(lock_status: Optional[str] = None):
    """List locked assignments with optional status filter."""
    try:
        terminal_id = db.get_active_terminal_id()
        rows = db.get_assignments(terminal_id)
        out = []
        for a in rows:
            ls = (a.get('lock_status') or 'UNLOCKED').upper()
            if lock_status and ls != (lock_status or '').upper():
                continue
            if ls != 'UNLOCKED':
                out.append(a)
        return out
    except Exception as e:
        logger.error(f"Error listing locked assignments: {e}")
        raise HTTPException(status_code=500, detail="Failed to list locked assignments")