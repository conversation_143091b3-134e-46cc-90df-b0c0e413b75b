"""
ML Prediction Service

This module handles loading ML models and generating predictions for vessel terminal times.
"""

import logging
import os
import pandas as pd
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta, timezone
import pickle
import json
import joblib
import hashlib
from collections import OrderedDict
import warnings

try:
    from .models import (
        VesselFeatures, 
        TimePrediction, 
        PredictionRequest, 
        PredictionResponse
    )
    from ..models.vessel import VesselBase
    from ..models.terminal import Terminal, Jetty
except ImportError:
    # Fallback for when running directly
    from src.ml.models import (
        VesselFeatures, 
        TimePrediction, 
        PredictionRequest, 
        PredictionResponse
    )
    from src.models.vessel import VesselBase
    from src.models.terminal import Terminal, Jetty

logger = logging.getLogger(__name__)


class MLPredictionService:
    """Service for ML-based vessel terminal time predictions"""
    
    def __init__(self, model_path: str = "models/", model_config: Optional[Dict[str, str]] = None,
                 terminal_only: bool = True, enable_cache: bool = True, cache_size: int = 1024):
        """
        Initialize the ML prediction service
        
        Args:
            model_path: Path to directory containing trained models
            model_config: Optional mapping of prediction tasks to model files
        """
        self.model_path = model_path
        self.models = {}
        self.model_metadata = {}
        self.business_rules = None
        self.terminal_only = terminal_only
        self.enable_cache = enable_cache
        self._cache_size = cache_size
        self._prediction_cache: "OrderedDict[str, TimePrediction]" = OrderedDict()
        
        # Default model configuration - can be overridden
        self.model_config = model_config or {
            'terminal': 'gradient_boosting_20250820_120027.joblib',   # Use available gradient boosting model
            'pre_pump': 'gradient_boosting_20250820_120027.joblib',   # Use same model for all phases for now
            'pump': 'gradient_boosting_20250820_120027.joblib',       # Use same model for all phases for now
            'post_pump': 'gradient_boosting_20250820_120027.joblib'   # Use same model for all phases for now
        }
        
        # Try to load models
        self._load_models()
        self._load_business_rules()
        # Reduce sklearn feature-name warnings if any remain
        warnings.filterwarnings(
            "ignore",
            message="X does not have valid feature names, but",
            category=UserWarning
        )

    def _make_cache_key(self, features: "VesselFeatures") -> str:
        """Create a stable cache key from features including jetty context."""
        fv = features.to_feature_vector()
        # Include jetty context explicitly
        fv["jetty_id"] = features.jetty_id
        fv["max_flow_rate"] = features.max_flow_rate
        # Sort keys for stability
        items = sorted((k, fv[k]) for k in fv)
        raw = json.dumps(items, separators=(",", ":"))
        return hashlib.sha256(raw.encode("utf-8")).hexdigest()

    def _cache_get(self, key: str) -> Optional["TimePrediction"]:
        if not self.enable_cache:
            return None
        pred = self._prediction_cache.get(key)
        if pred is not None:
            # Move to end to mark as recently used
            self._prediction_cache.move_to_end(key)
        return pred

    def _cache_set(self, key: str, value: "TimePrediction") -> None:
        if not self.enable_cache:
            return
        self._prediction_cache[key] = value
        self._prediction_cache.move_to_end(key)
        if len(self._prediction_cache) > self._cache_size:
            # Pop least-recently used
            self._prediction_cache.popitem(last=False)
    
    def _load_models(self):
        """Load trained ML models from joblib files"""
        # Decide which models to load based on mode
        model_items = (
            [("terminal", self.model_config.get("terminal", "unknown"))]
            if self.terminal_only else list(self.model_config.items())
        )

        # First, load models from the fixed configuration
        for model_name, filename in model_items:
            model_file = os.path.join(self.model_path, filename)
            
            if os.path.exists(model_file):
                success = self._load_single_model(model_name, model_file)
                if not success:
                    self.models[model_name] = None
            else:
                logger.warning(f"Model file not found: {model_file}")
                self.models[model_name] = None
        
        # Then, scan for newly uploaded models (extracted from zip files)
        self._load_uploaded_models()
        
        # Store overall metadata
        loaded_models = [name for name, model in self.models.items() if model is not None]
        self.model_metadata['loaded_models'] = loaded_models
        self.model_metadata['model_files'] = self.model_config
        self.model_metadata['last_loaded'] = datetime.now(timezone.utc).isoformat()
        
        logger.info(f"Loaded {len(loaded_models)} models: {', '.join(loaded_models)}")
    
    def _get_active_model_info(self) -> str:
        """Generate human-readable active model information"""
        loaded_models = []
        for model_name in (['terminal'] if self.terminal_only else ['terminal', 'pre_pump', 'pump', 'post_pump']):
            if self.models.get(model_name) is not None:
                algorithm = self.model_metadata.get(f"{model_name}_algorithm", "unknown")
                timestamp = self.model_metadata.get(f"{model_name}_timestamp", "")
                # Extract just the date from timestamp if available
                if timestamp and len(timestamp) > 10:
                    try:
                        date_part = timestamp.split('_')[0] if '_' in timestamp else timestamp[:10]
                        loaded_models.append(f"{algorithm}")
                    except:
                        loaded_models.append(f"{algorithm}")
                else:
                    loaded_models.append(f"{algorithm}")
        
        if loaded_models:
            # Show primary algorithm (most common) and count
            primary_algorithm = max(set(loaded_models), key=loaded_models.count) if loaded_models else "unknown"
            model_count = len(loaded_models)
            return f"{primary_algorithm} ({model_count} models)"
        else:
            return "No models loaded"
    
    def _load_single_model(self, model_name: str, model_file: str) -> bool:
        """Load a single model file and return success status"""
        try:
            # Load the model dictionary
            model_dict = joblib.load(model_file)
            
            # Extract the actual model from the dictionary
            if isinstance(model_dict, dict) and 'model' in model_dict:
                self.models[model_name] = model_dict['model']
                
                # Store metadata for this model
                self.model_metadata[f"{model_name}_algorithm"] = model_dict.get('algorithm', 'unknown')
                self.model_metadata[f"{model_name}_timestamp"] = model_dict.get('timestamp', 'unknown')
                self.model_metadata[f"{model_name}_target"] = model_dict.get('target_column', model_name)
                self.model_metadata[f"{model_name}_performance"] = model_dict.get('performance', {})
                
                logger.info(f"Loaded {model_name} model from {model_file} ({model_dict.get('algorithm', 'unknown')})")
                return True
            else:
                logger.warning(f"Invalid model format in {model_file} - expected dictionary with 'model' key")
                return False
                
        except Exception as e:
            logger.warning(f"Failed to load {model_name} model from {model_file}: {e}")
            return False
    
    def _load_uploaded_models(self):
        """Scan for and load newly uploaded models from zip files"""
        if not os.path.exists(self.model_path):
            return
        
        # Look for extracted models (from zip uploads)
        for filename in os.listdir(self.model_path):
            if filename.startswith('extracted_') and filename.endswith('.joblib'):
                model_file = os.path.join(self.model_path, filename)
                
                # Determine model type from filename
                model_type = self._determine_model_type_from_filename(filename)
                
                # Use a unique key for uploaded models
                model_key = f"uploaded_{model_type}"
                
                # Only load if we don't already have this model loaded
                if model_key not in self.models:
                    success = self._load_single_model(model_key, model_file)
                    if success:
                        # Update the configuration to use this newer model
                        if model_type in self.model_config:
                            # Replace the default model with the uploaded one
                            old_key = model_type
                            if old_key in self.models:
                                logger.info(f"Replacing {old_key} model with uploaded version: {filename}")
                                self.models[model_type] = self.models[model_key]
                                del self.models[model_key]
                        else:
                            # New model type, keep it with the uploaded_ prefix
                            logger.info(f"Added new uploaded model: {model_key}")
    
    def _determine_model_type_from_filename(self, filename: str) -> str:
        """Determine the model type from the filename"""
        filename_lower = filename.lower()
        
        # Check for specific model types
        if 'gradient_boosting' in filename_lower:
            return 'gradient_boosting'
        elif 'random_forest' in filename_lower:
            return 'random_forest'
        elif 'xgboost' in filename_lower:
            return 'xgboost'
        elif 'terminal' in filename_lower:
            return 'terminal'
        elif 'pre_pump' in filename_lower or 'prepump' in filename_lower:
            return 'pre_pump'
        elif 'post_pump' in filename_lower or 'postpump' in filename_lower:
            return 'post_pump'
        elif 'pump' in filename_lower:
            return 'pump'
        else:
            return 'unknown'
    
    def _load_business_rules(self):
        """Load business rules for jetty compatibility"""
        # This would load the business rules from your evos-terminal-business-rules.md
        # For now, we'll implement the key rules programmatically
        pass
    
    def extract_features(self, vessel: VesselBase, jetty: Optional[Jetty] = None) -> VesselFeatures:
        """
        Extract features from vessel data for ML prediction
        
        Args:
            vessel: Vessel object
            jetty: Optional jetty for jetty-specific features
            
        Returns:
            VesselFeatures object
        """
        # Normalize vessel_type to expected uppercase labels first
        try:
            vt_raw = getattr(vessel, 'vessel_type', None)
            vt_val = getattr(vt_raw, 'value', vt_raw)
            vt_str = str(vt_val or '').lower()
            vessel_type_norm = 'TANKER' if vt_str == 'tanker' else ('BARGE' if vt_str == 'barge' else 'TANKER')
        except Exception:
            vessel_type_norm = 'TANKER'
        
        # Extract product information
        product_types = list(vessel.get_cargo_products())
        
        # Handle empty product types with reasonable defaults
        if not product_types:
            # Default to common product based on vessel type and size
            if vessel_type_norm == 'BARGE':
                product_types = ['NAPHTA']  # Common barge product
            else:
                product_types = ['NAPHTA']  # Common tanker product
            logger.warning(f"Vessel {vessel.id} has no product types, using default: {product_types}")
        
        # Determine hazard level based on business rules
        product_hazard_level = self._classify_product_hazard(product_types)
        
        # Check operational requirements
        requires_vapor_return = self._requires_vapor_return(product_types)
        requires_nitrogen_purge = self._requires_nitrogen_purge(product_types)
        
        # Weather and environmental factors (simplified for now)
        weather_risk_score = 0.5  # Would integrate with weather API
        season = self._get_season()
        time_of_day = "day"  # Would use current time
        
        # Jetty-specific features
        jetty_id = jetty.id if jetty else None
        max_flow_rate = jetty.max_flow_rate if jetty else None
        jetty_product_compatible = self._check_jetty_product_compatibility(product_types, jetty) if jetty else None
        connection_size_match = True  # Would implement proper connection matching

        # Calculate cargo volume with fallback for empty/zero volume
        total_cargo_volume = vessel.total_cargo_volume()
        if total_cargo_volume <= 0:
            # Use a reasonable default based on vessel size
            if vessel.deadweight >= 50000:
                total_cargo_volume = 30000.0  # Large vessel default
            elif vessel.deadweight >= 25000:
                total_cargo_volume = 15000.0  # Medium vessel default
            else:
                total_cargo_volume = 8000.0   # Small vessel default
            logger.warning(f"Vessel {vessel.id} has zero/invalid cargo volume, using default: {total_cargo_volume}")

        return VesselFeatures(
            dwt=vessel.deadweight,
            loa=vessel.length,
            beam=vessel.beam,
            draft=vessel.draft,
            vessel_type=vessel_type_norm,
            cargo_volume=total_cargo_volume,
            product_types=product_types,
            product_hazard_level=product_hazard_level,
            is_first_visit=vessel.metadata.get('is_first_visit', False),
            requires_vapor_return=requires_vapor_return,
            requires_nitrogen_purge=requires_nitrogen_purge,
            multiple_products=len(product_types) > 1,
            connection_size="12\"",  # Would extract from vessel/cargo data
            weather_risk_score=weather_risk_score,
            season=season,
            time_of_day=time_of_day,
            jetty_id=jetty_id,
            max_flow_rate=max_flow_rate,
            jetty_product_compatible=jetty_product_compatible,
            connection_size_match=connection_size_match
        )
    
    def predict_times(self, features: VesselFeatures) -> TimePrediction:
        """
        Generate time predictions using ML models
        
        Args:
            features: Vessel features for prediction
            
        Returns:
            TimePrediction object
        """
        # Convert features to ML format
        feature_vector = features.to_feature_vector()

        predictions: Dict[str, float] = {}
        confidences: Dict[str, float] = {}

        model_names = ['terminal'] if self.terminal_only else ['pre_pump', 'pump', 'post_pump', 'terminal']

        for model_name in model_names:
            if self.models.get(model_name) is not None:
                try:
                    prediction = self._predict_single_model(model_name, feature_vector)
                    confidence = self._get_prediction_confidence(model_name, feature_vector)
                    predictions[model_name] = prediction
                    confidences[model_name] = confidence
                    logger.info(f"[ML MODEL] {model_name}: predicted {prediction:.1f} minutes (confidence: {confidence:.2f})")
                except Exception as e:
                    logger.error(f"[ERROR] predicting {model_name}: {e}")
                    predictions[model_name] = self._fallback_prediction(model_name, features)
                    confidences[model_name] = 0.1
                    logger.info(f"[FALLBACK] {model_name}: {predictions[model_name]:.1f} minutes")
            else:
                # When terminal_only is enabled, silently skip submodels
                if model_name != 'terminal':
                    predictions[model_name] = self._fallback_prediction(model_name, features)
                    confidences[model_name] = 0.1
                else:
                    predictions[model_name] = self._fallback_prediction(model_name, features)
                    confidences[model_name] = 0.1
        
        # Unit conversion configuration
        # Models should predict in minutes, use directly
        # If models predict in different units, adjust this conversion factor:
        # - 1.0: Models predict in minutes → use directly
        # - 60.0: Models predict in hours → convert to minutes 
        # - 1/60.0: Models predict in seconds → convert to minutes
        PREDICTION_TO_MINUTES = 1.0  # Models predict in minutes, use directly
        
        # Calculate individual times
        if self.terminal_only:
            # Use reasonable fallbacks for stage times
            pre_pump_minutes = self._fallback_prediction('pre_pump', features) * PREDICTION_TO_MINUTES
            pump_minutes = self._fallback_prediction('pump', features) * PREDICTION_TO_MINUTES
            post_pump_minutes = self._fallback_prediction('post_pump', features) * PREDICTION_TO_MINUTES
        else:
            pre_pump_minutes = predictions.get('pre_pump', 120.0) * PREDICTION_TO_MINUTES
            pump_minutes = predictions.get('pump', 480.0) * PREDICTION_TO_MINUTES
            post_pump_minutes = predictions.get('post_pump', 60.0) * PREDICTION_TO_MINUTES
        
        # Use ML model's terminal prediction (it has learned overlaps/efficiencies)
        # If no terminal prediction available, fall back to sum of components
        if 'terminal' in predictions:
            terminal_minutes = predictions['terminal'] * PREDICTION_TO_MINUTES
        else:
            terminal_minutes = pre_pump_minutes + pump_minutes + post_pump_minutes
        
        return TimePrediction(
            pre_pump_time=timedelta(minutes=pre_pump_minutes),  # 2h default
            pump_time=timedelta(minutes=pump_minutes),          # 8h default  
            post_pump_time=timedelta(minutes=post_pump_minutes), # 1h default
            terminal_time=timedelta(minutes=terminal_minutes),  # Sum of all components
            pre_pump_confidence=confidences.get('pre_pump', 0.1),
            pump_confidence=confidences.get('pump', 0.1),
            post_pump_confidence=confidences.get('post_pump', 0.1),
            terminal_confidence=confidences.get('terminal', 0.1),
            model_version=self._get_active_model_info(),
            prediction_timestamp=datetime.now(timezone.utc).isoformat()
        )
    
    def predict_for_vessel(self, request: PredictionRequest, terminal: Terminal) -> PredictionResponse:
        """
        Generate predictions for a vessel, including jetty recommendations
        
        Args:
            request: Prediction request
            terminal: Terminal configuration
            
        Returns:
            PredictionResponse with predictions and recommendations
        """
        try:
            # Normalize vessel_type on the incoming features to ensure validator harmony
            try:
                vt_in = str(getattr(request.features, 'vessel_type', '') or '').strip().upper()
                if vt_in in ('TANKER', 'BARGE'):
                    request.features.vessel_type = vt_in
            except Exception:
                pass
            # Validate critical features first and provide actionable feedback
            try:
                from .feature_validator import MLFeatureValidator
                validator = MLFeatureValidator()
                validation = validator.validate_critical_features(request.features)
                if not validation.get('is_valid', True):
                    issues = validation.get('issues', [])
                    reason = "Missing or invalid inputs: " + "; ".join(issues)
                    return PredictionResponse(
                        vessel_id=request.vessel_id,
                        jetty_id=None,
                        predictions=TimePrediction(timedelta(), timedelta(), timedelta(), timedelta()),
                        compatible_jetties=[],
                        recommendation_reason=reason,
                        success=False,
                        error_message=reason
                    )
            except Exception:
                # If validator import or execution fails, continue best-effort
                pass
            # Find compatible jetties
            compatible_jetties = self._find_compatible_jetties(request.features, terminal)
            
            # Check if any jetties are compatible
            if not compatible_jetties:
                # Provide detailed feedback about why no jetties are compatible
                reasons = []
                for jetty in terminal.jetties:
                    jetty_reasons = []
                    if request.features.dwt > jetty.max_deadweight:
                        jetty_reasons.append(f"DWT {request.features.dwt:,} > max {jetty.max_deadweight:,}")
                    if request.features.loa > jetty.max_length:
                        jetty_reasons.append(f"Length {request.features.loa}m > max {jetty.max_length}m")
                    if request.features.draft > jetty.max_draft:
                        jetty_reasons.append(f"Draft {request.features.draft}m > max {jetty.max_draft}m")
                    
                    if jetty_reasons:
                        reasons.append(f"{jetty.name}: {', '.join(jetty_reasons)}")
                
                error_msg = "No compatible jetties found. " + "; ".join(reasons) if reasons else "No compatible jetties found."
                
                return PredictionResponse(
                    vessel_id=request.vessel_id,
                    jetty_id=None,
                    predictions=TimePrediction(
                        timedelta(), timedelta(), timedelta(), timedelta()
                    ),
                    compatible_jetties=[],
                    recommendation_reason=error_msg,
                    success=False,
                    error_message=error_msg
                )
            
            # If specific jetty requested, use it (if compatible)
            if request.jetty_id:
                jetty = terminal.get_jetty_by_id(request.jetty_id)
                if jetty and jetty.id in [j.id for j in compatible_jetties]:
                    selected_jetty = jetty
                    reason = f"Using requested jetty {request.jetty_id}"
                else:
                    return PredictionResponse(
                        vessel_id=request.vessel_id,
                        jetty_id=None,
                        predictions=TimePrediction(
                            timedelta(), timedelta(), timedelta(), timedelta()
                        ),
                        compatible_jetties=[],
                        recommendation_reason="Requested jetty is not compatible",
                        success=False,
                        error_message=f"Jetty {request.jetty_id} is not compatible with vessel"
                    )
            else:
                # Select best jetty
                selected_jetty = self._select_best_jetty(compatible_jetties, request.features)
                reason = f"Selected optimal jetty {selected_jetty.id} based on ML analysis"
            
            # Generate predictions for selected jetty
            features_with_jetty = request.features
            features_with_jetty.jetty_id = selected_jetty.id
            features_with_jetty.max_flow_rate = selected_jetty.max_flow_rate
            
            # Cache lookup
            cache_key = self._make_cache_key(features_with_jetty)
            cached = self._cache_get(cache_key)
            if cached is not None:
                predictions = cached
            else:
                predictions = self.predict_times(features_with_jetty)
                self._cache_set(cache_key, predictions)
            
            return PredictionResponse(
                vessel_id=request.vessel_id,
                jetty_id=selected_jetty.id,
                predictions=predictions,
                compatible_jetties=[j.id for j in compatible_jetties],
                recommendation_reason=reason,
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error generating predictions for vessel {request.vessel_id}: {e}")
            return PredictionResponse(
                vessel_id=request.vessel_id,
                jetty_id=None,
                predictions=TimePrediction(timedelta(), timedelta(), timedelta(), timedelta()),
                compatible_jetties=[],
                recommendation_reason="Error occurred during prediction",
                success=False,
                error_message=str(e)
            )
    
    def _predict_single_model(self, model_name: str, features: Dict[str, Any]) -> float:
        """Predict using a single model"""
        model = self.models[model_name]
        
        if model is None:
            logger.warning(f"Model {model_name} not loaded, using fallback")
            return 0.0
        
        try:
            # Use the enhanced feature validator for proper feature mapping
            from .feature_validator import MLFeatureValidator
            
            # Create a proper VesselFeatures object for the validator
            from .models import VesselFeatures
            
            vessel_features_obj = VesselFeatures(
                dwt=features.get('dwt', 25000),
                loa=features.get('loa', 150),
                beam=features.get('beam', 20),
                draft=features.get('draft', 8),
                vessel_type='TANKER' if features.get('vessel_type_tanker', 1) == 1 else 'BARGE',
                cargo_volume=features.get('cargo_volume', 5000),
                product_types=[],  # Will be inferred from other features
                product_hazard_level=self._infer_hazard_from_features(features),
                is_first_visit=features.get('is_first_visit', 0) == 1,
                requires_vapor_return=features.get('requires_vapor_return', 0) == 1,
                requires_nitrogen_purge=features.get('requires_nitrogen_purge', 0) == 1,
                multiple_products=features.get('multiple_products', 0) == 1,
                connection_size=features.get('connection_size', '10"'),
                weather_risk_score=features.get('weather_risk_score', 0.5),
                season=features.get('season', 'summer'),
                time_of_day=features.get('time_of_day', 'day'),
                jetty_id=features.get('jetty_id'),
                max_flow_rate=features.get('max_flow_rate'),
                jetty_product_compatible=features.get('jetty_product_compatible'),
                connection_size_match=features.get('connection_size_match')
            )
            
            validator = MLFeatureValidator()
            prepared_features, warnings = validator.validate_and_prepare_features(vessel_features_obj)
            
            # Log any warnings
            for warning in warnings:
                logger.debug(f"Feature mapping warning: {warning}")
            
            # Apply preprocessing and predict
            processed_array = validator.apply_preprocessing(prepared_features)
            X = processed_array
            # If model expects named features, supply a DataFrame with matching columns to avoid warnings
            try:
                if hasattr(model, 'feature_names_in_'):
                    feature_names = list(getattr(model, 'feature_names_in_'))
                    if processed_array.shape[1] == len(feature_names):
                        X = pd.DataFrame(processed_array, columns=feature_names)
                else:
                    # Fallback to expected feature names from validator
                    X = pd.DataFrame(processed_array, columns=validator.expected_features)
            except Exception:
                # If anything goes wrong, proceed with ndarray
                X = processed_array
            prediction = model.predict(X)[0]
            
            logger.debug(f"Model {model_name}: raw features {list(prepared_features.values())} -> prediction {prediction:.1f}")
            return float(prediction)
            
        except Exception as e:
            logger.error(f"Error predicting with model {model_name}: {e}")
            logger.debug(f"Features were: {features}")
            return 0.0
    
    def _infer_hazard_from_features(self, features: Dict[str, Any]) -> str:
        """Infer product hazard level from available features"""
        if features.get('product_hazard_highly_hazardous', 0) == 1:
            return 'highly_hazardous'
        elif features.get('product_hazard_hazardous', 0) == 1:
            return 'hazardous'
        else:
            return 'standard'
    
    def _map_features_to_model(self, features: Dict[str, Any], expected_features: List[str]) -> Optional[List]:
        """
        Map our feature dictionary to the format expected by the trained model
        
        Args:
            features: Our feature dictionary
            expected_features: Features expected by the model
            
        Returns:
            List of feature values in the correct order, or None if mapping fails
        """
        try:
            feature_values = []
            
            for feature_name in expected_features:
                # Handle numpy string types
                feature_name_str = str(feature_name)
                
                if feature_name_str in features:
                    feature_values.append(features[feature_name_str])
                else:
                    # Map our rich features to what the historical models expect
                    mapped_value = self._map_feature_name(feature_name_str, features)
                    if mapped_value is not None:
                        feature_values.append(mapped_value)
                    else:
                        logger.warning(f"Feature {feature_name_str} not found, using default")
                        # Use a contextual default based on feature name
                        feature_values.append(self._get_contextual_default(feature_name_str, features))
            
            return feature_values
            
        except Exception as e:
            logger.error(f"Error mapping features: {e}")
            return None
    
    def _map_feature_name(self, model_feature: str, available_features: Dict[str, Any]) -> Optional[Any]:
        """
        Map model feature names to available feature names using our rich feature set
        Models expect numeric encodings, not strings
        
        Args:
            model_feature: Feature name expected by model
            available_features: Available features from our system
            
        Returns:
            Mapped feature value (numeric) or None if not found
        """
        # Direct mapping: use cargo volume for product quantity (tons)
        if model_feature == 'product_quantity':
            return float(available_features.get('cargo_volume', 5000.0))
        
        # Convert binary vessel type to numeric encoding (0=BARGE, 1=TANKER)
        elif model_feature == 'vessel_type':
            return 1 if available_features.get('vessel_type_tanker', 0) == 1 else 0
        
        # Infer operation type and encode numerically (0=loading, 1=discharge, 2=multiple)
        elif model_feature == 'operation_type':
            if available_features.get('multiple_products', 0) == 1:
                return 2  # Multiple products = 2
            elif available_features.get('requires_vapor_return', 0) == 1:
                return 1  # Vapor return = discharge = 1
            else:
                return 0  # Default loading = 0
        
        # Map hazard classifications to numeric product encoding
        # (0=minerals, 1=benzene, 2=propylene_oxide based on historical data)
        elif model_feature == 'product_type':
            if available_features.get('product_hazard_highly_hazardous', 0) == 1:
                return 2  # Highly hazardous = propylene oxide = 2
            elif available_features.get('product_hazard_hazardous', 0) == 1:
                return 1  # Hazardous = benzene = 1
            else:
                return 0  # Standard = minerals = 0
        
        # Encode vessel characteristics for vessel_name (first visit impact)
        elif model_feature == 'vessel_name':
            # Use DWT as a proxy for vessel name (larger vessels = higher values)
            # Add first visit bonus
            base_value = available_features.get('dwt', 25000) / 1000  # Scale to reasonable range
            if available_features.get('is_first_visit', 0) == 1:
                return base_value + 100  # First visit adds to complexity
            else:
                return base_value
        
        # Encode customer characteristics based on operation type
        elif model_feature == 'customer_name':
            # Encode different customer types numerically
            if available_features.get('product_hazard_highly_hazardous', 0) == 1:
                return 3  # Specialty chemicals = 3
            elif available_features.get('requires_vapor_return', 0) == 1:
                return 2  # Hydrocarbon company = 2
            elif available_features.get('vessel_type_tanker', 0) == 0:  # Barge
                return 1  # Inland transport = 1
            else:
                return 0  # Standard chemical = 0
        
        # Encode jetty location as numeric value
        elif model_feature == 'location':
            jetty_id = available_features.get('jetty_id', None)
            max_flow_rate = available_features.get('max_flow_rate', 0)
            
            if jetty_id:
                # Use jetty ID number directly
                try:
                    return int(str(jetty_id).replace('J', '').replace('jetty', ''))
                except:
                    return 1
            elif max_flow_rate >= 4000:
                return 5  # High flow rate = Jetty 5
            elif max_flow_rate >= 2000:
                return 1  # Medium flow rate = Jetty 1
            else:
                return 2  # Lower flow rate = Jetty 2
        
        return None
    
    def _get_contextual_default(self, feature_name: str, available_features: Dict[str, Any]) -> Any:
        """
        Get contextual default values based on available features (numeric encodings)
        
        Args:
            feature_name: Name of the feature needing a default
            available_features: Available features to provide context
            
        Returns:
            Contextual default value (numeric)
        """
        # Use vessel characteristics to provide intelligent defaults
        vessel_dwt = available_features.get('dwt', 25000)
        cargo_volume = available_features.get('cargo_volume', 5000)
        is_tanker = available_features.get('vessel_type_tanker', 1) == 1
        
        # All defaults must be numeric for ML models
        defaults = {
            'product_quantity': float(cargo_volume),
            'vessel_type': 1 if is_tanker else 0,  # 1=TANKER, 0=BARGE
            'operation_type': 0 if cargo_volume > 10000 else 1,  # 0=loading, 1=discharge
            'product_type': 0 if vessel_dwt > 50000 else 1,  # 0=minerals, 1=chemicals
            'vessel_name': vessel_dwt / 1000,  # Use scaled DWT as vessel identifier
            'customer_name': 0,  # Default customer type
            'location': 1  # Default to jetty 1
        }
        
        return defaults.get(feature_name, 0.0)
    
    def _get_prediction_confidence(self, model_name: str, features: Dict[str, Any]) -> float:
        """Get confidence score for prediction (if model supports it)"""
        # Implement based on your model's confidence estimation
        return 0.8
    
    def _fallback_prediction(self, model_name: str, features: VesselFeatures) -> float:
        """Fallback predictions based on business rules when ML models unavailable (returns minutes to match model assumption)"""
        if model_name == 'pre_pump':
            # Business rule: larger vessels need more setup time (in minutes)
            base_time = 120.0 if features.dwt < 10000 else 240.0  # 2-4 hours in minutes
            if features.product_hazard_level == 'highly_hazardous':
                base_time *= 1.5
            return base_time
        
        elif model_name == 'pump':
            # Business rule: cargo volume / flow rate (result in minutes)
            if features.max_flow_rate and features.max_flow_rate > 0:
                # Calculate pump time in hours, then convert to minutes
                pump_hours = features.cargo_volume / features.max_flow_rate
                pump_minutes = pump_hours * 60.0
                
                # Cap at reasonable maximum (24 hours = 1440 minutes)
                max_pump_minutes = 24 * 60  # 24 hours
                pump_minutes = min(pump_minutes, max_pump_minutes)
                
                logger.debug(f"Pump calculation: {features.cargo_volume}m³ ÷ {features.max_flow_rate}m³/h = {pump_hours:.1f}h = {pump_minutes:.0f}min")
                return pump_minutes
            return 480.0  # Default 8 hours in minutes
        
        elif model_name == 'post_pump':
            # Business rule: smaller for routine operations (in minutes)
            base_time = 60.0  # 1 hour in minutes
            if features.requires_nitrogen_purge:
                base_time += 30.0  # +0.5 hours
            return base_time
        
        elif model_name == 'terminal':
            # Sum of other components + buffer (in minutes)
            pre = self._fallback_prediction('pre_pump', features)
            pump = self._fallback_prediction('pump', features)
            post = self._fallback_prediction('post_pump', features)
            return pre + pump + post + 60.0  # Buffer time (1 hour in minutes)
        
        return 60.0  # Default 1 hour in minutes
    
    def _classify_product_hazard(self, products: List[str]) -> str:
        """Classify products by hazard level based on business rules and historical data"""
        # Based on historical data products and business rules from evos-terminal-business-rules.md
        highly_hazardous = {
            'propylene_oxide', 'propyleenoxide',  # Historical data format
            'acrylonitrile', 
            'butane'
        }
        hazardous = {
            'benzene', 
            'chemicals',
            'naphta', 'nafta'  # Historical data format - hydrocarbons
        }
        
        for product in products:
            if product.lower() in highly_hazardous:
                return 'highly_hazardous'
        
        for product in products:
            if product.lower() in hazardous:
                return 'hazardous'
        
        return 'standard'
    
    def _requires_vapor_return(self, products: List[str]) -> bool:
        """Check if products require vapor return based on business rules"""
        vapor_return_products = {
            'hydrocarbons', 'benzene', 'acrylonitrile', 'propylene_oxide',
            'naphta', 'nafta', 'propyleenoxide'  # Historical data formats
        }
        return any(p.lower() in vapor_return_products for p in products)
    
    def _requires_nitrogen_purge(self, products: List[str]) -> bool:
        """Check if products require nitrogen purging"""
        nitrogen_products = {
            'chemicals', 'propylene_oxide', 'propyleenoxide'  # Historical data format
        }
        return any(p.lower() in nitrogen_products for p in products)
    
    def _get_season(self) -> str:
        """Get current season (simplified)"""
        month = datetime.now(timezone.utc).month
        if month in [12, 1, 2]:
            return 'winter'
        elif month in [3, 4, 5]:
            return 'spring'
        elif month in [6, 7, 8]:
            return 'summer'
        else:
            return 'autumn'
    
    def _check_jetty_product_compatibility(self, products: List[str], jetty: Jetty) -> bool:
        """Check if jetty can handle the vessel's products"""
        # This would implement the product-jetty compatibility matrix from business rules
        # For now, simplified implementation
        return True
    
    def _find_compatible_jetties(self, features: VesselFeatures, terminal: Terminal) -> List[Jetty]:
        """Find jetties compatible with vessel based on business rules"""
        compatible = []
        
        for jetty in terminal.jetties:
            # Check physical compatibility
            if (features.dwt <= jetty.max_deadweight and
                features.loa <= jetty.max_length and
                features.draft <= jetty.max_draft):
                
                # Check product compatibility
                if self._check_jetty_product_compatibility(features.product_types, jetty):
                    compatible.append(jetty)
        
        return compatible
    
    def _select_best_jetty(self, compatible_jetties: List[Jetty], features: VesselFeatures) -> Jetty:
        """Select the best jetty from compatible options"""
        if not compatible_jetties:
            raise ValueError("No compatible jetties found")
        
        # Simple selection logic - could be enhanced with ML
        # Prefer jetties with higher flow rates for faster operations
        return max(compatible_jetties, key=lambda j: j.max_flow_rate)
