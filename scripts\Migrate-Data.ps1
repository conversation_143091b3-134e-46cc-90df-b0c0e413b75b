# Migrate Data from Old PostgreSQL to New PostgreSQL
# Usage: .\scripts\Migrate-Data.ps1

param(
    [string]$SourceContainer = "postgres-1",
    [string]$TargetContainer = "jetty-postgres",
    [string]$Database = "planner",
    [string]$User = "postgres",
    [string]$Password = "XM/8KBtBmMJcP2tmjvjNJx0KDtFPLMqU"
)

$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupFile = "backups\migration_from_old_$timestamp.sql"

Write-Host "🔄 Starting data migration..." -ForegroundColor Green
Write-Host "From: $SourceContainer" -ForegroundColor Yellow
Write-Host "To: $TargetContainer" -ForegroundColor Yellow
Write-Host ""

try {
    # Ensure backups directory exists
    New-Item -ItemType Directory -Force -Path "backups" | Out-Null

    Write-Host "📦 Creating backup from source database..." -ForegroundColor Yellow
    
    # Create backup from source
    $env:PGPASSWORD = $Password
    & docker exec $SourceContainer pg_dump -h localhost -U $User -d $Database --clean --if-exists --format=plain > $backupFile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Backup created: $backupFile" -ForegroundColor Green
        
        # Check backup size
        $backupSize = (Get-Item $backupFile).Length
        Write-Host "📊 Backup size: $([math]::Round($backupSize / 1KB, 2)) KB" -ForegroundColor Cyan
        
        if ($backupSize -gt 0) {
            Write-Host "📥 Importing to target database..." -ForegroundColor Yellow
            
            # Import to target database
            Get-Content $backupFile | docker exec -i $TargetContainer psql -h localhost -U $User -d $Database
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Migration completed successfully!" -ForegroundColor Green
                Write-Host ""
                
                # Verify migration
                Write-Host "🔍 Verifying migration..." -ForegroundColor Yellow
                docker exec $TargetContainer psql -U $User -d $Database -c "SELECT COUNT(*) as assignments FROM assignments; SELECT COUNT(*) as vessels FROM vessels; SELECT COUNT(*) as jetties FROM jetties;"
                
            } else {
                Write-Host "❌ Failed to import to target database" -ForegroundColor Red
            }
        } else {
            Write-Host "⚠️  Backup file is empty - source database may be empty" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Failed to create backup from source database" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Migration failed: $_" -ForegroundColor Red
} finally {
    # Clear password from environment
    Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "📁 Backup file preserved at: $backupFile" -ForegroundColor Cyan




