"""
Lock Information Service

Integration with Dutch lock systems including RWS and BGV APIs for real-time
lock status, planning, and scheduling information.
"""

import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Tuple
import aiohttp
import json
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class LockStatus(Enum):
    """Lock operational status"""
    OPEN = "open"
    CLOSED = "closed"
    SCHEDULED = "scheduled"
    MAINTENANCE = "maintenance"
    EMERGENCY_CLOSED = "emergency_closed"


class LockDirection(Enum):
    """Lock passage direction"""
    SEAWARD = "seaward"  # From canal to sea
    LANDWARD = "landward"  # From sea to canal
    BOTH = "both"  # Both directions


@dataclass
class LockRestriction:
    """Lock passage restriction"""
    type: str  # "length", "beam", "draft", "air_draft", "tonnage"
    value: float
    unit: str
    description: str


@dataclass
class LockSchedule:
    """Lock operation schedule"""
    lock_name: str
    opening_time: datetime
    closing_time: Optional[datetime]
    direction: LockDirection
    available_chambers: int
    restrictions: List[LockRestriction] = field(default_factory=list)
    notes: str = ""


@dataclass
class LockInfo:
    """Complete lock information"""
    name: str
    code: str
    location: Tuple[float, float]  # (latitude, longitude)
    status: LockStatus
    chambers: int
    max_length: float  # meters
    max_beam: float   # meters
    max_draft: float  # meters
    max_air_draft: Optional[float] = None  # meters
    current_schedule: List[LockSchedule] = field(default_factory=list)
    next_opening: Optional[datetime] = None
    restrictions: List[LockRestriction] = field(default_factory=list)
    contact_info: Dict[str, str] = field(default_factory=dict)


class LockService:
    class LockNotFoundError(Exception):
        pass

    class ServiceUnavailableError(Exception):
        pass

    """Service for fetching lock information from various Dutch APIs"""
    
    def __init__(self):
        """Initialize the lock service with API configurations"""
        # RWS (Rijkswaterstaat) Open Geo WFS for locks (public, no key)
        self.wfs_url = "https://geo.rijkswaterstaat.nl/services/ogc/gdr/fis_vnds/ows"
        self.wfs_layer = "fis_vnds:sluis_v"
        # Simple in-memory cache for WFS features
        self._wfs_loaded = False
        self._wfs_last_loaded = None
        self._wfs_ttl_seconds = 6 * 60 * 60  # 6 hours
        
        # Keep legacy endpoints as references (unused without BGV/private access)
        self.rws_base_url = "https://geo.rijkswaterstaat.nl"
        self.vaarweg_info_url = "https://vaarweginformatie.nl"
        self.bgv_base_url = "https://api.blauwegolf.nl"
        self.bgv_api_key = None
        self.sluisplanning_url = "https://sluisplanning.rijkswaterstaat.nl"
        
        # Aliases to resolve common short codes to name tokens
        self._alias_tokens: Dict[str, List[str]] = {
            "TNZN": ["TERNEUZEN"],
            "TERNEUZEN": ["TERNEUZEN"],
            "HANS": ["HANSWEERT"],
            "HANSWEERT": ["HANSWEERT"],
            "KREK": ["KREEKRAK"],
            "KREEKRAK": ["KREEKRAK"]
        }
        
        # Key locks for Westerschelde and surrounding areas
        self.locks = {
            "TERNEUZEN": LockInfo(
                name="Nieuwe Sluis Terneuzen",
                code="TNZN",
                location=(51.3294, 3.8091),
                status=LockStatus.OPEN,
                chambers=1,
                max_length=366.0,
                max_beam=45.0,
                max_draft=12.5,  # Updated for 2025 - now allows 12.5m at any tide
                max_air_draft=68.0,
                contact_info={
                    "phone": "+31 (0)115 696 911",
                    "email": "<EMAIL>",
                    "vhf": "Channel 18"
                }
            ),
            "HANSWEERT": LockInfo(
                name="Schutsluis Hansweert",
                code="HANS", 
                location=(51.4458, 4.0094),
                status=LockStatus.OPEN,
                chambers=1,
                max_length=125.0,
                max_beam=16.8,
                max_draft=4.2,
                contact_info={
                    "phone": "+31 (0)113 381 324",
                    "vhf": "Channel 20"
                }
            ),
            "KREEKRAK": LockInfo(
                name="Kreekraksluizen",
                code="KREK",
                location=(51.4294, 4.2847),
                status=LockStatus.OPEN,
                chambers=2,
                max_length=280.0,
                max_beam=32.0,
                max_draft=12.0,
                contact_info={
                    "phone": "+31 (0)167 581 200",
                    "vhf": "Channel 22"
                }
            )
        }
        
        logger.info("Lock service initialized with RWS WFS integration (no API key)")
    
    def _resolve_lock(self, identifier: str) -> Optional[LockInfo]:
        """Resolve a lock by short code (TNZN/HANS/KREK) or by dictionary key/name (TERNEUZEN/...)."""
        if not identifier:
            return None
        ident = identifier.strip().upper()
        # Try by short code first
        for _, lock_data in self.locks.items():
            if lock_data.code.upper() == ident:
                return lock_data
        # Try by dict key (full name keys used in self.locks)
        if ident in self.locks:
            return self.locks[ident]
        # Try by full display name match
        for _, lock_data in self.locks.items():
            if lock_data.name.strip().upper() == ident:
                return lock_data
        return None

    async def get_lock_status(self, lock_code: str) -> Optional[LockInfo]:
        """
        Get current status of a lock.
        
        Args:
            lock_code: Lock identification code
            
        Returns:
            LockInfo object or None if unavailable
        """
        # Ensure WFS data is loaded
        await self._ensure_wfs_loaded()
        # Resolve by code or name
        resolved = self._resolve_lock(lock_code)
        if resolved is None:
            available_codes = [lock.code for lock in self.locks.values()]
            logger.warning(f"Unknown lock identifier: {lock_code}. Available codes: {available_codes}")
            raise LockService.LockNotFoundError(f"Lock {lock_code} not found. Available codes: {available_codes}")
        
        # Create a copy to avoid modifying the original
        from dataclasses import replace
        lock_info = replace(resolved)
        
        try:
            # With WFS only, we return static attributes (no live status)
            # Keep status as-is (default OPEN) and return
            return lock_info
            
        except LockService.LockNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error fetching lock status for {resolved.code}: {e}")
            # Return proper error instead of mock data
            raise LockService.ServiceUnavailableError(
                f"Unable to fetch real-time status for lock {resolved.code}. Error: {str(e)}"
            )
    
    async def _fetch_rws_lock_data(self, lock_code: str) -> Optional[Dict[str, Any]]:
        """Deprecated: live lock status via private APIs not used. Always returns None."""
        return None
    
    async def _fetch_bgv_lock_data(self, lock_code: str) -> Optional[Dict[str, Any]]:
        """Fetch lock data from BGV API"""
        if not self.bgv_api_key:
            return None
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {"Authorization": f"Bearer {self.bgv_api_key}"}
                url = f"{self.bgv_base_url}/locks/{lock_code}/status"
                
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"BGV API returned status {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error fetching BGV lock data: {e}")
            return None
    

    
    def can_vessel_pass(self, lock_code: str, vessel_length: float, 
                       vessel_beam: float, vessel_draft: float,
                       vessel_air_draft: Optional[float] = None) -> Tuple[bool, List[str]]:
        """
        Check if a vessel can pass through a lock.
        
        Args:
            lock_code: Lock identification code
            vessel_length: Vessel length in meters
            vessel_beam: Vessel beam in meters  
            vessel_draft: Vessel draft in meters
            vessel_air_draft: Vessel air draft in meters (optional)
            
        Returns:
            Tuple of (can_pass, list_of_restrictions)
        """
        # Resolve lock by code, key or name (supports TNZN/HANS/KREK and WFS names)
        lock_info = self._resolve_lock(lock_code)
        
        if lock_info is None:
            return False, [f"Unknown lock: {lock_code}"]
        restrictions = []
        
        # Check dimensional constraints (ignore missing/zero limits from WFS)
        if lock_info.max_length and lock_info.max_length > 0 and vessel_length > lock_info.max_length:
            restrictions.append(f"Length {vessel_length}m exceeds maximum {lock_info.max_length}m")
        
        if lock_info.max_beam and lock_info.max_beam > 0 and vessel_beam > lock_info.max_beam:
            restrictions.append(f"Beam {vessel_beam}m exceeds maximum {lock_info.max_beam}m")
        
        if lock_info.max_draft and lock_info.max_draft > 0 and vessel_draft > lock_info.max_draft:
            restrictions.append(f"Draft {vessel_draft}m exceeds maximum {lock_info.max_draft}m")
        
        if vessel_air_draft and lock_info.max_air_draft and lock_info.max_air_draft > 0 and vessel_air_draft > lock_info.max_air_draft:
            restrictions.append(f"Air draft {vessel_air_draft}m exceeds maximum {lock_info.max_air_draft}m")
        
        can_pass = len(restrictions) == 0
        return can_pass, restrictions
    
    async def get_lock_schedule(self, lock_code: str, hours_ahead: int = 24) -> List[LockSchedule]:
        """
        Get lock operation schedule for the next specified hours.
        
        Args:
            lock_code: Lock identification code
            hours_ahead: Number of hours to look ahead
            
        Returns:
            List of LockSchedule objects
        """
        try:
            # In a real implementation, this would fetch from RWS Sluisplanning
            # For now, generate mock schedule
            return await self._get_mock_schedule(lock_code, hours_ahead)
            
        except Exception as e:
            logger.error(f"Error fetching lock schedule for {lock_code}: {e}")
            return []
    
    async def _get_mock_schedule(self, lock_code: str, hours_ahead: int) -> List[LockSchedule]:
        """Generate mock lock schedule for development"""
        # Find lock by code
        lock_info = None
        for lock_name, lock_data in self.locks.items():
            if lock_data.code == lock_code:
                lock_info = lock_data
                break
        
        if lock_info is None:
            return []
        schedule = []
        current_time = datetime.now(timezone.utc)
        
        # Generate schedule entries every 2-4 hours
        for i in range(0, hours_ahead, 3):
            opening_time = current_time + timedelta(hours=i)
            
            # Skip nighttime for smaller locks
            if lock_code != "TNZN" and (opening_time.hour < 6 or opening_time.hour > 22):
                continue
            
            schedule.append(LockSchedule(
                lock_name=lock_info.name,
                opening_time=opening_time,
                closing_time=opening_time + timedelta(hours=1),
                direction=LockDirection.BOTH,
                available_chambers=lock_info.chambers,
                notes="Regular operation"
            ))
        
        return schedule
    
    def get_available_locks(self) -> List[LockInfo]:
        """
        Get list of all available locks.
        
        Returns:
            List of LockInfo objects
        """
        # Return cached list (ensure loaded synchronously is not possible here; caller should ensure)
        return list(self.locks.values())
    
    def set_bgv_api_key(self, api_key: str):
        """
        Set BGV API key for enhanced lock data.
        
        Args:
            api_key: BGV API key
        """
        self.bgv_api_key = api_key
        logger.info("BGV API key configured, but WFS-only mode is active (no live status)")
    
    async def get_route_locks(self, start_location: Tuple[float, float], 
                            end_location: Tuple[float, float]) -> List[str]:
        """
        Determine which locks are on a route between two points.
        
        Args:
            start_location: (latitude, longitude) of start point
            end_location: (latitude, longitude) of end point
            
        Returns:
            List of lock codes that may be encountered on the route
        """
        # Simplified route analysis - in reality would use maritime routing
        route_locks = []
        
        start_lat, start_lon = start_location
        end_lat, end_lon = end_location
        
        # Check if route goes through major waterways requiring locks
        if start_lon < 3.5 and end_lon > 4.0:  # West to East, likely through Westerschelde
            route_locks.extend(["TERNEUZEN", "HANSWEERT", "KREEKRAK"])
        elif start_lat > 51.4 and end_lat < 51.3:  # North to South
            route_locks.append("TERNEUZEN")
        
        return route_locks

    async def _ensure_wfs_loaded(self):
        """Load WFS features into self.locks if cache is empty or expired."""
        from datetime import datetime as _dt
        import time as _time
        now_ts = _time.time()
        if self._wfs_loaded and self._wfs_last_loaded and (now_ts - self._wfs_last_loaded) < self._wfs_ttl_seconds:
            return
        try:
            async with aiohttp.ClientSession() as session:
                params = {
                    "service": "WFS",
                    "version": "2.0.0",
                    "request": "GetFeature",
                    "typeName": self.wfs_layer,
                    "outputFormat": "application/json"
                }
                async with session.get(self.wfs_url, params=params, timeout=aiohttp.ClientTimeout(total=20)) as resp:
                    if resp.status != 200:
                        logger.error(f"WFS GetFeature failed: HTTP {resp.status}")
                        return
                    data = await resp.json()
                    features = data.get("features", [])
                    loaded = 0
                    # Rebuild locks dict from WFS
                    self.locks = {}
                    for feat in features:
                        try:
                            lock_info = self._create_lockinfo_from_feature(feat)
                            # Key by uppercase display name; also allow code lookup later
                            self.locks[lock_info.name.strip().upper()] = lock_info
                            loaded += 1
                        except Exception as e:
                            logger.warning(f"Skipping WFS feature due to error: {e}")
                    self._wfs_loaded = True
                    self._wfs_last_loaded = now_ts
                    logger.info(f"Loaded {loaded} locks from RWS WFS")
        except Exception as e:
            logger.error(f"Error loading locks from WFS: {e}")

    def _create_lockinfo_from_feature(self, feature: Dict[str, Any]) -> LockInfo:
        """Map a WFS GeoJSON feature (sluis_v) to LockInfo."""
        props = feature.get("properties", {})
        geom = feature.get("geometry", {})
        name = props.get("name") or "Unknown Lock"
        code = str(props.get("vincode") or props.get("id") or name)
        # Compute a simple centroid for polygon or use first coordinate
        lat, lon = 0.0, 0.0
        try:
            coords = geom.get("coordinates")
            if geom.get("type") == "Polygon" and coords and len(coords) > 0 and len(coords[0]) > 0:
                ring = coords[0]
                xs = [pt[0] for pt in ring]
                ys = [pt[1] for pt in ring]
                lon = sum(xs) / len(xs)
                lat = sum(ys) / len(ys)
            elif geom.get("type") == "Point" and coords and len(coords) == 2:
                lon, lat = coords
        except Exception:
            pass
        # Map dimensions and other attributes if available
        chambers = props.get("numberofchambers") or 1
        max_length = float(props.get("length") or 0.0)
        max_beam = float(props.get("width") or 0.0)
        max_draft = float(props.get("maximumrise") or 0.0)  # Not exact, but best available
        phone = props.get("phonenumber")
        contact = {"phone": phone} if phone else {}
        return LockInfo(
            name=name,
            code=code,
            location=(lat, lon),
            status=LockStatus.OPEN,
            chambers=int(chambers) if isinstance(chambers, (int, float)) else 1,
            max_length=max_length,
            max_beam=max_beam,
            max_draft=max_draft,
            contact_info=contact
        )

    def _resolve_lock(self, identifier: str) -> Optional[LockInfo]:
        """Resolve a lock by code, key, or name, using WFS-loaded data first."""
        if not identifier:
            return None
        ident = identifier.strip().upper()
        # Try by code match (vincode/id)
        for _, lock_data in self.locks.items():
            if lock_data.code and str(lock_data.code).upper() == ident:
                return lock_data
        # Try by dict key (name upper)
        if ident in self.locks:
            return self.locks[ident]
        # Try by full display name match (case-insensitive)
        for _, lock_data in self.locks.items():
            if lock_data.name.strip().upper() == ident:
                return lock_data
        # Try startswith match on name
        for _, lock_data in self.locks.items():
            if lock_data.name.strip().upper().startswith(ident):
                return lock_data
        # Try alias tokens (map short codes to likely name tokens)
        tokens = self._alias_tokens.get(ident)
        if tokens:
            best_match = None
            for _, lock_data in self.locks.items():
                name_up = lock_data.name.strip().upper()
                if any(tok in name_up for tok in tokens):
                    if best_match is None or len(lock_data.name) > len(best_match.name):
                        best_match = lock_data
            if best_match:
                return best_match
        return None
