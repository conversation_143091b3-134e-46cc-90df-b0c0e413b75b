"""
Transaction Service - Database Transaction Management

Provides atomic operations for complex vessel and assignment operations
using database transactions to ensure data consistency.
"""

import logging
from contextlib import contextmanager
from typing import Dict, Any, Optional
from datetime import datetime

from src.database import Database
from src.repositories.vessel_repository import VesselRepository
from src.repositories.assignment_repository import AssignmentRepository

logger = logging.getLogger(__name__)


class TransactionService:
    """
    Service for managing database transactions and atomic operations.
    
    This service ensures that complex operations involving multiple
    database changes are performed atomically.
    """
    
    def __init__(self, database: Database, vessel_repo: VesselRepository, assignment_repo: AssignmentRepository):
        self.db = database
        self.vessel_repo = vessel_repo
        self.assignment_repo = assignment_repo
    
    @contextmanager
    def transaction(self):
        """
        Context manager for database transactions.
        
        Usage:
            with transaction_service.transaction():
                # Perform multiple database operations
                # All will be committed together or rolled back on error
        """
        session = self.db.get_session()
        try:
            yield session
            session.commit()
            logger.debug("Transaction committed successfully")
        except Exception as e:
            session.rollback()
            logger.error(f"Transaction rolled back due to error: {e}")
            raise
        finally:
            session.close()
    
    def schedule_vessel_atomic(self, vessel_id: str, assignment_data: Dict[str, Any], terminal_id: Optional[str] = None) -> int:
        """
        Atomically move vessel from nomination to scheduled assignment.
        
        This ensures that:
        1. Assignment is created
        2. Nomination status is updated (if exists)
        3. All changes are logged
        
        Args:
            vessel_id: Vessel ID to schedule
            assignment_data: Assignment details
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            Assignment ID of created assignment
            
        Raises:
            Exception: If any part of the operation fails
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            raise ValueError("No active terminal found")
        
        try:
            with self.transaction() as session:
                # Create assignment
                assignment_id = self.assignment_repo.create_assignment(assignment_data, terminal_id)
                
                # Try to update nomination status if it exists
                try:
                    nominations = self.db.get_nominations(terminal_id) or []
                    for nomination in nominations:
                        if str(nomination.get('runtime_vessel_id')) == str(vessel_id):
                            # Mark nomination as scheduled
                            # Note: This would require extending the database to support nomination status updates
                            logger.info(f"Vessel {vessel_id} scheduled from nomination {nomination.get('id')}")
                            break
                except Exception as e:
                    logger.warning(f"Could not update nomination status for vessel {vessel_id}: {e}")
                
                # Log the scheduling operation
                try:
                    self.db.log_assignment_change(
                        assignment_id=assignment_id,
                        old_start_time=None,
                        old_end_time=None,
                        new_start_time=assignment_data.get('start_time'),
                        new_end_time=assignment_data.get('end_time'),
                        reason="Vessel scheduled atomically",
                        vessel_id=vessel_id,
                        vessel_name=assignment_data.get('vessel_name'),
                        jetty_name=assignment_data.get('jetty_name'),
                        changed_by="system",
                        terminal_id=terminal_id
                    )
                except Exception as log_e:
                    logger.warning(f"Failed to log atomic scheduling: {log_e}")
                
                logger.info(f"Atomically scheduled vessel {vessel_id} to assignment {assignment_id}")
                return assignment_id
                
        except Exception as e:
            logger.error(f"Error in atomic vessel scheduling: {e}")
            raise
    
    def unschedule_vessel_atomic(self, assignment_id: int, terminal_id: Optional[str] = None) -> bool:
        """
        Atomically move vessel back to available nominations by cancelling assignment.
        
        This ensures that:
        1. Assignment is cancelled
        2. Nomination is reactivated (if exists)
        3. All changes are logged
        
        Args:
            assignment_id: Assignment ID to unschedule
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            True if successfully unscheduled
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            return False
        
        try:
            with self.transaction() as session:
                # Get assignment details before cancellation
                assignment = self.assignment_repo.get_assignment_by_id(assignment_id, terminal_id)
                if not assignment:
                    raise ValueError(f"Assignment {assignment_id} not found")
                
                vessel_id = assignment.get('vessel_id')
                
                # Cancel the assignment
                success = self.assignment_repo.update_assignment(assignment_id, {'status': 'CANCELLED'})
                if not success:
                    raise Exception(f"Failed to cancel assignment {assignment_id}")
                
                # Try to reactivate nomination if it exists
                try:
                    nominations = self.db.get_nominations(terminal_id) or []
                    for nomination in nominations:
                        if str(nomination.get('runtime_vessel_id')) == str(vessel_id):
                            # Reactivate nomination
                            logger.info(f"Reactivated nomination for vessel {vessel_id}")
                            break
                except Exception as e:
                    logger.warning(f"Could not reactivate nomination for vessel {vessel_id}: {e}")
                
                # Log the unscheduling operation
                try:
                    self.db.log_assignment_change(
                        assignment_id=assignment_id,
                        old_start_time=assignment.get('start_time'),
                        old_end_time=assignment.get('end_time'),
                        new_start_time=None,
                        new_end_time=None,
                        reason="Vessel unscheduled atomically",
                        vessel_id=vessel_id,
                        vessel_name=assignment.get('vessel_name'),
                        jetty_name=assignment.get('jetty_name'),
                        changed_by="system",
                        terminal_id=terminal_id
                    )
                except Exception as log_e:
                    logger.warning(f"Failed to log atomic unscheduling: {log_e}")
                
                logger.info(f"Atomically unscheduled assignment {assignment_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error in atomic vessel unscheduling: {e}")
            return False
    
    def bulk_schedule_vessels_atomic(self, vessel_assignments: List[Dict[str, Any]], terminal_id: Optional[str] = None) -> List[int]:
        """
        Atomically schedule multiple vessels in a single transaction.
        
        This is useful for optimization results where multiple vessels
        need to be scheduled together.
        
        Args:
            vessel_assignments: List of vessel-assignment pairs
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            List of created assignment IDs
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            raise ValueError("No active terminal found")
        
        assignment_ids = []
        
        try:
            with self.transaction() as session:
                for vessel_assignment in vessel_assignments:
                    vessel_id = vessel_assignment.get('vessel_id')
                    assignment_data = vessel_assignment.get('assignment_data')
                    
                    if not vessel_id or not assignment_data:
                        logger.warning(f"Invalid vessel assignment data: {vessel_assignment}")
                        continue
                    
                    # Create assignment
                    assignment_id = self.assignment_repo.create_assignment(assignment_data, terminal_id)
                    assignment_ids.append(assignment_id)
                
                # Log bulk operation
                try:
                    self.db.log_assignment_change(
                        assignment_id=0,  # System operation
                        old_start_time=None,
                        old_end_time=None,
                        new_start_time=datetime.now().isoformat(),
                        new_end_time=None,
                        reason=f"Bulk atomic scheduling: {len(assignment_ids)} vessels scheduled",
                        vessel_id="SYSTEM",
                        vessel_name="Transaction Service",
                        jetty_name=None,
                        changed_by="system",
                        terminal_id=terminal_id
                    )
                except Exception as log_e:
                    logger.warning(f"Failed to log bulk scheduling: {log_e}")
                
                logger.info(f"Atomically scheduled {len(assignment_ids)} vessels")
                return assignment_ids
                
        except Exception as e:
            logger.error(f"Error in bulk atomic vessel scheduling: {e}")
            raise
    
    def replace_schedule_atomic(self, new_assignments: List[Dict[str, Any]], terminal_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Atomically replace entire schedule with new assignments.
        
        This is used by the optimization engine to replace the current
        schedule with an optimized one in a single transaction.
        
        Args:
            new_assignments: List of new assignment data
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            List of created assignment records with IDs
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            raise ValueError("No active terminal found")
        
        try:
            with self.transaction() as session:
                # Clear existing assignments
                self.assignment_repo.clear_assignments(terminal_id)
                
                # Create new assignments
                created_assignments = self.assignment_repo.replace_assignments(new_assignments, terminal_id)
                
                # Log the replacement
                try:
                    self.db.log_assignment_change(
                        assignment_id=0,  # System operation
                        old_start_time=None,
                        old_end_time=None,
                        new_start_time=datetime.now().isoformat(),
                        new_end_time=None,
                        reason=f"Atomic schedule replacement: {len(created_assignments)} assignments created",
                        vessel_id="SYSTEM",
                        vessel_name="Transaction Service",
                        jetty_name=None,
                        changed_by="system",
                        terminal_id=terminal_id
                    )
                except Exception as log_e:
                    logger.warning(f"Failed to log schedule replacement: {log_e}")
                
                logger.info(f"Atomically replaced schedule with {len(created_assignments)} assignments")
                return created_assignments
                
        except Exception as e:
            logger.error(f"Error in atomic schedule replacement: {e}")
            raise
    
    def delete_vessel_and_assignments_atomic(self, vessel_id: str, terminal_id: Optional[str] = None) -> bool:
        """
        Atomically delete a vessel and all its related assignments.
        
        This ensures referential integrity when removing vessels.
        
        Args:
            vessel_id: Vessel ID to delete
            terminal_id: Terminal ID (uses active terminal if None)
            
        Returns:
            True if successfully deleted
        """
        if terminal_id is None:
            terminal_id = self.db.get_active_terminal_id()
        
        if not terminal_id:
            return False
        
        try:
            with self.transaction() as session:
                # Get vessel assignments before deletion
                vessel_assignments = self.assignment_repo.get_assignments_by_vessel(vessel_id, terminal_id)
                
                # Delete all assignments for this vessel
                for assignment in vessel_assignments:
                    assignment_id = assignment.get('id')
                    if assignment_id:
                        self.assignment_repo.delete_assignment(assignment_id)
                
                # Delete the vessel itself
                vessel_deleted = self.vessel_repo.delete_vessel(vessel_id, terminal_id)
                
                if vessel_deleted:
                    # Log the deletion
                    try:
                        self.db.log_assignment_change(
                            assignment_id=0,
                            old_start_time=None,
                            old_end_time=None,
                            new_start_time=None,
                            new_end_time=None,
                            reason=f"Atomic vessel deletion: vessel and {len(vessel_assignments)} assignments deleted",
                            vessel_id=vessel_id,
                            vessel_name="Deleted Vessel",
                            jetty_name=None,
                            changed_by="system",
                            terminal_id=terminal_id
                        )
                    except Exception as log_e:
                        logger.warning(f"Failed to log vessel deletion: {log_e}")
                    
                    logger.info(f"Atomically deleted vessel {vessel_id} and {len(vessel_assignments)} assignments")
                    return True
                else:
                    raise Exception(f"Failed to delete vessel {vessel_id}")
                
        except Exception as e:
            logger.error(f"Error in atomic vessel deletion: {e}")
            return False


