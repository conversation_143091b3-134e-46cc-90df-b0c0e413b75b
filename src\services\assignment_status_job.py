"""
Assignment Status Transition Job

This module implements automatic assignment status transitions based on time.
Provides the periodic job functionality as specified in assignment-status-job.md.
"""

import os
import logging
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from ..database import Database
from ..utils.status_utils import is_valid_assignment_transition

logger = logging.getLogger(__name__)


class AssignmentStatusJob:
    """
    Handles automatic status transitions for assignments based on time.
    
    Transitions:
    - To IN_PROGRESS when start_time <= now < end_time and status is SCHEDULED | APPROVED | PENDING_APPROVAL
    - To COMPLETED when end_time <= now and status is IN_PROGRESS | APPROVED | SCHEDULED
    """
    
    def __init__(self, database: Database):
        self.db = database
        self.batch_size = int(os.getenv('ASSIGNMENT_STATUS_CRON_BATCH_SIZE', '100'))
        self.lock_key = int(os.getenv('STATUS_CRON_LOCK_KEY', '123456789'))
        self.enabled = os.getenv('ENABLE_STATUS_CRON', 'true').lower() in ('true', '1', 'yes')
        
        # Job execution metrics
        self.last_run_time: Optional[datetime] = None
        self.last_run_duration: Optional[float] = None
        self.last_run_stats: Dict[str, int] = {
            'attempted': 0,
            'transitioned_to_in_progress': 0,
            'transitioned_to_completed': 0,
            'skipped_invalid': 0
        }
    
    async def run_transition_job(self) -> Dict[str, Any]:
        """
        Execute the assignment status transition job.
        
        Returns:
            Dict with job execution statistics
        """
        if not self.enabled:
            logger.debug("Assignment status cron job is disabled")
            return {'status': 'disabled'}
        
        start_time = datetime.now(timezone.utc)
        self.last_run_time = start_time
        
        # Initialize stats
        stats = {
            'attempted': 0,
            'transitioned_to_in_progress': 0,
            'transitioned_to_completed': 0,
            'skipped_invalid': 0,
            'errors': 0
        }
        
        # Check if we should use advisory locks (PostgreSQL only)
        using_postgres = self.db.is_using_postgres()
        lock_acquired = False
        
        try:
            # Acquire advisory lock if using PostgreSQL
            if using_postgres:
                lock_acquired = self.db.acquire_advisory_lock(self.lock_key)
                if not lock_acquired:
                    logger.info("Could not acquire advisory lock, skipping this run (another instance may be running)")
                    return {'status': 'skipped', 'reason': 'lock_not_acquired'}
            
            # Get current UTC time
            now = datetime.now(timezone.utc)
            
            # Process transitions to IN_PROGRESS
            stats.update(await self._transition_to_in_progress(now))
            
            # Process transitions to COMPLETED  
            completed_stats = await self._transition_to_completed(now)
            stats['attempted'] += completed_stats['attempted']
            stats['transitioned_to_completed'] += completed_stats['transitioned_to_completed']
            stats['skipped_invalid'] += completed_stats['skipped_invalid']
            stats['errors'] += completed_stats['errors']
            
        except Exception as e:
            logger.error(f"Error in assignment status transition job: {e}", exc_info=True)
            stats['errors'] += 1
        
        finally:
            # Release advisory lock if acquired
            if using_postgres and lock_acquired:
                self.db.release_advisory_lock(self.lock_key)
        
        # Calculate duration and store stats
        end_time = datetime.now(timezone.utc)
        self.last_run_duration = (end_time - start_time).total_seconds()
        self.last_run_stats = stats.copy()
        
        # Log summary
        logger.info(f"Assignment status transition job completed in {self.last_run_duration:.2f}s: "
                   f"{stats['transitioned_to_in_progress']} to IN_PROGRESS, "
                   f"{stats['transitioned_to_completed']} to COMPLETED, "
                   f"{stats['skipped_invalid']} skipped, {stats['errors']} errors")
        
        return {
            'status': 'completed',
            'duration_seconds': self.last_run_duration,
            'stats': stats
        }
    
    async def _transition_to_in_progress(self, now: datetime) -> Dict[str, int]:
        """Transition assignments to IN_PROGRESS status."""
        stats = {'attempted': 0, 'transitioned_to_in_progress': 0, 'skipped_invalid': 0, 'errors': 0}
        
        try:
            # Find assignments that should be IN_PROGRESS
            # WHERE status IN ('SCHEDULED','APPROVED','PENDING_APPROVAL') 
            # AND start_time <= now AND end_time > now
            candidates = self.db.find_assignments_for_status_transition(
                statuses=['SCHEDULED', 'APPROVED', 'PENDING_APPROVAL'],
                where_start_le=now,
                where_end_gt=now,
                limit=self.batch_size
            )
            
            for assignment in candidates:
                stats['attempted'] += 1
                try:
                    # Validate transition
                    current_status = assignment['status']
                    is_valid, message = is_valid_assignment_transition(current_status, 'IN_PROGRESS')
                    
                    if not is_valid:
                        logger.warning(f"Invalid transition for assignment {assignment['id']}: {message}")
                        stats['skipped_invalid'] += 1
                        continue
                    
                    # Update assignment status
                    update_success = self.db.update_assignment(assignment['id'], {'status': 'IN_PROGRESS'})
                    
                    if update_success:
                        # Log the change
                        self.db.log_assignment_change(
                            assignment_id=assignment['id'],
                            old_start_time=assignment['start_time'],
                            old_end_time=assignment['end_time'],
                            new_start_time=assignment['start_time'],
                            new_end_time=assignment['end_time'],
                            vessel_id=assignment['vessel_id'],
                            vessel_name=assignment['vessel_name'],
                            jetty_name=assignment['jetty_name'],
                            reason=f"Auto transition: {current_status} -> IN_PROGRESS",
                            changed_by='scheduler'
                        )
                        
                        stats['transitioned_to_in_progress'] += 1
                        logger.debug(f"Transitioned assignment {assignment['id']} from {current_status} to IN_PROGRESS")
                    else:
                        logger.warning(f"Failed to update assignment {assignment['id']} status")
                        stats['errors'] += 1
                        
                except Exception as e:
                    logger.error(f"Error processing assignment {assignment.get('id', 'unknown')}: {e}")
                    stats['errors'] += 1
        
        except Exception as e:
            logger.error(f"Error finding assignments for IN_PROGRESS transition: {e}")
            stats['errors'] += 1
        
        return stats
    
    async def _transition_to_completed(self, now: datetime) -> Dict[str, int]:
        """Transition assignments to COMPLETED status."""
        stats = {'attempted': 0, 'transitioned_to_completed': 0, 'skipped_invalid': 0, 'errors': 0}
        
        try:
            # Find assignments that should be COMPLETED
            # WHERE status IN ('IN_PROGRESS','APPROVED','SCHEDULED') AND end_time <= now
            candidates = self.db.find_assignments_for_status_transition(
                statuses=['IN_PROGRESS', 'APPROVED', 'SCHEDULED'],
                where_end_le=now,
                limit=self.batch_size
            )
            
            for assignment in candidates:
                stats['attempted'] += 1
                try:
                    # Validate transition
                    current_status = assignment['status']
                    is_valid, message = is_valid_assignment_transition(current_status, 'COMPLETED')
                    
                    if not is_valid:
                        logger.warning(f"Invalid transition for assignment {assignment['id']}: {message}")
                        stats['skipped_invalid'] += 1
                        continue
                    
                    # Update assignment status
                    update_success = self.db.update_assignment(assignment['id'], {'status': 'COMPLETED'})
                    
                    if update_success:
                        # Log the change
                        self.db.log_assignment_change(
                            assignment_id=assignment['id'],
                            old_start_time=assignment['start_time'],
                            old_end_time=assignment['end_time'],
                            new_start_time=assignment['start_time'],
                            new_end_time=assignment['end_time'],
                            vessel_id=assignment['vessel_id'],
                            vessel_name=assignment['vessel_name'],
                            jetty_name=assignment['jetty_name'],
                            reason=f"Auto transition: {current_status} -> COMPLETED",
                            changed_by='scheduler'
                        )
                        
                        stats['transitioned_to_completed'] += 1
                        logger.debug(f"Transitioned assignment {assignment['id']} from {current_status} to COMPLETED")
                    else:
                        logger.warning(f"Failed to update assignment {assignment['id']} status")
                        stats['errors'] += 1
                        
                except Exception as e:
                    logger.error(f"Error processing assignment {assignment.get('id', 'unknown')}: {e}")
                    stats['errors'] += 1
        
        except Exception as e:
            logger.error(f"Error finding assignments for COMPLETED transition: {e}")
            stats['errors'] += 1
        
        return stats
    
    def get_job_status(self) -> Dict[str, Any]:
        """Get current job status and metrics."""
        return {
            'enabled': self.enabled,
            'batch_size': self.batch_size,
            'lock_key': self.lock_key,
            'last_run_time': self.last_run_time.isoformat() if self.last_run_time else None,
            'last_run_duration_seconds': self.last_run_duration,
            'last_run_stats': self.last_run_stats
        }
