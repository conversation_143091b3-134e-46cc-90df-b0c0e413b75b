#!/usr/bin/env python3
"""
Jetty Planning Optimizer for Petrochemical Terminals

This application optimizes jetty planning for a petrochemical terminal using Google OR-Tools.
It considers constraints such as pump flow rates, floating roof tanks, surveyor availability,
weather conditions, and loading arm compatibility.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
from dotenv import load_dotenv
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from datetime import datetime, timedelta

# Add the project root directory to the path
sys.path.append(str(Path(__file__).parent.parent))

from src.database import Database
from src.models.terminal import Terminal
from src.models.vessel import Vessel, Barge
from src.models.product import Product
from src.optimization.scheduler import JettyScheduler
from src.llm_interface.claude_interface import ClaudeInterface
from src.api.fastapi_app import start_api_server, app as fastapi_app
from src.integration.aisstream_client import <PERSON><PERSON>treamClient
from src.integration.openmeteo_api import OpenMeteoApiClient
# Test data generation removed for production

# Setup logging
def configure_logging():
    """Configure logging based on environment (production or development)"""
    log_level = logging.INFO
    if os.getenv("DEBUG", "false").lower() == "true":
        log_level = logging.DEBUG

    if os.getenv("PRODUCTION", "false").lower() == "true":
        # Production logging setup
        handlers = [logging.StreamHandler()]
        log_dir = os.getenv("LOG_DIR", "logs")

        # Create logs directory if it doesn't exist
        os.makedirs(log_dir, exist_ok=True)

        # Add rotating file handler for production
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            os.path.join(log_dir, "jetty_optimizer.log"),
            maxBytes=10485760,  # 10MB
            backupCount=5
        )
        handlers.append(file_handler)
    else:
        # Development logging setup
        handlers = [
            logging.StreamHandler(),
            logging.FileHandler('jetty_optimizer.log')
        ]

    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=handlers
    )

configure_logging()
logger = logging.getLogger(__name__)

# Initialize database
db = Database()

def load_environment():
    """Load environment variables from .env file"""
    # Always load from .env when present (do not override already-set env)
    env_path = Path('.') / '.env'
    if env_path.exists():
        logger.info(f"Loading environment from {env_path}")
        load_dotenv(dotenv_path=env_path, override=False)

    # Check for required API keys
    is_test_mode = os.getenv('TEST_MODE', 'false').lower() == 'true'

    missing_keys = []
    for key in ['ANTHROPIC_API_KEY']:
        value = os.getenv(key)
        if value is None or value == "":
            missing_keys.append(key)
        elif value == "TEST" and not is_test_mode:
            logger.warning(f"{key} is set to TEST value but not in test mode")
        elif key == 'ANTHROPIC_API_KEY' and value != "TEST":
            logger.info(f"Using real {key} for Claude API")

    if missing_keys:
        if is_test_mode:
            logger.info(f"Missing API keys: {', '.join(missing_keys)}. Using test data.")
        else:
            logger.warning(f"Missing API keys: {', '.join(missing_keys)}")
            logger.warning("Some features may not work properly.")

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Jetty Planning Optimizer')
    parser.add_argument('--test', action='store_true', help='Use test data instead of real APIs')
    parser.add_argument('--api', action='store_true', help='Start the API server')
    parser.add_argument('--optimize', action='store_true', help='Run optimization with current data')
    parser.add_argument('--interactive', action='store_true', help='Start interactive LLM interface')
    parser.add_argument('--host', type=str, default=os.getenv('API_HOST', '127.0.0.1'),
                        help='Host to run the API server on')
    parser.add_argument('--port', type=int, default=int(os.getenv('API_PORT', '7000')),
                        help='Port to run the API server on')
    parser.add_argument('--debug', action='store_true', help='Run in debug mode')

    args = parser.parse_args()

    # Set environment variables based on args
    if args.test:
        os.environ['TEST_MODE'] = 'true'

    if args.debug:
        os.environ['DEBUG'] = 'true'

    return args

def main():
    """Main entry point of the application"""
    # Load environment variables
    load_environment()

    # Parse command line arguments
    args = parse_arguments()

    # In production mode, always default to running the API server
    if os.getenv("PRODUCTION", "false").lower() == "true" and not (args.api or args.optimize or args.interactive):
        logger.info("Running in production mode - defaulting to API server")
        args.api = True

    # Production mode: Data should come from database
    logger.info("Running in production mode - data will be loaded from database as needed")

    # Start API server if requested
    if args.api:
        logger.info(f"Starting API server on {args.host}:{args.port}...")
        is_debug = os.getenv("DEBUG", "false").lower() == "true"
        start_api_server(host=args.host, port=args.port, debug=is_debug)

    # Run optimization if requested
    if args.optimize:
        logger.info("Running optimization...")
        # Initialize clients for external APIs
        aisstream = AISStreamClient(api_key=os.getenv('AISSTREAM_API_KEY', 'TEST'))

        # Initialize weather API client
        weather_api = OpenMeteoApiClient()

        # Run optimization
        scheduler = JettyScheduler(terminal, vessels, weather_api)
        optimized_schedule = scheduler.optimize()

        logger.info(f"Optimization completed with objective value: {optimized_schedule.objective_value}")

        # Print a summary of the schedule
        print("Optimized Schedule:")
        for assignment in optimized_schedule.assignments:
            print(f"Jetty {assignment.jetty.id}: {assignment.vessel.name} - {assignment.start_time} to {assignment.end_time}")

    # Start interactive interface if requested
    if args.interactive:
        logger.info("Starting interactive LLM interface...")
        claude_interface = ClaudeInterface(api_key=os.getenv('ANTHROPIC_API_KEY', 'TEST'))
        claude_interface.start_interactive_session()

    # If no arguments are provided, show help
    if not any(var for name, var in vars(args).items() if name not in ('host', 'port', 'debug')):
        parser.print_help()

# Use the comprehensive app from fastapi_app.py instead of creating a basic one
app = fastapi_app

# All routes and configurations are already defined in fastapi_app.py
# No need to duplicate them here

@app.get("/schedule", response_class=HTMLResponse)
async def schedule_page(request: Request):
    # Get assignments from database
    assignments = db.get_assignments()
    return templates.TemplateResponse(
        "schedule.html",
        {
            "request": request,
            "assignments": assignments
        }
    )

@app.get("/schedule/add", response_class=HTMLResponse)
async def add_assignment_page(request: Request):
    # Get vessels and jetties from database
    vessels = db.get_vessels()
    jetties = db.get_jetties()
    return templates.TemplateResponse(
        "add_assignment.html",
        {
            "request": request,
            "vessels": vessels,
            "jetties": jetties
        }
    )

@app.get("/schedule/edit/{assignment_id}", response_class=HTMLResponse)
async def edit_assignment_page(request: Request, assignment_id: int):
    # Get assignment from database
    assignments = db.get_assignments()
    assignment = next((a for a in assignments if a['id'] == assignment_id), None)

    if not assignment:
        raise HTTPException(status_code=404, detail="Assignment not found")

    # Get vessels and jetties from database
    vessels = db.get_vessels()
    jetties = db.get_jetties()

    return templates.TemplateResponse(
        "add_assignment.html",
        {
            "request": request,
            "assignment": assignment,
            "vessels": vessels,
            "jetties": jetties
        }
    )

@app.post("/api/schedule/assignments")
async def create_assignment(assignment: dict):
    try:
        # Convert string dates to datetime objects
        assignment['start_time'] = datetime.fromisoformat(assignment['start_time'])
        assignment['end_time'] = datetime.fromisoformat(assignment['end_time'])

        # Add assignment to database
        assignment_id = db.add_assignment(assignment)
        return {"id": assignment_id, "message": "Assignment created successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.put("/api/schedule/assignments/{assignment_id}")
async def update_assignment(assignment_id: int, assignment: dict):
    try:
        # Convert string dates to datetime objects
        assignment['start_time'] = datetime.fromisoformat(assignment['start_time'])
        assignment['end_time'] = datetime.fromisoformat(assignment['end_time'])

        # Update assignment in database
        success = db.update_assignment(assignment_id, assignment)
        if not success:
            raise HTTPException(status_code=404, detail="Assignment not found")
        return {"message": "Assignment updated successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.delete("/api/schedule/assignments/{assignment_id}")
async def delete_assignment(assignment_id: int):
    try:
        # Delete assignment from database
        success = db.delete_assignment(assignment_id)
        if not success:
            raise HTTPException(status_code=404, detail="Assignment not found")
        return {"message": "Assignment deleted successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/schedule/assignments")
async def get_assignments():
    return db.get_assignments()

@app.get("/api/vessels")
async def get_vessels():
    return db.get_vessels()

@app.get("/api/jetties")
async def get_jetties():
    return db.get_jetties()

# Terminal page route removed - functionality not essential for production

@app.get("/terminals", response_class=HTMLResponse)
async def terminals_comparison_page(request: Request):
    try:
        logger.info("Loading terminals comparison page")
        return templates.TemplateResponse("terminals.html", {"request": request})
    except Exception as e:
        logger.error(f"Error loading terminals comparison page: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to load terminals comparison page")

@app.get("/api/terminal/tanks")
async def get_tanks():
    raise HTTPException(status_code=410, detail="Endpoint removed: tanks feature not in use")

@app.get("/api/terminal/pumps")
async def get_pumps():
    raise HTTPException(status_code=410, detail="Endpoint removed: pumps feature not in use")

@app.get("/api/terminal/surveyors")
async def get_surveyors():
    raise HTTPException(status_code=410, detail="Endpoint removed: surveyors feature not in use")

@app.put("/api/terminal/tanks/{tank_id}/level")
async def update_tank_level(tank_id: int, level: float):
    raise HTTPException(status_code=410, detail="Endpoint removed: tanks feature not in use")

@app.put("/api/terminal/pumps/{pump_id}/status")
async def update_pump_status(pump_id: int, status: str):
    raise HTTPException(status_code=410, detail="Endpoint removed: pumps feature not in use")

@app.put("/api/terminal/surveyors/{surveyor_id}/status")
async def update_surveyor_status(surveyor_id: int, status: str):
    raise HTTPException(status_code=410, detail="Endpoint removed: surveyors feature not in use")

@app.get("/terminal/tanks/{tank_id}", response_class=HTMLResponse)
async def tank_details(request: Request, tank_id: int):
    raise HTTPException(status_code=404, detail="Page not found")

@app.get("/terminal/pumps/{pump_id}", response_class=HTMLResponse)
async def pump_details(request: Request, pump_id: int):
    raise HTTPException(status_code=404, detail="Page not found")

@app.get("/terminal/surveyors/{surveyor_id}", response_class=HTMLResponse)
async def surveyor_details(request: Request, surveyor_id: int):
    raise HTTPException(status_code=404, detail="Page not found")

@app.get("/api/terminal/tanks/{tank_id}/history")
async def get_tank_history(tank_id: int):
    raise HTTPException(status_code=410, detail="Endpoint removed: tanks feature not in use")

@app.get("/api/terminal/pumps/{pump_id}/history")
async def get_pump_history(pump_id: int):
    raise HTTPException(status_code=410, detail="Endpoint removed: pumps feature not in use")

@app.get("/api/terminal/pumps/{pump_id}/maintenance")
async def get_pump_maintenance(pump_id: int):
    raise HTTPException(status_code=410, detail="Endpoint removed: pumps feature not in use")

@app.get("/api/terminal/surveyors/{surveyor_id}/history")
async def get_surveyor_history(surveyor_id: int):
    raise HTTPException(status_code=410, detail="Endpoint removed: surveyors feature not in use")

@app.get("/api/terminal/surveyors/{surveyor_id}/schedule")
async def get_surveyor_schedule(surveyor_id: int):
    raise HTTPException(status_code=410, detail="Endpoint removed: surveyors feature not in use")

if __name__ == "__main__":
    main()
