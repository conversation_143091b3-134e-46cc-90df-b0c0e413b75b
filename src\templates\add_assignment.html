{% extends "base.html" %}

{% block title %}Add Assignment - Terneuzen Terminal Jetty Planning{% endblock %}

{% block head %}
<style>
.assignment-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-section-title {
    color: #003b6f;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-section-title i {
    color: #2196f3;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #495057;
    font-weight: 500;
}

.form-group select,
.form-group input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.2s;
}

.form-group select:focus,
.form-group input:focus {
    border-color: #2196f3;
    outline: none;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.time-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.time-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.time-input input {
    width: 80px;
}

.time-input span {
    color: #6c757d;
}

.preview-section {
    background: #e3f2fd;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.preview-title {
    color: #003b6f;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.preview-title i {
    color: #2196f3;
}

.preview-content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.preview-item {
    background: white;
    padding: 1rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.preview-item-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.preview-item-value {
    color: #212529;
    font-weight: 500;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-cancel {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #ced4da;
}

.btn-cancel:hover {
    background: #e9ecef;
}

.btn-submit {
    background: #2196f3;
    color: white;
    border: none;
}

.btn-submit:hover {
    background: #1976d2;
}

.required-field::after {
    content: "*";
    color: #dc3545;
    margin-left: 4px;
}
</style>
{% endblock %}

{% block header %}<span id="page-title">Add Assignment</span>{% endblock %}

{% block user_actions %}
    <a href="/schedule" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i>
        Back to Schedule
    </a>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h3 id="form-title">Schedule New Assignment</h3>
    </div>
    <div class="card-body">
        <form id="add-assignment-form" class="assignment-form">
            <!-- Vessel and Jetty Selection -->
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-ship"></i>
                    Vessel and Jetty Selection
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="vessel-select" class="required-field">Vessel</label>
                        <select id="vessel-select" required>
                            <option value="">Select a vessel</option>
                            <!-- Vessels will be loaded here via JavaScript -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="jetty-select" class="required-field">Jetty</label>
                        <select id="jetty-select" required>
                            <option value="">Select a jetty</option>
                            <!-- Jetties will be loaded here via JavaScript -->
                        </select>
                    </div>
                </div>
            </div>

            <!-- Time Selection -->
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-clock"></i>
                    Time Selection
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="start-date" class="required-field">Start Date</label>
                        <input type="date" id="start-date" required>
                    </div>
                    <div class="form-group">
                        <label for="start-time" class="required-field">Start Time</label>
                        <input type="time" id="start-time" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="end-date" class="required-field">End Date</label>
                        <input type="date" id="end-date" required>
                    </div>
                    <div class="form-group">
                        <label for="end-time" class="required-field">End Time</label>
                        <input type="time" id="end-time" required>
                    </div>
                </div>
            </div>

            <!-- Reason for Change (only used on edit) -->
            <div class="form-section" id="reason-section" style="display:none;">
                <div class="form-section-title">
                    <i class="fas fa-comment-dots"></i>
                    Reason for Change
                </div>
                <div class="form-group">
                    <label for="change-reason" class="required-field">Reason</label>
                    <input type="text" id="change-reason" placeholder="Describe why this assignment is changed">
                </div>
            </div>

            <!-- Pre/Post Pump Times and ML Prediction -->
            <div class="form-section">
                <div class="form-section-title">
                    <i class="fas fa-pump"></i>
                    Pre/Post Pump Times
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Pre-Pump Time</label>
                        <div class="time-inputs">
                            <div class="time-input">
                                <input type="number" id="pre-pump-hours" min="0" max="24" value="7">
                                <span>hours</span>
                            </div>
                            <div class="time-input">
                                <input type="number" id="pre-pump-minutes" min="0" max="59" value="14">
                                <span>minutes</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Post-Pump Time</label>
                        <div class="time-inputs">
                            <div class="time-input">
                                <input type="number" id="post-pump-hours" min="0" max="24" value="4">
                                <span>hours</span>
                            </div>
                            <div class="time-input">
                                <input type="number" id="post-pump-minutes" min="0" max="59" value="19">
                                <span>minutes</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="ml-terminal-minutes">Predicted Terminal Time (minutes)</label>
                        <input type="number" id="ml-terminal-minutes" min="0" step="1" placeholder="auto">
                        <div style="color:#6c757d; font-size: 0.85rem; margin-top: 4px;">
                            If left empty, the ML predicted value will be used. Override to adjust.
                        </div>
                    </div>
                    <div class="form-group">
                        <label>ML Confidence</label>
                        <input type="text" id="ml-confidence" disabled>
                    </div>
                </div>
            </div>

            <!-- Assignment Preview -->
            <div class="preview-section">
                <div class="preview-title">
                    <i class="fas fa-eye"></i>
                    Assignment Preview
                </div>
                <div class="preview-content">
                    <div class="preview-item">
                        <div class="preview-item-label">Vessel</div>
                        <div class="preview-item-value" id="preview-vessel">Not selected</div>
                    </div>
                    <div class="preview-item">
                        <div class="preview-item-label">Jetty</div>
                        <div class="preview-item-value" id="preview-jetty">Not selected</div>
                    </div>
                    <div class="preview-item">
                        <div class="preview-item-label">Start Time</div>
                        <div class="preview-item-value" id="preview-start">Not set</div>
                    </div>
                    <div class="preview-item">
                        <div class="preview-item-label">End Time</div>
                        <div class="preview-item-value" id="preview-end">Not set</div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" class="btn btn-cancel" id="cancel-button">
                    Cancel
                </button>
                <button type="submit" class="btn btn-submit">
                    <i class="fas fa-save"></i>
                    Save Assignment
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ nonce }}">
    document.addEventListener('DOMContentLoaded', function() {
        // Wire up cancel button without inline handler (CSP-safe)
        const cancelBtn = document.getElementById('cancel-button');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                window.location.href = '/schedule';
            });
        }
        // Check if we're in edit mode by looking for an ID in the URL
        const assignmentId = new URLSearchParams(window.location.search).get('id');
        const isEditMode = assignmentId !== null;
        
        // Update page title and form title based on mode
        if (isEditMode) {
            document.getElementById('page-title').textContent = 'Edit Assignment';
            document.getElementById('form-title').textContent = 'Edit Existing Assignment';
            document.title = 'Edit Assignment - Terneuzen Terminal Jetty Planning';
            // Show reason section in edit mode
            const rs = document.getElementById('reason-section');
            if (rs) rs.style.display = 'block';
        } else {
            document.getElementById('page-title').textContent = 'Add Assignment';
            document.getElementById('form-title').textContent = 'Schedule New Assignment';
            document.title = 'Add Assignment - Terneuzen Terminal Jetty Planning';
        }

        // Add active class to current nav link
        const currentPath = window.location.pathname;
        document.querySelectorAll('.nav-link').forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });

        // Load vessels and jetties
        loadVessels();
        loadJetties();

        // Add event listeners for form updates
        document.getElementById('vessel-select').addEventListener('change', updatePreview);
        document.getElementById('jetty-select').addEventListener('change', updatePreview);
        document.getElementById('start-date').addEventListener('change', updatePreview);
        document.getElementById('start-time').addEventListener('change', updatePreview);
        document.getElementById('end-date').addEventListener('change', updatePreview);
        document.getElementById('end-time').addEventListener('change', updatePreview);

        // Add form submission handler
        document.getElementById('add-assignment-form').addEventListener('submit', handleFormSubmit);

        // If in edit mode, load the assignment data
        if (isEditMode && assignmentId) {
            loadAssignmentForEdit(assignmentId);
        }

        // Add click handler for the Cancel button
        document.getElementById('cancel-button').addEventListener('click', function() {
            window.location.href = '/schedule';
        });
    });

    async function loadVessels() {
        try {
            const response = await fetch('/api/vessels');
            if (!response.ok) {
                throw new Error('Failed to fetch vessels');
            }

            const vessels = await response.json();
            const vesselSelect = document.getElementById('vessel-select');

            vessels.forEach(vessel => {
                const option = document.createElement('option');
                option.value = vessel.id;
                option.textContent = `${vessel.name} (${vessel.type})`;
                vesselSelect.appendChild(option);
            });

        } catch (error) {
            console.error('Error loading vessels:', error);
            // Fall back to test data
            const testVessels = [
                { id: 1, name: 'MV Atlantic Pioneer', type: 'Tanker' },
                { id: 2, name: 'SS Northern Star', type: 'Cargo' },
                { id: 3, name: 'MV Ocean Voyager', type: 'Container' },
                { id: 4, name: 'HMS Victory II', type: 'Naval' },
                { id: 5, name: 'MV Baltic Express', type: 'Ferry' }
            ];

            const vesselSelect = document.getElementById('vessel-select');
            testVessels.forEach(vessel => {
                const option = document.createElement('option');
                option.value = vessel.id;
                option.textContent = `${vessel.name} (${vessel.type})`;
                vesselSelect.appendChild(option);
            });
        }
    }

    async function loadJetties() {
        try {
            const response = await fetch('/api/jetties');
            if (!response.ok) {
                throw new Error('Failed to fetch jetties');
            }

            const jetties = await response.json();
            const jettySelect = document.getElementById('jetty-select');

            jetties.forEach(jetty => {
                const option = document.createElement('option');
                option.value = jetty.id;
                option.textContent = `${jetty.name} (${jetty.type})`;
                jettySelect.appendChild(option);
            });

        } catch (error) {
            console.error('Error loading jetties:', error);
            // Fall back to test data
            const testJetties = [
                { id: 1, name: 'Jetty A-1', type: 'Container' },
                { id: 2, name: 'Jetty A-2', type: 'Tanker' },
                { id: 3, name: 'Jetty B-1', type: 'Bulk' },
                { id: 4, name: 'Jetty B-2', type: 'General Cargo' },
                { id: 5, name: 'Jetty C-1', type: 'Ferry' }
            ];

            const jettySelect = document.getElementById('jetty-select');
            testJetties.forEach(jetty => {
                const option = document.createElement('option');
                option.value = jetty.id;
                option.textContent = `${jetty.name} (${jetty.type})`;
                jettySelect.appendChild(option);
            });
        }
    }

    async function loadAssignmentForEdit(assignmentId) {
        try {
            // Use compat endpoint that reads from the same DB as the schedule views
            const response = await fetch(`/api/schedule/assignments/${assignmentId}`);
            if (!response.ok) {
                throw new Error('Failed to fetch assignment');
            }

            const assignment = await response.json();

            // Populate form fields
            document.getElementById('vessel-select').value = assignment.vessel_id;
            // Jetty may come as name or id; try value match first, then fallback to text match
            const jettySelect = document.getElementById('jetty-select');
            jettySelect.value = assignment.jetty_id;
            if (jettySelect.value !== String(assignment.jetty_id)) {
                // Try matching by option text (e.g., "Jetty Name (type)")
                const opt = Array.from(jettySelect.options).find(o => (o.textContent || '').startsWith(String(assignment.jetty_id)));
                if (opt) jettySelect.value = opt.value;
            }

            // Parse start and end times
            const startDateTime = new Date(assignment.start_time);
            const endDateTime = new Date(assignment.end_time);

            document.getElementById('start-date').value = startDateTime.toISOString().split('T')[0];
            document.getElementById('start-time').value = startDateTime.toTimeString().substring(0, 5);
            document.getElementById('end-date').value = endDateTime.toISOString().split('T')[0];
            document.getElementById('end-time').value = endDateTime.toTimeString().substring(0, 5);

            // Populate pump times if available
            if (assignment.pre_pump_duration) {
                const prePumpMinutes = assignment.pre_pump_duration;
                document.getElementById('pre-pump-hours').value = Math.floor(prePumpMinutes / 60);
                document.getElementById('pre-pump-minutes').value = prePumpMinutes % 60;
            }

            if (assignment.post_pump_duration) {
                const postPumpMinutes = assignment.post_pump_duration;
                document.getElementById('post-pump-hours').value = Math.floor(postPumpMinutes / 60);
                document.getElementById('post-pump-minutes').value = postPumpMinutes % 60;
            }

            // Populate ML prediction if available (compat fields)
            if (assignment.ml_predicted_terminal_minutes) {
                document.getElementById('ml-terminal-minutes').value = assignment.ml_predicted_terminal_minutes;
            }

            if (assignment.ml_average_confidence) {
                document.getElementById('ml-confidence').value = `${(assignment.ml_average_confidence * 100).toFixed(1)}%`;
            }

            // Update preview
            updatePreview();

        } catch (error) {
            console.error('Error loading assignment for edit:', error);
            showErrorToast('Failed to load assignment data for editing. Please try again.');
        }
    }

    async function handleFormSubmit(event) {
        event.preventDefault();

        // Validate required fields
        const requiredFields = ['vessel-select', 'jetty-select', 'start-date', 'start-time', 'end-date', 'end-time'];
        const isEditMode = new URLSearchParams(window.location.search).get('id') !== null;

        if (isEditMode) {
            requiredFields.push('change-reason');
        }

        for (const fieldId of requiredFields) {
            const field = document.getElementById(fieldId);
            if (!field.value.trim()) {
                showErrorToast(`Please fill in the ${field.previousElementSibling.textContent.replace('*', '')} field.`);
                field.focus();
                return;
            }
        }

        // Collect form data
        const formData = {
            vessel_id: parseInt(document.getElementById('vessel-select').value),
            jetty_id: parseInt(document.getElementById('jetty-select').value),
            start_time: document.getElementById('start-date').value + 'T' + document.getElementById('start-time').value,
            end_time: document.getElementById('end-date').value + 'T' + document.getElementById('end-time').value,
            pre_pump_duration: parseInt(document.getElementById('pre-pump-hours').value) * 60 + parseInt(document.getElementById('pre-pump-minutes').value),
            post_pump_duration: parseInt(document.getElementById('post-pump-hours').value) * 60 + parseInt(document.getElementById('post-pump-minutes').value),
            predicted_terminal_time: document.getElementById('ml-terminal-minutes').value ? parseInt(document.getElementById('ml-terminal-minutes').value) : null
        };

        if (isEditMode) {
            formData.change_reason = document.getElementById('change-reason').value;
        }

        try {
            const assignmentId = new URLSearchParams(window.location.search).get('id');
            const url = isEditMode ? `/api/assignments/${assignmentId}` : '/api/assignments';
            const method = isEditMode ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Failed to save assignment');
            }

            const result = await response.json();
            showSuccessToast(isEditMode ? 'Assignment updated successfully!' : 'Assignment created successfully!');

            // Redirect to schedule page after a short delay to show the toast
            setTimeout(() => {
                window.location.href = '/schedule';
            }, 1500);

        } catch (error) {
            console.error('Error submitting form:', error);
            showErrorToast(`Error: ${error.message}`);
        }
    }

    function updatePreview() {
        const vesselSelect = document.getElementById('vessel-select');
        const jettySelect = document.getElementById('jetty-select');
        const startDate = document.getElementById('start-date').value;
        const startTime = document.getElementById('start-time').value;
        const endDate = document.getElementById('end-date').value;
        const endTime = document.getElementById('end-time').value;

        // Update vessel preview
        const selectedVessel = vesselSelect.options[vesselSelect.selectedIndex];
        document.getElementById('preview-vessel').textContent = selectedVessel && selectedVessel.value
            ? selectedVessel.textContent
            : 'Not selected';

        // Update jetty preview
        const selectedJetty = jettySelect.options[jettySelect.selectedIndex];
        document.getElementById('preview-jetty').textContent = selectedJetty && selectedJetty.value
            ? selectedJetty.textContent
            : 'Not selected';

        // Update start time preview
        document.getElementById('preview-start').textContent = startDate && startTime
            ? `${startDate} ${startTime}`
            : 'Not set';

        // Update end time preview
        document.getElementById('preview-end').textContent = endDate && endTime
            ? `${endDate} ${endTime}`
            : 'Not set';
    }
</script>
{% endblock %}