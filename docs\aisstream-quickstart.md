## AIS Stream API Quickstart (JettyPlanner)

### Overview
- **Service**: Real-time AIS data over WebSocket.
- **Endpoint**: `wss://stream.aisstream.io/v0/stream`
- **Status**: BETA (no SLA; models subject to change).

### Authentication
- **API Key required**: generate/revoke in your AIS Stream account.
- **Transport**: wss only.
- **Do not use from browsers**: CORS is not supported and exposing keys is unsafe. Connect from backend only.

### Creating a connection
1. Open a WebSocket to `wss://stream.aisstream.io/v0/stream`.
2. Within 3 seconds, send a JSON subscription message:

```json
{
  "APIKey": "<YOUR_API_KEY>",
  "BoundingBoxes": [[[lat1, lon1], [lat2, lon2]]],
  "FiltersShipMMSI": ["*********", "*********"],
  "FilterMessageTypes": ["PositionReport", "ShipStaticData"]
}
```

- **BoundingBoxes (required)**: one or more bboxes; format `[[[latSW, lonSW], [latNE, lonNE]]]`.
- **FiltersShipMMSI (optional, max 50)**: limit to specific MMSIs.
- **FilterMessageTypes (optional)**: reduce message volume to needed types (e.g., `PositionReport`, `ShipStaticData`).

### Updating a subscription
- Send a new subscription message on the same socket. It replaces the previous one (not merged).

### Keeping the connection healthy
- Process messages quickly; AIS Stream may close slow consumers.
- Avoid global coverage; use tight **BoundingBoxes** and **message-type filters**.
- Prefer **MMSI filters** when tracking a small set of vessels.

### Message format
All messages share a wrapper containing the type, metadata, and message payload:

```json
{
  "MessageType": "PositionReport",
  "Metadata": { "Latitude": 51.44, "Longitude": 3.59, ... },
  "Message": { "PositionReport": { "UserID": 245473000, ... } }
}
```

Common types you will see/use:
- **PositionReport**: dynamic positions, COG/SOG, heading.
- **ShipStaticData**: name, IMO, type, dimensions, max static draught, destination, ETA.
- Others: ExtendedClassBPositionReport, StandardClassBPositionReport, etc.

### JettyPlanner integration notes
- On backend startup we initialize the AIS client and set a bounding box around the active terminal using a configurable radius (default 100 km):

```357:373:src/api/fastapi_app.py
            global_state["aisstream"] = AISStreamClient(api_key=aisstream_key)
            # Constrain AIS region to active terminal radius (server-side bbox + client-side filter)
            try:
                terminal_for_bbox = global_state.get("terminal")
                if terminal_for_bbox and hasattr(terminal_for_bbox, 'location'):
                    lat, lon = terminal_for_bbox.location
                else:
                    # Default to EVOS Terneuzen
                    lat, lon = (51.34543250288062, 3.751466718019277)
                # Default preload radius 100 km (fits Antwerp)
                try:
                    setting = db.get_setting('ais_radius_km')
                    radius_km = float(setting['value']) if setting and setting.get('value') else 100.0
                except Exception:
                    radius_km = 100.0
                global_state["aisstream"].set_subscription_region(lat, lon, radius_km=radius_km)
            except Exception as e:
```

- The client converts a center + radius to a bounding box for the subscription:

```212:233:src/integration/aisstream_client.py
    def set_subscription_region(self, latitude: float, longitude: float, radius_km: float = 50.0) -> None:
        try:
            delta_lat = radius_km / 111.0
            cos_lat = max(0.0001, math.cos(math.radians(latitude)))
            delta_lon = radius_km / (111.320 * cos_lat)
            south = latitude - delta_lat
            north = latitude + delta_lat
            west = longitude - delta_lon
            east = longitude + delta_lon
            self.bounding_boxes = [[south, west], [north, east]]
            logger.info(
                f"AIS subscription region set to bbox: S{south:.4f}, W{west:.4f} to N{north:.4f}, E{east:.4f} (~{radius_km} km radius)"
            )
        except Exception as e:
```

### Code examples
- JavaScript (official example):

```javascript
const WebSocket = require('ws');
const socket = new WebSocket("wss://stream.aisstream.io/v0/stream")

socket.onopen = function (_) {
    let subscriptionMessage = {
        Apikey: "<YOUR API KEY>",
        BoundingBoxes: [[[-90, -180], [90, 180]]],
        FiltersShipMMSI: ["*********", "*********", "211476060"], // Optional!
        FilterMessageTypes: ["PositionReport"] // Optional!
    }
    socket.send(JSON.stringify(subscriptionMessage));
};

socket.onmessage = function (event) {
    let aisMessage = JSON.parse(event.data)
    console.log(aisMessage)
};
```

Note: The API spec uses `APIKey`. The JavaScript example above from the docs uses `Apikey`.

- Python (async):

```python
import asyncio, json, websockets

async def main():
    async with websockets.connect('wss://stream.aisstream.io/v0/stream') as ws:
        sub = {
            'APIKey': '<API_KEY>',
            'BoundingBoxes': [[[-90, -180], [90, 180]]],
            'FiltersShipMMSI': ['*********'],
            'FilterMessageTypes': ['PositionReport']
        }
        await ws.send(json.dumps(sub))
        async for message in ws:
            data = json.loads(message)
            if data.get('MessageType') == 'PositionReport':
                pr = data['Message']['PositionReport']
                print(pr['UserID'], pr['Latitude'], pr['Longitude'])

asyncio.run(main())
```

### Best practices for lower data volume
- **Keep bboxes tight** around current operations.
- Use **FilterMessageTypes** to limit to `PositionReport` and `ShipStaticData` for search/track use-cases.
- When tracking a small set of ships, use **FiltersShipMMSI** and shorter sessions.
- Backoff/retry responsibly and avoid multiple concurrent sockets per API key.

### Troubleshooting
- Connection closes shortly after open: ensure subscription is sent within 3 seconds.
- High CPU or dropped messages: narrow bboxes or reduce message types.
- Browser errors/CORS: move connection logic to backend.


