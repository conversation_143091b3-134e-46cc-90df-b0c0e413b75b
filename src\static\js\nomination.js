/**
 * Vessel Nomination Form with ML Predictions
 */

class NominationForm {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.cargoCounter = 1;
        this.selectedJetty = null;
        this.selectedVessel = null;
        this.predictions = null;
        this.logValidation = false;
        this.lastSelectedModel = null;
        this.predictionsCalculated = false;
        this.searchEnrichmentAttempts = 0;
        this.maxSearchEnrichmentAttempts = 3;
        this.lastSearchResult = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setDefaultETA();
        this.loadAvailableModels();
        this.updateStepNavigation();
    }
    
    bindEvents() {
        console.log('Binding events for nomination form');
        
        // Step navigation
        const nextBtn = document.getElementById('next-step');
        const prevBtn = document.getElementById('prev-step');
        
        if (!nextBtn) {
            console.error('Next button not found!');
            return;
        }
        if (!prevBtn) {
            console.error('Previous button not found!');
            return;
        }
        
        nextBtn.addEventListener('click', () => {
            console.log('Next button clicked');
            this.nextStep();
        });
        prevBtn.addEventListener('click', () => {
            console.log('Previous button clicked');
            this.prevStep();
        });
        
        // Step headers
        document.querySelectorAll('.step').forEach(step => {
            step.addEventListener('click', (e) => {
                const stepNum = parseInt(e.currentTarget.dataset.step);
                if (stepNum <= this.currentStep || this.canNavigateToStep(stepNum)) {
                    this.goToStep(stepNum);
                }
            });
        });
        
        // Form validation (debounced to reduce console spam)
        document.getElementById('nomination-form').addEventListener('input', () => {
            clearTimeout(this.validationTimeout);
            this.validationTimeout = setTimeout(() => this.updateStepNavigation(), 300);
        });
        
        // Cargo management
        document.getElementById('add-cargo').addEventListener('click', () => this.addCargoItem());
        
        // Manual prediction control buttons
        const generateBtn = document.getElementById('generate-predictions-btn');
        const recalculateBtn = document.getElementById('recalculate-predictions-btn');
        
        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.triggerPredictions());
        }
        if (recalculateBtn) {
            recalculateBtn.addEventListener('click', () => this.triggerPredictions());
        }
        
        // Form submission
        document.getElementById('nomination-form').addEventListener('submit', (e) => this.handleSubmit(e));
        
        // Save draft
        document.getElementById('save-draft').addEventListener('click', () => this.saveDraft());
        
        // Vessel search functionality
        this.bindVesselSearchEvents();
        
        // Model upload functionality
        this.bindModelUploadEvents();
        
        // Model selection functionality
        this.bindModelSelectionEvents();
        
        // Model change detection
        const modelSelect = document.getElementById('model-select');
        if (modelSelect) {
            modelSelect.addEventListener('change', () => this.onModelSelectionChange());
        }
    }
    
    setDefaultETA() {
        // Set default ETA to 6 hours from now
        const now = new Date();
        now.setHours(now.getHours() + 6);
        const etaString = now.toISOString().slice(0, 16); // Format for datetime-local
        
        const etaField = document.getElementById('vessel-eta');
        if (etaField) {
            etaField.value = etaString;
            console.log('Set default ETA:', etaString);
        } else {
            console.error('ETA field not found');
        }
        
        // Set some default values for easier testing
        this.setDefaultValues();
    }
    
    setDefaultValues() {
        // Only set defaults if fields are empty (for easier testing)
        const defaults = {
            'vessel-name': 'Test Vessel',
            'vessel-length': '180',
            'vessel-beam': '25',
            'vessel-draft': '8.5'
        };
        
        Object.entries(defaults).forEach(([id, value]) => {
            const field = document.getElementById(id);
            if (field && !field.value) {
                field.value = value;
            }
        });
        
        // Set default cargo values
        const cargoDefaults = {
            'cargo_product_0': 'hydrocarbons',
            'cargo_volume_0': '15000',
            'cargo_operation_0': 'loading'
        };
        
        Object.entries(cargoDefaults).forEach(([name, value]) => {
            const field = document.querySelector(`[name="${name}"]`);
            if (field && !field.value) {
                field.value = value;
            }
        });
        
        // Set vessel type if not selected
        const vesselTypeField = document.getElementById('vessel-type');
        if (vesselTypeField && !vesselTypeField.value) {
            vesselTypeField.value = 'tanker';
        }
        
        console.log('Set default test values');
    }
    
    nextStep() {
        console.log('Attempting to go to next step from step', this.currentStep);
        
        // Enable validation logging for this check
        this.logValidation = true;
        const isValid = this.validateCurrentStep();
        this.logValidation = false;
        
        console.log('Current step valid:', isValid);
        
        if (isValid && this.currentStep < this.totalSteps) {
            this.goToStep(this.currentStep + 1);
        } else {
            console.log('Cannot proceed to next step. Valid:', isValid, 'Current step:', this.currentStep, 'Total steps:', this.totalSteps);
        }
    }
    
    prevStep() {
        if (this.currentStep > 1) {
            this.goToStep(this.currentStep - 1);
        }
    }
    
    goToStep(stepNum) {
        // Hide current step
        document.querySelector(`.step-content[data-step="${this.currentStep}"]`).classList.remove('active');
        document.querySelector(`.step[data-step="${this.currentStep}"]`).classList.remove('active');
        
        // Show new step
        this.currentStep = stepNum;
        document.querySelector(`.step-content[data-step="${stepNum}"]`).classList.add('active');
        document.querySelector(`.step[data-step="${stepNum}"]`).classList.add('active');
        
        // Initialize step-specific UI
        if (stepNum === 3) {
            this.initializePredictionStep();
        }
        
        // Mark completed steps
        for (let i = 1; i < stepNum; i++) {
            document.querySelector(`.step[data-step="${i}"]`).classList.add('completed');
        }
        
        this.updateStepNavigation();

        // Smooth scroll to the top of the nomination section for better UX
        this.scrollToTop();
        
        // Step-specific actions
        if (stepNum === 4) {
            this.generateSummary();
        }
    }

    scrollToTop() {
        try {
            // Prefer scrolling the active step or header into view to support nested scroll containers
            const activeStep = document.querySelector(`.step-content[data-step="${this.currentStep}"]`);
            const header = document.querySelector('.content-header');
            const target = header || activeStep || document.body;

            if (typeof target.scrollIntoView === 'function') {
                target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                return;
            }

            // Fallback: try to scroll a known content container
            const container = document.querySelector('.content')
                || document.querySelector('.main-content')
                || document.querySelector('.nomination-form');
            if (container && typeof container.scrollTo === 'function') {
                container.scrollTo({ top: 0, behavior: 'smooth' });
                return;
            }

            // Final fallback: scroll window
            window.scrollTo({ top: 0, behavior: 'smooth' });
        } catch (e) {
            // Fallback
            window.scrollTo(0, 0);
        }
    }
    
    updateStepNavigation() {
        const prevBtn = document.getElementById('prev-step');
        const nextBtn = document.getElementById('next-step');
        const submitBtn = document.getElementById('submit-nomination');
        
        // Previous button
        prevBtn.style.display = this.currentStep > 1 ? 'block' : 'none';
        
        // Next/Submit button
        if (this.currentStep === this.totalSteps) {
            nextBtn.style.display = 'none';
            submitBtn.style.display = 'block';
        } else {
            nextBtn.style.display = 'block';
            submitBtn.style.display = 'none';
        }
        
        // Update button states
        const isValid = this.validateCurrentStep();
        nextBtn.disabled = !isValid;
        submitBtn.disabled = !isValid;
    }
    
    validateCurrentStep() {
        const currentStepElement = document.querySelector(`.step-content[data-step="${this.currentStep}"]`);
        if (!currentStepElement) {
            console.error('Current step element not found:', this.currentStep);
            return false;
        }
        
        const requiredFields = currentStepElement.querySelectorAll('[required]');
        
        let isValid = true;
        let invalidFields = [];
        requiredFields.forEach(field => {
            const value = (field.value || '').trim();
            let fieldValid = value.length > 0;
            // Numeric constraints when present
            if (fieldValid && field.type === 'number') {
                const num = parseFloat(value);
                if (!isFinite(num)) fieldValid = false;
                if (field.hasAttribute('min') && num < parseFloat(field.min)) fieldValid = false;
                if (field.hasAttribute('max') && num > parseFloat(field.max)) fieldValid = false;
            }
            if (!fieldValid) {
                invalidFields.push(field.name || field.id);
                isValid = false;
                field.classList.add('input-validation-error');
            } else {
                field.classList.remove('input-validation-error');
            }
        });
        
        // Only log if we're actually trying to proceed (less spam)
        if (!isValid && this.logValidation) {
            console.log('Step', this.currentStep, 'validation failed. Missing:', invalidFields);
            // Surface a toast with first actionable hint
            const first = invalidFields[0] || 'field';
            this.showError(`Please correct the highlighted fields (e.g., ${first}).`);
        }
        
        // Step-specific validation
        if (this.currentStep === 2) {
            // At least one cargo item must be complete
            const cargoItems = document.querySelectorAll('.cargo-item');
            let hasValidCargo = false;
            
            cargoItems.forEach(item => {
                const product = item.querySelector('[name^="cargo_product_"]').value;
                const volume = item.querySelector('[name^="cargo_volume_"]').value;
                
                if (product && volume && parseFloat(volume) > 0) {
                    hasValidCargo = true;
                }
            });
            
            isValid = isValid && hasValidCargo;
        }
        
        return isValid;
    }
    
    canNavigateToStep(stepNum) {
        // Can navigate if all previous steps are valid
        for (let i = 1; i < stepNum; i++) {
            const stepElement = document.querySelector(`.step-content[data-step="${i}"]`);
            const requiredFields = stepElement.querySelectorAll('[required]');
            
            for (let field of requiredFields) {
                if (!field.value.trim()) {
                    return false;
                }
            }
        }
        return true;
    }
    
    addCargoItem() {
        const container = document.getElementById('cargo-container');
        const cargoItem = container.children[0].cloneNode(true);
        
        // Update field names and IDs
        cargoItem.dataset.cargo = this.cargoCounter;
        cargoItem.querySelectorAll('[name]').forEach(field => {
            const baseName = field.name.replace(/_\d+$/, '');
            field.name = `${baseName}_${this.cargoCounter}`;
            field.value = '';
            if (field.type === 'checkbox') {
                field.checked = false;
            }
        });
        
        // Add remove button
        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'btn btn-outline btn-sm';
        removeBtn.innerHTML = '<i class="fas fa-trash"></i> Remove';
        removeBtn.style.float = 'right';
        removeBtn.onclick = () => this.removeCargoItem(cargoItem);
        
        cargoItem.appendChild(removeBtn);
        container.appendChild(cargoItem);
        
        this.cargoCounter++;
    }
    
    removeCargoItem(cargoItem) {
        cargoItem.remove();
        this.validateCurrentStep();
    }
    
    // Manual prediction control methods
    async triggerPredictions() {
        // Validate that we have the required data
        if (!this.validateCurrentStep()) {
            this.showError('Please complete all required fields before generating predictions.');
            return;
        }
        
        const modelSelect = document.getElementById('model-select');
        if (!modelSelect.value) {
            this.showError('Please select an ML model before generating predictions.');
            return;
        }
        
        // Update button states
        this.updatePredictionButtonState('calculating');
        
        try {
            await this.generatePredictions();
            this.predictionsCalculated = true;
            this.lastSelectedModel = modelSelect.value;
            this.updatePredictionButtonState('completed');
            this.hideModelChangeNotice();
        } catch (error) {
            this.updatePredictionButtonState('error');
            throw error; // Re-throw to be handled by generatePredictions
        }
    }
    
    onModelSelectionChange() {
        const modelSelect = document.getElementById('model-select');
        const currentModel = modelSelect.value;
        const generateBtn = document.getElementById('generate-predictions-btn');
        
        // If predictions have been calculated and model changed, show recalculate option
        if (this.predictionsCalculated && this.lastSelectedModel && this.lastSelectedModel !== currentModel) {
            this.showModelChangeNotice();
            this.showRecalculateButton();
        } else {
            this.hideModelChangeNotice();
            this.hideRecalculateButton();
        }
        
        // Enable/disable generate button based on model selection
        if (generateBtn) {
            if (currentModel) {
                generateBtn.disabled = false;
                if (!this.predictionsCalculated) {
                    generateBtn.innerHTML = '<i class="fas fa-calculator"></i> Calculate Predictions';
                }
            } else {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-calculator"></i> Select Model & Calculate Predictions';
            }
        }
    }
    
    updatePredictionButtonState(state) {
        const generateBtn = document.getElementById('generate-predictions-btn');
        const recalculateBtn = document.getElementById('recalculate-predictions-btn');
        
        // Reset classes
        generateBtn.classList.remove('calculating', 'completed');
        
        switch (state) {
            case 'ready':
                generateBtn.innerHTML = '<i class="fas fa-calculator"></i> Calculate Predictions';
                generateBtn.disabled = false;
                break;
            case 'calculating':
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Calculating...';
                generateBtn.disabled = true;
                generateBtn.classList.add('calculating');
                break;
            case 'completed':
                generateBtn.innerHTML = '<i class="fas fa-check"></i> Predictions Generated';
                generateBtn.disabled = false;
                generateBtn.classList.add('completed');
                break;
            case 'error':
                generateBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Calculation Failed - Retry';
                generateBtn.disabled = false;
                break;
        }
    }
    
    showModelChangeNotice() {
        const notice = document.getElementById('model-change-notice');
        if (notice) {
            notice.style.display = 'block';
        }
    }
    
    hideModelChangeNotice() {
        const notice = document.getElementById('model-change-notice');
        if (notice) {
            notice.style.display = 'none';
        }
    }
    
    showRecalculateButton() {
        const recalculateBtn = document.getElementById('recalculate-predictions-btn');
        if (recalculateBtn) {
            recalculateBtn.style.display = 'inline-block';
        }
    }
    
    hideRecalculateButton() {
        const recalculateBtn = document.getElementById('recalculate-predictions-btn');
        if (recalculateBtn) {
            recalculateBtn.style.display = 'none';
        }
    }
    
    initializePredictionStep() {
        // Initialize the prediction step UI when user navigates to step 3
        const modelSelect = document.getElementById('model-select');
        
        if (!this.predictionsCalculated) {
            // If no predictions have been calculated yet
            if (modelSelect && modelSelect.value) {
                this.updatePredictionButtonState('ready');
            } else {
                this.updatePredictionButtonState('ready');
                const generateBtn = document.getElementById('generate-predictions-btn');
                if (generateBtn) {
                    generateBtn.innerHTML = '<i class="fas fa-calculator"></i> Select Model & Calculate Predictions';
                    generateBtn.disabled = true;
                }
            }
            
            // Hide predictions content initially
            const contentDiv = document.getElementById('predictions-content');
            if (contentDiv) {
                contentDiv.style.display = 'none';
            }
        }
        
        // Reset notices
        this.hideModelChangeNotice();
        this.hideRecalculateButton();
        
        console.log('Initialized prediction step - predictions calculated:', this.predictionsCalculated);
    }
    
    async generatePredictions() {
        const loadingDiv = document.getElementById('predictions-loading');
        const contentDiv = document.getElementById('predictions-content');
        
        loadingDiv.style.display = 'block';
        contentDiv.style.display = 'none';
        
        try {
            // Extract vessel features
            const features = this.extractVesselFeatures();
            
            // Include selected model information
            const selectedModel = this.getSelectedModel();
            const requestBody = {
                ...features,
                selected_model: selectedModel
            };
            
            // Get ML predictions
            let predictUrl = '/api/ml/predict-features';
            if (features.preferred_jetty) {
                predictUrl += `?jetty_id=${encodeURIComponent(features.preferred_jetty)}`;
            }
            
            const response = await fetch(predictUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });
            
            if (!response.ok) {
                throw new Error('Failed to get predictions');
            }
            
            const predictionData = await response.json();
            this.predictions = predictionData;
            
            // Get jetty recommendations (include selected model)
            await this.getJettyRecommendations(requestBody);
            
            // Display predictions
            this.displayPredictions(predictionData);
            
            // Validate business rules
            this.validateBusinessRules(requestBody);
            
            loadingDiv.style.display = 'none';
            contentDiv.style.display = 'block';
            
        } catch (error) {
            console.error('Error generating predictions:', error);
            loadingDiv.innerHTML = `
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i> 
                    Error generating predictions: ${error.message}
                </div>
            `;
            
            // Reset button state and hide loading
            setTimeout(() => {
                loadingDiv.style.display = 'none';
                this.updatePredictionButtonState('error');
            }, 3000);
        }
    }
    
    extractVesselFeatures() {
        const formData = new FormData(document.getElementById('nomination-form'));
        
        // Extract cargo information
        const products = [];
        const operations = [];
        const cargoItems = document.querySelectorAll('.cargo-item');
        cargoItems.forEach((item, index) => {
            const product = formData.get(`cargo_product_${index}`);
            const operation = formData.get(`cargo_operation_${index}`);
            if (product) {
                products.push(product);
            }
            if (operation) {
                operations.push(operation);
            }
        });
        
        // Calculate total cargo volume
        let totalVolume = 0;
        cargoItems.forEach((item, index) => {
            const volume = parseFloat(formData.get(`cargo_volume_${index}`) || 0);
            totalVolume += volume;
        });
        
        return {
            dwt: parseFloat(formData.get('deadweight')),
            loa: parseFloat(formData.get('length')),
            beam: parseFloat(formData.get('beam')),
            draft: parseFloat(formData.get('draft')),
            vessel_type: formData.get('vessel_type'),
            cargo_volume: totalVolume,
            product_types: products,
            operation_types: operations,
            customer_name: formData.get('customer'),
            preferred_jetty: formData.get('preferred_jetty'),
            is_first_visit: formData.get('notes')?.toLowerCase().includes('first') || false,
            connection_size: formData.get('cargo_connection_0') || '12"'
        };
    }
    
    async getJettyRecommendations(features) {
        try {
            // Call the real jetty recommendations API with vessel features
            console.log('CALLING JETTY RECOMMENDATIONS API with features:', features);
            const response = await fetch('/api/ml/jetty-recommendations-features', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(features)
            });
            
            if (!response.ok) {
                throw new Error('Failed to get jetty recommendations');
            }
            
            const recommendations = await response.json();
            console.log('JETTY RECOMMENDATIONS API SUCCESS:', recommendations);
            this.displayJettyRecommendations(recommendations);
            
        } catch (error) {
            console.error('JETTY RECOMMENDATIONS API FAILED:', error);
            console.error('Response status:', error.status);
            console.error('Response text:', error.message);
            
            // Fallback to mock data only if API call fails
            const mockRecommendations = [
                {
                    jetty_id: 'J1',
                    jetty_name: 'Jetty 1',
                    terminal_time_hours: 12.5,
                    pump_time_hours: 8.2,
                    average_confidence: 0.85,
                    max_flow_rate: 2000,
                    suitability_score: 0.92
                },
                {
                    jetty_id: 'J3',
                    jetty_name: 'Jetty 3',
                    terminal_time_hours: 14.1,
                    pump_time_hours: 9.8,
                    average_confidence: 0.78,
                    max_flow_rate: 1800,
                    suitability_score: 0.84
                },
                {
                    jetty_id: 'J5',
                    jetty_name: 'Jetty 5',
                    terminal_time_hours: 11.8,
                    pump_time_hours: 7.5,
                    average_confidence: 0.91,
                    max_flow_rate: 2200,
                    suitability_score: 0.95
                }
            ];
            
            this.displayJettyRecommendations(mockRecommendations);
        }
    }
    
    displayPredictions(data) {
        const totalEl = document.getElementById('pred-total');
        if (totalEl && typeof data.terminal_time_hours === 'number') {
            totalEl.textContent = `${data.terminal_time_hours.toFixed(1)}h`;
        }
        this.updateConfidenceBar('conf-total', data.terminal_confidence || 0);
        this.displayModelInfo(data);
    }
    
    displayModelInfo(data) {
        // Display model version
        const modelVersion = data.model_version || 'unknown';
        document.getElementById('model-version').textContent = modelVersion;
        
        // Display prediction timestamp (format nicely)
        if (data.prediction_timestamp) {
            try {
                const timestamp = new Date(data.prediction_timestamp);
                const formattedTime = timestamp.toLocaleString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
                document.getElementById('prediction-timestamp').textContent = formattedTime;
            } catch (e) {
                document.getElementById('prediction-timestamp').textContent = 'Just now';
            }
        } else {
            document.getElementById('prediction-timestamp').textContent = 'Just now';
        }
    }
    
    updateConfidenceBar(id, confidence) {
        const bar = document.getElementById(id);
        bar.style.width = `${confidence * 100}%`;
    }
    
    displayJettyRecommendations(recommendations) {
        const container = document.getElementById('jetty-list');
        container.innerHTML = '';
        
        recommendations.forEach(jetty => {
            const jettyCard = document.createElement('div');
            jettyCard.className = 'jetty-card';
            jettyCard.dataset.jettyId = jetty.jetty_id;
            jettyCard.onclick = () => this.selectJetty(jettyCard, jetty);
            
            jettyCard.innerHTML = `
                <div class="jetty-header">
                    <span class="jetty-name">${jetty.jetty_name}</span>
                    <span class="suitability-score">${(jetty.suitability_score * 100).toFixed(0)}% match</span>
                </div>
                <div class="jetty-details">
                    <div><strong>Terminal Time:</strong> ${jetty.terminal_time_hours.toFixed(1)}h</div>
                    <div><strong>Pump Time:</strong> ${jetty.pump_time_hours.toFixed(1)}h</div>
                    <div><strong>Flow Rate:</strong> ${jetty.max_flow_rate} m³/h</div>
                </div>
            `;
            
            container.appendChild(jettyCard);
        });
        
        // Auto-select the best recommendation
        if (recommendations.length > 0) {
            const bestJetty = container.children[0];
            this.selectJetty(bestJetty, recommendations[0]);
        }
    }
    
    selectJetty(jettyCard, jettyData) {
        // Remove previous selection
        document.querySelectorAll('.jetty-card.selected').forEach(card => {
            card.classList.remove('selected');
        });
        
        // Select new jetty
        jettyCard.classList.add('selected');
        this.selectedJetty = jettyData;
    }
    
    validateBusinessRules(features) {
        const rulesContainer = document.getElementById('business-rules-list');
        
        // Generic mock business rules validation for demonstration purposes
        const rules = [
            {
                name: 'Vessel size validation',
                status: features.dwt > 500 && features.dwt < 200000 ? 'pass' : 'fail',
                description: 'Vessel size must be within acceptable range for terminal operations'
            },
            {
                name: 'Dimensional compliance',
                status: features.loa > 30 && features.loa < 350 ? 'pass' : 'warning',
                description: 'Vessel dimensions should be compatible with berth infrastructure'
            },
            {
                name: 'Cargo specification',
                status: features.product_types.length > 0 ? 'pass' : 'fail',
                description: 'Cargo type must be specified and approved for handling'
            },
            {
                name: 'Safety clearance',
                status: features.draft < 20.0 ? 'pass' : 'warning',
                description: 'Vessel draft should be within safe operational limits'
            },
            {
                name: 'Documentation review',
                status: Math.random() > 0.2 ? 'pass' : 'warning',
                description: 'All required documentation and certifications must be complete'
            },
            {
                name: 'Environmental compliance',
                status: this.checkEnvironmentalCompliance(features.product_types) ? 'pass' : 'warning',
                description: 'Cargo and operations must meet environmental standards'
            }
        ];
        
        rulesContainer.innerHTML = '';
        rules.forEach(rule => {
            const ruleDiv = document.createElement('div');
            ruleDiv.className = 'rule-item';
            
            const icon = rule.status === 'pass' ? 'fa-check-circle' : 
                        rule.status === 'warning' ? 'fa-exclamation-triangle' : 'fa-times-circle';
            
            ruleDiv.innerHTML = `
                <span class="rule-status ${rule.status}">
                    <i class="fas ${icon}"></i>
                </span>
                <div>
                    <strong>${rule.name}</strong><br>
                    <small>${rule.description}</small>
                </div>
            `;
            
            rulesContainer.appendChild(ruleDiv);
        });
    }
    
    checkEnvironmentalCompliance(products) {
        // Generic mock environmental check
        const environmentallyControlledProducts = ['chemicals', 'other_chemicals'];
        return !products.some(product => environmentallyControlledProducts.includes(product));
    }
    
    generateSummary() {
        const formData = new FormData(document.getElementById('nomination-form'));
        const summaryContainer = document.getElementById('nomination-summary');
        
        const vesselName = formData.get('vessel_name');
        const vesselTypeRaw = formData.get('vessel_type');
        const vesselType = vesselTypeRaw === 'tanker' ? 'vessel' : vesselTypeRaw;
        const eta = new Date(formData.get('eta')).toLocaleString('nl-NL');
        
        // Get cargo summary (include operation type)
        const cargoSummary = [];
        const cargoItems = document.querySelectorAll('.cargo-item');
        cargoItems.forEach((item, index) => {
            const product = formData.get(`cargo_product_${index}`);
            const volume = formData.get(`cargo_volume_${index}`);
            const operation = formData.get(`cargo_operation_${index}`);
            if (product && volume) {
                const opLabel = operation === 'loading' ? 'Loading' : (operation === 'discharge' ? 'Discharge' : '');
                cargoSummary.push(`${product}: ${volume} m³${opLabel ? ` (${opLabel})` : ''}`);
            }
        });
        
        summaryContainer.innerHTML = `
            <div class="form-section vessel-section">
                <h4>Vessel Information</h4>
                <p><strong>Name:</strong> ${vesselName}</p>
                <p><strong>Type:</strong> ${vesselType}</p>
                <p><strong>ETA:</strong> ${eta}</p>
                <p><strong>Dimensions:</strong> ${formData.get('length')}m × ${formData.get('beam')}m × ${formData.get('draft')}m</p>
                <p><strong>Deadweight:</strong> ${formData.get('deadweight')} tonnes</p>
            </div>
            
            <div class="form-section cargo-section">
                <h4>Cargo Details</h4>
                <ul>
                    ${cargoSummary.map(cargo => `<li>${cargo}</li>`).join('')}
                </ul>
            </div>
            
            ${this.predictions ? `
                <div class="form-section ml-section">
                    <h4>ML Predictions</h4>
                    <p><strong>Predicted Terminal Time:</strong> ${this.predictions.terminal_time_hours.toFixed(1)} hours</p>
                    <p><strong>Average Confidence:</strong> ${(this.predictions.average_confidence * 100).toFixed(0)}%</p>
                </div>
            ` : ''}
            
            ${this.selectedJetty ? `
                <div class="form-section jetty-section">
                    <h4>Recommended Jetty</h4>
                    <p><strong>Selected:</strong> ${this.selectedJetty.jetty_name}</p>
                    <p><strong>Predicted Terminal Time:</strong> ${this.selectedJetty.terminal_time_hours.toFixed(1)} hours</p>
                </div>
            ` : ''}
        `;
    }
    
    async handleSubmit(e) {
        e.preventDefault();
        
        if (!this.validateCurrentStep()) {
            return;
        }
        
        const submitBtn = document.getElementById('submit-nomination');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<div class="loading-spinner"></div> Creating unscheduled vessel...';
        submitBtn.disabled = true;
        
        try {
            // Create nomination from form data
            const nominationData = this.extractNominationData();
            
            // Create vessel from nomination
            const nominationResponse = await fetch('/api/nominations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(nominationData)
            });
            
            if (!nominationResponse.ok) {
                const errorData = await nominationResponse.json();
                throw new Error(errorData.detail || 'Failed to submit nomination');
            }
            
            const nominationResult = await nominationResponse.json();
            
            // Show success message and redirect to schedule page
            this.showSuccess(`Vessel ${nominationResult.name} created successfully! (ID: ${nominationResult.id})<br>
                Your vessel is now in the unscheduled vessels list and ready for optimization.`);
            
            setTimeout(() => {
                window.location.href = '/schedule'; // Redirect to schedule page to see unscheduled vessels
            }, 3000);
            
        } catch (error) {
            console.error('Error submitting nomination:', error);
            this.showError(`Error submitting nomination: ${error.message}`);
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }
    
    extractNominationData() {
        const formData = new FormData(document.getElementById('nomination-form'));
        
        // Extract cargo data
        const cargoes = [];
        const cargoItems = document.querySelectorAll('.cargo-item');
        cargoItems.forEach((item, index) => {
            const product = formData.get(`cargo_product_${index}`);
            const volume = formData.get(`cargo_volume_${index}`);
            const operation = formData.get(`cargo_operation_${index}`);
            
            if (product && volume && !isNaN(parseFloat(volume)) && parseFloat(volume) > 0) {
                // Get selected tanks for this cargo
                const tanksValue = formData.get(`cargo_tanks_${index}`) || '';
                const tanks = tanksValue ? tanksValue.split(',').filter(t => t.trim()) : [];
                
                cargoes.push({
                    product: product,
                    volume: parseFloat(volume),
                    is_loading: operation === 'Ex' || operation === 'loading', // Handle both 'Ex' and 'loading'
                    tanks: tanks,
                    surveyor_required: true,
                    completed_volume: 0.0
                });
            }
        });
        
        // Get ETA/ETD dates
        const etaValue = formData.get('eta');
        const etdValue = formData.get('etd');
        
        return {
            name: formData.get('vessel_name'),
            vessel_type: formData.get('vessel_type') || 'tanker',
            length: parseFloat(formData.get('length')) || 0,
            beam: parseFloat(formData.get('beam')) || 0,
            draft: parseFloat(formData.get('draft')) || 0,
            deadweight: parseFloat(formData.get('deadweight')) || 0,
            eta: etaValue ? new Date(etaValue).toISOString() : null,
            etd: etdValue ? new Date(etdValue).toISOString() : null,
            customer: formData.get('customer') || null,
            priority: parseInt(formData.get('priority')) || 1,
            capacity: parseFloat(formData.get('capacity')) || 0,
            width: parseFloat(formData.get('beam')) || 0, // Use beam as width
            cargoes: cargoes,
            
            // Vessel-specific fields (for tankers)
            cargo_type: formData.get('cargo_type') || null,
            flag: formData.get('flag') || null,
            imo: formData.get('imo') || (this.selectedVessel ? this.selectedVessel.imo : null) || null,
            mmsi: formData.get('mmsi') || (this.selectedVessel ? this.selectedVessel.mmsi : null) || null,
            customs_cleared: formData.get('customs_cleared') === 'on' || false,
            last_port: formData.get('last_port') || null,
            next_port: formData.get('next_port') || null,
            
            // Barge-specific fields
            owner: formData.get('owner') || null,
            registration_number: formData.get('registration_number') || null,
            tug_boat: formData.get('tug_boat') || null,
            operation_type: formData.get('operation_type') || null,
            has_crane: formData.get('has_crane') === 'on' || false,
            
            // Administrative fields
            submitted_by: 'UI_User', // Could be enhanced to use actual user info
            metadata: {
                submitted_via: 'web_form',
                submission_timestamp: new Date().toISOString(),
                selected_model: this.getSelectedModel(),
                predictions: this.predictions // Include ML predictions if available
            }
        };
    }
    
    // Keep the old method for backwards compatibility if needed
    extractVesselData() {
        const nominationData = this.extractNominationData();
        // Convert nomination data to vessel data format
        return {
            name: nominationData.name,
            vessel_type: nominationData.vessel_type,
            length: nominationData.length,
            beam: nominationData.beam,
            draft: nominationData.draft,
            deadweight: nominationData.deadweight,
            eta: nominationData.eta,
            customer: nominationData.customer,
            priority: nominationData.priority,
            status: 'EN_ROUTE',
            cargoes: nominationData.cargoes
        };
    }
    
    async triggerOptimization() {
        try {
            const response = await fetch('/api/optimize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    horizon_days: 7,
                    time_granularity_hours: 1,
                    force_assign_all: false
                })
            });
            
            if (!response.ok) {
                console.warn('Auto-optimization failed, but vessel was created successfully');
            }
        } catch (error) {
            console.warn('Auto-optimization error:', error);
        }
    }
    
    async saveDraft() {
        // Save current form state to localStorage
        const formData = new FormData(document.getElementById('nomination-form'));
        const draftData = {};
        
        for (let [key, value] of formData.entries()) {
            draftData[key] = value;
        }
        
        localStorage.setItem('nomination_draft', JSON.stringify(draftData));
        this.showSuccess('Draft saved successfully!');
    }
    
    loadDraft() {
        const draftData = localStorage.getItem('nomination_draft');
        if (draftData) {
            const data = JSON.parse(draftData);
            
            Object.keys(data).forEach(key => {
                const field = document.querySelector(`[name="${key}"]`);
                if (field) {
                    if (field.type === 'checkbox') {
                        field.checked = data[key] === 'on';
                    } else {
                        field.value = data[key];
                    }
                }
            });
        }
    }
    
    // Toast methods now use the unified toast system
    showSuccess(message) { showSuccessToast(message); }
    showError(message) { showErrorToast(message); }
    showInfo(message) { showInfoToast(message); }

    // ========== VESSEL SEARCH FUNCTIONALITY ==========
    
    bindVesselSearchEvents() {
        const searchBtn = document.getElementById('search-vessels-btn');
        const searchInput = document.getElementById('vessel-search');
        const manualToggle = document.getElementById('toggle-manual-entry');
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.searchVessels());
        }
        
        if (searchInput) {
            // Search on Enter key (prevent form submission)
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault(); // Prevent form submission
                    this.searchVessels();
                }
            });
            
            // Auto-search after typing (debounced)
            searchInput.addEventListener('input', () => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    if (searchInput.value.length >= 2) {
                        this.searchVessels();
                    }
                }, 500);
            });
        }
        
        if (manualToggle) {
            manualToggle.addEventListener('click', () => this.toggleManualEntry());
        }
    }
    
    async searchVessels() {
        const searchInput = document.getElementById('vessel-search');
        const resultsContainer = document.getElementById('vessel-search-results');
        const searchBtn = document.getElementById('search-vessels-btn');
        const onlyLiquidEl = document.getElementById('filter-only-liquid');
        const typeFilterEl = document.getElementById('filter-type-substrings');
        const extendedSearchEl = document.getElementById('filter-extended-search');
        
        const query = searchInput.value.trim();
        if (query.length < 2) {
            resultsContainer.style.display = 'none';
            return;
        }
        
        // Show loading state
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Searching...';
        searchBtn.disabled = true;
        
        try {
            const params = new URLSearchParams({
                query,
                wait_for_data: 'true'
            });
            if (onlyLiquidEl && onlyLiquidEl.checked) {
                params.append('only_liquid', 'true');
            }
            if (typeFilterEl && typeFilterEl.value && typeFilterEl.value.trim().length > 0) {
                params.append('type_filter', typeFilterEl.value.trim());
            }
            if (extendedSearchEl && extendedSearchEl.checked) {
                params.append('extended_search', 'true');
            }
            // If user entered a 9-digit MMSI or enabled extended search, bypass radius filter server-side
            const isMmsiQuery = /^\d{9}$/.test(query);
            if ((extendedSearchEl && extendedSearchEl.checked) || isMmsiQuery) {
                params.append('disable_radius_filter', 'true');
            }
            const response = await fetch(`/api/vessels/search?${params.toString()}`);
            if (!response.ok) {
                throw new Error('Search failed');
            }
            
            const data = await response.json();
            this.lastSearchResult = data;

            // Enrich results with latest positions from tracking API when missing
            try {
                const shipsRes = await fetch('/api/tracking/ships');
                if (shipsRes.ok) {
                    const shipsData = await shipsRes.json();
                    const ships = Array.isArray(shipsData?.ships) ? shipsData.ships : [];
                    const byMmsi = new Map(ships.map(s => [String(s.mmsi || ''), s]));
                    (data.vessels || []).forEach(v => {
                        const mmsi = v && v.mmsi != null ? String(v.mmsi) : null;
                        if (!mmsi) return;
                        const hasPos = v.position && v.position.latitude != null && v.position.longitude != null;
                        if (hasPos) return;
                        const ship = byMmsi.get(mmsi);
                        const pos = ship && ship.current_position;
                        if (pos && pos.latitude != null && pos.longitude != null) {
                            v.position = {
                                latitude: pos.latitude,
                                longitude: pos.longitude,
                                speed: pos.speed,
                                course: pos.course,
                                timestamp: pos.timestamp
                            };
                            if (!v.last_update && ship.last_update) {
                                v.last_update = ship.last_update;
                            }
                        }
                    });
                }
            } catch (e) { /* non-fatal enrichment */ }

            this.displaySearchResults(data);

            // If AIS is still warming up and some items lack positions, retry enrichment briefly
            try {
                const status = data?.status;
                const vessels = Array.isArray(data?.vessels) ? data.vessels : [];
                const anyUnknown = vessels.some(v => !v?.position || v.position.latitude == null || v.position.longitude == null);
                if (anyUnknown && (status === 'connecting' || status === 'collecting') && this.searchEnrichmentAttempts < this.maxSearchEnrichmentAttempts) {
                    this.searchEnrichmentAttempts++;
                    setTimeout(() => this.retrySearchEnrichment(), 3000);
                } else if (!anyUnknown) {
                    this.searchEnrichmentAttempts = 0;
                }
            } catch (e) { /* ignore */ }
            
        } catch (error) {
            console.error('Vessel search error:', error);
            this.showError('Error searching vessels. Please try again.');
        } finally {
            searchBtn.innerHTML = '<i class="fas fa-search"></i> Search';
            searchBtn.disabled = false;
        }
    }
    
    displaySearchResults(result) {
        const { vessels = [], status, message, connection_info } = result || {};
        const resultsContainer = document.getElementById('vessel-search-results');
        
        if (vessels.length === 0) {
            // Show AIS loading state vs. hard no-results
            if (status === 'connecting' || status === 'collecting') {
                const info = connection_info || {};
                const line1 = message || info.message || 'Gathering AIS vessel data...';
                const line2 = info.suggestion || 'More vessels may appear shortly.';
                const wait = info.estimated_wait_seconds ? ` (~${info.estimated_wait_seconds}s)` : '';
                resultsContainer.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #0ea5e9;">
                        <i class="fas fa-satellite-dish fa-spin"></i>
                        <p><strong>${line1}${wait}</strong></p>
                        <p style="color:#6c757d;">${line2}</p>
                    </div>
                `;
            } else {
                resultsContainer.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #6c757d;">
                        <i class="fas fa-search"></i>
                        <p>${message || 'No vessels found. Try a different search term or use manual entry.'}</p>
                    </div>
                `;
            }
            resultsContainer.style.display = 'block';
            return;
        }
        
        // If we have a helpful message while results are coming in, show it above the list
        const headerMsg = message && status && status !== 'ready' 
            ? `<div style="padding:10px 12px; color:#0ea5e9;">${message}</div>` 
            : '';

        resultsContainer.innerHTML = headerMsg + vessels.map(vessel => `
            <div class="vessel-result" data-vessel='${JSON.stringify(vessel)}'>
                <div class="vessel-name">${vessel.name || 'Unknown Vessel'}</div>
                <div class="vessel-details">
                    <div><strong>MMSI:</strong> ${vessel.mmsi || 'N/A'}</div>
                    <div><strong>IMO:</strong> ${vessel.imo || 'N/A'}</div>
                    <div><strong>Type:</strong> ${vessel.vessel_type_text || this.formatVesselType(vessel.vessel_type) || 'Unknown'}</div>
                    <div><strong>Length:</strong> ${vessel.length ? vessel.length + 'm' : 'N/A'}</div>
                    <div><strong>Destination:</strong> ${vessel.destination || 'N/A'}</div>
                    <div><strong>Status:</strong> ${this.formatNavigationStatus(vessel.status) || 'Unknown'}</div>
                    <div><strong>Position:</strong> ${vessel.position && vessel.position.latitude != null && vessel.position.longitude != null 
                        ? `${Number(vessel.position.latitude).toFixed(4)}, ${Number(vessel.position.longitude).toFixed(4)}` 
                        : 'Unknown'}</div>
                    <div><strong>Speed/Course:</strong> ${vessel.position && vessel.position.speed != null ? Number(vessel.position.speed).toFixed(1) + ' kn' : '—'}
                        ${vessel.position && vessel.position.course != null ? ' | ' + Math.round(Number(vessel.position.course)) + '°' : ''}
                    </div>
                    ${vessel.last_update || (vessel.position && vessel.position.timestamp) ? 
                        `<div><strong>Last update:</strong> ${new Date(vessel.last_update || vessel.position.timestamp).toLocaleString('nl-NL')}</div>` : ''}
                </div>
            </div>
        `).join('');
        
        // Bind click events
        resultsContainer.querySelectorAll('.vessel-result').forEach(result => {
            result.addEventListener('click', () => {
                const vesselData = JSON.parse(result.dataset.vessel);
                this.selectVessel(vesselData);
            });
        });
        
        resultsContainer.style.display = 'block';
    }

    async retrySearchEnrichment() {
        try {
            const resultsContainer = document.getElementById('vessel-search-results');
            if (!this.lastSearchResult || !resultsContainer || resultsContainer.style.display === 'none') {
                this.searchEnrichmentAttempts = 0;
                return;
            }
            const shipsRes = await fetch('/api/tracking/ships');
            if (!shipsRes.ok) return;
            const shipsData = await shipsRes.json();
            const ships = Array.isArray(shipsData?.ships) ? shipsData.ships : [];
            const byMmsi = new Map(ships.map(s => [String(s.mmsi || ''), s]));

            let updated = false;
            (this.lastSearchResult.vessels || []).forEach(v => {
                const mmsi = v && v.mmsi != null ? String(v.mmsi) : null;
                if (!mmsi) return;
                const hasPos = v.position && v.position.latitude != null && v.position.longitude != null;
                if (hasPos) return;
                const ship = byMmsi.get(mmsi);
                const pos = ship && ship.current_position;
                if (pos && pos.latitude != null && pos.longitude != null) {
                    v.position = {
                        latitude: pos.latitude,
                        longitude: pos.longitude,
                        speed: pos.speed,
                        course: pos.course,
                        timestamp: pos.timestamp
                    };
                    if (!v.last_update && ship.last_update) {
                        v.last_update = ship.last_update;
                    }
                    updated = true;
                }
            });

            if (updated) {
                this.displaySearchResults(this.lastSearchResult);
                this.searchEnrichmentAttempts = 0;
            } else if (this.searchEnrichmentAttempts < this.maxSearchEnrichmentAttempts) {
                this.searchEnrichmentAttempts++;
                setTimeout(() => this.retrySearchEnrichment(), 3000);
            }
        } catch (e) {
            // Give up quietly on errors
            this.searchEnrichmentAttempts = 0;
        }
    }
    
    selectVessel(vessel) {
        this.selectedVessel = vessel;
        
        // Update UI to show selected vessel
        this.showSelectedVessel(vessel);
        
        // Auto-populate form fields
        this.populateVesselForm(vessel);

        // Store identifiers for submission
        const mmsiField = document.getElementById('vessel-mmsi');
        const imoField = document.getElementById('vessel-imo');
        if (mmsiField) mmsiField.value = vessel.mmsi || '';
        if (imoField) imoField.value = vessel.imo || '';
        
        // Hide search results
        document.getElementById('vessel-search-results').style.display = 'none';
        
        // Show vessel details form
        const vesselForm = document.getElementById('vessel-details-form');
        if (vesselForm) {
            vesselForm.style.display = 'block';
        }
        
        // Trigger map refresh to ensure selected MMSI is highlighted/visible
        try {
            if (typeof window.refreshNominationMap === 'function') {
                window.refreshNominationMap();
            }
        } catch (e) { /* ignore */ }
        
        console.log('Selected vessel:', vessel);
    }
    
    showSelectedVessel(vessel) {
        const searchContainer = document.querySelector('.vessel-search-section');
        
        // Remove any existing selected vessel info
        const existingInfo = searchContainer.querySelector('.selected-vessel-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        // Create selected vessel display
        const selectedInfo = document.createElement('div');
        selectedInfo.className = 'selected-vessel-info';
        selectedInfo.innerHTML = `
            <h5><i class="fas fa-check-circle"></i> Selected Vessel: ${vessel.name || 'Unknown'}</h5>
            <div class="selected-vessel-details">
                <div><strong>MMSI:</strong> ${vessel.mmsi || 'N/A'}</div>
                <div><strong>IMO:</strong> ${vessel.imo || 'N/A'}</div>
                <div><strong>Type:</strong> ${vessel.vessel_type_text || this.formatVesselType(vessel.vessel_type) || 'Unknown'}</div>
                <div><strong>Length:</strong> ${vessel.length ? vessel.length + 'm' : 'N/A'}</div>
                <div><strong>Beam:</strong> ${vessel.beam ? vessel.beam + 'm' : 'N/A'}</div>
                <div><strong>Draft:</strong> ${vessel.draft ? vessel.draft + 'm' : 'N/A'}</div>
                <div><strong>Position:</strong> ${vessel.position && vessel.position.latitude != null && vessel.position.longitude != null 
                    ? `${Number(vessel.position.latitude).toFixed(4)}, ${Number(vessel.position.longitude).toFixed(4)}` 
                    : 'Unknown'}</div>
                <div><strong>Speed/Course:</strong> ${vessel.position && vessel.position.speed != null ? Number(vessel.position.speed).toFixed(1) + ' kn' : '—'}
                    ${vessel.position && vessel.position.course != null ? ' | ' + Math.round(Number(vessel.position.course)) + '°' : ''}
                </div>
                ${vessel.last_update || (vessel.position && vessel.position.timestamp) ? 
                    `<div><strong>Last update:</strong> ${new Date(vessel.last_update || vessel.position.timestamp).toLocaleString('nl-NL')}</div>` : ''}
            </div>
            <button type="button" class="btn btn-sm btn-outline" onclick="nominationForm.clearSelectedVessel()">
                <i class="fas fa-times"></i> Clear Selection
            </button>
        `;
        
        searchContainer.appendChild(selectedInfo);
    }
    
    // Rough deadweight estimator for when AIS lacks DWT
    // Uses DWT ≈ C * L * B * T where C depends on vessel class
    // C ~ 0.35 for seagoing tankers; ~0.20 for inland tankers/barges
    estimateDeadweightTonnes(vessel) {
        const length = parseFloat(vessel.length);
        const beam = parseFloat(vessel.beam || vessel.width);
        const draft = parseFloat(vessel.draft);
        if (!isFinite(length) || !isFinite(beam) || !isFinite(draft)) return null;
        const t = String(vessel.vessel_type_text || vessel.vessel_type || '').toLowerCase();
        const isInland = t.includes('inland') || t.includes('barge');
        const coeff = isInland ? 0.20 : 0.35;
        const estimate = coeff * length * beam * draft;
        // Clamp to reasonable ranges to avoid wild values
        if (isInland) {
            return Math.max(500, Math.min(20000, Math.round(estimate / 10) * 10));
        } else {
            return Math.max(1000, Math.min(300000, Math.round(estimate / 100) * 100));
        }
    }

    populateVesselForm(vessel) {
        // Populate form fields with vessel data
        const fieldMappings = {
            'vessel-name': vessel.name,
            'vessel-length': vessel.length,
            'vessel-beam': vessel.beam,
            'vessel-draft': vessel.draft,
            // Do not auto-map destination to customer; only use provided customer field
            'vessel-customer': vessel.customer
        };
        
        Object.entries(fieldMappings).forEach(([fieldId, value]) => {
            const field = document.getElementById(fieldId);
            if (field && value) {
                field.value = value;
                field.dispatchEvent(new Event('input')); // Trigger validation
            }
        });
        
        // Deadweight: set only if provided by the selected vessel (> 0); otherwise estimate/clear
        const dwtField = document.getElementById('vessel-dwt');
        if (dwtField) {
            const dwtNumeric = vessel.deadweight != null && vessel.deadweight !== '' ? Number(vessel.deadweight) : null;
            if (dwtNumeric != null && isFinite(dwtNumeric) && dwtNumeric > 0) {
                dwtField.value = dwtNumeric;
            } else {
                // Try to estimate DWT from geometry if available
                const estimate = this.estimateDeadweightTonnes(vessel);
                if (estimate != null) {
                    dwtField.value = estimate;
                    const notesField = document.getElementById('vessel-notes');
                    if (notesField) {
                        const prefix = notesField.value ? notesField.value + '\n' : '';
                        notesField.value = `${prefix}Deadweight estimated from AIS geometry (adjust if known).`;
                    }
                } else {
                    dwtField.value = '';
                }
            }
            // Ensure validation/UI updates after programmatic change
            try { dwtField.dispatchEvent(new Event('input')); } catch (e) {}
        }
        
        // Set vessel type using AIS-to-internal mapping heuristics
        const vesselTypeField = document.getElementById('vessel-type');
        if (vesselTypeField) {
            const mappedType = this.mapVesselType(vessel);
            if (mappedType) {
                vesselTypeField.value = mappedType;
                vesselTypeField.dispatchEvent(new Event('change'));
            }
        }
        
        // Try to parse and set ETA if available
        if (vessel.eta) {
            const etaField = document.getElementById('vessel-eta');
            if (etaField) {
                try {
                    const etaDate = new Date(vessel.eta);
                    if (!isNaN(etaDate.getTime())) {
                        etaField.value = etaDate.toISOString().slice(0, 16);
                    }
                } catch (e) {
                    console.warn('Could not parse ETA:', vessel.eta);
                }
            }
        }
        
        // Add note about AIS data source
        const notesField = document.getElementById('vessel-notes');
        if (notesField && !notesField.value) {
            notesField.value = `Vessel data auto-populated from AIS Stream (MMSI: ${vessel.mmsi})`;
        }

        // Final pass: refresh navigation state so Next/Submit reflects autofilled values
        try { this.updateStepNavigation(); } catch (e) {}
    }
    
    clearSelectedVessel() {
        this.selectedVessel = null;
        
        // Remove selected vessel display
        const existingInfo = document.querySelector('.selected-vessel-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        // Clear form fields
        const form = document.getElementById('nomination-form');
        if (form) {
            form.reset();
            this.setDefaultETA();
            this.setDefaultValues();
        }
        
        // Hide vessel details form
        const vesselForm = document.getElementById('vessel-details-form');
        if (vesselForm) {
            vesselForm.style.display = 'none';
        }
        
        // Clear search
        document.getElementById('vessel-search').value = '';
        document.getElementById('vessel-search-results').style.display = 'none';
    }
    
    toggleManualEntry() {
        const vesselForm = document.getElementById('vessel-details-form');
        const toggle = document.getElementById('toggle-manual-entry');
        
        if (vesselForm.style.display === 'none' || !vesselForm.style.display) {
            vesselForm.style.display = 'block';
            toggle.innerHTML = '<i class="fas fa-times"></i> Hide Manual Entry';
        } else {
            vesselForm.style.display = 'none';
            toggle.innerHTML = '<i class="fas fa-edit"></i> Enter Vessel Details Manually';
        }
    }
    
    formatVesselType(type) {
        const typeMap = {
            '70': 'Cargo Ship',
            '71': 'Cargo Ship - Hazardous Category A',
            '72': 'Cargo Ship - Hazardous Category B', 
            '73': 'Cargo Ship - Hazardous Category C',
            '74': 'Cargo Ship - Hazardous Category D',
            '80': 'Tanker',
            '81': 'Tanker - Hazardous Category A',
            '82': 'Tanker - Hazardous Category B',
            '83': 'Tanker - Hazardous Category C',
            '84': 'Tanker - Hazardous Category D'
        };
        
        return typeMap[type] || type;
    }
    
    mapVesselType(vessel) {
        // Map AIS vessel data to internal types ('tanker' | 'barge') using heuristics
        if (!vessel) return 'tanker';

        const type = String(vessel.vessel_type || '');
        const name = String(vessel.name || '');
        const length = Number(vessel.length || 0);
        const beam = Number(vessel.beam || 0);

        // Strong name-based hints for barges across languages
        const bargeNameRegex = /(barge|pontoon|ponton|pusher|push|duw|duwbak|lighter)/i;
        if (bargeNameRegex.test(name)) {
            return 'barge';
        }

        // AIS type heuristics
        if (type.startsWith('8')) {
            // 80-89: Tankers
            return 'tanker';
        }
        if (type.startsWith('7')) {
            // 70-79: Cargo. Many inland/canal cargo units are barges; use dimensions to infer
            if ((length && length <= 120) && (beam && beam <= 16)) {
                return 'barge';
            }
            return 'tanker';
        }

        // Fallbacks: classify very small/skinny craft as barge
        if ((length && length <= 110) && (beam && beam <= 12)) {
            return 'barge';
        }

        return 'tanker'; // Default for terminal context
    }
    
    formatNavigationStatus(status) {
        const statusMap = {
            '0': 'Under way using engine',
            '1': 'At anchor',
            '2': 'Not under command',
            '3': 'Restricted manoeuvrability',
            '4': 'Constrained by her draught',
            '5': 'Moored',
            '6': 'Aground',
            '7': 'Engaged in fishing',
            '8': 'Under way sailing',
            '15': 'Undefined'
        };
        
        return statusMap[status] || status;
    }
    
    bindModelUploadEvents() {
        const uploadArea = document.getElementById('model-upload-area');
        const fileInput = document.getElementById('model-file-input');
        const uploadLink = uploadArea?.querySelector('.upload-link');
        
        if (!uploadArea || !fileInput || !uploadLink) {
            console.error('Model upload elements not found');
            return;
        }
        
        // File input change event
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleModelUpload(e.target.files[0]);
            }
        });
        
        // Upload link click
        uploadLink.addEventListener('click', (e) => {
            e.preventDefault();
            fileInput.click();
        });
        
        // Drag and drop events
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleModelUpload(files[0]);
            }
        });
    }
    
    async handleModelUpload(file) {
        // Validate file type
        if (!file.name.endsWith('.zip') && !file.name.endsWith('.joblib')) {
            this.showUploadResult('error', 'Invalid file type', 'Only .zip and .joblib files are supported.');
            return;
        }
        
        // Validate file size (50MB limit)
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (file.size > maxSize) {
            this.showUploadResult('error', 'File too large', 'File size must be less than 50MB.');
            return;
        }
        
        console.log('Uploading model file:', file.name);
        
        // Show upload progress
        this.showUploadProgress(true);
        
        try {
            const formData = new FormData();
            formData.append('file', file);
            
            const response = await fetch('/api/ml/upload-model', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (response.ok && result.success) {
                this.showUploadResult('success', 'Upload successful', result.message, result.models_found);
                console.log('Model uploaded successfully:', result);
                
                // Refresh the model list to show newly uploaded models
                setTimeout(() => {
                    this.refreshModelList();
                }, 500);
                
                // Reset prediction state for new model
                if (this.currentStep === 3) {
                    this.predictionsCalculated = false;
                    this.updatePredictionButtonState('ready');
                }
            } else {
                this.showUploadResult('error', 'Upload failed', result.message || 'Unknown error occurred');
            }
            
        } catch (error) {
            console.error('Model upload error:', error);
            this.showUploadResult('error', 'Upload failed', 'Network error occurred. Please try again.');
        } finally {
            this.showUploadProgress(false);
        }
    }
    
    showUploadProgress(show) {
        const uploadProgress = document.getElementById('upload-progress');
        const uploadResult = document.getElementById('upload-result');
        
        if (uploadProgress) {
            uploadProgress.style.display = show ? 'block' : 'none';
            
            if (show) {
                // Animate progress bar
                const progressFill = uploadProgress.querySelector('.progress-fill');
                const progressText = uploadProgress.querySelector('.progress-text');
                
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 10;
                    if (progress > 90) progress = 90; // Don't complete until actual upload is done
                    
                    if (progressFill) progressFill.style.width = `${progress}%`;
                    if (progressText) progressText.textContent = `Uploading... ${Math.round(progress)}%`;
                    
                    if (!show) {
                        clearInterval(interval);
                        if (progressFill) progressFill.style.width = '100%';
                        if (progressText) progressText.textContent = 'Upload complete';
                    }
                }, 100);
                
                // Store interval for cleanup
                this.uploadProgressInterval = interval;
            } else {
                // Clean up progress animation
                if (this.uploadProgressInterval) {
                    clearInterval(this.uploadProgressInterval);
                    this.uploadProgressInterval = null;
                }
            }
        }
        
        if (uploadResult && show) {
            uploadResult.style.display = 'none';
        }
    }
    
    showUploadResult(type, title, message, modelsList = null) {
        const uploadResult = document.getElementById('upload-result');
        if (!uploadResult) return;
        
        uploadResult.className = `upload-result ${type}`;
        
        let content = `<h5><i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i> ${title}</h5>`;
        content += `<p>${message}</p>`;
        
        if (modelsList && modelsList.length > 0) {
            content += '<p>Extracted models:</p><ul>';
            modelsList.forEach(model => {
                content += `<li>${model}</li>`;
            });
            content += '</ul>';
        }
        
        uploadResult.innerHTML = content;
        uploadResult.style.display = 'block';
        
        // Auto-hide success messages after 10 seconds
        if (type === 'success') {
            setTimeout(() => {
                uploadResult.style.display = 'none';
            }, 10000);
        }
    }
    
    async loadAvailableModels() {
        console.log('Loading available ML models...');
        
        const modelSelect = document.getElementById('model-select');
        if (!modelSelect) {
            console.error('Model select element not found');
            return;
        }
        
        try {
            const response = await fetch('/api/ml/available-models');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            if (result.success && result.models) {
                this.populateModelDropdown(result.models);
                console.log(`Loaded ${result.models.length} available models`);
            } else {
                console.error('Failed to load models:', result);
                this.showModelLoadError();
            }
            
        } catch (error) {
            console.error('Error loading available models:', error);
            this.showModelLoadError();
        }
    }
    
    populateModelDropdown(models) {
        const modelSelect = document.getElementById('model-select');
        if (!modelSelect) return;
        
        // Clear existing options
        modelSelect.innerHTML = '';
        
        // Add placeholder option
        const placeholder = document.createElement('option');
        placeholder.value = '';
        placeholder.textContent = 'Select a prediction model...';
        placeholder.disabled = true;
        placeholder.selected = true;
        modelSelect.appendChild(placeholder);
        
        // Group models by loading status
        const loadedModels = models.filter(m => m.loaded);
        const unloadedModels = models.filter(m => !m.loaded);
        
        // Add loaded models first
        if (loadedModels.length > 0) {
            const loadedGroup = document.createElement('optgroup');
            loadedGroup.label = '✓ Loaded Models (Ready)';
            
            loadedModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                // Show model name with type, highlight most recent
                const typeText = model.type !== 'unknown' ? ` (${model.type})` : '';
                const recentText = model.is_most_recent ? ' ⭐' : '';
                option.textContent = `${model.display_name}${typeText}${recentText}`;
                option.dataset.modelType = model.type;
                option.dataset.filename = model.filename;
                option.dataset.description = model.description;
                option.dataset.loaded = 'true';
                option.dataset.isMostRecent = model.is_most_recent ? 'true' : 'false';
                loadedGroup.appendChild(option);
            });
            
            modelSelect.appendChild(loadedGroup);
        }
        
        // Add unloaded models
        if (unloadedModels.length > 0) {
            const unloadedGroup = document.createElement('optgroup');
            unloadedGroup.label = '⚠ Uploaded Models (Not Loaded)';
            
            unloadedModels.forEach(model => {
                const option = document.createElement('option');
                option.value = model.name;
                option.textContent = `${model.display_name} (${model.type})`;
                option.dataset.modelType = model.type;
                option.dataset.filename = model.filename;
                option.dataset.description = model.description;
                option.dataset.loaded = 'false';
                option.disabled = true; // Disable unloaded models
                unloadedGroup.appendChild(option);
            });
            
            modelSelect.appendChild(unloadedGroup);
        }
        
        // Set default selection to most recent model if available, otherwise first loaded model
        if (loadedModels.length > 0) {
            const mostRecentModel = loadedModels.find(m => m.is_most_recent) || loadedModels[0];
            modelSelect.value = mostRecentModel.name;
            this.updateModelDetails(mostRecentModel);
        }
    }
    
    showModelLoadError() {
        const modelSelect = document.getElementById('model-select');
        if (!modelSelect) return;
        
        modelSelect.innerHTML = '';
        const errorOption = document.createElement('option');
        errorOption.value = '';
        errorOption.textContent = 'Error loading models - using default';
        errorOption.selected = true;
        modelSelect.appendChild(errorOption);
    }
    
    bindModelSelectionEvents() {
        const modelSelect = document.getElementById('model-select');
        if (!modelSelect) {
            console.error('Model select element not found');
            return;
        }
        
        modelSelect.addEventListener('change', (e) => {
            const selectedOption = e.target.selectedOptions[0];
            if (selectedOption && selectedOption.value) {
                const modelData = {
                    name: selectedOption.value,
                    display_name: selectedOption.textContent.split(' (')[0],
                    type: selectedOption.dataset.modelType,
                    filename: selectedOption.dataset.filename,
                    description: selectedOption.dataset.description,
                    loaded: selectedOption.dataset.loaded === 'true'
                };
                
                this.updateModelDetails(modelData);
                console.log('Selected model:', modelData);
                
                // Update button state if we're on the ML step
                if (this.currentStep === 3) {
                    this.onModelSelectionChange();
                }
            } else {
                this.hideModelDetails();
            }
        });
    }
    
    updateModelDetails(modelData) {
        const detailsPanel = document.getElementById('model-details');
        if (!detailsPanel) return;
        
        // Update detail fields
        document.getElementById('selected-model-type').textContent = modelData.type || 'Unknown';
        document.getElementById('selected-model-filename').textContent = modelData.filename || 'Unknown';
        document.getElementById('selected-model-description').textContent = modelData.description || 'No description available';
        
        // Show the details panel with animation
        detailsPanel.style.display = 'block';
        // Force reflow to ensure the display change is applied
        detailsPanel.offsetHeight;
        detailsPanel.classList.add('show');
    }
    
    hideModelDetails() {
        const detailsPanel = document.getElementById('model-details');
        if (!detailsPanel) return;
        
        detailsPanel.classList.remove('show');
        setTimeout(() => {
            if (!detailsPanel.classList.contains('show')) {
                detailsPanel.style.display = 'none';
            }
        }, 400); // Match the CSS transition duration
    }
    
    getSelectedModel() {
        const modelSelect = document.getElementById('model-select');
        if (!modelSelect || !modelSelect.value) {
            return null;
        }
        
        const selectedOption = modelSelect.selectedOptions[0];
        if (!selectedOption) return null;
        
        return {
            name: selectedOption.value,
            type: selectedOption.dataset.modelType,
            filename: selectedOption.dataset.filename,
            loaded: selectedOption.dataset.loaded === 'true'
        };
    }
    
    async refreshModelList() {
        console.log('Refreshing model list after upload...');
        await this.loadAvailableModels();
    }
}

// Initialize the nomination form when the page loads
let nominationForm; // Global reference for onclick events

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM Content Loaded - Initializing nomination form');
    try {
        nominationForm = new NominationForm();
        console.log('Nomination form initialized successfully');
    } catch (error) {
        console.error('Error initializing nomination form:', error);
    }
});
