# Database-First Architecture Proposal

## Current Problems with In-Memory State

The current application uses `global_state` dictionary for critical data storage, which causes:

1. **Data Loss**: Server restarts lose all in-memory vessels and nominations
2. **Inconsistency**: Database and memory can become out of sync
3. **Concurrency Issues**: No proper locking for multi-user access
4. **Scalability Limits**: Cannot scale horizontally
5. **Testing Complexity**: Hard to test with reliable state

## Proposed Database-First Architecture

### Phase 1: Immediate Improvements (Low Risk)

#### 1.1 Replace In-Memory Vessels with Database Storage
```python
# Instead of: state["vessels"]
# Use: db.get_vessels(terminal_id, status=["APPROACHING", "ARRIVED"])

class VesselService:
    def __init__(self, db: Database):
        self.db = db
    
    def get_available_vessels(self, terminal_id: str) -> List[VesselBase]:
        """Get vessels available for optimization from database"""
        # Get from nominations table
        nominations = self.db.get_nominations(terminal_id, status="ACTIVE")
        vessels = []
        for nom in nominations:
            vessel = self._nomination_to_vessel(nom)
            vessels.append(vessel)
        
        # Get from cancelled assignments (unscheduled vessels)
        cancelled_assignments = self.db.get_assignments(terminal_id, status="CANCELLED")
        for assignment in cancelled_assignments:
            vessel = self._assignment_to_vessel(assignment)
            vessels.append(vessel)
            
        return vessels
```

#### 1.2 Implement Proper Vessel Lifecycle Management
```python
# Instead of manipulating state["vessels"] directly
class VesselLifecycleManager:
    def nominate_vessel(self, vessel_data: dict) -> int:
        """Add vessel nomination to database"""
        return self.db.add_nomination(vessel_data)
    
    def schedule_vessel(self, vessel_id: str, assignment_data: dict) -> int:
        """Move vessel from nomination to scheduled assignment"""
        # Create assignment
        assignment_id = self.db.add_assignment(assignment_data)
        # Mark nomination as scheduled
        self.db.update_nomination(vessel_id, {"status": "SCHEDULED"})
        return assignment_id
    
    def unschedule_vessel(self, assignment_id: int) -> bool:
        """Move vessel back to available nominations"""
        assignment = self.db.get_assignment(assignment_id)
        # Cancel assignment
        self.db.update_assignment(assignment_id, {"status": "CANCELLED"})
        # Reactivate nomination if it exists
        if assignment.get('nomination_reference'):
            self.db.update_nomination(
                assignment['nomination_reference'], 
                {"status": "ACTIVE"}
            )
        return True
```

### Phase 2: Architecture Modernization (Medium Risk)

#### 2.1 Implement Repository Pattern
```python
from abc import ABC, abstractmethod
from typing import List, Optional

class VesselRepository(ABC):
    @abstractmethod
    def get_available_vessels(self, terminal_id: str) -> List[VesselBase]:
        pass
    
    @abstractmethod
    def save_vessel(self, vessel: VesselBase) -> str:
        pass

class DatabaseVesselRepository(VesselRepository):
    def __init__(self, db: Database):
        self.db = db
    
    def get_available_vessels(self, terminal_id: str) -> List[VesselBase]:
        """Single source of truth for available vessels"""
        # Combine nominations and unscheduled assignments
        vessels = []
        
        # Active nominations
        nominations = self.db.get_nominations(terminal_id, status="ACTIVE")
        vessels.extend(self._nominations_to_vessels(nominations))
        
        # Cancelled assignments (unscheduled vessels)
        cancelled = self.db.get_assignments(terminal_id, status="CANCELLED")
        vessels.extend(self._assignments_to_vessels(cancelled))
        
        return vessels
```

#### 2.2 Add Database Transaction Support
```python
from contextlib import contextmanager

class TransactionalDatabase(Database):
    @contextmanager
    def transaction(self):
        """Ensure ACID transactions for complex operations"""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def schedule_vessel_atomic(self, vessel_id: str, assignment_data: dict):
        """Atomically move vessel from nomination to assignment"""
        with self.transaction() as session:
            # Create assignment
            assignment = Assignment(**assignment_data)
            session.add(assignment)
            session.flush()  # Get ID
            
            # Update nomination status
            nomination = session.query(Nomination).filter_by(
                runtime_vessel_id=vessel_id
            ).first()
            if nomination:
                nomination.status = "SCHEDULED"
                nomination.assignment_reference = assignment.id
            
            return assignment.id
```

### Phase 3: Full Modernization (Higher Risk)

#### 3.1 Event-Driven Architecture
```python
from dataclasses import dataclass
from typing import Any
from enum import Enum

class EventType(Enum):
    VESSEL_NOMINATED = "vessel_nominated"
    VESSEL_SCHEDULED = "vessel_scheduled"
    VESSEL_UNSCHEDULED = "vessel_unscheduled"
    OPTIMIZATION_COMPLETED = "optimization_completed"

@dataclass
class DomainEvent:
    event_type: EventType
    entity_id: str
    data: dict
    timestamp: datetime

class EventBus:
    def __init__(self):
        self.handlers = {}
    
    def publish(self, event: DomainEvent):
        handlers = self.handlers.get(event.event_type, [])
        for handler in handlers:
            handler(event)
    
    def subscribe(self, event_type: EventType, handler):
        if event_type not in self.handlers:
            self.handlers[event_type] = []
        self.handlers[event_type].append(handler)

# Usage
def handle_vessel_scheduled(event: DomainEvent):
    """Update vessel status when scheduled"""
    vessel_id = event.entity_id
    db.update_nomination(vessel_id, {"status": "SCHEDULED"})

event_bus.subscribe(EventType.VESSEL_SCHEDULED, handle_vessel_scheduled)
```

#### 3.2 Caching Layer (Redis/In-Memory Cache)
```python
from functools import wraps
import json
import redis

class CacheService:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
    
    def cached(self, key_prefix: str, ttl: int = 300):
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
                
                # Try cache first
                cached_result = self.redis.get(cache_key)
                if cached_result:
                    return json.loads(cached_result)
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.redis.setex(cache_key, ttl, json.dumps(result))
                return result
            return wrapper
        return decorator

# Usage
@cache_service.cached("available_vessels", ttl=60)
def get_available_vessels(terminal_id: str) -> List[dict]:
    return vessel_repository.get_available_vessels(terminal_id)
```

## Migration Strategy

### Step 1: Immediate (This Week)
1. ✅ **Already Fixed**: Convert cancelled assignments to vessels in optimizer
2. **Add Vessel Service Layer**: Create `VesselService` class to encapsulate vessel operations
3. **Database-First Vessels**: Replace `state["vessels"]` reads with database queries

### Step 2: Short Term (Next Week)
1. **Repository Pattern**: Implement `VesselRepository` and `AssignmentRepository`
2. **Transaction Support**: Add atomic operations for vessel lifecycle
3. **Remove In-Memory Dependencies**: Phase out `global_state["vessels"]`

### Step 3: Medium Term (Next Month)
1. **Event System**: Implement domain events for vessel state changes
2. **Caching Layer**: Add Redis for performance optimization
3. **API Cleanup**: Ensure all endpoints use database-first approach

## Benefits of Database-First Architecture

1. **Data Integrity**: ACID transactions ensure consistency
2. **Persistence**: Data survives server restarts
3. **Scalability**: Can scale horizontally with proper database design
4. **Testability**: Easier to test with predictable database state
5. **Auditability**: Full audit trail of all changes
6. **Concurrency**: Proper locking and isolation levels

## Implementation Priority

**High Priority (Fix Now)**:
- Replace vessel state reads with database queries
- Implement atomic vessel scheduling/unscheduling

**Medium Priority (Next Sprint)**:
- Add repository pattern
- Remove global_state dependencies

**Low Priority (Future Enhancement)**:
- Event-driven architecture
- Advanced caching strategies

This approach will solve the consistency and persistence issues while maintaining the existing API contracts.
