"""
Terminal Configuration Models

This module defines the configuration models for different EVOS terminals
including Ghent and Terneuzen terminals with their specific characteristics.
"""

from datetime import date
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from enum import Enum


class CertificationType(str, Enum):
    """Types of terminal certifications"""
    ISO9001 = "ISO9001"
    ISO14001 = "ISO14001" 
    ISO45001 = "ISO45001"
    ISPS = "ISPS"
    ISCC = "ISCC"
    SEVESO = "SEVESO"
    ECOVADIS_SILVER = "ECOVADIS_SILVER"


class TankType(str, Enum):
    """Types of storage tanks"""
    MILD_STEEL = "mild_steel"
    STAINLESS_STEEL = "stainless_steel"
    PRESSURIZED = "pressurized"


class TankFeature(str, Enum):
    """Tank features and equipment"""
    VAPOR_TREATMENT = "vapor_treatment"
    DOME_ROOF = "dome_roof"
    INNER_FLOATING_ROOF = "inner_floating_roof"
    MIXING = "mixing"
    CIRCULATION_LINE = "circulation_line"
    OVERFILL_PROTECTION = "overfill_protection"
    LEAK_DETECTION = "leak_detection"
    NITROGEN_BLANKETING = "nitrogen_blanketing"


class OperationalService(str, Enum):
    """Operational services available at terminal"""
    BLENDING = "blending"
    CIRCULATION = "circulation"
    HEATING = "heating"
    MIXING = "mixing"
    ADDITIVATION = "additivation"
    HOMOGENIZING = "homogenizing"
    BUTANISATION = "butanisation"
    FILTRATION = "filtration"
    NITROGEN_BLANKETING = "nitrogen_blanketing"
    NITROGEN_SUPPLY = "nitrogen_supply"


class LogisticService(str, Enum):
    """Logistic services available"""
    DISCHARGING = "discharging"
    LOADING = "loading"
    SHIP_TO_SHIP = "ship_to_ship"
    TANK_TO_TANK = "tank_to_tank"
    SHUNTING = "shunting"


class AdminService(str, Enum):
    """Administrative services"""
    CUSTOMS_PROCEDURE = "customs_procedure"
    EXCISE_WAREHOUSE = "excise_warehouse"


class TankConfig(BaseModel):
    """Configuration for a storage tank"""
    id: str
    name: str
    capacity_cbm: float
    tank_type: TankType
    features: List[TankFeature] = []
    products: List[str] = []
    current_level: float = 0.0
    max_level: float = 0.95  # 95% max fill level
    
    
class BerthConfig(BaseModel):
    """Configuration for a berth (vessel or barge)"""
    id: str
    name: str
    max_length: float  # meters
    max_draft: float   # meters
    max_dwt: Optional[float] = None  # deadweight tonnage
    connected_tanks: List[str] = []
    connected_pumps: List[str] = []
    loading_arms: int = 1


class PumpConfig(BaseModel):
    """Configuration for a pump"""
    id: str
    name: str
    flow_rate_cbm_per_hour: float
    connected_tanks: List[str] = []
    connected_berths: List[str] = []
    products: List[str] = []


class TerminalConfig(BaseModel):
    """Complete terminal configuration"""
    terminal_id: str = Field(..., description="Unique terminal identifier")
    name: str = Field(..., description="Terminal display name")
    location: tuple[float, float] = Field(..., description="Latitude, Longitude")
    
    # Basic specifications
    total_capacity_cbm: float = Field(..., description="Total storage capacity in cubic meters")
    number_of_tanks: int = Field(..., description="Total number of storage tanks")
    draft_meters: float = Field(..., description="Maximum draft in meters")
    operational_since: date = Field(..., description="Year terminal became operational")
    
    # Berths
    vessel_berths: List[BerthConfig] = Field(default_factory=list)
    barge_berths: List[BerthConfig] = Field(default_factory=list)
    
    # Equipment
    tanks: List[TankConfig] = Field(default_factory=list)
    pumps: List[PumpConfig] = Field(default_factory=list)
    
    # Products and services
    products: List[str] = Field(default_factory=list, description="Products handled at terminal")
    operational_services: List[OperationalService] = Field(default_factory=list)
    logistic_services: List[LogisticService] = Field(default_factory=list)
    admin_services: List[AdminService] = Field(default_factory=list)
    
    # Certifications
    certifications: List[CertificationType] = Field(default_factory=list)
    
    # Terminal-specific settings
    timezone: str = "Europe/Brussels"
    currency: str = "EUR"
    weight_unit: str = "MT"  # Metric Tons
    volume_unit: str = "CBM"  # Cubic Meters
    
    class Config:
        """Pydantic configuration"""
        use_enum_values = True
        validate_assignment = True


class TerminalRegistry:
    """Registry for managing multiple terminal configurations"""
    
    def __init__(self):
        self._terminals: Dict[str, TerminalConfig] = {}
        self._active_terminal: Optional[str] = None
        
    def register_terminal(self, config: TerminalConfig) -> None:
        """Register a terminal configuration"""
        self._terminals[config.terminal_id] = config
        
        # Set as active if it's the first terminal
        if self._active_terminal is None:
            self._active_terminal = config.terminal_id
    
    def get_terminal(self, terminal_id: str) -> Optional[TerminalConfig]:
        """Get terminal configuration by ID"""
        return self._terminals.get(terminal_id)
    
    def get_active_terminal(self) -> Optional[TerminalConfig]:
        """Get the currently active terminal"""
        if self._active_terminal:
            return self._terminals.get(self._active_terminal)
        return None
    
    def set_active_terminal(self, terminal_id: str) -> bool:
        """Set the active terminal"""
        if terminal_id in self._terminals:
            self._active_terminal = terminal_id
            return True
        return False
    
    def list_terminals(self) -> List[TerminalConfig]:
        """List all registered terminals"""
        return list(self._terminals.values())
    
    def get_terminal_ids(self) -> List[str]:
        """Get all terminal IDs"""
        return list(self._terminals.keys())


# Create default terminal configurations
def create_ghent_terminal_config() -> TerminalConfig:
    """Create the Ghent terminal configuration (existing)"""
    return TerminalConfig(
        terminal_id="GHNT",
        name="EVOS Ghent",
        location=(51.0543, 3.7174),
        total_capacity_cbm=800000,  # Estimated based on current system
        number_of_tanks=30,  # Estimated
        draft_meters=14.0,
        operational_since=date(1990, 1, 1),  # Estimated
        vessel_berths=[
            BerthConfig(id="VB01", name="Vessel Berth 1", max_length=250, max_draft=14, max_dwt=100000),
            BerthConfig(id="VB02", name="Vessel Berth 2", max_length=250, max_draft=14, max_dwt=100000),
            BerthConfig(id="VB03", name="Vessel Berth 3", max_length=200, max_draft=12, max_dwt=80000),
        ],
        barge_berths=[
            BerthConfig(id="BB01", name="Barge Berth 1", max_length=120, max_draft=6, max_dwt=5000),
            BerthConfig(id="BB02", name="Barge Berth 2", max_length=120, max_draft=6, max_dwt=5000),
        ],
        products=["gasoline", "gasoil", "jet_fuel", "naphtha"],
        operational_services=[
            OperationalService.BLENDING,
            OperationalService.CIRCULATION,
            OperationalService.HEATING,
            OperationalService.MIXING
        ],
        logistic_services=[
            LogisticService.DISCHARGING,
            LogisticService.LOADING,
            LogisticService.SHIP_TO_SHIP,
            LogisticService.TANK_TO_TANK
        ],
        admin_services=[
            AdminService.CUSTOMS_PROCEDURE,
            AdminService.EXCISE_WAREHOUSE
        ],
        certifications=[
            CertificationType.ISO9001,
            CertificationType.ISO14001,
            CertificationType.ISPS
        ]
    )


def create_terneuzen_terminal_config() -> TerminalConfig:
    """Create the Terneuzen terminal configuration based on EVOS specs"""
    return TerminalConfig(
        terminal_id="TNZN",
        name="EVOS Terneuzen",
        location=(51.3294, 3.8091),  # Terneuzen coordinates
        total_capacity_cbm=537000,
        number_of_tanks=42,
        draft_meters=15.0,
        operational_since=date(2005, 1, 1),
        vessel_berths=[
            BerthConfig(
                id="VB01", 
                name="Vessel Berth 1", 
                max_length=280, 
                max_draft=15, 
                max_dwt=120000,
                loading_arms=2
            ),
            BerthConfig(
                id="VB02", 
                name="Vessel Berth 2", 
                max_length=280, 
                max_draft=15, 
                max_dwt=120000,
                loading_arms=2
            ),
            BerthConfig(
                id="VB03", 
                name="Vessel Berth 3", 
                max_length=250, 
                max_draft=15, 
                max_dwt=120000,
                loading_arms=1
            ),
        ],
        barge_berths=[
            BerthConfig(id="BB01", name="Barge Berth 1", max_length=120, max_draft=6, max_dwt=5000),
            BerthConfig(id="BB02", name="Barge Berth 2", max_length=120, max_draft=6, max_dwt=5000),
            BerthConfig(id="BB03", name="Barge Berth 3", max_length=110, max_draft=5.5, max_dwt=4500),
        ],
        products=[
            "bio_naphtha",
            "pyrolysis_oil", 
            "chemical_feedstocks",
            "aromatics",
            "specialty_chemicals",
            "gasoline"
        ],
        operational_services=[
            OperationalService.BLENDING,
            OperationalService.CIRCULATION,
            OperationalService.HEATING,
            OperationalService.MIXING,
            OperationalService.ADDITIVATION,
            OperationalService.HOMOGENIZING,
            OperationalService.BUTANISATION,
            OperationalService.FILTRATION,
            OperationalService.NITROGEN_BLANKETING,
            OperationalService.NITROGEN_SUPPLY
        ],
        logistic_services=[
            LogisticService.DISCHARGING,
            LogisticService.LOADING,
            LogisticService.SHIP_TO_SHIP,
            LogisticService.TANK_TO_TANK,
            LogisticService.SHUNTING
        ],
        admin_services=[
            AdminService.CUSTOMS_PROCEDURE,
            AdminService.EXCISE_WAREHOUSE
        ],
        certifications=[
            CertificationType.ISO9001,
            CertificationType.ISO14001,
            CertificationType.ISO45001,
            CertificationType.ISPS,
            CertificationType.ISCC,
            CertificationType.SEVESO,
            CertificationType.ECOVADIS_SILVER
        ]
    )


# Global terminal registry
terminal_registry = TerminalRegistry()

# Register default terminals
terminal_registry.register_terminal(create_ghent_terminal_config())
terminal_registry.register_terminal(create_terneuzen_terminal_config()) 