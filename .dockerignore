# Version control directories
.git
.github
.gitignore

# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Development and test files
tests/
test_*.py
*.test.py

# Environment and local config
.env
.env.local
.venv
env/
venv/
ENV/

# Local data and logs
*.log
data/
logs/
*.db
*.sqlite

# Docker specific
.dockerignore
Dockerfile
docker-compose*.yml

# Ensure app source and essential files are included in build context
!requirements.txt
!run.py
!alembic/**
!alembic.ini
!src/**

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.coverage
htmlcov/
