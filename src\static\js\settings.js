// Settings management
const settingsState = {
    settings: null,
    isDirty: false,
    isSaving: false,
    lastSaved: null
};

// DOM Elements
const saveButton = document.getElementById('save-settings');
const saveStatus = document.getElementById('save-status');
// Toast container is now managed by the unified toast system
const autoRefreshCheckbox = document.getElementById('auto-refresh');
const refreshIntervalContainer = document.getElementById('refresh-interval-container');

// Initialize settings
document.addEventListener('DOMContentLoaded', async () => {
    await loadSettings();
    setupEventListeners();
    updateAutoRefreshVisibility();
    updateEmailSettingsVisibility();
    // Note: Optimization presets setup moved to optimize page
});

// Load settings from server with localStorage fallback
async function loadSettings() {
    try {
        const response = await fetch('/api/settings');
        if (!response.ok) throw new Error('Failed to fetch settings from server');
        const raw = await response.json();
        // Convert array of settings into key-value object if needed
        if (Array.isArray(raw)) {
            const obj = {};
            raw.forEach(item => {
                if (item && item.key !== undefined) {
                    obj[item.key] = coerceSettingValue(item.value);
                }
            });
            settingsState.settings = obj;
        } else {
            settingsState.settings = raw;
        }
    } catch (error) {
        console.warn('Failed to load settings from server, falling back to localStorage:', error);
        settingsState.settings = loadFromLocalStorage() || getDefaultSettings();
    }
    
    applySettings(settingsState.settings);
}

// Save settings to both server and localStorage
async function saveSettings() {
    if (!settingsState.isDirty || settingsState.isSaving) return;
    
    settingsState.isSaving = true;
    updateSaveStatus('Saving...');
    saveButton.classList.add('saving');
    
    const settings = gatherCurrentSettings();
    
    try {
        const response = await fetch('/api/settings', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ settings })
        });
        
        if (!response.ok) throw new Error('Failed to save settings to server');
        
        // Save to localStorage as backup
        saveToLocalStorage(settings);
        
        settingsState.settings = settings;
        settingsState.isDirty = false;
        settingsState.lastSaved = new Date();
        
        showToast('Settings saved successfully', 'success');
        updateSaveStatus('All changes saved');
    } catch (error) {
        console.error('Failed to save settings:', error);
        // Save to localStorage as fallback
        saveToLocalStorage(settings);
        showToast('Settings saved locally (offline mode)', 'warning');
        updateSaveStatus('Saved locally');
    } finally {
        settingsState.isSaving = false;
        saveButton.classList.remove('saving');
    }
}

// Gather current settings from form elements
function gatherCurrentSettings() {
    const settings = {};
    
    // General settings
    document.querySelectorAll('[data-setting]').forEach(element => {
        const key = element.dataset.setting;
        if (element.type === 'checkbox') {
            settings[key] = element.checked;
        } else if (element.type === 'number') {
            settings[key] = parseInt(element.value, 10);
        } else {
            settings[key] = element.value;
        }
    });
    
    // Weather settings
    settings.weather_api_provider = document.getElementById('weather-api-provider').value;
    settings.weather_display_units = document.getElementById('weather-display-units').value;
    settings.weather_refresh_interval = parseInt(document.getElementById('weather-refresh-interval').value, 10);
    settings.show_wind_alerts = document.getElementById('show-wind-alerts').checked;
    settings.wind_caution_threshold = parseInt(document.getElementById('wind-caution-threshold').value, 10);
    settings.wind_danger_threshold = parseInt(document.getElementById('wind-danger-threshold').value, 10);
    settings.show_12h_forecast = document.getElementById('show-12h-forecast').checked;
    
    // Notification settings
    settings.enable_email_notifications = document.getElementById('enable-email-notifications').checked;
    settings.email_recipients = document.getElementById('email-recipients').value;
    settings.notification_events = Array.from(document.querySelectorAll('input[name="notification-event"]:checked'))
        .map(checkbox => checkbox.value);
    
    // OR-Tools settings
    settings.solver_time_limit = parseInt(document.getElementById('solver-time-limit').value, 10);
    settings.solver_strategy = document.getElementById('solver-strategy').value;
    settings.parallel_solving = document.getElementById('parallel-solving').checked;
    
    // Assistant settings
    settings.claude_api_key = document.getElementById('claude-api-key').value;
    settings.claude_model = document.getElementById('claude-model').value;
    settings.use_mock_responses = document.getElementById('use-mock-responses').checked;
    
    // AIS settings
    const aisRadiusEl = document.getElementById('ais-radius-km');
    if (aisRadiusEl) {
        settings.ais_radius_km = parseInt(aisRadiusEl.value, 10);
    }
    const aisPreloadEl = document.getElementById('ais-preload');
    if (aisPreloadEl) {
        settings.ais_preload = aisPreloadEl.checked;
    }
    
    return settings;
}

// Apply settings to form elements
function applySettings(settings) {
    // General settings
    Object.entries(settings).forEach(([key, value]) => {
        const element = document.querySelector(`[data-setting="${key}"]`);
        if (!element) return;
        
        if (element.type === 'checkbox') {
            element.checked = value;
        } else {
            element.value = value;
        }
    });
    
    // Weather settings
    if (settings.weather_api_provider) document.getElementById('weather-api-provider').value = settings.weather_api_provider;
    if (settings.weather_display_units) document.getElementById('weather-display-units').value = settings.weather_display_units;
    if (settings.weather_refresh_interval) document.getElementById('weather-refresh-interval').value = settings.weather_refresh_interval;
    if (settings.show_wind_alerts !== undefined) document.getElementById('show-wind-alerts').checked = settings.show_wind_alerts;
    if (settings.wind_caution_threshold) document.getElementById('wind-caution-threshold').value = settings.wind_caution_threshold;
    if (settings.wind_danger_threshold) document.getElementById('wind-danger-threshold').value = settings.wind_danger_threshold;
    if (settings.show_12h_forecast !== undefined) document.getElementById('show-12h-forecast').checked = settings.show_12h_forecast;
    
    // Notification settings
    if (settings.enable_email_notifications !== undefined) {
        document.getElementById('enable-email-notifications').checked = settings.enable_email_notifications;
        updateEmailSettingsVisibility();
    }
    if (settings.email_recipients) document.getElementById('email-recipients').value = settings.email_recipients;
    if (settings.notification_events) {
        document.querySelectorAll('input[name="notification-event"]').forEach(checkbox => {
            checkbox.checked = settings.notification_events.includes(checkbox.value);
        });
    }
    
    // OR-Tools settings
    if (settings.solver_time_limit) document.getElementById('solver-time-limit').value = settings.solver_time_limit;
    if (settings.solver_strategy) document.getElementById('solver-strategy').value = settings.solver_strategy;
    if (settings.parallel_solving !== undefined) document.getElementById('parallel-solving').checked = settings.parallel_solving;
    
    // Assistant settings
    if (settings.claude_api_key) document.getElementById('claude-api-key').value = settings.claude_api_key;
    if (settings.claude_model) document.getElementById('claude-model').value = settings.claude_model;
    if (settings.use_mock_responses !== undefined) document.getElementById('use-mock-responses').checked = settings.use_mock_responses;
    
    // AIS settings
    if (document.getElementById('ais-radius-km') && settings.ais_radius_km !== undefined) {
        document.getElementById('ais-radius-km').value = settings.ais_radius_km;
    }
    if (document.getElementById('ais-preload') && settings.ais_preload !== undefined) {
        document.getElementById('ais-preload').checked = settings.ais_preload;
    }
}

// Local storage functions
function saveToLocalStorage(settings) {
    try {
        localStorage.setItem('terminalSettings', JSON.stringify(settings));
    } catch (error) {
        console.error('Failed to save to localStorage:', error);
    }
}

function loadFromLocalStorage() {
    try {
        const settings = localStorage.getItem('terminalSettings');
        return settings ? JSON.parse(settings) : null;
    } catch (error) {
        console.error('Failed to load from localStorage:', error);
        return null;
    }
}

// Default settings
function getDefaultSettings() {
    return {
        // General settings
        terminal_name: '',
        terminal_lat: '',
        terminal_lon: '',
        time_zone: 'Europe/Brussels',
        date_format: 'DD-MM-YYYY',
        language: 'nl-NL',
        dark_mode: false,
        auto_refresh: true,
        refresh_interval: 30,
        aisstream_api_key: '',
        ais_radius_km: 100,
        ais_preload: true,
        
        // Weather settings
        weather_api_provider: 'openmeteo',
        weather_display_units: 'metric',
        weather_refresh_interval: 15,
        show_wind_alerts: true,
        wind_caution_threshold: 12,
        wind_danger_threshold: 17,
        show_12h_forecast: true,
        
        // Notification settings
        enable_email_notifications: false,
        email_recipients: '',
        notification_events: ['optimization-complete', 'vessel-arrival', 'schedule-conflict', 'weather-alert'],
        
        // OR-Tools settings
        solver_time_limit: 60,
        solver_strategy: 'AUTOMATIC',
        parallel_solving: true,
        
        // Assistant settings
        claude_api_key: '',
        claude_model: 'claude-3-5-sonnet-20240620',
        use_mock_responses: false
    };
}

// Helper to coerce stored string values into proper types
function coerceSettingValue(value) {
    if (value === 'true') return true;
    if (value === 'false') return false;
    if (value !== null && value !== undefined && value !== '' && !isNaN(value)) {
        return Number(value);
    }
    return value;
}

// Event listeners setup
function setupEventListeners() {
    // Save button
    saveButton.addEventListener('click', () => saveSettings());
    
    // Track changes
    document.querySelectorAll('input, select, textarea').forEach(element => {
        element.addEventListener('change', () => {
            settingsState.isDirty = true;
            updateSaveStatus('Unsaved changes');
        });
    });
    
    // Auto-refresh checkbox
    autoRefreshCheckbox.addEventListener('change', updateAutoRefreshVisibility);
    
    // Email notifications checkbox
    document.getElementById('enable-email-notifications').addEventListener('change', updateEmailSettingsVisibility);
    
    // Password visibility toggles
    document.querySelectorAll('.toggle-password').forEach(button => {
        button.addEventListener('click', (e) => {
            const input = e.target.closest('.api-key-input').querySelector('input');
            const icon = e.target.closest('.toggle-password').querySelector('i');
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });
    
    // API connection tests
    document.querySelectorAll('.test-connection').forEach(button => {
        button.addEventListener('click', async (e) => {
            const api = e.target.dataset.api;
            const statusElement = document.getElementById(`${api}-status`);
            
            try {
                statusElement.textContent = 'Testing...';
                const response = await fetch(`/api/test-connection/${api}`);
                if (!response.ok) throw new Error('Connection failed');
                
                const result = await response.json();
                statusElement.textContent = result.success ? 'Connected' : 'Failed';
                statusElement.className = `connection-status ${result.success ? 'success' : 'error'}`;
            } catch (error) {
                statusElement.textContent = 'Connection failed';
                statusElement.className = 'connection-status error';
            }
        });
    });
}

// UI update functions
function updateAutoRefreshVisibility() {
    refreshIntervalContainer.style.display = 
        autoRefreshCheckbox.checked ? 'block' : 'none';
}

function updateEmailSettingsVisibility() {
    const emailSettings = document.getElementById('email-settings');
    const enableEmailNotifications = document.getElementById('enable-email-notifications');
    emailSettings.style.display = enableEmailNotifications.checked ? 'block' : 'none';
}

function updateSaveStatus(message) {
    saveStatus.textContent = message;
    saveStatus.className = `save-status ${settingsState.isDirty ? 'unsaved' : 'saved'}`;
}

// Toast functionality is now handled by the unified toast system
// showToast, showSuccessToast, showErrorToast, showInfoToast, showWarningToast are available globally

// Auto-save functionality (optional, uncomment if needed)
/*
let autoSaveTimeout;
function setupAutoSave() {
    document.querySelectorAll('[data-setting]').forEach(element => {
        element.addEventListener('change', () => {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => saveSettings(), 2000);
        });
    });
}
*/

// Note: Optimization presets functionality has been moved to the optimize page
// The optimization presets configuration is now handled in optimize.html and its associated JavaScript 