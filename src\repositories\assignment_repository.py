"""
Assignment Repository - Database Access Layer

Implements the Repository pattern for assignment data access,
providing a clean abstraction over database operations.
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime
import logging

from src.database import Database

logger = logging.getLogger(__name__)


class AssignmentRepository(ABC):
    """Abstract base class for assignment data access."""
    
    @abstractmethod
    def get_assignments(self, terminal_id: str, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get assignments, optionally filtered by status."""
        pass
    
    @abstractmethod
    def get_assignment_by_id(self, assignment_id: int, terminal_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific assignment by ID."""
        pass
    
    @abstractmethod
    def create_assignment(self, assignment_data: Dict[str, Any], terminal_id: str) -> int:
        """Create a new assignment."""
        pass
    
    @abstractmethod
    def update_assignment(self, assignment_id: int, updates: Dict[str, Any]) -> bool:
        """Update an existing assignment."""
        pass
    
    @abstractmethod
    def delete_assignment(self, assignment_id: int) -> bool:
        """Delete an assignment."""
        pass
    
    @abstractmethod
    def get_assignments_by_vessel(self, vessel_id: str, terminal_id: str) -> List[Dict[str, Any]]:
        """Get all assignments for a specific vessel."""
        pass
    
    @abstractmethod
    def get_assignments_by_jetty(self, jetty_name: str, terminal_id: str) -> List[Dict[str, Any]]:
        """Get all assignments for a specific jetty."""
        pass
    
    @abstractmethod
    def get_assignments_by_date_range(self, start_date: datetime, end_date: datetime, terminal_id: str) -> List[Dict[str, Any]]:
        """Get assignments within a date range."""
        pass


class DatabaseAssignmentRepository(AssignmentRepository):
    """Database implementation of assignment repository."""
    
    def __init__(self, database: Database):
        self.db = database
    
    def get_assignments(self, terminal_id: str, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get assignments from database, optionally filtered by status."""
        try:
            assignments = self.db.get_assignments(terminal_id) or []
            
            if status:
                status_upper = status.upper()
                assignments = [a for a in assignments if a.get('status', '').upper() == status_upper]
            
            logger.debug(f"Repository found {len(assignments)} assignments for terminal {terminal_id}")
            return assignments
            
        except Exception as e:
            logger.error(f"Error getting assignments from repository: {e}")
            return []
    
    def get_assignment_by_id(self, assignment_id: int, terminal_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific assignment by ID."""
        try:
            assignment = self.db.get_assignment(assignment_id, terminal_id)
            if assignment:
                logger.debug(f"Repository found assignment {assignment_id}")
            return assignment
            
        except Exception as e:
            logger.error(f"Error getting assignment {assignment_id} from repository: {e}")
            return None
    
    def create_assignment(self, assignment_data: Dict[str, Any], terminal_id: str) -> int:
        """Create a new assignment in database."""
        try:
            assignment_id = self.db.add_assignment({
                **assignment_data,
                'terminal_id': terminal_id
            })
            
            logger.info(f"Repository created assignment {assignment_id}")
            
            # Log creation for analytics
            try:
                self.db.log_assignment_change(
                    assignment_id=assignment_id,
                    old_start_time=None,
                    old_end_time=None,
                    new_start_time=assignment_data.get('start_time'),
                    new_end_time=assignment_data.get('end_time'),
                    reason="Assignment created via repository",
                    vessel_id=assignment_data.get('vessel_id'),
                    vessel_name=assignment_data.get('vessel_name'),
                    jetty_name=assignment_data.get('jetty_name'),
                    changed_by="system",
                    terminal_id=terminal_id
                )
            except Exception as log_e:
                logger.warning(f"Failed to log assignment creation: {log_e}")
            
            return assignment_id
            
        except Exception as e:
            logger.error(f"Error creating assignment in repository: {e}")
            raise
    
    def update_assignment(self, assignment_id: int, updates: Dict[str, Any]) -> bool:
        """Update an existing assignment."""
        try:
            # Get current assignment for logging
            current = self.db.get_assignment(assignment_id)
            
            success = self.db.update_assignment(assignment_id, updates)
            
            if success and current:
                logger.info(f"Repository updated assignment {assignment_id}")
                
                # Log update for analytics if time changes
                try:
                    old_start = current.get('start_time')
                    old_end = current.get('end_time')
                    new_start = updates.get('start_time', old_start)
                    new_end = updates.get('end_time', old_end)
                    
                    if old_start != new_start or old_end != new_end:
                        self.db.log_assignment_change(
                            assignment_id=assignment_id,
                            old_start_time=old_start,
                            old_end_time=old_end,
                            new_start_time=new_start,
                            new_end_time=new_end,
                            reason="Assignment updated via repository",
                            vessel_id=current.get('vessel_id'),
                            vessel_name=current.get('vessel_name'),
                            jetty_name=updates.get('jetty_name', current.get('jetty_name')),
                            changed_by="system",
                            terminal_id=current.get('terminal_id')
                        )
                except Exception as log_e:
                    logger.warning(f"Failed to log assignment update: {log_e}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error updating assignment {assignment_id} in repository: {e}")
            return False
    
    def delete_assignment(self, assignment_id: int) -> bool:
        """Delete an assignment from database."""
        try:
            # Get assignment details for logging
            assignment = self.db.get_assignment(assignment_id)
            
            success = self.db.delete_assignment(assignment_id)
            
            if success and assignment:
                logger.info(f"Repository deleted assignment {assignment_id}")
                
                # Log deletion for analytics
                try:
                    self.db.log_assignment_change(
                        assignment_id=assignment_id,
                        old_start_time=assignment.get('start_time'),
                        old_end_time=assignment.get('end_time'),
                        new_start_time=None,
                        new_end_time=None,
                        reason="Assignment deleted via repository",
                        vessel_id=assignment.get('vessel_id'),
                        vessel_name=assignment.get('vessel_name'),
                        jetty_name=assignment.get('jetty_name'),
                        changed_by="system",
                        terminal_id=assignment.get('terminal_id')
                    )
                except Exception as log_e:
                    logger.warning(f"Failed to log assignment deletion: {log_e}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error deleting assignment {assignment_id} from repository: {e}")
            return False
    
    def get_assignments_by_vessel(self, vessel_id: str, terminal_id: str) -> List[Dict[str, Any]]:
        """Get all assignments for a specific vessel."""
        try:
            all_assignments = self.get_assignments(terminal_id)
            vessel_assignments = [a for a in all_assignments if str(a.get('vessel_id')) == str(vessel_id)]
            
            logger.debug(f"Repository found {len(vessel_assignments)} assignments for vessel {vessel_id}")
            return vessel_assignments
            
        except Exception as e:
            logger.error(f"Error getting assignments for vessel {vessel_id} from repository: {e}")
            return []
    
    def get_assignments_by_jetty(self, jetty_name: str, terminal_id: str) -> List[Dict[str, Any]]:
        """Get all assignments for a specific jetty."""
        try:
            all_assignments = self.get_assignments(terminal_id)
            jetty_assignments = [a for a in all_assignments if a.get('jetty_name') == jetty_name]
            
            logger.debug(f"Repository found {len(jetty_assignments)} assignments for jetty {jetty_name}")
            return jetty_assignments
            
        except Exception as e:
            logger.error(f"Error getting assignments for jetty {jetty_name} from repository: {e}")
            return []
    
    def get_assignments_by_date_range(self, start_date: datetime, end_date: datetime, terminal_id: str) -> List[Dict[str, Any]]:
        """Get assignments within a date range."""
        try:
            all_assignments = self.get_assignments(terminal_id)
            filtered_assignments = []
            
            for assignment in all_assignments:
                try:
                    # Parse assignment times
                    assignment_start = assignment.get('start_time')
                    assignment_end = assignment.get('end_time')
                    
                    if assignment_start and assignment_end:
                        if isinstance(assignment_start, str):
                            assignment_start = datetime.fromisoformat(assignment_start.replace('Z', '+00:00'))
                        if isinstance(assignment_end, str):
                            assignment_end = datetime.fromisoformat(assignment_end.replace('Z', '+00:00'))
                        
                        # Check if assignment overlaps with date range
                        if (assignment_start <= end_date and assignment_end >= start_date):
                            filtered_assignments.append(assignment)
                            
                except Exception as e:
                    logger.warning(f"Error parsing assignment {assignment.get('id')} dates: {e}")
                    continue
            
            logger.debug(f"Repository found {len(filtered_assignments)} assignments in date range")
            return filtered_assignments
            
        except Exception as e:
            logger.error(f"Error getting assignments by date range from repository: {e}")
            return []
    
    def replace_assignments(self, assignments: List[Dict[str, Any]], terminal_id: str) -> List[Dict[str, Any]]:
        """Replace all assignments for a terminal (used by optimization)."""
        try:
            created_assignments = self.db.replace_assignments(assignments, terminal_id)
            
            logger.info(f"Repository replaced assignments: {len(created_assignments)} assignments created")
            
            # Log bulk replacement for analytics
            try:
                self.db.log_assignment_change(
                    assignment_id=0,  # System operation
                    old_start_time=None,
                    old_end_time=None,
                    new_start_time=datetime.now().isoformat(),
                    new_end_time=None,
                    reason=f"Bulk assignment replacement: {len(created_assignments)} assignments created",
                    vessel_id="SYSTEM",
                    vessel_name="Assignment Repository",
                    jetty_name=None,
                    changed_by="system",
                    terminal_id=terminal_id
                )
            except Exception as log_e:
                logger.warning(f"Failed to log bulk assignment replacement: {log_e}")
            
            return created_assignments
            
        except Exception as e:
            logger.error(f"Error replacing assignments in repository: {e}")
            raise
    
    def clear_assignments(self, terminal_id: str) -> bool:
        """Clear all assignments for a terminal."""
        try:
            success = self.db.clear_assignments(terminal_id)
            
            if success:
                logger.info(f"Repository cleared all assignments for terminal {terminal_id}")
                
                # Log clearing for analytics
                try:
                    self.db.log_assignment_change(
                        assignment_id=0,
                        old_start_time=None,
                        old_end_time=None,
                        new_start_time=None,
                        new_end_time=None,
                        reason="All assignments cleared via repository",
                        vessel_id="SYSTEM",
                        vessel_name="Assignment Repository",
                        jetty_name=None,
                        changed_by="system",
                        terminal_id=terminal_id
                    )
                except Exception as log_e:
                    logger.warning(f"Failed to log assignment clearing: {log_e}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error clearing assignments in repository: {e}")
            return False


